"""
ETL Pipeline DAG for S3 dbt-Snowflake C360 Pipeline
Orchestrates the complete end-to-end data pipeline with monitoring
"""

from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.operators.bash import BashOperator
from airflow.operators.dummy import DummyOperator
from airflow.sensors.filesystem import FileSensor
from airflow.utils.dates import days_ago
import os
import sys
import subprocess
import logging

# Add workspace to Python path for imports
sys.path.append('/opt/airflow/workspace')

# Default arguments for the DAG
default_args = {
    'owner': 'data-team',
    'depends_on_past': False,
    'start_date': days_ago(1),
    'email_on_failure': True,
    'email_on_retry': False,
    'retries': 2,
    'retry_delay': timedelta(minutes=5),
    'catchup': False,
}

# DAG definition
dag = DAG(
    'etl_pipeline_daily',
    default_args=default_args,
    description='Daily ETL Pipeline with Health Monitoring',
    schedule_interval='0 6 * * *',  # Daily at 6 AM
    max_active_runs=1,
    tags=['etl', 'daily', 'snowflake', 'dbt'],
)

def run_data_generation(**context):
    """Generate daily data and upload to S3"""
    logging.info("Starting daily data generation...")
    
    # Change to workspace directory
    os.chdir('/opt/airflow/workspace')
    
    # Run the data generation script
    result = subprocess.run([
        'python', 'daily_data_generator.py'
    ], capture_output=True, text=True)
    
    if result.returncode != 0:
        logging.error(f"Data generation failed: {result.stderr}")
        raise Exception(f"Data generation failed: {result.stderr}")
    
    logging.info(f"Data generation completed: {result.stdout}")
    return "Data generation successful"

def refresh_snowflake_stages(**context):
    """Refresh Snowflake external stages"""
    logging.info("Refreshing Snowflake external stages...")
    
    os.chdir('/opt/airflow/workspace')
    
    result = subprocess.run([
        'python', 'create_snowflake_stages.py'
    ], capture_output=True, text=True)
    
    if result.returncode != 0:
        logging.error(f"Stage refresh failed: {result.stderr}")
        raise Exception(f"Stage refresh failed: {result.stderr}")
    
    logging.info(f"Stages refreshed: {result.stdout}")
    return "Stage refresh successful"

def run_dbt_pipeline(**context):
    """Run dbt models"""
    logging.info("Running dbt pipeline...")
    
    os.chdir('/opt/airflow/workspace/dbt_live')
    
    # Run dbt deps first
    deps_result = subprocess.run(['dbt', 'deps'], capture_output=True, text=True)
    if deps_result.returncode != 0:
        logging.error(f"dbt deps failed: {deps_result.stderr}")
        raise Exception(f"dbt deps failed: {deps_result.stderr}")
    
    # Run dbt models
    run_result = subprocess.run(['dbt', 'run'], capture_output=True, text=True)
    if run_result.returncode != 0:
        logging.error(f"dbt run failed: {run_result.stderr}")
        raise Exception(f"dbt run failed: {run_result.stderr}")
    
    logging.info(f"dbt pipeline completed: {run_result.stdout}")
    return "dbt pipeline successful"

def run_dbt_tests(**context):
    """Run dbt tests"""
    logging.info("Running dbt tests...")
    
    os.chdir('/opt/airflow/workspace/dbt_live')
    
    result = subprocess.run(['dbt', 'test'], capture_output=True, text=True)
    
    # dbt test returns non-zero if tests fail, but we want to capture results
    logging.info(f"dbt test results: {result.stdout}")
    if result.stderr:
        logging.warning(f"dbt test warnings: {result.stderr}")
    
    return "dbt tests completed"

def run_monitoring_models(**context):
    """Run monitoring and health check models"""
    logging.info("Running monitoring models...")
    
    os.chdir('/opt/airflow/workspace/dbt_live')
    
    result = subprocess.run([
        'dbt', 'run', '--models', 'monitoring'
    ], capture_output=True, text=True)
    
    if result.returncode != 0:
        logging.error(f"Monitoring models failed: {result.stderr}")
        raise Exception(f"Monitoring models failed: {result.stderr}")
    
    logging.info(f"Monitoring models completed: {result.stdout}")
    return "Monitoring models successful"

def generate_health_report(**context):
    """Generate health dashboard and alerts"""
    logging.info("Generating health report...")
    
    os.chdir('/opt/airflow/workspace')
    
    # Run health dashboard
    dashboard_result = subprocess.run([
        'python', 'show_etl_health_dashboard.py'
    ], capture_output=True, text=True)
    
    if dashboard_result.returncode != 0:
        logging.error(f"Health dashboard failed: {dashboard_result.stderr}")
        raise Exception(f"Health dashboard failed: {dashboard_result.stderr}")
    
    # Run alert monitoring
    alert_result = subprocess.run([
        'python', 'monitor_pipeline_alerts.py'
    ], capture_output=True, text=True)
    
    if alert_result.returncode != 0:
        logging.warning(f"Alert monitoring had issues: {alert_result.stderr}")
    
    logging.info(f"Health report generated: {dashboard_result.stdout}")
    return "Health report successful"

def validate_pipeline_results(**context):
    """Validate final pipeline results"""
    logging.info("Validating pipeline results...")
    
    os.chdir('/opt/airflow/workspace')
    
    result = subprocess.run([
        'python', 'check_snowflake_dbt_tables.py'
    ], capture_output=True, text=True)
    
    if result.returncode != 0:
        logging.error(f"Pipeline validation failed: {result.stderr}")
        raise Exception(f"Pipeline validation failed: {result.stderr}")
    
    logging.info(f"Pipeline validation completed: {result.stdout}")
    return "Pipeline validation successful"

# Task definitions
start_task = DummyOperator(
    task_id='start_pipeline',
    dag=dag,
)

generate_data_task = PythonOperator(
    task_id='generate_daily_data',
    python_callable=run_data_generation,
    dag=dag,
)

refresh_stages_task = PythonOperator(
    task_id='refresh_snowflake_stages',
    python_callable=refresh_snowflake_stages,
    dag=dag,
)

run_dbt_task = PythonOperator(
    task_id='run_dbt_pipeline',
    python_callable=run_dbt_pipeline,
    dag=dag,
)

run_tests_task = PythonOperator(
    task_id='run_dbt_tests',
    python_callable=run_dbt_tests,
    dag=dag,
)

run_monitoring_task = PythonOperator(
    task_id='run_monitoring_models',
    python_callable=run_monitoring_models,
    dag=dag,
)

generate_report_task = PythonOperator(
    task_id='generate_health_report',
    python_callable=generate_health_report,
    dag=dag,
)

validate_results_task = PythonOperator(
    task_id='validate_pipeline_results',
    python_callable=validate_pipeline_results,
    dag=dag,
)

end_task = DummyOperator(
    task_id='end_pipeline',
    dag=dag,
)

# Task dependencies
start_task >> generate_data_task >> refresh_stages_task >> run_dbt_task
run_dbt_task >> run_tests_task >> run_monitoring_task
run_monitoring_task >> generate_report_task >> validate_results_task >> end_task
