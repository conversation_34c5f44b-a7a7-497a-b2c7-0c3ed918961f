
  
    

create or replace transient table MYDB.LIVE_DATA.dbt_test_health
    

    
    as (

-- ETL Health Check: dbt Test Results Monitoring
-- Tracks dbt test execution and results

with test_metadata as (
    -- This would typically come from dbt artifacts or a custom logging solution
    -- For now, we'll create a mock structure based on our known tests
    select 'source_not_null_live_external_ext_live_users_id' as test_name,
           'source' as test_type,
           'ext_live_users' as model_name,
           'id' as column_name,
           'not_null' as test_category,
           'pass' as status,
           0 as failures,
           current_timestamp() - interval '1 hour' as execution_time
    
    union all
    
    select 'source_unique_live_external_ext_live_users_id' as test_name,
           'source' as test_type,
           'ext_live_users' as model_name,
           'id' as column_name,
           'unique' as test_category,
           'pass' as status,
           0 as failures,
           current_timestamp() - interval '1 hour' as execution_time
    
    union all
    
    select 'source_not_null_live_external_ext_live_orders_id' as test_name,
           'source' as test_type,
           'ext_live_orders' as model_name,
           'id' as column_name,
           'not_null' as test_category,
           'pass' as status,
           0 as failures,
           current_timestamp() - interval '1 hour' as execution_time
    
    union all
    
    select 'source_relationships_live_external_ext_live_orders_user_id' as test_name,
           'source' as test_type,
           'ext_live_orders' as model_name,
           'user_id' as column_name,
           'relationships' as test_category,
           'pass' as status,
           0 as failures,
           current_timestamp() - interval '1 hour' as execution_time
    
    union all
    
    select 'source_accepted_values_live_external_ext_live_events_action' as test_name,
           'source' as test_type,
           'ext_live_events' as model_name,
           'action' as column_name,
           'accepted_values' as test_category,
           'pass' as status,
           0 as failures,
           current_timestamp() - interval '1 hour' as execution_time
),

test_summary as (
    select
        date(execution_time) as test_date,
        test_type,
        model_name,
        test_category,
        
        -- Test counts
        count(*) as total_tests,
        sum(case when status = 'pass' then 1 else 0 end) as passed_tests,
        sum(case when status = 'fail' then 1 else 0 end) as failed_tests,
        sum(case when status = 'warn' then 1 else 0 end) as warning_tests,
        sum(case when status = 'skip' then 1 else 0 end) as skipped_tests,
        
        -- Failure details
        sum(failures) as total_failures,
        max(execution_time) as latest_execution,
        
        -- Calculate pass rate
        round((sum(case when status = 'pass' then 1 else 0 end)::float / count(*)) * 100, 2) as pass_rate
        
    from test_metadata
    group by date(execution_time), test_type, model_name, test_category
),

model_coverage as (
    -- Calculate test coverage per model
    select
        model_name,
        count(distinct test_category) as test_types_covered,
        count(*) as total_tests_on_model,
        
        -- Check for essential test coverage
        max(case when test_category = 'not_null' then 1 else 0 end) as has_not_null_tests,
        max(case when test_category = 'unique' then 1 else 0 end) as has_unique_tests,
        max(case when test_category = 'relationships' then 1 else 0 end) as has_relationship_tests,
        max(case when test_category = 'accepted_values' then 1 else 0 end) as has_accepted_values_tests
        
    from test_metadata
    group by model_name
),

health_scores as (
    select
        ts.*,
        mc.test_types_covered,
        mc.total_tests_on_model,
        mc.has_not_null_tests,
        mc.has_unique_tests,
        mc.has_relationship_tests,
        mc.has_accepted_values_tests,
        
        -- Test reliability score
        case 
            when pass_rate = 100 then 100
            when pass_rate >= 95 then 90
            when pass_rate >= 90 then 80
            when pass_rate >= 80 then 70
            else 50
        end as reliability_score,
        
        -- Test coverage score
        case 
            when test_types_covered >= 4 then 100
            when test_types_covered >= 3 then 80
            when test_types_covered >= 2 then 60
            when test_types_covered >= 1 then 40
            else 20
        end as coverage_score
        
    from test_summary ts
    left join model_coverage mc on ts.model_name = mc.model_name
)

select
    test_date,
    test_type,
    model_name,
    test_category,
    total_tests,
    passed_tests,
    failed_tests,
    warning_tests,
    skipped_tests,
    total_failures,
    pass_rate,
    test_types_covered,
    total_tests_on_model,
    has_not_null_tests,
    has_unique_tests,
    has_relationship_tests,
    has_accepted_values_tests,
    reliability_score,
    coverage_score,
    
    -- Overall test health score
    round((reliability_score * 0.7 + coverage_score * 0.3), 0) as overall_test_health_score,
    
    -- Test status
    case 
        when pass_rate = 100 and coverage_score >= 80 then 'Excellent'
        when pass_rate >= 95 and coverage_score >= 60 then 'Good'
        when pass_rate >= 90 and coverage_score >= 40 then 'Fair'
        when pass_rate >= 80 then 'Poor'
        else 'Critical'
    end as test_health_status,
    
    latest_execution,
    current_timestamp() as health_check_timestamp
    
from health_scores
order by test_date desc, model_name, test_category
    )
;


  