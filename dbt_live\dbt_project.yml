name: 'live_c360'
version: '1.0.0'
config-version: 2

# This setting configures which "profile" dbt uses for this project.
profile: 'live_c360'

# These configurations specify where dbt should look for different types of files.
model-paths: ["models"]
analysis-paths: ["analyses"]
test-paths: ["tests"]
seed-paths: ["seeds"]
macro-paths: ["macros"]
snapshot-paths: ["snapshots"]

target-path: "target"
clean-targets:
  - "target"
  - "dbt_packages"

# Configuring models
models:
  live_c360:
    # Config indicated by + and applies to all files under models/example/
    staging:
      +materialized: view
      +tags: ["staging"]
    marts:
      +materialized: table
      +tags: ["marts"]
    monitoring:
      +materialized: table
      +tags: ["monitoring", "health_check"]
    incremental:
      +materialized: incremental
      +on_schema_change: "sync_all_columns"
      +tags: ["incremental"]

# Global variables
vars:
  # dbt-utils
  'dbt_date:time_zone': 'Australia/Sydney'

  # Live data pipeline settings
  live_data_lookback_days: 7
  batch_processing_enabled: true
  data_quality_checks_enabled: true

  # Snowflake specific
  database: 'MYDB'
  schema: 'LIVE_DATA'

# Seeds configuration
seeds:
  live_c360:
    +quote_columns: false

# Data tests configuration (updated from deprecated 'tests')
data_tests:
  live_c360:
    +store_failures: true
    +severity: 'warn'

# Snapshots configuration
snapshots:
  live_c360:
    +target_schema: 'snapshots'
    +strategy: 'timestamp'
    +updated_at: 'updated_at'

# Documentation configuration removed for compatibility
