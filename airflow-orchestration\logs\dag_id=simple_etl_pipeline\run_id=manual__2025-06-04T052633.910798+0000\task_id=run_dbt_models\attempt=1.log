[2025-06-04T05:26:43.692+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=non-requeueable deps ti=<TaskInstance: simple_etl_pipeline.run_dbt_models manual__2025-06-04T05:26:33.910798+00:00 [queued]>
[2025-06-04T05:26:43.708+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=requeueable deps ti=<TaskInstance: simple_etl_pipeline.run_dbt_models manual__2025-06-04T05:26:33.910798+00:00 [queued]>
[2025-06-04T05:26:43.709+0000] {taskinstance.py:2170} INFO - Starting attempt 1 of 2
[2025-06-04T05:26:43.729+0000] {taskinstance.py:2191} INFO - Executing <Task(PythonOperator): run_dbt_models> on 2025-06-04 05:26:33.910798+00:00
[2025-06-04T05:26:43.739+0000] {standard_task_runner.py:60} INFO - Started process 877 to run task
[2025-06-04T05:26:43.749+0000] {standard_task_runner.py:87} INFO - Running: ['***', 'tasks', 'run', 'simple_etl_pipeline', 'run_dbt_models', 'manual__2025-06-04T05:26:33.910798+00:00', '--job-id', '98', '--raw', '--subdir', 'DAGS_FOLDER/simple_etl_dag.py', '--cfg-path', '/tmp/tmpkvc0q4wn']
[2025-06-04T05:26:43.751+0000] {standard_task_runner.py:88} INFO - Job 98: Subtask run_dbt_models
[2025-06-04T05:26:43.820+0000] {task_command.py:423} INFO - Running <TaskInstance: simple_etl_pipeline.run_dbt_models manual__2025-06-04T05:26:33.910798+00:00 [running]> on host 8fb7c8dc5097
[2025-06-04T05:26:43.931+0000] {taskinstance.py:2480} INFO - Exporting env vars: AIRFLOW_CTX_DAG_OWNER='data-team' AIRFLOW_CTX_DAG_ID='simple_etl_pipeline' AIRFLOW_CTX_TASK_ID='run_dbt_models' AIRFLOW_CTX_EXECUTION_DATE='2025-06-04T05:26:33.910798+00:00' AIRFLOW_CTX_TRY_NUMBER='1' AIRFLOW_CTX_DAG_RUN_ID='manual__2025-06-04T05:26:33.910798+00:00'
[2025-06-04T05:26:43.936+0000] {simple_etl_dag.py:78} INFO - Running dbt run using Python API
[2025-06-04T05:26:46.230+0000] {simple_etl_dag.py:109} INFO - Executing dbt run
[2025-06-04T05:26:46.329+0000] {logging_mixin.py:188} INFO - 05:26:46  Running with dbt=1.9.6
[2025-06-04T05:26:47.451+0000] {logging_mixin.py:188} INFO - 05:26:47  Registered adapter: snowflake=1.9.4
[2025-06-04T05:26:47.840+0000] {logging_mixin.py:188} INFO - 05:26:47  Unable to do partial parsing because saved manifest not found. Starting full parse.
[2025-06-04T05:26:51.002+0000] {logging_mixin.py:188} INFO - 05:26:51  [WARNING]: Configuration paths exist in your dbt_project.yml file which do not apply to any resources.
There are 3 unused configuration paths:
- models.live_c360.incremental
- seeds.live_c360
- snapshots.live_c360
[2025-06-04T05:26:51.536+0000] {logging_mixin.py:188} INFO - 05:26:51  Found 12 models, 21 data tests, 3 sources, 590 macros
[2025-06-04T05:26:51.544+0000] {logging_mixin.py:188} INFO - 05:26:51
[2025-06-04T05:26:51.546+0000] {logging_mixin.py:188} INFO - 05:26:51  Concurrency: 4 threads (target='live')
[2025-06-04T05:26:51.547+0000] {logging_mixin.py:188} INFO - 05:26:51
[2025-06-04T05:26:51.617+0000] {connection.py:486} INFO - Snowflake Connector for Python Version: 3.15.0, Python Version: 3.10.13, Platform: Linux-********-microsoft-standard-WSL2-x86_64-with-glibc2.36
[2025-06-04T05:26:51.618+0000] {connection.py:1391} INFO - Connecting to GLOBAL Snowflake domain
[2025-06-04T05:26:51.618+0000] {connection.py:486} INFO - Snowflake Connector for Python Version: 3.15.0, Python Version: 3.10.13, Platform: Linux-********-microsoft-standard-WSL2-x86_64-with-glibc2.36
[2025-06-04T05:26:51.621+0000] {connection.py:1391} INFO - Connecting to GLOBAL Snowflake domain
[2025-06-04T05:26:52.309+0000] {connection.py:486} INFO - Snowflake Connector for Python Version: 3.15.0, Python Version: 3.10.13, Platform: Linux-********-microsoft-standard-WSL2-x86_64-with-glibc2.36
[2025-06-04T05:26:52.310+0000] {connection.py:1391} INFO - Connecting to GLOBAL Snowflake domain
[2025-06-04T05:26:52.953+0000] {logging_mixin.py:188} INFO - 05:26:52  1 of 12 START sql table model LIVE_DATA.dbt_test_health ........................ [RUN]
[2025-06-04T05:26:52.955+0000] {logging_mixin.py:188} INFO - 05:26:52  2 of 12 START sql table model LIVE_DATA.pipeline_runtime_health ................ [RUN]
[2025-06-04T05:26:52.961+0000] {logging_mixin.py:188} INFO - 05:26:52  3 of 12 START sql table model LIVE_DATA.query_history_health ................... [RUN]
[2025-06-04T05:26:52.963+0000] {logging_mixin.py:188} INFO - 05:26:52  4 of 12 START sql view model LIVE_DATA.stg_events .............................. [RUN]
[2025-06-04T05:26:53.212+0000] {connection.py:486} INFO - Snowflake Connector for Python Version: 3.15.0, Python Version: 3.10.13, Platform: Linux-********-microsoft-standard-WSL2-x86_64-with-glibc2.36
[2025-06-04T05:26:53.213+0000] {connection.py:1391} INFO - Connecting to GLOBAL Snowflake domain
[2025-06-04T05:26:53.750+0000] {logging_mixin.py:188} INFO - 05:26:53  4 of 12 ERROR creating sql view model LIVE_DATA.stg_events ..................... [ERROR in 0.77s]
[2025-06-04T05:26:53.754+0000] {logging_mixin.py:188} INFO - 05:26:53  5 of 12 START sql view model LIVE_DATA.stg_orders .............................. [RUN]
[2025-06-04T05:26:53.937+0000] {logging_mixin.py:188} INFO - 05:26:53  5 of 12 ERROR creating sql view model LIVE_DATA.stg_orders ..................... [ERROR in 0.16s]
[2025-06-04T05:26:53.996+0000] {logging_mixin.py:188} INFO - 05:26:53  1 of 12 OK created sql table model LIVE_DATA.dbt_test_health ................... [SUCCESS 1 in 1.03s]
[2025-06-04T05:26:53.998+0000] {logging_mixin.py:188} INFO - 05:26:53  6 of 12 START sql view model LIVE_DATA.stg_users ............................... [RUN]
[2025-06-04T05:26:54.009+0000] {logging_mixin.py:188} INFO - 05:26:54  7 of 12 SKIP relation LIVE_DATA.fact_orders .................................... [SKIP]
[2025-06-04T05:26:54.020+0000] {logging_mixin.py:188} INFO - 05:26:54  8 of 12 SKIP relation LIVE_DATA_ANALYTICS.mv_fact_orders ....................... [SKIP]
[2025-06-04T05:26:54.170+0000] {logging_mixin.py:188} INFO - 05:26:54  6 of 12 ERROR creating sql view model LIVE_DATA.stg_users ...................... [ERROR in 0.16s]
[2025-06-04T05:26:54.193+0000] {logging_mixin.py:188} INFO - 05:26:54  9 of 12 SKIP relation LIVE_DATA.dim_users ...................................... [SKIP]
[2025-06-04T05:26:54.195+0000] {logging_mixin.py:188} INFO - 05:26:54  10 of 12 SKIP relation LIVE_DATA.dim_users_scd2 ................................ [SKIP]
[2025-06-04T05:26:54.203+0000] {logging_mixin.py:188} INFO - 05:26:54  11 of 12 SKIP relation LIVE_DATA.data_quality_health ........................... [SKIP]
[2025-06-04T05:26:54.207+0000] {logging_mixin.py:188} INFO - 05:26:54  12 of 12 SKIP relation LIVE_DATA.etl_health_dashboard .......................... [SKIP]
[2025-06-04T05:26:55.380+0000] {logging_mixin.py:188} INFO - 05:26:55  2 of 12 OK created sql table model LIVE_DATA.pipeline_runtime_health ........... [SUCCESS 1 in 2.39s]
[2025-06-04T05:26:55.512+0000] {logging_mixin.py:188} INFO - 05:26:55  3 of 12 OK created sql table model LIVE_DATA.query_history_health .............. [SUCCESS 1 in 2.51s]
[2025-06-04T05:26:55.834+0000] {logging_mixin.py:188} INFO - 05:26:55
[2025-06-04T05:26:55.837+0000] {logging_mixin.py:188} INFO - 05:26:55  Finished running 1 incremental model, 7 table models, 4 view models in 0 hours 0 minutes and 4.28 seconds (4.28s).
[2025-06-04T05:26:55.960+0000] {logging_mixin.py:188} INFO - 05:26:55
[2025-06-04T05:26:55.967+0000] {logging_mixin.py:188} INFO - 05:26:55  Completed with 3 errors, 0 partial successes, and 0 warnings:
[2025-06-04T05:26:55.982+0000] {logging_mixin.py:188} INFO - 05:26:55
[2025-06-04T05:26:55.989+0000] {logging_mixin.py:188} INFO - 05:26:55    Database Error in model stg_events (models/staging/stg_events.sql)
  091093 (55000): 01bccc26-3204-802b-0002-4ad60006809e: External table EXT_LIVE_EVENTS marked invalid. Stage S3_LIVE_EVENTS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_events.sql
[2025-06-04T05:26:55.991+0000] {logging_mixin.py:188} INFO - 05:26:55
[2025-06-04T05:26:56.002+0000] {logging_mixin.py:188} INFO - 05:26:56    Database Error in model stg_orders (models/staging/stg_orders.sql)
  091093 (55000): 01bccc26-3204-7ee0-0002-4ad60005c13e: External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_orders.sql
[2025-06-04T05:26:56.007+0000] {logging_mixin.py:188} INFO - 05:26:56
[2025-06-04T05:26:56.012+0000] {logging_mixin.py:188} INFO - 05:26:56    Database Error in model stg_users (models/staging/stg_users.sql)
  091093 (55000): 01bccc26-3204-7f69-0002-4ad60005d192: External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_users.sql
[2025-06-04T05:26:56.021+0000] {logging_mixin.py:188} INFO - 05:26:56
[2025-06-04T05:26:56.032+0000] {logging_mixin.py:188} INFO - 05:26:56  Done. PASS=3 WARN=0 ERROR=3 SKIP=6 TOTAL=12
[2025-06-04T05:26:57.230+0000] {simple_etl_dag.py:116} ERROR - dbt run failed
[2025-06-04T05:26:57.231+0000] {taskinstance.py:2698} ERROR - Task failed with exception
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/models/taskinstance.py", line 433, in _execute_task
    result = execute_callable(context=context, **execute_callable_kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/operators/python.py", line 199, in execute
    return_value = self.execute_callable()
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/operators/python.py", line 216, in execute_callable
    return self.python_callable(*self.op_args, **self.op_kwargs)
  File "/opt/airflow/dags/simple_etl_dag.py", line 219, in run_dbt_models
    return run_dbt_command_python('run', **context)
  File "/opt/airflow/dags/simple_etl_dag.py", line 117, in run_dbt_command_python
    raise Exception(f"dbt {command_type} failed")
Exception: dbt run failed
[2025-06-04T05:26:57.245+0000] {taskinstance.py:1138} INFO - Marking task as UP_FOR_RETRY. dag_id=simple_etl_pipeline, task_id=run_dbt_models, execution_date=20250604T052633, start_date=20250604T052643, end_date=20250604T052657
[2025-06-04T05:26:57.260+0000] {standard_task_runner.py:107} ERROR - Failed to execute job 98 for task run_dbt_models (dbt run failed; 877)
[2025-06-04T05:26:57.299+0000] {local_task_job_runner.py:234} INFO - Task exited with return code 1
[2025-06-04T05:26:57.334+0000] {taskinstance.py:3280} INFO - 0 downstream tasks scheduled from follow-on schedule check
