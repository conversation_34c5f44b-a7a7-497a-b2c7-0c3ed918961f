# AWS & Snowflake Learning Guide for Azure/Databricks Experts

## 🎯 Overview
This guide maps your Azure/Databricks knowledge to AWS/Snowflake concepts using our real ETL pipeline as examples.

## 📊 Platform Comparison Matrix

| Concept | Azure/Databricks | AWS/Snowflake | Our Pipeline Example |
|---------|------------------|---------------|---------------------|
| **Data Lake** | Azure Data Lake Storage Gen2 | Amazon S3 | `s3://lake-loader-input-************-********-001439/` |
| **Compute Engine** | Databricks Clusters | Snowflake Virtual Warehouses | `COMPUTE_WH` |
| **Data Warehouse** | Azure Synapse Analytics | Snowflake | `MYDB.LIVE_DATA` schema |
| **ETL Orchestration** | Azure Data Factory | AWS Glue / Airflow | Our `run_end2end_pipeline.py` |
| **Identity Management** | Azure AD | AWS IAM | `SnowflakeS3AccessRole` |
| **Data Transformation** | Databricks Notebooks | dbt + Snowflake SQL | `dbt_live/models/` |
| **File Formats** | Delta Lake | Parquet/CSV + Iceberg | CSV files with external tables |
| **Streaming** | Databricks Structured Streaming | Snowpipe | External tables with auto-refresh |
| **Security** | Azure Key Vault | AWS Secrets Manager | IAM roles and policies |

---

## 🏗️ Core AWS Concepts You Need to Know

### 1. **Amazon S3 (Simple Storage Service)**
**Azure Equivalent**: Azure Blob Storage / ADLS Gen2

#### Key Concepts:
- **Buckets**: Top-level containers (like Azure containers)
- **Objects**: Files stored in buckets
- **Prefixes**: Folder-like organization (no real folders)
- **Lifecycle Policies**: Automatic data archiving

#### In Our Pipeline:
```bash
# Our S3 structure
s3://lake-loader-input-************-********-001439/
├── live-data/
│   ├── users/daily_users_20250125.csv
│   ├── orders/daily_orders_20250125.csv
│   └── events/daily_events_20250125.csv
```

#### Where to See in AWS Console:
1. **AWS Console** → **S3**
2. **Buckets** → `lake-loader-input-************-********-001439`
3. **Objects** → Browse folders: `live-data/users/`, `live-data/orders/`, etc.
4. **Properties** → See encryption, versioning, lifecycle rules

#### Key AWS CLI Commands:
```bash
# List buckets (like az storage account list)
aws s3 ls

# List objects in bucket
aws s3 ls s3://lake-loader-input-************-********-001439/live-data/ --recursive

# Copy file to S3
aws s3 cp local_file.csv s3://bucket-name/path/

# Sync directory
aws s3 sync ./local-folder s3://bucket-name/remote-folder/
```

### 2. **AWS IAM (Identity and Access Management)**
**Azure Equivalent**: Azure Active Directory + RBAC

#### Key Concepts:
- **Users**: Individual identities
- **Roles**: Assumable identities with permissions
- **Policies**: JSON documents defining permissions
- **Trust Relationships**: Who can assume a role

#### In Our Pipeline:
```json
// Trust Policy (who can assume the role)
{
  "Version": "2012-10-17",
  "Statement": [{
    "Effect": "Allow",
    "Principal": {
      "AWS": "arn:aws:iam::************:user/k7m2-s-v2st2732"  // Snowflake's AWS user
    },
    "Action": "sts:AssumeRole",
    "Condition": {
      "StringEquals": {
        "sts:ExternalId": "SVLFKJI_SFCRole=2_L/MM2FeX9Dp2V/C="  // Snowflake's external ID
      }
    }
  }]
}
```

#### Where to See in AWS Console:
1. **AWS Console** → **IAM**
2. **Roles** → `SnowflakeS3AccessRole`
3. **Trust relationships** → See who can assume this role
4. **Permissions** → See attached policies
5. **Policies** → `SnowflakeS3AccessPolicy` → See JSON permissions

#### Key Differences from Azure:
- **Azure**: Service Principal + App Registration
- **AWS**: IAM Role + Trust Policy
- **Azure**: Resource Groups for organization
- **AWS**: Tags and naming conventions

### 3. **AWS Regions and Availability Zones**
**Azure Equivalent**: Azure Regions and Availability Zones

#### Our Setup:
- **Region**: `ap-southeast-2` (Sydney)
- **Snowflake Account**: `SVLFKJI-IX89869` (region-specific)

#### Where to See:
1. **AWS Console** → Top-right corner shows current region
2. **S3** → Bucket properties → Region
3. **IAM** → Global service (no region)

---

## ❄️ Core Snowflake Concepts You Need to Know

### 1. **Snowflake Architecture**
**Databricks Equivalent**: Separated compute and storage

#### Key Concepts:
- **Storage Layer**: Automatically managed (like ADLS Gen2)
- **Compute Layer**: Virtual Warehouses (like Databricks clusters)
- **Services Layer**: Query processing and metadata

#### In Our Pipeline:
```sql
-- Our Snowflake hierarchy
ACCOUNT: SVLFKJI-IX89869
├── DATABASE: MYDB
│   └── SCHEMA: LIVE_DATA
│       ├── WAREHOUSE: COMPUTE_WH
│       ├── EXTERNAL TABLES: EXT_LIVE_USERS, EXT_LIVE_ORDERS, EXT_LIVE_EVENTS
│       └── DBT MODELS: STG_USERS, DIM_USERS, etc.
```

#### Where to See in Snowflake:
1. **Snowflake Web UI** → **Databases** → `MYDB` → `LIVE_DATA`
2. **Warehouses** → `COMPUTE_WH` → See size, auto-suspend settings
3. **Account** → **Usage** → Monitor compute credits

### 2. **Virtual Warehouses**
**Databricks Equivalent**: Compute Clusters

#### Key Concepts:
- **Sizes**: X-Small to 6X-Large (like cluster node types)
- **Auto-suspend**: Automatically pause when idle
- **Auto-resume**: Start when queries arrive
- **Multi-cluster**: Scale out for concurrency

#### In Our Pipeline:
```sql
-- Our warehouse configuration
WAREHOUSE: COMPUTE_WH
├── Size: X-SMALL (1 credit/hour)
├── Auto-suspend: 10 minutes
├── Auto-resume: Enabled
└── Max clusters: 1
```

#### Where to See:
1. **Snowflake** → **Admin** → **Warehouses**
2. **COMPUTE_WH** → **Configure** → See size and auto-suspend
3. **Account** → **Usage** → **Warehouses** → Monitor credit consumption

#### Key Differences from Databricks:
- **Databricks**: Choose driver/worker node types, autoscaling
- **Snowflake**: Choose warehouse size, auto-suspend/resume
- **Databricks**: Cluster warmup time
- **Snowflake**: Instant resume from cache

### 3. **External Tables and Stages**
**Databricks Equivalent**: External tables with mount points

#### Key Concepts:
- **Stages**: Pointers to external storage (S3, Azure, GCS)
- **File Formats**: Define how to parse files
- **External Tables**: Query external data without loading
- **Storage Integrations**: Secure connection to cloud storage

#### In Our Pipeline:
```sql
-- Storage Integration (like Databricks mount)
CREATE STORAGE INTEGRATION S3_LIVE_INTEGRATION
  TYPE = EXTERNAL_STAGE
  STORAGE_PROVIDER = 'S3'
  STORAGE_AWS_ROLE_ARN = 'arn:aws:iam::YOUR_ACCOUNT:role/SnowflakeS3AccessRole'
  STORAGE_ALLOWED_LOCATIONS = ('s3://bucket-name/live-data/');

-- External Stage (like Databricks external location)
CREATE STAGE S3_LIVE_USERS_STAGE
  STORAGE_INTEGRATION = S3_LIVE_INTEGRATION
  URL = 's3://bucket-name/live-data/users/'
  FILE_FORMAT = CSV_LIVE_FORMAT;

-- External Table (like Databricks external table)
CREATE EXTERNAL TABLE EXT_LIVE_USERS (
  id STRING AS (value:c1::STRING),
  firstname STRING AS (value:c2::STRING),
  -- ... more columns
)
WITH LOCATION = @S3_LIVE_USERS_STAGE
AUTO_REFRESH = TRUE;
```

#### Where to See:
1. **Snowflake** → **Data** → **Databases** → `MYDB` → `LIVE_DATA`
2. **Stages** → `S3_LIVE_USERS_STAGE` → Test and browse files
3. **Tables** → `EXT_LIVE_USERS` → Query external data
4. **Account** → **Security** → **Storage Integrations**

### 4. **Snowflake SQL Extensions**
**Databricks Equivalent**: Spark SQL + Delta Lake functions

#### Key Functions:
```sql
-- Semi-structured data (like Databricks complex types)
SELECT value:c1::STRING as id FROM external_table;

-- Time travel (like Delta Lake time travel)
SELECT * FROM table AT (TIMESTAMP => '2025-01-15 10:00:00');

-- Cloning (like Delta Lake shallow clone)
CREATE TABLE new_table CLONE existing_table;

-- Streams (like Databricks Delta Live Tables)
CREATE STREAM user_stream ON TABLE users;
```

---

## 🔧 Hands-On Learning Path

### Phase 1: AWS Fundamentals (Week 1)
1. **S3 Exploration**:
   ```bash
   # Run our data generator
   python daily_data_generator.py
   
   # Explore in AWS Console
   # Go to S3 → Your bucket → Browse generated files
   ```

2. **IAM Understanding**:
   ```bash
   # Run our AWS setup
   python setup_aws_credentials.py
   
   # Explore in AWS Console
   # Go to IAM → Roles → SnowflakeS3AccessRole
   ```

### Phase 2: Snowflake Fundamentals (Week 2)
1. **External Tables**:
   ```bash
   # Set up Snowflake stages
   python create_snowflake_stages.py --role-arn "YOUR_ROLE_ARN"
   
   # Query in Snowflake
   SELECT COUNT(*) FROM EXT_LIVE_USERS;
   ```

2. **Virtual Warehouses**:
   ```sql
   -- In Snowflake worksheet
   USE WAREHOUSE COMPUTE_WH;
   SHOW WAREHOUSES;
   ALTER WAREHOUSE COMPUTE_WH SET WAREHOUSE_SIZE = 'MEDIUM';
   ```

### Phase 3: ETL Pipeline (Week 3)
1. **End-to-End Pipeline**:
   ```bash
   # Run complete pipeline
   python run_end2end_pipeline.py
   
   # Monitor in both AWS and Snowflake
   ```

2. **dbt Integration**:
   ```bash
   cd dbt_live
   dbt run --models staging
   dbt test
   ```

---

## 🎓 Learning Resources

### AWS Learning:
1. **AWS Console**: https://console.aws.amazon.com/
2. **AWS CLI Documentation**: https://docs.aws.amazon.com/cli/
3. **S3 User Guide**: https://docs.aws.amazon.com/s3/
4. **IAM User Guide**: https://docs.aws.amazon.com/iam/

### Snowflake Learning:
1. **Snowflake Web UI**: https://app.snowflake.com/
2. **Snowflake Documentation**: https://docs.snowflake.com/
3. **SQL Reference**: https://docs.snowflake.com/en/sql-reference
4. **External Tables Guide**: https://docs.snowflake.com/en/user-guide/tables-external

### Comparison Guides:
1. **Azure to AWS**: https://docs.aws.amazon.com/whitepapers/latest/aws-microsoft-workload-comparison/
2. **Databricks to Snowflake**: https://www.snowflake.com/guides/databricks-vs-snowflake/

---

## 🔍 Troubleshooting Common Issues

### AWS Issues:
```bash
# Check AWS credentials
aws sts get-caller-identity

# Check S3 access
aws s3 ls s3://your-bucket-name/

# Check IAM role
aws iam get-role --role-name SnowflakeS3AccessRole
```

### Snowflake Issues:
```sql
-- Check warehouse status
SHOW WAREHOUSES;

-- Check external table refresh
ALTER EXTERNAL TABLE EXT_LIVE_USERS REFRESH;

-- Check storage integration
DESC STORAGE INTEGRATION S3_LIVE_INTEGRATION;
```

---

## 📝 Next Steps

1. **Run our test pipeline**: `python test_daily_pipeline.py`
2. **Explore AWS Console**: Navigate S3 and IAM
3. **Explore Snowflake UI**: Query external tables
4. **Modify our pipeline**: Add your own transformations
5. **Scale up**: Increase data volumes and warehouse sizes

This guide provides the foundation to understand AWS/Snowflake through hands-on experience with our real ETL pipeline! 🚀
