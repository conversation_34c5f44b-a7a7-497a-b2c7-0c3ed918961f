# DAG Comparison Guide

This document compares the different dbt DAGs available in your Airflow setup.

## 📊 DAG Overview

| DAG | Purpose | Tasks | Execution Time | Use Case |
|-----|---------|-------|----------------|----------|
| `dbt_only_pipeline` | Full dbt pipeline (no staging) | 10 tasks | 10-15 minutes | Complete data transformation |
| `dbt_marts_only` | Marts models only | 5 tasks | 2-5 minutes | Quick business logic refresh |

## 🔍 Detailed Comparison

### dbt_only_pipeline (Full Pipeline)

**File**: `airflow-orchestration/dags/dbt_only_dag.py`

#### Task Flow
```
Start → Debug → Deps → Refresh Stages → Marts → Test Marts → Monitoring → Test All → Validate → Docs → Cleanup → End
```

#### Features
- ✅ **Complete pipeline** - Full dbt transformation workflow
- ✅ **External table refresh** - Fixes Snowflake stage issues
- ✅ **Health checks** - Connection validation and debugging
- ✅ **Monitoring models** - Data quality and health metrics
- ✅ **Documentation** - Optional dbt docs generation
- ✅ **Comprehensive testing** - Tests at multiple levels
- ✅ **Result validation** - Thorough pipeline verification

#### When to Use
- **Full data refresh** needed
- **External table issues** need fixing
- **Complete validation** required
- **Monitoring data** needs updating
- **Documentation** generation needed
- **Production workflows**

#### Execution Time
- **Typical**: 10-15 minutes
- **With docs**: 15-20 minutes

---

### dbt_marts_only (Minimal Pipeline)

**File**: `airflow-orchestration/dags/dbt_marts_only_dag.py`

#### Task Flow
```
Start → Run Marts → Test Marts → Validate → End
```

#### Features
- ✅ **Minimal overhead** - Only essential tasks
- ✅ **Fast execution** - Quick turnaround
- ✅ **Business logic focus** - Only marts layer
- ✅ **Simple debugging** - Easy to troubleshoot
- ✅ **Independent** - No external dependencies

#### When to Use
- **Quick marts refresh** only needed
- **Testing marts changes** in isolation
- **Fast turnaround** required
- **Source data already available**
- **Development/testing** scenarios

#### Execution Time
- **Typical**: 2-5 minutes
- **Small marts**: 1-2 minutes

## 🎯 Decision Matrix

### Choose `dbt_only_pipeline` when:
- [ ] You need to refresh external tables
- [ ] You want monitoring models updated
- [ ] You need comprehensive testing
- [ ] You're running a production workflow
- [ ] You want documentation generated
- [ ] You need the full transformation pipeline

### Choose `dbt_marts_only` when:
- [ ] You only need business logic updated
- [ ] Source data is already available
- [ ] You want fast execution
- [ ] You're testing marts model changes
- [ ] You need minimal resource usage
- [ ] You're doing development work

## 🔄 Workflow Recommendations

### Development Workflow
```
1. Use dbt_marts_only for rapid iteration
2. Test marts changes quickly
3. Use dbt_only_pipeline for final validation
```

### Production Workflow
```
1. Use dbt_only_pipeline for scheduled runs
2. Use dbt_marts_only for emergency updates
3. Use dbt_only_pipeline for comprehensive refresh
```

### Troubleshooting Workflow
```
1. Use dbt_marts_only to isolate marts issues
2. Use dbt_only_pipeline to check full pipeline
3. Compare results between both DAGs
```

## ⚙️ Configuration Comparison

### Scheduling
| DAG | Default Schedule | Recommended Use |
|-----|------------------|-----------------|
| `dbt_only_pipeline` | Daily at 2:00 AM | Main production schedule |
| `dbt_marts_only` | Daily at 3:00 AM | After main pipeline |

### Parameters
| Parameter | dbt_only_pipeline | dbt_marts_only |
|-----------|-------------------|----------------|
| `run_marts` | ✅ | ✅ (always) |
| `run_monitoring` | ✅ | ❌ |
| `run_tests` | ✅ | ✅ |
| `generate_docs` | ✅ | ❌ |
| `validate_results` | ✅ | ✅ |

### Resource Usage
| Aspect | dbt_only_pipeline | dbt_marts_only |
|--------|-------------------|----------------|
| **CPU Usage** | High | Low |
| **Memory Usage** | High | Low |
| **Snowflake Compute** | High | Medium |
| **Execution Time** | Long | Short |
| **Log Volume** | High | Low |

## 🚀 Integration Patterns

### Pattern 1: Sequential Execution
```
Main Data Ingestion → dbt_only_pipeline → Reporting
```

### Pattern 2: Parallel Execution
```
Main Data Ingestion → dbt_only_pipeline
                   → dbt_marts_only (for urgent updates)
```

### Pattern 3: Development Cycle
```
Code Changes → dbt_marts_only (testing) → dbt_only_pipeline (validation)
```

### Pattern 4: Emergency Updates
```
Data Issue Detected → dbt_marts_only (quick fix) → dbt_only_pipeline (full refresh)
```

## 📊 Performance Comparison

### Execution Time by Component

| Component | dbt_only_pipeline | dbt_marts_only |
|-----------|-------------------|----------------|
| Setup (debug, deps) | 2-3 minutes | 0 minutes |
| Stage refresh | 1-2 minutes | 0 minutes |
| Marts execution | 3-5 minutes | 3-5 minutes |
| Monitoring | 2-3 minutes | 0 minutes |
| Testing | 2-4 minutes | 1-2 minutes |
| Validation | 1-2 minutes | 1 minute |
| **Total** | **10-15 minutes** | **2-5 minutes** |

### Resource Efficiency

| Metric | dbt_only_pipeline | dbt_marts_only |
|--------|-------------------|----------------|
| **Tasks** | 10 | 5 |
| **Complexity** | High | Low |
| **Maintenance** | Medium | Low |
| **Debugging** | Complex | Simple |
| **Flexibility** | High | Medium |

## 🎉 Recommendations

### For Most Users
- **Primary**: Use `dbt_only_pipeline` for scheduled production runs
- **Secondary**: Use `dbt_marts_only` for quick updates and testing

### For Development Teams
- **Development**: Use `dbt_marts_only` for rapid iteration
- **Testing**: Use both DAGs to validate changes
- **Production**: Use `dbt_only_pipeline` for releases

### For Operations Teams
- **Monitoring**: Use `dbt_only_pipeline` for comprehensive health checks
- **Incidents**: Use `dbt_marts_only` for quick fixes
- **Maintenance**: Use `dbt_only_pipeline` for full refreshes

Both DAGs serve different purposes and can complement each other in a robust data pipeline architecture! 🚀
