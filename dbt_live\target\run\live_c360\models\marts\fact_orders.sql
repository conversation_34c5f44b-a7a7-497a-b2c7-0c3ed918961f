
  
    

create or replace transient table MYDB.LIVE_DATA.fact_orders
    

    
    as (

with 
-- Get the 90th percentile order amount for high-value threshold
order_thresholds as (
    select 
        percentile_cont(0.9) within group (order by amount) as high_value_threshold
    from MYDB.LIVE_DATA.stg_orders
    where amount is not null
),

-- Get basic order metrics
order_metrics as (
    select
        -- Primary key and foreign key
        order_id,
        user_id,
        
        -- Core order details
        transaction_date,
        item_count,
        amount as order_amount,
        
        -- Basic metrics
        row_number() over (partition by user_id order by transaction_date) as user_order_sequence,
        
        -- Time since last order
        datediff(
            'day',
            lag(transaction_date) over (partition by user_id order by transaction_date),
            transaction_date
        ) as days_since_previous_order,
        
        -- Running totals
        sum(amount) over (order by transaction_date) as running_total_amount,
        count(*) over (order by date_trunc('day', transaction_date)) as running_order_count
        
    from MYDB.LIVE_DATA.stg_orders
    where transaction_date is not null
    qualify row_number() over (partition by order_id order by generated_at desc) = 1
)

-- Final select with basic segmentation
select
    om.*,
    
    -- Simple customer type
    case 
        when user_order_sequence = 1 then 'New Customer'
        when days_since_previous_order is null then 'New Customer'
        when days_since_previous_order <= 30 then 'Repeat Customer (30d)'
        else 'Reactivated Customer'
    end as customer_type,
    
    -- High value flag (using pre-calculated threshold)
    case 
        when order_amount > (select high_value_threshold from order_thresholds) then true
        else false
    end as is_high_value_order,
    
    -- Large order flag
    case 
        when item_count > 10 then true
        else false
    end as is_large_order,
    
    -- Time of day
    case 
        when extract(hour from transaction_date) between 6 and 11 then 'Morning'
        when extract(hour from transaction_date) between 12 and 17 then 'Afternoon'
        when extract(hour from transaction_date) between 18 and 21 then 'Evening'
        else 'Night'
    end as time_of_day
    
from order_metrics om
    )
;


  