[2025-06-04T06:22:33.212+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=non-requeueable deps ti=<TaskInstance: simple_etl_pipeline.run_dbt_models manual__2025-06-04T06:22:25.908250+00:00 [queued]>
[2025-06-04T06:22:33.236+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=requeueable deps ti=<TaskInstance: simple_etl_pipeline.run_dbt_models manual__2025-06-04T06:22:25.908250+00:00 [queued]>
[2025-06-04T06:22:33.237+0000] {taskinstance.py:2170} INFO - Starting attempt 1 of 2
[2025-06-04T06:22:33.264+0000] {taskinstance.py:2191} INFO - Executing <Task(PythonOperator): run_dbt_models> on 2025-06-04 06:22:25.908250+00:00
[2025-06-04T06:22:33.272+0000] {standard_task_runner.py:60} INFO - Started process 1610 to run task
[2025-06-04T06:22:33.277+0000] {standard_task_runner.py:87} INFO - Running: ['***', 'tasks', 'run', 'simple_etl_pipeline', 'run_dbt_models', 'manual__2025-06-04T06:22:25.908250+00:00', '--job-id', '103', '--raw', '--subdir', 'DAGS_FOLDER/simple_etl_dag.py', '--cfg-path', '/tmp/tmplpgt_in9']
[2025-06-04T06:22:33.279+0000] {standard_task_runner.py:88} INFO - Job 103: Subtask run_dbt_models
[2025-06-04T06:22:33.418+0000] {task_command.py:423} INFO - Running <TaskInstance: simple_etl_pipeline.run_dbt_models manual__2025-06-04T06:22:25.908250+00:00 [running]> on host 8fb7c8dc5097
[2025-06-04T06:22:33.579+0000] {taskinstance.py:2480} INFO - Exporting env vars: AIRFLOW_CTX_DAG_OWNER='data-team' AIRFLOW_CTX_DAG_ID='simple_etl_pipeline' AIRFLOW_CTX_TASK_ID='run_dbt_models' AIRFLOW_CTX_EXECUTION_DATE='2025-06-04T06:22:25.908250+00:00' AIRFLOW_CTX_TRY_NUMBER='1' AIRFLOW_CTX_DAG_RUN_ID='manual__2025-06-04T06:22:25.908250+00:00'
[2025-06-04T06:22:33.580+0000] {simple_etl_dag.py:266} INFO - 🔧 Running dbt models using local successful approach...
[2025-06-04T06:22:51.390+0000] {simple_etl_dag.py:287} ERROR - ❌ dbt run failed: 
[2025-06-04T06:22:51.390+0000] {simple_etl_dag.py:294} ERROR - ❌ dbt run failed: dbt run failed: 
[2025-06-04T06:22:51.391+0000] {taskinstance.py:2698} ERROR - Task failed with exception
Traceback (most recent call last):
  File "/opt/airflow/dags/simple_etl_dag.py", line 288, in run_dbt_models
    raise Exception(f"dbt run failed: {result.stderr}")
Exception: dbt run failed: 

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/models/taskinstance.py", line 433, in _execute_task
    result = execute_callable(context=context, **execute_callable_kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/operators/python.py", line 199, in execute
    return_value = self.execute_callable()
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/operators/python.py", line 216, in execute_callable
    return self.python_callable(*self.op_args, **self.op_kwargs)
  File "/opt/airflow/dags/simple_etl_dag.py", line 295, in run_dbt_models
    raise Exception(f"dbt run failed: {e}")
Exception: dbt run failed: dbt run failed: 
[2025-06-04T06:22:51.404+0000] {taskinstance.py:1138} INFO - Marking task as UP_FOR_RETRY. dag_id=simple_etl_pipeline, task_id=run_dbt_models, execution_date=20250604T062225, start_date=20250604T062233, end_date=20250604T062251
[2025-06-04T06:22:51.416+0000] {standard_task_runner.py:107} ERROR - Failed to execute job 103 for task run_dbt_models (dbt run failed: dbt run failed: ; 1610)
[2025-06-04T06:22:51.456+0000] {local_task_job_runner.py:234} INFO - Task exited with return code 1
[2025-06-04T06:22:51.481+0000] {taskinstance.py:3280} INFO - 0 downstream tasks scheduled from follow-on schedule check
