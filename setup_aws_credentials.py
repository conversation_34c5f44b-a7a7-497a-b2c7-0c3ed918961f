#!/usr/bin/env python3
"""
AWS Credentials Setup for Snowflake
Creates IAM role and policies for Snowflake to access S3 buckets.
"""

import boto3
import json
import logging
import yaml
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AWSCredentialsSetup:
    def __init__(self, config_file='config/live_pipeline_config.yml'):
        """Initialize AWS credentials setup."""
        self.config = self._load_config(config_file)
        self.iam_client = boto3.client('iam')
        self.sts_client = boto3.client('sts')
        
        # Configuration
        self.role_name = self.config['aws']['snowflake_role_name']
        self.policy_name = self.config['aws']['snowflake_policy_name']
        self.bucket_name = self.config['s3']['input_bucket']
        
        logger.info("✅ AWS Credentials Setup initialized")

    def _load_config(self, config_file):
        """Load configuration from YAML file."""
        try:
            with open(config_file, 'r') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            logger.error(f"❌ Configuration file {config_file} not found")
            raise

    def get_snowflake_aws_info(self):
        """Get Snowflake AWS account info for trust policy."""
        logger.info("📋 Getting Snowflake AWS account information...")
        
        # These are Snowflake's AWS account details for ap-southeast-2
        snowflake_aws_info = {
            "aws_account_id": "************",
            "aws_user_arn": "arn:aws:iam::************:user/k7m2-s-v2st2732",
            "external_id": "SVLFKJI_SFCRole=2_L/MM2FeX9Dp2V/C="
        }
        
        logger.info(f"✅ Snowflake AWS Account ID: {snowflake_aws_info['aws_account_id']}")
        logger.info(f"✅ Snowflake AWS User ARN: {snowflake_aws_info['aws_user_arn']}")
        
        return snowflake_aws_info

    def create_trust_policy(self, snowflake_info):
        """Create trust policy for Snowflake IAM role."""
        trust_policy = {
            "Version": "2012-10-17",
            "Statement": [
                {
                    "Effect": "Allow",
                    "Principal": {
                        "AWS": snowflake_info["aws_user_arn"]
                    },
                    "Action": "sts:AssumeRole",
                    "Condition": {
                        "StringEquals": {
                            "sts:ExternalId": snowflake_info["external_id"]
                        }
                    }
                }
            ]
        }
        
        # Save trust policy to file
        trust_policy_file = Path("aws_setup/trust_policy.json")
        with open(trust_policy_file, 'w') as f:
            json.dump(trust_policy, f, indent=2)
        
        logger.info(f"✅ Trust policy saved to {trust_policy_file}")
        return trust_policy

    def create_s3_access_policy(self):
        """Create S3 access policy for Snowflake."""
        s3_policy = {
            "Version": "2012-10-17",
            "Statement": [
                {
                    "Effect": "Allow",
                    "Action": [
                        "s3:GetObject",
                        "s3:GetObjectVersion",
                        "s3:ListBucket",
                        "s3:GetBucketLocation"
                    ],
                    "Resource": [
                        f"arn:aws:s3:::{self.bucket_name}",
                        f"arn:aws:s3:::{self.bucket_name}/*"
                    ]
                },
                {
                    "Effect": "Allow",
                    "Action": [
                        "s3:ListAllMyBuckets"
                    ],
                    "Resource": "*"
                }
            ]
        }
        
        # Save S3 policy to file
        s3_policy_file = Path("aws_setup/s3_policy.json")
        with open(s3_policy_file, 'w') as f:
            json.dump(s3_policy, f, indent=2)
        
        logger.info(f"✅ S3 policy saved to {s3_policy_file}")
        return s3_policy

    def create_iam_policy(self, policy_document):
        """Create IAM policy."""
        try:
            logger.info(f"🔄 Creating IAM policy: {self.policy_name}")
            
            response = self.iam_client.create_policy(
                PolicyName=self.policy_name,
                PolicyDocument=json.dumps(policy_document),
                Description="Policy for Snowflake to access S3 buckets"
            )
            
            policy_arn = response['Policy']['Arn']
            logger.info(f"✅ IAM policy created: {policy_arn}")
            return policy_arn
            
        except self.iam_client.exceptions.EntityAlreadyExistsException:
            logger.info(f"⚠️ Policy {self.policy_name} already exists")
            # Get existing policy ARN
            account_id = self.sts_client.get_caller_identity()['Account']
            policy_arn = f"arn:aws:iam::{account_id}:policy/{self.policy_name}"
            return policy_arn
        except Exception as e:
            logger.error(f"❌ Failed to create IAM policy: {e}")
            raise

    def create_iam_role(self, trust_policy):
        """Create IAM role for Snowflake."""
        try:
            logger.info(f"🔄 Creating IAM role: {self.role_name}")
            
            response = self.iam_client.create_role(
                RoleName=self.role_name,
                AssumeRolePolicyDocument=json.dumps(trust_policy),
                Description="Role for Snowflake to access S3 buckets"
            )
            
            role_arn = response['Role']['Arn']
            logger.info(f"✅ IAM role created: {role_arn}")
            return role_arn
            
        except self.iam_client.exceptions.EntityAlreadyExistsException:
            logger.info(f"⚠️ Role {self.role_name} already exists")
            # Get existing role ARN
            response = self.iam_client.get_role(RoleName=self.role_name)
            role_arn = response['Role']['Arn']
            return role_arn
        except Exception as e:
            logger.error(f"❌ Failed to create IAM role: {e}")
            raise

    def attach_policy_to_role(self, policy_arn):
        """Attach policy to IAM role."""
        try:
            logger.info(f"🔄 Attaching policy to role...")
            
            self.iam_client.attach_role_policy(
                RoleName=self.role_name,
                PolicyArn=policy_arn
            )
            
            logger.info(f"✅ Policy attached to role successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to attach policy to role: {e}")
            raise

    def setup_aws_credentials(self):
        """Complete AWS credentials setup process."""
        logger.info("🚀 Starting AWS credentials setup for Snowflake...")
        
        try:
            # Step 1: Get Snowflake AWS information
            snowflake_info = self.get_snowflake_aws_info()
            
            # Step 2: Create trust policy
            trust_policy = self.create_trust_policy(snowflake_info)
            
            # Step 3: Create S3 access policy
            s3_policy = self.create_s3_access_policy()
            
            # Step 4: Create IAM policy
            policy_arn = self.create_iam_policy(s3_policy)
            
            # Step 5: Create IAM role
            role_arn = self.create_iam_role(trust_policy)
            
            # Step 6: Attach policy to role
            self.attach_policy_to_role(policy_arn)
            
            # Step 7: Generate Snowflake configuration
            snowflake_config = self.generate_snowflake_config(role_arn, snowflake_info)
            
            logger.info("🎉 AWS credentials setup completed successfully!")
            return {
                'role_arn': role_arn,
                'policy_arn': policy_arn,
                'snowflake_config': snowflake_config
            }
            
        except Exception as e:
            logger.error(f"❌ AWS credentials setup failed: {e}")
            raise

    def generate_snowflake_config(self, role_arn, snowflake_info):
        """Generate Snowflake configuration for external stages."""
        config = {
            'storage_integration': {
                'name': 'S3_LIVE_INTEGRATION',
                'type': 'EXTERNAL_STAGE',
                'storage_provider': 'S3',
                'enabled': True,
                'storage_aws_role_arn': role_arn,
                'storage_aws_external_id': snowflake_info['external_id'],
                'storage_allowed_locations': [
                    f"s3://{self.bucket_name}/live-data/"
                ]
            },
            'file_format': {
                'name': 'CSV_LIVE_FORMAT',
                'type': 'CSV',
                'field_delimiter': ',',
                'record_delimiter': '\\n',
                'skip_header': 1,
                'field_optionally_enclosed_by': '"',
                'trim_space': True,
                'error_on_column_count_mismatch': False,
                'escape': 'NONE',
                'date_format': 'AUTO',
                'timestamp_format': 'AUTO',
                'null_if': ['NULL', 'null', '', 'N/A', 'n/a']
            }
        }
        
        # Save configuration to file
        config_file = Path("config/snowflake_integration_config.yml")
        with open(config_file, 'w') as f:
            yaml.dump(config, f, default_flow_style=False, indent=2)
        
        logger.info(f"✅ Snowflake configuration saved to {config_file}")
        return config

    def generate_aws_cli_commands(self):
        """Generate AWS CLI commands for manual setup."""
        commands = [
            "# AWS CLI Commands for Snowflake S3 Integration Setup",
            "",
            "# 1. Create IAM policy",
            f"aws iam create-policy \\",
            f"    --policy-name {self.policy_name} \\",
            f"    --policy-document file://aws_setup/s3_policy.json",
            "",
            "# 2. Create IAM role",
            f"aws iam create-role \\",
            f"    --role-name {self.role_name} \\",
            f"    --assume-role-policy-document file://aws_setup/trust_policy.json",
            "",
            "# 3. Attach policy to role",
            f"aws iam attach-role-policy \\",
            f"    --role-name {self.role_name} \\",
            f"    --policy-arn arn:aws:iam::$(aws sts get-caller-identity --query Account --output text):policy/{self.policy_name}",
            "",
            "# 4. Get role ARN (for Snowflake configuration)",
            f"aws iam get-role --role-name {self.role_name} --query 'Role.Arn' --output text"
        ]
        
        # Save commands to file
        commands_file = Path("aws_setup/aws_cli_commands.sh")
        with open(commands_file, 'w') as f:
            f.write('\n'.join(commands))
        
        logger.info(f"✅ AWS CLI commands saved to {commands_file}")
        return commands

def main():
    """Main function for command line usage."""
    setup = AWSCredentialsSetup()
    
    try:
        # Generate AWS CLI commands
        setup.generate_aws_cli_commands()
        
        # Run setup
        result = setup.setup_aws_credentials()
        
        logger.info("\n📋 SETUP SUMMARY:")
        logger.info(f"✅ IAM Role ARN: {result['role_arn']}")
        logger.info(f"✅ IAM Policy ARN: {result['policy_arn']}")
        logger.info(f"✅ Configuration files created in aws_setup/ and config/")
        logger.info("\n🎯 Next steps:")
        logger.info("1. Run create_snowflake_stages.py to set up Snowflake external stages")
        logger.info("2. Test the integration with live_data_generator.py")
        
    except Exception as e:
        logger.error(f"❌ Setup failed: {e}")
        logger.info("\n🔧 Manual setup option:")
        logger.info("Run the commands in aws_setup/aws_cli_commands.sh manually")

if __name__ == "__main__":
    main()
