[2025-06-04T05:24:44.746+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=non-requeueable deps ti=<TaskInstance: simple_etl_pipeline.run_dbt_models manual__2025-06-04T05:19:27.959189+00:00 [queued]>
[2025-06-04T05:24:44.760+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=requeueable deps ti=<TaskInstance: simple_etl_pipeline.run_dbt_models manual__2025-06-04T05:19:27.959189+00:00 [queued]>
[2025-06-04T05:24:44.761+0000] {taskinstance.py:2170} INFO - Starting attempt 2 of 2
[2025-06-04T05:24:44.993+0000] {taskinstance.py:2191} INFO - Executing <Task(PythonOperator): run_dbt_models> on 2025-06-04 05:19:27.959189+00:00
[2025-06-04T05:24:44.999+0000] {standard_task_runner.py:60} INFO - Started process 829 to run task
[2025-06-04T05:24:45.003+0000] {standard_task_runner.py:87} INFO - Running: ['***', 'tasks', 'run', 'simple_etl_pipeline', 'run_dbt_models', 'manual__2025-06-04T05:19:27.959189+00:00', '--job-id', '94', '--raw', '--subdir', 'DAGS_FOLDER/simple_etl_dag.py', '--cfg-path', '/tmp/tmpwoixi33s']
[2025-06-04T05:24:45.004+0000] {standard_task_runner.py:88} INFO - Job 94: Subtask run_dbt_models
[2025-06-04T05:24:45.184+0000] {task_command.py:423} INFO - Running <TaskInstance: simple_etl_pipeline.run_dbt_models manual__2025-06-04T05:19:27.959189+00:00 [running]> on host 8fb7c8dc5097
[2025-06-04T05:24:45.525+0000] {taskinstance.py:2480} INFO - Exporting env vars: AIRFLOW_CTX_DAG_OWNER='data-team' AIRFLOW_CTX_DAG_ID='simple_etl_pipeline' AIRFLOW_CTX_TASK_ID='run_dbt_models' AIRFLOW_CTX_EXECUTION_DATE='2025-06-04T05:19:27.959189+00:00' AIRFLOW_CTX_TRY_NUMBER='2' AIRFLOW_CTX_DAG_RUN_ID='manual__2025-06-04T05:19:27.959189+00:00'
[2025-06-04T05:24:45.528+0000] {simple_etl_dag.py:78} INFO - Running dbt run using Python API
[2025-06-04T05:24:47.133+0000] {simple_etl_dag.py:109} INFO - Executing dbt run
[2025-06-04T05:24:47.267+0000] {logging_mixin.py:188} INFO - 05:24:47  Running with dbt=1.9.6
[2025-06-04T05:24:48.386+0000] {logging_mixin.py:188} INFO - 05:24:48  Registered adapter: snowflake=1.9.4
[2025-06-04T05:24:49.876+0000] {logging_mixin.py:188} INFO - 05:24:49  [WARNING]: Configuration paths exist in your dbt_project.yml file which do not apply to any resources.
There are 3 unused configuration paths:
- models.live_c360.incremental
- seeds.live_c360
- snapshots.live_c360
[2025-06-04T05:24:50.291+0000] {logging_mixin.py:188} INFO - 05:24:50  Found 12 models, 21 data tests, 3 sources, 590 macros
[2025-06-04T05:24:50.295+0000] {logging_mixin.py:188} INFO - 05:24:50
[2025-06-04T05:24:50.296+0000] {logging_mixin.py:188} INFO - 05:24:50  Concurrency: 4 threads (target='live')
[2025-06-04T05:24:50.298+0000] {logging_mixin.py:188} INFO - 05:24:50
[2025-06-04T05:24:50.349+0000] {connection.py:486} INFO - Snowflake Connector for Python Version: 3.15.0, Python Version: 3.10.13, Platform: Linux-********-microsoft-standard-WSL2-x86_64-with-glibc2.36
[2025-06-04T05:24:50.350+0000] {connection.py:1391} INFO - Connecting to GLOBAL Snowflake domain
[2025-06-04T05:24:50.350+0000] {connection.py:486} INFO - Snowflake Connector for Python Version: 3.15.0, Python Version: 3.10.13, Platform: Linux-********-microsoft-standard-WSL2-x86_64-with-glibc2.36
[2025-06-04T05:24:50.354+0000] {connection.py:1391} INFO - Connecting to GLOBAL Snowflake domain
[2025-06-04T05:24:51.128+0000] {connection.py:486} INFO - Snowflake Connector for Python Version: 3.15.0, Python Version: 3.10.13, Platform: Linux-********-microsoft-standard-WSL2-x86_64-with-glibc2.36
[2025-06-04T05:24:51.129+0000] {connection.py:1391} INFO - Connecting to GLOBAL Snowflake domain
[2025-06-04T05:24:51.848+0000] {logging_mixin.py:188} INFO - 05:24:51  1 of 12 START sql table model LIVE_DATA.dbt_test_health ........................ [RUN]
[2025-06-04T05:24:51.852+0000] {logging_mixin.py:188} INFO - 05:24:51  2 of 12 START sql table model LIVE_DATA.pipeline_runtime_health ................ [RUN]
[2025-06-04T05:24:51.854+0000] {logging_mixin.py:188} INFO - 05:24:51  3 of 12 START sql table model LIVE_DATA.query_history_health ................... [RUN]
[2025-06-04T05:24:51.856+0000] {logging_mixin.py:188} INFO - 05:24:51  4 of 12 START sql view model LIVE_DATA.stg_events .............................. [RUN]
[2025-06-04T05:24:52.164+0000] {connection.py:486} INFO - Snowflake Connector for Python Version: 3.15.0, Python Version: 3.10.13, Platform: Linux-********-microsoft-standard-WSL2-x86_64-with-glibc2.36
[2025-06-04T05:24:52.169+0000] {connection.py:1391} INFO - Connecting to GLOBAL Snowflake domain
[2025-06-04T05:24:52.764+0000] {logging_mixin.py:188} INFO - 05:24:52  4 of 12 ERROR creating sql view model LIVE_DATA.stg_events ..................... [ERROR in 0.90s]
[2025-06-04T05:24:52.770+0000] {logging_mixin.py:188} INFO - 05:24:52  5 of 12 START sql view model LIVE_DATA.stg_orders .............................. [RUN]
[2025-06-04T05:24:52.961+0000] {logging_mixin.py:188} INFO - 05:24:52  1 of 12 OK created sql table model LIVE_DATA.dbt_test_health ................... [SUCCESS 1 in 1.09s]
[2025-06-04T05:24:52.985+0000] {logging_mixin.py:188} INFO - 05:24:52  5 of 12 ERROR creating sql view model LIVE_DATA.stg_orders ..................... [ERROR in 0.20s]
[2025-06-04T05:24:52.991+0000] {logging_mixin.py:188} INFO - 05:24:52  6 of 12 START sql view model LIVE_DATA.stg_users ............................... [RUN]
[2025-06-04T05:24:53.011+0000] {logging_mixin.py:188} INFO - 05:24:53  7 of 12 SKIP relation LIVE_DATA.fact_orders .................................... [SKIP]
[2025-06-04T05:24:53.027+0000] {logging_mixin.py:188} INFO - 05:24:53  8 of 12 SKIP relation LIVE_DATA_ANALYTICS.mv_fact_orders ....................... [SKIP]
[2025-06-04T05:24:53.162+0000] {logging_mixin.py:188} INFO - 05:24:53  6 of 12 ERROR creating sql view model LIVE_DATA.stg_users ...................... [ERROR in 0.16s]
[2025-06-04T05:24:53.169+0000] {logging_mixin.py:188} INFO - 05:24:53  9 of 12 SKIP relation LIVE_DATA.dim_users ...................................... [SKIP]
[2025-06-04T05:24:53.171+0000] {logging_mixin.py:188} INFO - 05:24:53  10 of 12 SKIP relation LIVE_DATA.dim_users_scd2 ................................ [SKIP]
[2025-06-04T05:24:53.178+0000] {logging_mixin.py:188} INFO - 05:24:53  11 of 12 SKIP relation LIVE_DATA.data_quality_health ........................... [SKIP]
[2025-06-04T05:24:53.183+0000] {logging_mixin.py:188} INFO - 05:24:53  12 of 12 SKIP relation LIVE_DATA.etl_health_dashboard .......................... [SKIP]
[2025-06-04T05:24:54.572+0000] {logging_mixin.py:188} INFO - 05:24:54  2 of 12 OK created sql table model LIVE_DATA.pipeline_runtime_health ........... [SUCCESS 1 in 2.71s]
[2025-06-04T05:24:54.598+0000] {logging_mixin.py:188} INFO - 05:24:54  3 of 12 OK created sql table model LIVE_DATA.query_history_health .............. [SUCCESS 1 in 2.74s]
[2025-06-04T05:24:55.207+0000] {logging_mixin.py:188} INFO - 05:24:55
[2025-06-04T05:24:55.209+0000] {logging_mixin.py:188} INFO - 05:24:55  Finished running 1 incremental model, 7 table models, 4 view models in 0 hours 0 minutes and 4.91 seconds (4.91s).
[2025-06-04T05:24:55.302+0000] {logging_mixin.py:188} INFO - 05:24:55
[2025-06-04T05:24:55.304+0000] {logging_mixin.py:188} INFO - 05:24:55  Completed with 3 errors, 0 partial successes, and 0 warnings:
[2025-06-04T05:24:55.306+0000] {logging_mixin.py:188} INFO - 05:24:55
[2025-06-04T05:24:55.308+0000] {logging_mixin.py:188} INFO - 05:24:55    Database Error in model stg_events (models/staging/stg_events.sql)
  091093 (55000): 01bccc24-3204-7f80-0002-4ad6000670e6: External table EXT_LIVE_EVENTS marked invalid. Stage S3_LIVE_EVENTS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_events.sql
[2025-06-04T05:24:55.310+0000] {logging_mixin.py:188} INFO - 05:24:55
[2025-06-04T05:24:55.315+0000] {logging_mixin.py:188} INFO - 05:24:55    Database Error in model stg_orders (models/staging/stg_orders.sql)
  091093 (55000): 01bccc24-3204-802b-0002-4ad60006808a: External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_orders.sql
[2025-06-04T05:24:55.316+0000] {logging_mixin.py:188} INFO - 05:24:55
[2025-06-04T05:24:55.317+0000] {logging_mixin.py:188} INFO - 05:24:55    Database Error in model stg_users (models/staging/stg_users.sql)
  091093 (55000): 01bccc24-3204-7f80-0002-4ad6000670ea: External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_users.sql
[2025-06-04T05:24:55.319+0000] {logging_mixin.py:188} INFO - 05:24:55
[2025-06-04T05:24:55.320+0000] {logging_mixin.py:188} INFO - 05:24:55  Done. PASS=3 WARN=0 ERROR=3 SKIP=6 TOTAL=12
[2025-06-04T05:24:56.501+0000] {simple_etl_dag.py:116} ERROR - dbt run failed
[2025-06-04T05:24:56.503+0000] {taskinstance.py:2698} ERROR - Task failed with exception
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/models/taskinstance.py", line 433, in _execute_task
    result = execute_callable(context=context, **execute_callable_kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/operators/python.py", line 199, in execute
    return_value = self.execute_callable()
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/operators/python.py", line 216, in execute_callable
    return self.python_callable(*self.op_args, **self.op_kwargs)
  File "/opt/airflow/dags/simple_etl_dag.py", line 219, in run_dbt_models
    return run_dbt_command_python('run', **context)
  File "/opt/airflow/dags/simple_etl_dag.py", line 117, in run_dbt_command_python
    raise Exception(f"dbt {command_type} failed")
Exception: dbt run failed
[2025-06-04T05:24:56.522+0000] {taskinstance.py:1138} INFO - Marking task as FAILED. dag_id=simple_etl_pipeline, task_id=run_dbt_models, execution_date=20250604T051927, start_date=20250604T052444, end_date=20250604T052456
[2025-06-04T05:24:56.548+0000] {standard_task_runner.py:107} ERROR - Failed to execute job 94 for task run_dbt_models (dbt run failed; 829)
[2025-06-04T05:24:56.667+0000] {local_task_job_runner.py:234} INFO - Task exited with return code 1
[2025-06-04T05:24:56.700+0000] {taskinstance.py:3280} INFO - 0 downstream tasks scheduled from follow-on schedule check
