[2025-06-04T05:13:20.264+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=non-requeueable deps ti=<TaskInstance: simple_etl_pipeline.run_dbt_models manual__2025-06-04T05:13:10.079396+00:00 [queued]>
[2025-06-04T05:13:20.274+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=requeueable deps ti=<TaskInstance: simple_etl_pipeline.run_dbt_models manual__2025-06-04T05:13:10.079396+00:00 [queued]>
[2025-06-04T05:13:20.274+0000] {taskinstance.py:2170} INFO - Starting attempt 1 of 2
[2025-06-04T05:13:20.287+0000] {taskinstance.py:2191} INFO - Executing <Task(PythonOperator): run_dbt_models> on 2025-06-04 05:13:10.079396+00:00
[2025-06-04T05:13:20.296+0000] {standard_task_runner.py:60} INFO - Started process 615 to run task
[2025-06-04T05:13:20.304+0000] {standard_task_runner.py:87} INFO - Running: ['***', 'tasks', 'run', 'simple_etl_pipeline', 'run_dbt_models', 'manual__2025-06-04T05:13:10.079396+00:00', '--job-id', '88', '--raw', '--subdir', 'DAGS_FOLDER/simple_etl_dag.py', '--cfg-path', '/tmp/tmp2plj580o']
[2025-06-04T05:13:20.306+0000] {standard_task_runner.py:88} INFO - Job 88: Subtask run_dbt_models
[2025-06-04T05:13:20.362+0000] {task_command.py:423} INFO - Running <TaskInstance: simple_etl_pipeline.run_dbt_models manual__2025-06-04T05:13:10.079396+00:00 [running]> on host 8fb7c8dc5097
[2025-06-04T05:13:20.443+0000] {taskinstance.py:2480} INFO - Exporting env vars: AIRFLOW_CTX_DAG_OWNER='data-team' AIRFLOW_CTX_DAG_ID='simple_etl_pipeline' AIRFLOW_CTX_TASK_ID='run_dbt_models' AIRFLOW_CTX_EXECUTION_DATE='2025-06-04T05:13:10.079396+00:00' AIRFLOW_CTX_TRY_NUMBER='1' AIRFLOW_CTX_DAG_RUN_ID='manual__2025-06-04T05:13:10.079396+00:00'
[2025-06-04T05:13:20.444+0000] {simple_etl_dag.py:78} INFO - Running dbt run using Python API
[2025-06-04T05:13:21.972+0000] {simple_etl_dag.py:109} INFO - Executing dbt run
[2025-06-04T05:13:22.064+0000] {logging_mixin.py:188} INFO - 05:13:22  Running with dbt=1.9.6
[2025-06-04T05:13:22.919+0000] {logging_mixin.py:188} INFO - 05:13:22  Registered adapter: snowflake=1.9.4
[2025-06-04T05:13:23.295+0000] {logging_mixin.py:188} INFO - 05:13:23  Unable to do partial parsing because saved manifest not found. Starting full parse.
[2025-06-04T05:13:26.514+0000] {logging_mixin.py:188} INFO - 05:13:26  [WARNING]: Configuration paths exist in your dbt_project.yml file which do not apply to any resources.
There are 3 unused configuration paths:
- models.live_c360.incremental
- seeds.live_c360
- snapshots.live_c360
[2025-06-04T05:13:26.869+0000] {logging_mixin.py:188} INFO - 05:13:26  Found 12 models, 21 data tests, 3 sources, 590 macros
[2025-06-04T05:13:26.875+0000] {logging_mixin.py:188} INFO - 05:13:26
[2025-06-04T05:13:26.877+0000] {logging_mixin.py:188} INFO - 05:13:26  Concurrency: 4 threads (target='live')
[2025-06-04T05:13:26.879+0000] {logging_mixin.py:188} INFO - 05:13:26
[2025-06-04T05:13:26.943+0000] {connection.py:486} INFO - Snowflake Connector for Python Version: 3.15.0, Python Version: 3.10.13, Platform: Linux-********-microsoft-standard-WSL2-x86_64-with-glibc2.36
[2025-06-04T05:13:26.944+0000] {connection.py:1391} INFO - Connecting to GLOBAL Snowflake domain
[2025-06-04T05:13:26.945+0000] {connection.py:486} INFO - Snowflake Connector for Python Version: 3.15.0, Python Version: 3.10.13, Platform: Linux-********-microsoft-standard-WSL2-x86_64-with-glibc2.36
[2025-06-04T05:13:26.950+0000] {connection.py:1391} INFO - Connecting to GLOBAL Snowflake domain
[2025-06-04T05:13:27.543+0000] {connection.py:486} INFO - Snowflake Connector for Python Version: 3.15.0, Python Version: 3.10.13, Platform: Linux-********-microsoft-standard-WSL2-x86_64-with-glibc2.36
[2025-06-04T05:13:27.544+0000] {connection.py:1391} INFO - Connecting to GLOBAL Snowflake domain
[2025-06-04T05:13:28.611+0000] {logging_mixin.py:188} INFO - 05:13:28  1 of 12 START sql table model LIVE_DATA.dbt_test_health ........................ [RUN]
[2025-06-04T05:13:28.619+0000] {logging_mixin.py:188} INFO - 05:13:28  2 of 12 START sql table model LIVE_DATA.pipeline_runtime_health ................ [RUN]
[2025-06-04T05:13:28.621+0000] {logging_mixin.py:188} INFO - 05:13:28  3 of 12 START sql table model LIVE_DATA.query_history_health ................... [RUN]
[2025-06-04T05:13:28.624+0000] {logging_mixin.py:188} INFO - 05:13:28  4 of 12 START sql view model LIVE_DATA.stg_events .............................. [RUN]
[2025-06-04T05:13:28.795+0000] {connection.py:486} INFO - Snowflake Connector for Python Version: 3.15.0, Python Version: 3.10.13, Platform: Linux-********-microsoft-standard-WSL2-x86_64-with-glibc2.36
[2025-06-04T05:13:28.797+0000] {connection.py:1391} INFO - Connecting to GLOBAL Snowflake domain
[2025-06-04T05:13:29.131+0000] {logging_mixin.py:188} INFO - 05:13:29  4 of 12 ERROR creating sql view model LIVE_DATA.stg_events ..................... [ERROR in 0.50s]
[2025-06-04T05:13:29.137+0000] {logging_mixin.py:188} INFO - 05:13:29  5 of 12 START sql view model LIVE_DATA.stg_orders .............................. [RUN]
[2025-06-04T05:13:29.356+0000] {logging_mixin.py:188} INFO - 05:13:29  5 of 12 ERROR creating sql view model LIVE_DATA.stg_orders ..................... [ERROR in 0.21s]
[2025-06-04T05:13:29.363+0000] {logging_mixin.py:188} INFO - 05:13:29  6 of 12 START sql view model LIVE_DATA.stg_users ............................... [RUN]
[2025-06-04T05:13:29.564+0000] {logging_mixin.py:188} INFO - 05:13:29  6 of 12 ERROR creating sql view model LIVE_DATA.stg_users ...................... [ERROR in 0.19s]
[2025-06-04T05:13:29.570+0000] {logging_mixin.py:188} INFO - 05:13:29  7 of 12 SKIP relation LIVE_DATA.fact_orders .................................... [SKIP]
[2025-06-04T05:13:29.575+0000] {logging_mixin.py:188} INFO - 05:13:29  8 of 12 SKIP relation LIVE_DATA_ANALYTICS.mv_fact_orders ....................... [SKIP]
[2025-06-04T05:13:29.579+0000] {logging_mixin.py:188} INFO - 05:13:29  9 of 12 SKIP relation LIVE_DATA.dim_users ...................................... [SKIP]
[2025-06-04T05:13:29.584+0000] {logging_mixin.py:188} INFO - 05:13:29  10 of 12 SKIP relation LIVE_DATA.dim_users_scd2 ................................ [SKIP]
[2025-06-04T05:13:29.588+0000] {logging_mixin.py:188} INFO - 05:13:29  11 of 12 SKIP relation LIVE_DATA.data_quality_health ........................... [SKIP]
[2025-06-04T05:13:29.597+0000] {logging_mixin.py:188} INFO - 05:13:29  12 of 12 SKIP relation LIVE_DATA.etl_health_dashboard .......................... [SKIP]
[2025-06-04T05:13:29.905+0000] {logging_mixin.py:188} INFO - 05:13:29  1 of 12 OK created sql table model LIVE_DATA.dbt_test_health ................... [SUCCESS 1 in 1.28s]
[2025-06-04T05:13:32.211+0000] {logging_mixin.py:188} INFO - 05:13:32  3 of 12 OK created sql table model LIVE_DATA.query_history_health .............. [SUCCESS 1 in 3.58s]
[2025-06-04T05:13:32.246+0000] {logging_mixin.py:188} INFO - 05:13:32  2 of 12 OK created sql table model LIVE_DATA.pipeline_runtime_health ........... [SUCCESS 1 in 3.62s]
[2025-06-04T05:13:32.523+0000] {logging_mixin.py:188} INFO - 05:13:32
[2025-06-04T05:13:32.525+0000] {logging_mixin.py:188} INFO - 05:13:32  Finished running 1 incremental model, 7 table models, 4 view models in 0 hours 0 minutes and 5.64 seconds (5.64s).
[2025-06-04T05:13:32.620+0000] {logging_mixin.py:188} INFO - 05:13:32
[2025-06-04T05:13:32.622+0000] {logging_mixin.py:188} INFO - 05:13:32  Completed with 3 errors, 0 partial successes, and 0 warnings:
[2025-06-04T05:13:32.623+0000] {logging_mixin.py:188} INFO - 05:13:32
[2025-06-04T05:13:32.625+0000] {logging_mixin.py:188} INFO - 05:13:32    Database Error in model stg_events (models/staging/stg_events.sql)
  091093 (55000): 01bccc19-3204-802a-0002-4ad60006904e: External table EXT_LIVE_EVENTS marked invalid. Stage S3_LIVE_EVENTS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_events.sql
[2025-06-04T05:13:32.627+0000] {logging_mixin.py:188} INFO - 05:13:32
[2025-06-04T05:13:32.628+0000] {logging_mixin.py:188} INFO - 05:13:32    Database Error in model stg_orders (models/staging/stg_orders.sql)
  091093 (55000): 01bccc19-3204-7ffc-0002-4ad6000660de: External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_orders.sql
[2025-06-04T05:13:32.630+0000] {logging_mixin.py:188} INFO - 05:13:32
[2025-06-04T05:13:32.632+0000] {logging_mixin.py:188} INFO - 05:13:32    Database Error in model stg_users (models/staging/stg_users.sql)
  091093 (55000): 01bccc19-3204-7f69-0002-4ad60005d16a: External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_users.sql
[2025-06-04T05:13:32.634+0000] {logging_mixin.py:188} INFO - 05:13:32
[2025-06-04T05:13:32.636+0000] {logging_mixin.py:188} INFO - 05:13:32  Done. PASS=3 WARN=0 ERROR=3 SKIP=6 TOTAL=12
[2025-06-04T05:13:33.770+0000] {simple_etl_dag.py:116} ERROR - dbt run failed
[2025-06-04T05:13:33.771+0000] {taskinstance.py:2698} ERROR - Task failed with exception
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/models/taskinstance.py", line 433, in _execute_task
    result = execute_callable(context=context, **execute_callable_kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/operators/python.py", line 199, in execute
    return_value = self.execute_callable()
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/operators/python.py", line 216, in execute_callable
    return self.python_callable(*self.op_args, **self.op_kwargs)
  File "/opt/airflow/dags/simple_etl_dag.py", line 219, in run_dbt_models
    return run_dbt_command_python('run', **context)
  File "/opt/airflow/dags/simple_etl_dag.py", line 117, in run_dbt_command_python
    raise Exception(f"dbt {command_type} failed")
Exception: dbt run failed
[2025-06-04T05:13:33.781+0000] {taskinstance.py:1138} INFO - Marking task as UP_FOR_RETRY. dag_id=simple_etl_pipeline, task_id=run_dbt_models, execution_date=20250604T051310, start_date=20250604T051320, end_date=20250604T051333
[2025-06-04T05:13:33.794+0000] {standard_task_runner.py:107} ERROR - Failed to execute job 88 for task run_dbt_models (dbt run failed; 615)
[2025-06-04T05:13:33.840+0000] {local_task_job_runner.py:234} INFO - Task exited with return code 1
[2025-06-04T05:13:33.865+0000] {taskinstance.py:3280} INFO - 0 downstream tasks scheduled from follow-on schedule check
