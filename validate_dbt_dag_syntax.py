#!/usr/bin/env python3
"""
Simple syntax validation for dbt-only DAG
Checks Python syntax without importing Airflow
"""

import ast
import sys
from pathlib import Path

def validate_python_syntax(file_path):
    """Validate Python syntax of a file"""
    
    print(f"🔍 Validating syntax: {file_path}")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Parse the AST
        ast.parse(content)
        print("✅ Syntax validation passed")
        return True
        
    except SyntaxError as e:
        print(f"❌ Syntax error: {e}")
        print(f"   Line {e.lineno}: {e.text}")
        return False
    except Exception as e:
        print(f"❌ Error reading file: {e}")
        return False

def check_dag_structure(file_path):
    """Check basic DAG structure without importing"""
    
    print(f"📋 Checking DAG structure: {file_path}")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for required elements
        required_elements = [
            'from airflow import DAG',
            'from airflow.operators.python import PythonOperator',
            'dag = DAG(',
            'def run_dbt_command(',
            'task_id=',
            'python_callable=',
        ]
        
        missing_elements = []
        for element in required_elements:
            if element not in content:
                missing_elements.append(element)
        
        if missing_elements:
            print(f"⚠️ Missing elements: {missing_elements}")
        else:
            print("✅ All required DAG elements found")
        
        # Check for task definitions
        task_patterns = [
            'start_task',
            'debug_task', 
            'deps_task',
            'run_staging_task',
            'run_marts_task',
            'test_all_task',
            'end_task'
        ]
        
        found_tasks = []
        for pattern in task_patterns:
            if pattern in content:
                found_tasks.append(pattern)
        
        print(f"📋 Found {len(found_tasks)} expected task definitions")
        
        # Check for dependencies
        if '>>' in content:
            print("✅ Task dependencies found")
        else:
            print("⚠️ No task dependencies found")
        
        return len(missing_elements) == 0
        
    except Exception as e:
        print(f"❌ Error checking structure: {e}")
        return False

def main():
    """Main validation function"""
    
    print("🚀 Starting dbt DAG syntax validation...\n")
    
    dag_file = Path("airflow-orchestration/dags/dbt_only_dag.py")
    
    if not dag_file.exists():
        print(f"❌ DAG file not found: {dag_file}")
        return False
    
    # Validate syntax
    syntax_valid = validate_python_syntax(dag_file)
    
    # Check structure
    structure_valid = check_dag_structure(dag_file)
    
    # Summary
    print("\n📊 Validation Summary:")
    print(f"   Python Syntax: {'✅ PASS' if syntax_valid else '❌ FAIL'}")
    print(f"   DAG Structure: {'✅ PASS' if structure_valid else '❌ FAIL'}")
    
    if syntax_valid and structure_valid:
        print("\n🎉 Validation passed! The dbt-only DAG syntax is correct.")
        print("\n📝 Next steps:")
        print("   1. Copy the DAG to your Airflow DAGs directory")
        print("   2. Restart Airflow to load the new DAG")
        print("   3. Check the Airflow UI for the 'dbt_only_pipeline' DAG")
        print("   4. Test run the DAG manually")
        return True
    else:
        print("\n❌ Validation failed. Please fix the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
