#!/usr/bin/env python3
"""
Daily Data Generator for S3
Generates daily CSV files with realistic data volumes for production-like scenarios.
"""

import pandas as pd
import numpy as np
import boto3
import logging
import uuid
from datetime import datetime, timedelta
from faker import Faker
import random
import io
import yaml
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DailyDataGenerator:
    def __init__(self, config_file='config/live_pipeline_config.yml'):
        """Initialize the daily data generator."""
        self.config = self._load_config(config_file)
        self.s3_client = self._create_s3_client()
        self.fake = Faker()
        Faker.seed(42)
        random.seed(42)
        np.random.seed(42)
        
        # S3 configuration
        self.bucket_name = self.config['s3']['input_bucket']
        self.users_prefix = self.config['s3']['users_prefix']
        self.orders_prefix = self.config['s3']['orders_prefix']
        self.events_prefix = self.config['s3']['events_prefix']
        
        # Daily generation parameters
        self.daily_users = self.config['generation']['daily_users']
        self.daily_orders = self.config['generation']['daily_orders']
        self.daily_events = self.config['generation']['daily_events']
        
        logger.info(f"✅ Daily Data Generator initialized")
        logger.info(f"📊 Daily targets: {self.daily_users:,} users, {self.daily_orders:,} orders, {self.daily_events:,} events")

    def _load_config(self, config_file):
        """Load configuration from YAML file."""
        try:
            with open(config_file, 'r') as f:
                config = yaml.safe_load(f)
            logger.info(f"✅ Configuration loaded from {config_file}")
            return config
        except FileNotFoundError:
            logger.error(f"❌ Configuration file {config_file} not found")
            return self._get_default_config()

    def _get_default_config(self):
        """Get default configuration if file not found."""
        return {
            's3': {
                'input_bucket': 'lake-loader-input-365542662955-20250525-001439',
                'users_prefix': 'live-data/users',
                'orders_prefix': 'live-data/orders',
                'events_prefix': 'live-data/events'
            },
            'generation': {
                'daily_users': 5000,
                'daily_orders': 15000,
                'daily_events': 75000,
                'file_prefix_format': 'daily_%Y%m%d'
            }
        }

    def _create_s3_client(self):
        """Create S3 client using AWS CLI credentials."""
        try:
            s3_client = boto3.client('s3')
            s3_client.list_buckets()
            logger.info("✅ S3 client created successfully")
            return s3_client
        except Exception as e:
            logger.error(f"❌ Failed to create S3 client: {e}")
            raise

    def generate_daily_users(self, target_date=None):
        """Generate daily users data."""
        if target_date is None:
            target_date = datetime.now().date()
        
        logger.info(f"🔄 Generating {self.daily_users:,} users for {target_date}")
        
        users_data = []
        generation_time = datetime.now()
        
        for i in range(self.daily_users):
            user_id = str(uuid.uuid4())
            
            # Users created throughout the day
            creation_hour = random.randint(0, 23)
            creation_minute = random.randint(0, 59)
            creation_date = datetime.combine(target_date, datetime.min.time()) + timedelta(hours=creation_hour, minutes=creation_minute)
            
            # Last activity within 30 days of creation
            last_activity = creation_date + timedelta(days=random.randint(0, 30))
            
            user = {
                'id': user_id,
                'firstname': self.fake.first_name(),
                'lastname': self.fake.last_name(),
                'email': self.fake.email(),
                'address': self.fake.address().replace('\n', ', '),
                'canal': random.choice(['WEBAPP', 'MOBILE', 'PHONE', None]),
                'country': random.choice(['FR', 'USA', 'SPAIN']),
                'creation_date': creation_date.strftime("%m-%d-%Y %H:%M:%S"),
                'last_activity_date': last_activity.strftime("%m-%d-%Y %H:%M:%S"),
                'gender': random.randint(0, 1),
                'age_group': random.randint(0, 10),
                'churn': random.choice([True, False]),
                'batch_id': f"daily_{target_date.strftime('%Y%m%d')}",
                'generated_at': generation_time.isoformat()
            }
            users_data.append(user)
        
        return pd.DataFrame(users_data)

    def generate_daily_orders(self, users_df, target_date=None):
        """Generate daily orders data."""
        if target_date is None:
            target_date = datetime.now().date()
        
        logger.info(f"🔄 Generating {self.daily_orders:,} orders for {target_date}")
        
        orders_data = []
        generation_time = datetime.now()
        
        # Create orders for existing users (80%) and new users (20%)
        existing_user_orders = int(self.daily_orders * 0.8)
        new_user_orders = self.daily_orders - existing_user_orders
        
        # Orders from new users
        new_users = users_df.sample(min(len(users_df), new_user_orders))
        
        # Simulate existing users (generate some historical user IDs)
        existing_user_ids = [str(uuid.uuid4()) for _ in range(existing_user_orders)]
        
        all_user_ids = list(new_users['id']) + existing_user_ids
        random.shuffle(all_user_ids)
        
        for i in range(self.daily_orders):
            order_id = str(uuid.uuid4())
            user_id = all_user_ids[i % len(all_user_ids)]
            
            # Orders distributed throughout the day with peak hours
            hour_weights = [0.5, 0.3, 0.2, 0.2, 0.3, 0.5, 0.8, 1.2, 1.5, 1.8, 2.0, 2.2,  # 0-11
                           2.5, 2.3, 2.0, 1.8, 1.5, 1.2, 1.0, 0.8, 0.7, 0.6, 0.5, 0.4]   # 12-23
            order_hour = random.choices(range(24), weights=hour_weights)[0]
            order_minute = random.randint(0, 59)
            order_date = datetime.combine(target_date, datetime.min.time()) + timedelta(hours=order_hour, minutes=order_minute)
            
            order = {
                'id': order_id,
                'user_id': user_id,
                'transaction_date': order_date.strftime("%m-%d-%Y %H:%M:%S"),
                'item_count': random.randint(1, 8),
                'amount': round(random.uniform(15, 300), 2),
                'batch_id': f"daily_{target_date.strftime('%Y%m%d')}",
                'generated_at': generation_time.isoformat()
            }
            orders_data.append(order)
        
        return pd.DataFrame(orders_data)

    def generate_daily_events(self, orders_df, target_date=None):
        """Generate daily events data."""
        if target_date is None:
            target_date = datetime.now().date()
        
        logger.info(f"🔄 Generating {self.daily_events:,} events for {target_date}")
        
        events_data = []
        generation_time = datetime.now()
        
        # Generate events for orders (60%) and standalone browsing (40%)
        order_events = int(self.daily_events * 0.6)
        browsing_events = self.daily_events - order_events
        
        # Events from orders
        for i in range(order_events):
            if len(orders_df) > 0:
                order = orders_df.iloc[i % len(orders_df)]
                user_id = order['user_id']
                order_time = datetime.strptime(order['transaction_date'], "%m-%d-%Y %H:%M:%S")
                
                # Events happen around order time
                event_offset = random.randint(-120, 30)  # 2 hours before to 30 minutes after
                event_time = order_time + timedelta(minutes=event_offset)
            else:
                user_id = str(uuid.uuid4())
                event_hour = random.randint(0, 23)
                event_minute = random.randint(0, 59)
                event_time = datetime.combine(target_date, datetime.min.time()) + timedelta(hours=event_hour, minutes=event_minute)
            
            event = {
                'user_id': user_id,
                'event_id': str(uuid.uuid4()),
                'platform': random.choice(['ios', 'android', 'web', None]),
                'date': event_time.strftime("%m-%d-%Y %H:%M:%S"),
                'action': random.choice(['view', 'click', 'log', 'purchase']),
                'session_id': str(uuid.uuid4()),
                'url': f"https://example.com/{self.fake.uri_path()}",
                'batch_id': f"daily_{target_date.strftime('%Y%m%d')}",
                'generated_at': generation_time.isoformat()
            }
            events_data.append(event)
        
        # Standalone browsing events
        for i in range(browsing_events):
            user_id = str(uuid.uuid4())
            event_hour = random.randint(0, 23)
            event_minute = random.randint(0, 59)
            event_time = datetime.combine(target_date, datetime.min.time()) + timedelta(hours=event_hour, minutes=event_minute)
            
            event = {
                'user_id': user_id,
                'event_id': str(uuid.uuid4()),
                'platform': random.choice(['ios', 'android', 'web']),
                'date': event_time.strftime("%m-%d-%Y %H:%M:%S"),
                'action': random.choice(['view', 'click', 'log']),
                'session_id': str(uuid.uuid4()),
                'url': f"https://example.com/{self.fake.uri_path()}",
                'batch_id': f"daily_{target_date.strftime('%Y%m%d')}",
                'generated_at': generation_time.isoformat()
            }
            events_data.append(event)
        
        return pd.DataFrame(events_data)

    def upload_dataframe_to_s3(self, df, prefix, filename):
        """Upload DataFrame as CSV to S3."""
        try:
            csv_buffer = io.StringIO()
            df.to_csv(csv_buffer, index=False)
            csv_content = csv_buffer.getvalue()
            
            s3_key = f"{prefix}/{filename}"
            self.s3_client.put_object(
                Bucket=self.bucket_name,
                Key=s3_key,
                Body=csv_content,
                ContentType='text/csv',
                ServerSideEncryption='AES256'
            )
            
            logger.info(f"✅ Uploaded {len(df):,} records to s3://{self.bucket_name}/{s3_key}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to upload to S3: {e}")
            return False

    def generate_daily_data(self, target_date=None):
        """Generate complete daily dataset."""
        if target_date is None:
            target_date = datetime.now().date()
        
        date_str = target_date.strftime("%Y%m%d")
        logger.info(f"🚀 Starting daily data generation for {target_date}")
        
        try:
            # Generate users
            users_df = self.generate_daily_users(target_date)
            users_filename = f"daily_users_{date_str}.csv"
            users_success = self.upload_dataframe_to_s3(users_df, self.users_prefix, users_filename)
            
            # Generate orders
            orders_df = self.generate_daily_orders(users_df, target_date)
            orders_filename = f"daily_orders_{date_str}.csv"
            orders_success = self.upload_dataframe_to_s3(orders_df, self.orders_prefix, orders_filename)
            
            # Generate events
            events_df = self.generate_daily_events(orders_df, target_date)
            events_filename = f"daily_events_{date_str}.csv"
            events_success = self.upload_dataframe_to_s3(events_df, self.events_prefix, events_filename)
            
            # Summary
            total_records = len(users_df) + len(orders_df) + len(events_df)
            success = users_success and orders_success and events_success
            
            if success:
                logger.info(f"🎉 Daily data generation completed successfully!")
                logger.info(f"📊 Generated {total_records:,} total records for {target_date}")
            else:
                logger.error(f"❌ Daily data generation completed with errors")
            
            return {
                'date': target_date.isoformat(),
                'users_count': len(users_df),
                'orders_count': len(orders_df),
                'events_count': len(events_df),
                'total_records': total_records,
                'success': success,
                'files': {
                    'users': f"{self.users_prefix}/{users_filename}",
                    'orders': f"{self.orders_prefix}/{orders_filename}",
                    'events': f"{self.events_prefix}/{events_filename}"
                }
            }
            
        except Exception as e:
            logger.error(f"❌ Daily data generation failed: {e}")
            return {
                'date': target_date.isoformat(),
                'success': False,
                'error': str(e)
            }

def main():
    """Main function for command line usage."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Daily Data Generator for S3')
    parser.add_argument('--date', type=str, help='Target date (YYYY-MM-DD), defaults to today')
    parser.add_argument('--days', type=int, default=1, help='Number of days to generate')
    parser.add_argument('--backfill', action='store_true', help='Generate data for past days')
    
    args = parser.parse_args()
    
    generator = DailyDataGenerator()
    
    if args.date:
        target_date = datetime.strptime(args.date, '%Y-%m-%d').date()
    else:
        target_date = datetime.now().date()
    
    results = []
    
    for i in range(args.days):
        if args.backfill:
            current_date = target_date - timedelta(days=i)
        else:
            current_date = target_date + timedelta(days=i)
        
        result = generator.generate_daily_data(current_date)
        results.append(result)
    
    # Summary
    successful_days = sum(1 for r in results if r['success'])
    total_records = sum(r.get('total_records', 0) for r in results if r['success'])
    
    logger.info(f"\n📊 GENERATION SUMMARY:")
    logger.info(f"✅ Successful days: {successful_days}/{len(results)}")
    logger.info(f"📈 Total records generated: {total_records:,}")
    
    for result in results:
        if result['success']:
            logger.info(f"📅 {result['date']}: {result['total_records']:,} records")
        else:
            logger.error(f"❌ {result['date']}: Failed - {result.get('error', 'Unknown error')}")

if __name__ == "__main__":
    main()
