

-- Staging model for live events data
-- Cleans and standardizes raw event data from S3 external table

with source_data as (
    select * from MYDB.LIVE_DATA.ext_live_events
),

cleaned_data as (
    select
        -- Primary key
        event_id,
        user_id,
        session_id,
        
        -- Event details
        try_to_timestamp(date, 'MM-DD-YYYY HH24:MI:SS') as event_timestamp,
        lower(trim(platform)) as platform,
        lower(trim(action)) as action,
        trim(url) as url,
        
        -- Metadata
        batch_id,
        try_to_timestamp(generated_at) as generated_at,
        current_timestamp() as processed_at
        
    from source_data
    where event_id is not null  -- Filter out any null event IDs
      and user_id is not null   -- Filter out events without user IDs
),

url_parsed as (
    select
        *,
        -- URL parsing
        case 
            when url like '%/product/%' then 'Product Page'
            when url like '%/cart%' then 'Cart'
            when url like '%/checkout%' then 'Checkout'
            when url like '%/search%' then 'Search'
            when url like '%/category/%' then 'Category'
            when url like '%/home%' or url = 'https://example.com/' then 'Home'
            else 'Other'
        end as page_type,
        
        -- Extract path from URL
        regexp_substr(url, 'https?://[^/]+(/.*)', 1, 1, 'e', 1) as url_path
        
    from cleaned_data
),

final as (
    select
        *,
        -- Date extractions
        date(event_timestamp) as event_date,
        extract(year from event_timestamp) as event_year,
        extract(month from event_timestamp) as event_month,
        extract(day from event_timestamp) as event_day,
        extract(hour from event_timestamp) as event_hour,
        dayname(event_timestamp) as event_day_name,
        
        -- Time-based segments
        case 
            when extract(hour from event_timestamp) between 6 and 11 then 'Morning'
            when extract(hour from event_timestamp) between 12 and 17 then 'Afternoon'
            when extract(hour from event_timestamp) between 18 and 22 then 'Evening'
            else 'Night'
        end as time_of_day_segment,
        
        -- Platform standardization
        case 
            when platform in ('ios', 'android') then 'Mobile'
            when platform = 'web' then 'Web'
            else 'Unknown'
        end as device_category,
        
        -- Action categorization
        case 
            when action in ('view', 'click') then 'Engagement'
            when action = 'log' then 'Authentication'
            when action = 'purchase' then 'Conversion'
            else 'Other'
        end as action_category,
        
        -- Funnel stage
        case 
            when action = 'view' and page_type = 'Home' then 'Awareness'
            when action = 'view' and page_type in ('Category', 'Search') then 'Interest'
            when action = 'view' and page_type = 'Product Page' then 'Consideration'
            when action = 'click' and page_type = 'Cart' then 'Intent'
            when action = 'purchase' then 'Purchase'
            else 'Other'
        end as funnel_stage,
        
        -- Data quality flags
        case 
            when event_timestamp > current_timestamp() then true
            else false
        end as has_future_event_timestamp,
        
        -- Recency
        datediff('minute', event_timestamp, current_timestamp()) as minutes_since_event,
        datediff('hour', event_timestamp, current_timestamp()) as hours_since_event,
        datediff('day', event_timestamp, current_timestamp()) as days_since_event
        
    from url_parsed
)

select * from final