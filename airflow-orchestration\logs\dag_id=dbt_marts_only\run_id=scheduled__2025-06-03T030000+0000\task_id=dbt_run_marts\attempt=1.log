[2025-06-04T10:07:49.785+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=non-requeueable deps ti=<TaskInstance: dbt_marts_only.dbt_run_marts scheduled__2025-06-03T03:00:00+00:00 [queued]>
[2025-06-04T10:07:49.798+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=requeueable deps ti=<TaskInstance: dbt_marts_only.dbt_run_marts scheduled__2025-06-03T03:00:00+00:00 [queued]>
[2025-06-04T10:07:49.799+0000] {taskinstance.py:2170} INFO - Starting attempt 1 of 2
[2025-06-04T10:07:49.815+0000] {taskinstance.py:2191} INFO - Executing <Task(PythonOperator): dbt_run_marts> on 2025-06-03 03:00:00+00:00
[2025-06-04T10:07:49.826+0000] {standard_task_runner.py:60} INFO - Started process 67 to run task
[2025-06-04T10:07:49.835+0000] {standard_task_runner.py:87} INFO - Running: ['***', 'tasks', 'run', 'dbt_marts_only', 'dbt_run_marts', 'scheduled__2025-06-03T03:00:00+00:00', '--job-id', '117', '--raw', '--subdir', 'DAGS_FOLDER/dbt_marts_only_dag.py', '--cfg-path', '/tmp/tmpmf8if7h9']
[2025-06-04T10:07:49.838+0000] {standard_task_runner.py:88} INFO - Job 117: Subtask dbt_run_marts
[2025-06-04T10:07:49.922+0000] {task_command.py:423} INFO - Running <TaskInstance: dbt_marts_only.dbt_run_marts scheduled__2025-06-03T03:00:00+00:00 [running]> on host bad0c90122e4
[2025-06-04T10:07:50.012+0000] {taskinstance.py:2480} INFO - Exporting env vars: AIRFLOW_CTX_DAG_OWNER='data-team' AIRFLOW_CTX_DAG_ID='dbt_marts_only' AIRFLOW_CTX_TASK_ID='dbt_run_marts' AIRFLOW_CTX_EXECUTION_DATE='2025-06-03T03:00:00+00:00' AIRFLOW_CTX_TRY_NUMBER='1' AIRFLOW_CTX_DAG_RUN_ID='scheduled__2025-06-03T03:00:00+00:00'
[2025-06-04T10:07:50.014+0000] {dbt_marts_only_dag.py:129} INFO - 🏪 Running dbt marts models...
[2025-06-04T10:07:50.014+0000] {dbt_marts_only_dag.py:62} INFO - 🚀 Running dbt run
[2025-06-04T10:07:50.016+0000] {dbt_marts_only_dag.py:82} INFO - 📋 Executing: dbt run --models marts
[2025-06-04T10:08:01.908+0000] {dbt_marts_only_dag.py:95} INFO - 📄 stdout: [0m10:07:52  Running with dbt=1.9.6
[0m10:07:53  Registered adapter: snowflake=1.9.4
[0m10:07:55  [[33mWARNING[0m]: Configuration paths exist in your dbt_project.yml file which do not apply to any resources.
There are 3 unused configuration paths:
- models.live_c360.incremental
- seeds.live_c360
- snapshots.live_c360
[0m10:07:55  Found 12 models, 21 data tests, 3 sources, 590 macros
[0m10:07:55  
[0m10:07:55  Concurrency: 4 threads (target='live')
[0m10:07:55  
[0m10:07:58  1 of 4 START sql table model LIVE_DATA.dim_users ............................... [RUN]
[0m10:07:58  2 of 4 START sql incremental model LIVE_DATA.dim_users_scd2 .................... [RUN]
[0m10:07:58  3 of 4 START sql table model LIVE_DATA.fact_orders ............................. [RUN]
[0m10:07:58  4 of 4 START sql view model LIVE_DATA_ANALYTICS.mv_fact_orders ................. [RUN]
[0m10:07:59  2 of 4 ERROR creating sql incremental model LIVE_DATA.dim_users_scd2 ........... [[31mERROR[0m in 0.49s]
[0m10:07:59  3 of 4 ERROR creating sql table model LIVE_DATA.fact_orders .................... [[31mERROR[0m in 0.53s]
[0m10:07:59  4 of 4 ERROR creating sql view model LIVE_DATA_ANALYTICS.mv_fact_orders ........ [[31mERROR[0m in 0.73s]
[0m10:07:59  1 of 4 ERROR creating sql table model LIVE_DATA.dim_users ...................... [[31mERROR[0m in 1.07s]
[0m10:08:00  
[0m10:08:00  Finished running 1 incremental model, 2 table models, 1 view model in 0 hours 0 minutes and 4.40 seconds (4.40s).
[0m10:08:00  
[0m10:08:00  [31mCompleted with 4 errors, 0 partial successes, and 0 warnings:[0m
[0m10:08:00  
[0m10:08:00    Database Error in model dim_users_scd2 (models/marts/dim_users_scd2.sql)
  091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
[0m10:08:00  
[0m10:08:00    Database Error in model fact_orders (models/marts/fact_orders.sql)
  091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/marts/fact_orders.sql
[0m10:08:00  
[0m10:08:00    Database Error in model mv_fact_orders (models/marts/mv_fact_orders.sql)
  091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/marts/mv_fact_orders.sql
[0m10:08:00  
[0m10:08:00    Database Error in model dim_users (models/marts/dim_users.sql)
  091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
  compiled code at target/run/live_c360/models/marts/dim_users.sql
[0m10:08:00  
[0m10:08:00  Done. PASS=0 WARN=0 ERROR=4 SKIP=0 TOTAL=4

[2025-06-04T10:08:01.909+0000] {dbt_marts_only_dag.py:110} ERROR - ❌ dbt run failed with return code 1
[2025-06-04T10:08:01.909+0000] {dbt_marts_only_dag.py:111} ERROR - ❌ Error output: 
[2025-06-04T10:08:01.909+0000] {dbt_marts_only_dag.py:120} ERROR - ❌ dbt run failed: dbt run failed with return code 1: 
[2025-06-04T10:08:01.910+0000] {taskinstance.py:2698} ERROR - Task failed with exception
Traceback (most recent call last):
  File "/opt/airflow/dags/dbt_marts_only_dag.py", line 112, in run_dbt_command
    raise Exception(f"{error_msg}: {result.stderr}")
Exception: dbt run failed with return code 1: 

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/models/taskinstance.py", line 433, in _execute_task
    result = execute_callable(context=context, **execute_callable_kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/operators/python.py", line 199, in execute
    return_value = self.execute_callable()
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/operators/python.py", line 216, in execute_callable
    return self.python_callable(*self.op_args, **self.op_kwargs)
  File "/opt/airflow/dags/dbt_marts_only_dag.py", line 130, in dbt_run_marts
    return run_dbt_command('run', models='marts', **context)
  File "/opt/airflow/dags/dbt_marts_only_dag.py", line 121, in run_dbt_command
    raise Exception(f"dbt {command_type} failed: {e}")
Exception: dbt run failed: dbt run failed with return code 1: 
[2025-06-04T10:08:01.927+0000] {taskinstance.py:1138} INFO - Marking task as UP_FOR_RETRY. dag_id=dbt_marts_only, task_id=dbt_run_marts, execution_date=20250603T030000, start_date=20250604T100749, end_date=20250604T100801
[2025-06-04T10:08:01.946+0000] {standard_task_runner.py:107} ERROR - Failed to execute job 117 for task dbt_run_marts (dbt run failed: dbt run failed with return code 1: ; 67)
[2025-06-04T10:08:01.974+0000] {local_task_job_runner.py:234} INFO - Task exited with return code 1
[2025-06-04T10:08:02.002+0000] {taskinstance.py:3280} INFO - 0 downstream tasks scheduled from follow-on schedule check
