[2025-06-04T05:19:29.271+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=non-requeueable deps ti=<TaskInstance: simple_etl_pipeline.fix_dbt_packages manual__2025-06-04T05:19:27.959189+00:00 [queued]>
[2025-06-04T05:19:29.282+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=requeueable deps ti=<TaskInstance: simple_etl_pipeline.fix_dbt_packages manual__2025-06-04T05:19:27.959189+00:00 [queued]>
[2025-06-04T05:19:29.283+0000] {taskinstance.py:2170} INFO - Starting attempt 1 of 2
[2025-06-04T05:19:29.300+0000] {taskinstance.py:2191} INFO - Executing <Task(PythonOperator): fix_dbt_packages> on 2025-06-04 05:19:27.959189+00:00
[2025-06-04T05:19:29.307+0000] {standard_task_runner.py:60} INFO - Started process 741 to run task
[2025-06-04T05:19:29.315+0000] {standard_task_runner.py:87} INFO - Running: ['***', 'tasks', 'run', 'simple_etl_pipeline', 'fix_dbt_packages', 'manual__2025-06-04T05:19:27.959189+00:00', '--job-id', '90', '--raw', '--subdir', 'DAGS_FOLDER/simple_etl_dag.py', '--cfg-path', '/tmp/tmpgemn7bme']
[2025-06-04T05:19:29.316+0000] {standard_task_runner.py:88} INFO - Job 90: Subtask fix_dbt_packages
[2025-06-04T05:19:29.380+0000] {task_command.py:423} INFO - Running <TaskInstance: simple_etl_pipeline.fix_dbt_packages manual__2025-06-04T05:19:27.959189+00:00 [running]> on host 8fb7c8dc5097
[2025-06-04T05:19:29.464+0000] {taskinstance.py:2480} INFO - Exporting env vars: AIRFLOW_CTX_DAG_OWNER='data-team' AIRFLOW_CTX_DAG_ID='simple_etl_pipeline' AIRFLOW_CTX_TASK_ID='fix_dbt_packages' AIRFLOW_CTX_EXECUTION_DATE='2025-06-04T05:19:27.959189+00:00' AIRFLOW_CTX_TRY_NUMBER='1' AIRFLOW_CTX_DAG_RUN_ID='manual__2025-06-04T05:19:27.959189+00:00'
[2025-06-04T05:19:29.465+0000] {simple_etl_dag.py:48} INFO - Running script: /opt/***/workspace/fix_docker_dbt_packages.py
[2025-06-04T05:19:54.736+0000] {simple_etl_dag.py:67} INFO - Script completed successfully: 
[2025-06-04T05:19:54.737+0000] {python.py:201} INFO - Done. Returned value was: 
[2025-06-04T05:19:54.760+0000] {taskinstance.py:1138} INFO - Marking task as SUCCESS. dag_id=simple_etl_pipeline, task_id=fix_dbt_packages, execution_date=20250604T051927, start_date=20250604T051929, end_date=20250604T051954
[2025-06-04T05:19:54.832+0000] {local_task_job_runner.py:234} INFO - Task exited with return code 0
[2025-06-04T05:19:54.848+0000] {taskinstance.py:3280} INFO - 0 downstream tasks scheduled from follow-on schedule check
