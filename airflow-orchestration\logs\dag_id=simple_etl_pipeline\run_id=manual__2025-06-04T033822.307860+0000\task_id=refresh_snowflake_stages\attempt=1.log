[2025-06-04T03:38:25.329+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=non-requeueable deps ti=<TaskInstance: simple_etl_pipeline.refresh_snowflake_stages manual__2025-06-04T03:38:22.307860+00:00 [queued]>
[2025-06-04T03:38:25.339+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=requeueable deps ti=<TaskInstance: simple_etl_pipeline.refresh_snowflake_stages manual__2025-06-04T03:38:22.307860+00:00 [queued]>
[2025-06-04T03:38:25.339+0000] {taskinstance.py:2170} INFO - Starting attempt 1 of 2
[2025-06-04T03:38:25.354+0000] {taskinstance.py:2191} INFO - Executing <Task(PythonOperator): refresh_snowflake_stages> on 2025-06-04 03:38:22.307860+00:00
[2025-06-04T03:38:25.360+0000] {standard_task_runner.py:60} INFO - Started process 942 to run task
[2025-06-04T03:38:25.364+0000] {standard_task_runner.py:87} INFO - Running: ['***', 'tasks', 'run', 'simple_etl_pipeline', 'refresh_snowflake_stages', 'manual__2025-06-04T03:38:22.307860+00:00', '--job-id', '61', '--raw', '--subdir', 'DAGS_FOLDER/simple_etl_dag.py', '--cfg-path', '/tmp/tmpibcqd_tq']
[2025-06-04T03:38:25.367+0000] {standard_task_runner.py:88} INFO - Job 61: Subtask refresh_snowflake_stages
[2025-06-04T03:38:25.421+0000] {task_command.py:423} INFO - Running <TaskInstance: simple_etl_pipeline.refresh_snowflake_stages manual__2025-06-04T03:38:22.307860+00:00 [running]> on host 9fdd53a423c8
[2025-06-04T03:38:25.497+0000] {taskinstance.py:2480} INFO - Exporting env vars: AIRFLOW_CTX_DAG_OWNER='data-team' AIRFLOW_CTX_DAG_ID='simple_etl_pipeline' AIRFLOW_CTX_TASK_ID='refresh_snowflake_stages' AIRFLOW_CTX_EXECUTION_DATE='2025-06-04T03:38:22.307860+00:00' AIRFLOW_CTX_TRY_NUMBER='1' AIRFLOW_CTX_DAG_RUN_ID='manual__2025-06-04T03:38:22.307860+00:00'
[2025-06-04T03:38:25.498+0000] {simple_etl_dag.py:115} INFO - Refreshing Snowflake external stages...
[2025-06-04T03:38:28.894+0000] {create_snowflake_stages.py:36} INFO - ✅ Snowflake Stages Setup initialized
[2025-06-04T03:38:28.898+0000] {create_snowflake_stages.py:408} INFO - ✅ Test queries saved to config/test_queries.sql
[2025-06-04T03:38:28.898+0000] {create_snowflake_stages.py:311} INFO - 🚀 Starting Snowflake external stages setup...
[2025-06-04T03:38:28.899+0000] {create_snowflake_stages.py:315} INFO - 🔍 Role ARN not provided, attempting to get/create it...
[2025-06-04T03:38:30.172+0000] {create_snowflake_stages.py:304} ERROR - ❌ Failed to get/create role ARN: Unable to locate credentials
[2025-06-04T03:38:30.172+0000] {create_snowflake_stages.py:306} WARNING - ⚠️ Using development placeholder role ARN
[2025-06-04T03:38:30.173+0000] {create_snowflake_stages.py:50} INFO - 🔄 Connecting to Snowflake...
[2025-06-04T03:38:30.174+0000] {connection.py:370} INFO - Snowflake Connector for Python Version: 3.6.0, Python Version: 3.10.13, Platform: Linux-6.6.87.1-microsoft-standard-WSL2-x86_64-with-glibc2.36
[2025-06-04T03:38:30.175+0000] {connection.py:1171} INFO - This connection is in OCSP Fail Open Mode. TLS Certificates would be checked for validity and revocation status. Any other Certificate Revocation related exceptions or OCSP Responder failures would be disregarded in favor of connectivity.
[2025-06-04T03:38:32.548+0000] {cursor.py:1028} INFO - query: [USE DATABASE MYDB]
[2025-06-04T03:38:32.715+0000] {cursor.py:1041} INFO - query execution done
[2025-06-04T03:38:32.716+0000] {cursor.py:1205} INFO - Number of results in first chunk: 1
[2025-06-04T03:38:32.717+0000] {cursor.py:1028} INFO - query: [USE SCHEMA LIVE_DATA]
[2025-06-04T03:38:32.835+0000] {cursor.py:1041} INFO - query execution done
[2025-06-04T03:38:32.836+0000] {cursor.py:1205} INFO - Number of results in first chunk: 1
[2025-06-04T03:38:32.837+0000] {create_snowflake_stages.py:58} INFO - ✅ Connected to Snowflake successfully
[2025-06-04T03:38:32.838+0000] {create_snowflake_stages.py:69} INFO - 🔄 Creating schema LIVE_DATA...
[2025-06-04T03:38:32.838+0000] {cursor.py:1028} INFO - query: [CREATE SCHEMA IF NOT EXISTS LIVE_DATA]
[2025-06-04T03:38:33.514+0000] {cursor.py:1041} INFO - query execution done
[2025-06-04T03:38:33.515+0000] {cursor.py:1205} INFO - Number of results in first chunk: 1
[2025-06-04T03:38:33.516+0000] {cursor.py:1028} INFO - query: [USE SCHEMA LIVE_DATA]
[2025-06-04T03:38:33.699+0000] {cursor.py:1041} INFO - query execution done
[2025-06-04T03:38:33.700+0000] {cursor.py:1205} INFO - Number of results in first chunk: 1
[2025-06-04T03:38:33.701+0000] {create_snowflake_stages.py:74} INFO - ✅ Schema LIVE_DATA ready
[2025-06-04T03:38:33.701+0000] {create_snowflake_stages.py:83} INFO - 🔄 Creating storage integration...
[2025-06-04T03:38:33.702+0000] {cursor.py:1028} INFO - query: [CREATE OR REPLACE STORAGE INTEGRATION S3_LIVE_INTEGRATION TYPE = EXTERNAL_STAGE ...]
[2025-06-04T03:38:33.986+0000] {cursor.py:1041} INFO - query execution done
[2025-06-04T03:38:33.987+0000] {cursor.py:1205} INFO - Number of results in first chunk: 1
[2025-06-04T03:38:33.988+0000] {create_snowflake_stages.py:100} INFO - ✅ Storage integration created successfully
[2025-06-04T03:38:33.988+0000] {cursor.py:1028} INFO - query: [DESC STORAGE INTEGRATION S3_LIVE_INTEGRATION]
[2025-06-04T03:38:34.111+0000] {cursor.py:1041} INFO - query execution done
[2025-06-04T03:38:34.112+0000] {cursor.py:1205} INFO - Number of results in first chunk: 9
[2025-06-04T03:38:34.113+0000] {create_snowflake_stages.py:107} INFO -    STORAGE_AWS_IAM_USER_ARN: String
[2025-06-04T03:38:34.113+0000] {create_snowflake_stages.py:107} INFO -    STORAGE_AWS_EXTERNAL_ID: String
[2025-06-04T03:38:34.114+0000] {create_snowflake_stages.py:116} INFO - 🔄 Creating file format...
[2025-06-04T03:38:34.115+0000] {cursor.py:1028} INFO - query: [CREATE OR REPLACE FILE FORMAT CSV_LIVE_FORMAT TYPE = 'CSV' FIELD_DELIMITER = ','...]
[2025-06-04T03:38:34.260+0000] {cursor.py:1041} INFO - query execution done
[2025-06-04T03:38:34.261+0000] {cursor.py:1205} INFO - Number of results in first chunk: 1
[2025-06-04T03:38:34.261+0000] {create_snowflake_stages.py:136} INFO - ✅ File format created successfully
[2025-06-04T03:38:34.262+0000] {create_snowflake_stages.py:152} INFO - 🔄 Creating stage S3_LIVE_USERS_STAGE...
[2025-06-04T03:38:34.263+0000] {cursor.py:1028} INFO - query: [CREATE OR REPLACE STAGE S3_LIVE_USERS_STAGE STORAGE_INTEGRATION = S3_LIVE_INTEGR...]
[2025-06-04T03:38:34.453+0000] {cursor.py:1041} INFO - query execution done
[2025-06-04T03:38:34.454+0000] {cursor.py:1205} INFO - Number of results in first chunk: 1
[2025-06-04T03:38:34.454+0000] {create_snowflake_stages.py:163} INFO - ✅ Stage S3_LIVE_USERS_STAGE created successfully
[2025-06-04T03:38:34.455+0000] {create_snowflake_stages.py:152} INFO - 🔄 Creating stage S3_LIVE_ORDERS_STAGE...
[2025-06-04T03:38:34.455+0000] {cursor.py:1028} INFO - query: [CREATE OR REPLACE STAGE S3_LIVE_ORDERS_STAGE STORAGE_INTEGRATION = S3_LIVE_INTEG...]
[2025-06-04T03:38:34.700+0000] {cursor.py:1041} INFO - query execution done
[2025-06-04T03:38:34.700+0000] {cursor.py:1205} INFO - Number of results in first chunk: 1
[2025-06-04T03:38:34.701+0000] {create_snowflake_stages.py:163} INFO - ✅ Stage S3_LIVE_ORDERS_STAGE created successfully
[2025-06-04T03:38:34.701+0000] {create_snowflake_stages.py:152} INFO - 🔄 Creating stage S3_LIVE_EVENTS_STAGE...
[2025-06-04T03:38:34.702+0000] {cursor.py:1028} INFO - query: [CREATE OR REPLACE STAGE S3_LIVE_EVENTS_STAGE STORAGE_INTEGRATION = S3_LIVE_INTEG...]
[2025-06-04T03:38:34.814+0000] {cursor.py:1041} INFO - query execution done
[2025-06-04T03:38:34.815+0000] {cursor.py:1205} INFO - Number of results in first chunk: 1
[2025-06-04T03:38:34.815+0000] {create_snowflake_stages.py:163} INFO - ✅ Stage S3_LIVE_EVENTS_STAGE created successfully
[2025-06-04T03:38:34.816+0000] {create_snowflake_stages.py:172} INFO - 🔄 Creating external tables...
[2025-06-04T03:38:34.817+0000] {cursor.py:1028} INFO - query: [CREATE OR REPLACE EXTERNAL TABLE EXT_LIVE_USERS ( id STRING AS (value:c1::STRING...]
[2025-06-04T03:38:38.207+0000] {cursor.py:1041} INFO - query execution done
[2025-06-04T03:38:39.025+0000] {create_snowflake_stages.py:241} ERROR - ❌ Failed to create external tables: 003167 (42601): 01bccbba-3204-7ee0-0002-4ad60005c0ee: Error assuming AWS_ROLE:
User: arn:aws:iam::184862803517:user/400z0000-s is not authorized to perform: sts:AssumeRole on resource: arn:aws:iam::365542662955:role/SnowflakeS3AccessRole
[2025-06-04T03:38:39.026+0000] {create_snowflake_stages.py:339} WARNING - ⚠️ External tables creation failed (IAM role issue): 003167 (42601): 01bccbba-3204-7ee0-0002-4ad60005c0ee: Error assuming AWS_ROLE:
User: arn:aws:iam::184862803517:user/400z0000-s is not authorized to perform: sts:AssumeRole on resource: arn:aws:iam::365542662955:role/SnowflakeS3AccessRole
[2025-06-04T03:38:39.026+0000] {create_snowflake_stages.py:340} INFO - 📝 Continuing without external tables - stages are ready for dbt
[2025-06-04T03:38:39.037+0000] {create_snowflake_stages.py:347} INFO - ⏭️ Skipping stage tests (external tables not created)
[2025-06-04T03:38:39.102+0000] {connection.py:718} INFO - closed
[2025-06-04T03:38:39.151+0000] {connection.py:724} INFO - No async queries seem to be running, deleting session
[2025-06-04T03:38:39.224+0000] {create_snowflake_stages.py:353} INFO - 🎉 Snowflake external stages setup completed successfully!
[2025-06-04T03:38:39.225+0000] {create_snowflake_stages.py:422} INFO - 
📋 AIRFLOW SETUP SUMMARY:
[2025-06-04T03:38:39.226+0000] {create_snowflake_stages.py:423} INFO - ✅ Storage Integration: S3_LIVE_INTEGRATION
[2025-06-04T03:38:39.226+0000] {create_snowflake_stages.py:424} INFO - ✅ File Format: CSV_LIVE_FORMAT
[2025-06-04T03:38:39.226+0000] {create_snowflake_stages.py:425} INFO - ✅ External Stages: S3_LIVE_USERS_STAGE, S3_LIVE_ORDERS_STAGE, S3_LIVE_EVENTS_STAGE
[2025-06-04T03:38:39.227+0000] {create_snowflake_stages.py:429} INFO - ⚠️ External Tables: Not created (IAM role configuration needed)
[2025-06-04T03:38:39.227+0000] {create_snowflake_stages.py:430} INFO - ✅ IAM Role ARN: arn:aws:iam::365542662955:role/SnowflakeS3AccessRole
[2025-06-04T03:38:39.227+0000] {simple_etl_dag.py:130} INFO - Stages refreshed successfully: {'storage_integration': 'S3_LIVE_INTEGRATION', 'file_format': 'CSV_LIVE_FORMAT', 'stages': ['S3_LIVE_USERS_STAGE', 'S3_LIVE_ORDERS_STAGE', 'S3_LIVE_EVENTS_STAGE'], 'role_arn': 'arn:aws:iam::365542662955:role/SnowflakeS3AccessRole', 'external_tables_created': False}
[2025-06-04T03:38:39.228+0000] {python.py:201} INFO - Done. Returned value was: Stage refresh successful: {'storage_integration': 'S3_LIVE_INTEGRATION', 'file_format': 'CSV_LIVE_FORMAT', 'stages': ['S3_LIVE_USERS_STAGE', 'S3_LIVE_ORDERS_STAGE', 'S3_LIVE_EVENTS_STAGE'], 'role_arn': 'arn:aws:iam::365542662955:role/SnowflakeS3AccessRole', 'external_tables_created': False}
[2025-06-04T03:38:39.479+0000] {taskinstance.py:1138} INFO - Marking task as SUCCESS. dag_id=simple_etl_pipeline, task_id=refresh_snowflake_stages, execution_date=20250604T033822, start_date=20250604T033825, end_date=20250604T033839
[2025-06-04T03:38:39.549+0000] {local_task_job_runner.py:234} INFO - Task exited with return code 0
[2025-06-04T03:38:39.585+0000] {taskinstance.py:3280} INFO - 1 downstream tasks scheduled from follow-on schedule check
