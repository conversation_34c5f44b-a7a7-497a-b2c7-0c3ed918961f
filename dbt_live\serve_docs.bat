@echo off
REM dbt Documentation Server Script
REM Serves dbt docs on port 8088 to avoid conflict with Airflow (port 8080)

echo.
echo 🚀 Starting dbt documentation server on port 8088...
echo 📊 Airflow is running on port 8080
echo 📖 dbt docs will be available at: http://localhost:8088
echo.

REM Generate fresh documentation
echo 📝 Generating documentation...
dbt docs generate

if %ERRORLEVEL% EQU 0 (
    echo ✅ Documentation generated successfully
    echo.
    
    REM Serve documentation on port 8088
    echo 🌐 Starting web server on port 8088...
    echo 💡 Press Ctrl+C to stop the server
    echo.
    
    dbt docs serve --port 8088
) else (
    echo ❌ Failed to generate documentation
    echo 🔍 Please check for errors above and fix them before serving docs
    pause
    exit /b 1
)
