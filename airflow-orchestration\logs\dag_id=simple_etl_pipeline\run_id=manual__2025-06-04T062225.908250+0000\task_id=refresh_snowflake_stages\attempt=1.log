[2025-06-04T06:22:28.480+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=non-requeueable deps ti=<TaskInstance: simple_etl_pipeline.refresh_snowflake_stages manual__2025-06-04T06:22:25.908250+00:00 [queued]>
[2025-06-04T06:22:28.494+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=requeueable deps ti=<TaskInstance: simple_etl_pipeline.refresh_snowflake_stages manual__2025-06-04T06:22:25.908250+00:00 [queued]>
[2025-06-04T06:22:28.496+0000] {taskinstance.py:2170} INFO - Starting attempt 1 of 2
[2025-06-04T06:22:28.518+0000] {taskinstance.py:2191} INFO - Executing <Task(PythonOperator): refresh_snowflake_stages> on 2025-06-04 06:22:25.908250+00:00
[2025-06-04T06:22:28.571+0000] {standard_task_runner.py:60} INFO - Started process 1606 to run task
[2025-06-04T06:22:28.576+0000] {standard_task_runner.py:87} INFO - Running: ['***', 'tasks', 'run', 'simple_etl_pipeline', 'refresh_snowflake_stages', 'manual__2025-06-04T06:22:25.908250+00:00', '--job-id', '102', '--raw', '--subdir', 'DAGS_FOLDER/simple_etl_dag.py', '--cfg-path', '/tmp/tmp3quq_ibg']
[2025-06-04T06:22:28.639+0000] {standard_task_runner.py:88} INFO - Job 102: Subtask refresh_snowflake_stages
[2025-06-04T06:22:28.840+0000] {task_command.py:423} INFO - Running <TaskInstance: simple_etl_pipeline.refresh_snowflake_stages manual__2025-06-04T06:22:25.908250+00:00 [running]> on host 8fb7c8dc5097
[2025-06-04T06:22:28.984+0000] {taskinstance.py:2480} INFO - Exporting env vars: AIRFLOW_CTX_DAG_OWNER='data-team' AIRFLOW_CTX_DAG_ID='simple_etl_pipeline' AIRFLOW_CTX_TASK_ID='refresh_snowflake_stages' AIRFLOW_CTX_EXECUTION_DATE='2025-06-04T06:22:25.908250+00:00' AIRFLOW_CTX_TRY_NUMBER='1' AIRFLOW_CTX_DAG_RUN_ID='manual__2025-06-04T06:22:25.908250+00:00'
[2025-06-04T06:22:28.986+0000] {simple_etl_dag.py:192} INFO - Refreshing Snowflake external stages using local successful approach...
[2025-06-04T06:22:30.399+0000] {connection.py:486} INFO - Snowflake Connector for Python Version: 3.15.0, Python Version: 3.10.13, Platform: Linux-********-microsoft-standard-WSL2-x86_64-with-glibc2.36
[2025-06-04T06:22:30.399+0000] {connection.py:1391} INFO - Connecting to GLOBAL Snowflake domain
[2025-06-04T06:22:31.804+0000] {simple_etl_dag.py:249} ERROR - ❌ EXT_LIVE_USERS: 091093 (55000): 01bccc5e-3204-7eab-0002-4ad6000611da: External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
[2025-06-04T06:22:31.898+0000] {simple_etl_dag.py:249} ERROR - ❌ EXT_LIVE_ORDERS: 091093 (55000): 01bccc5e-3204-802a-0002-4ad60006907a: External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
[2025-06-04T06:22:31.984+0000] {simple_etl_dag.py:249} ERROR - ❌ EXT_LIVE_EVENTS: 091093 (55000): 01bccc5e-3204-7f69-0002-4ad60005d19e: External table EXT_LIVE_EVENTS marked invalid. Stage S3_LIVE_EVENTS_STAGE dropped.
[2025-06-04T06:22:32.075+0000] {simple_etl_dag.py:254} INFO - ✅ Snowflake stages refreshed successfully
[2025-06-04T06:22:32.078+0000] {python.py:201} INFO - Done. Returned value was: Stage refresh successful: {'EXT_LIVE_USERS': {'error': '091093 (55000): 01bccc5e-3204-7eab-0002-4ad6000611da: External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.'}, 'EXT_LIVE_ORDERS': {'error': '091093 (55000): 01bccc5e-3204-802a-0002-4ad60006907a: External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.'}, 'EXT_LIVE_EVENTS': {'error': '091093 (55000): 01bccc5e-3204-7f69-0002-4ad60005d19e: External table EXT_LIVE_EVENTS marked invalid. Stage S3_LIVE_EVENTS_STAGE dropped.'}}
[2025-06-04T06:22:32.113+0000] {taskinstance.py:1138} INFO - Marking task as SUCCESS. dag_id=simple_etl_pipeline, task_id=refresh_snowflake_stages, execution_date=20250604T062225, start_date=20250604T062228, end_date=20250604T062232
[2025-06-04T06:22:32.181+0000] {local_task_job_runner.py:234} INFO - Task exited with return code 0
[2025-06-04T06:22:32.220+0000] {taskinstance.py:3280} INFO - 1 downstream tasks scheduled from follow-on schedule check
