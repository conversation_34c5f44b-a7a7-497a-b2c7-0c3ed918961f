-- back compat for old kwarg name
  
  begin;
    
        
            
	    
	    
            
        
    

    

    merge into MYDB.LIVE_DATA.dim_users_scd2 as DBT_INTERNAL_DEST
        using MYDB.LIVE_DATA.dim_users_scd2__dbt_tmp as DBT_INTERNAL_SOURCE
        on ((DBT_INTERNAL_SOURCE.user_sk = DBT_INTERNAL_DEST.user_sk))

    
    when matched then update set
        "USER_SK" = DBT_INTERNAL_SOURCE."USER_SK","USER_ID" = DBT_INTERNAL_SOURCE."USER_ID","FIRSTNAME" = DBT_INTERNAL_SOURCE."FIRSTNAME","LASTNAME" = DBT_INTERNAL_SOURCE."LASTNAME","EMAIL" = DBT_INTERNAL_SOURCE."EMAIL","ADDRESS" = DBT_INTERNAL_SOURCE."ADDRESS","ACQUISITION_CHANNEL" = DBT_INTERNAL_SOURCE."ACQUISITION_CHANNEL","COUNTRY" = DBT_INTERNAL_SOURCE."COUNTRY","GENDER" = DBT_INTERNAL_SOURCE."GENDER","AGE_GROUP" = DBT_INTERNAL_SOURCE."AGE_GROUP","CREATION_DATE" = DBT_INTERNAL_SOURCE."CREATION_DATE","LAST_ACTIVITY_DATE" = DBT_INTERNAL_SOURCE."LAST_ACTIVITY_DATE","CHURN" = DBT_INTERNAL_SOURCE."CHURN","BATCH_ID" = DBT_INTERNAL_SOURCE."BATCH_ID","DBT_VALID_FROM" = DBT_INTERNAL_SOURCE."DBT_VALID_FROM","DBT_VALID_TO" = DBT_INTERNAL_SOURCE."DBT_VALID_TO","IS_CURRENT_VERSION" = DBT_INTERNAL_SOURCE."IS_CURRENT_VERSION","DBT_CHANGE_TYPE" = DBT_INTERNAL_SOURCE."DBT_CHANGE_TYPE","PROCESSED_AT" = DBT_INTERNAL_SOURCE."PROCESSED_AT"
    

    when not matched then insert
        ("USER_SK", "USER_ID", "FIRSTNAME", "LASTNAME", "EMAIL", "ADDRESS", "ACQUISITION_CHANNEL", "COUNTRY", "GENDER", "AGE_GROUP", "CREATION_DATE", "LAST_ACTIVITY_DATE", "CHURN", "BATCH_ID", "DBT_VALID_FROM", "DBT_VALID_TO", "IS_CURRENT_VERSION", "DBT_CHANGE_TYPE", "PROCESSED_AT")
    values
        ("USER_SK", "USER_ID", "FIRSTNAME", "LASTNAME", "EMAIL", "ADDRESS", "ACQUISITION_CHANNEL", "COUNTRY", "GENDER", "AGE_GROUP", "CREATION_DATE", "LAST_ACTIVITY_DATE", "CHURN", "BATCH_ID", "DBT_VALID_FROM", "DBT_VALID_TO", "IS_CURRENT_VERSION", "DBT_CHANGE_TYPE", "PROCESSED_AT")

;
    commit;