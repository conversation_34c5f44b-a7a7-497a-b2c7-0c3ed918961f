[2025-06-04T02:46:27.484+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=non-requeueable deps ti=<TaskInstance: simple_etl_pipeline.refresh_snowflake_stages manual__2025-06-04T02:46:23.260425+00:00 [queued]>
[2025-06-04T02:46:27.495+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=requeueable deps ti=<TaskInstance: simple_etl_pipeline.refresh_snowflake_stages manual__2025-06-04T02:46:23.260425+00:00 [queued]>
[2025-06-04T02:46:27.496+0000] {taskinstance.py:2170} INFO - Starting attempt 1 of 2
[2025-06-04T02:46:27.510+0000] {taskinstance.py:2191} INFO - Executing <Task(PythonOperator): refresh_snowflake_stages> on 2025-06-04 02:46:23.260425+00:00
[2025-06-04T02:46:27.528+0000] {standard_task_runner.py:60} INFO - Started process 165 to run task
[2025-06-04T02:46:27.532+0000] {standard_task_runner.py:87} INFO - Running: ['***', 'tasks', 'run', 'simple_etl_pipeline', 'refresh_snowflake_stages', 'manual__2025-06-04T02:46:23.260425+00:00', '--job-id', '17', '--raw', '--subdir', 'DAGS_FOLDER/simple_etl_dag.py', '--cfg-path', '/tmp/tmpgh7tgit0']
[2025-06-04T02:46:27.538+0000] {standard_task_runner.py:88} INFO - Job 17: Subtask refresh_snowflake_stages
[2025-06-04T02:46:27.605+0000] {task_command.py:423} INFO - Running <TaskInstance: simple_etl_pipeline.refresh_snowflake_stages manual__2025-06-04T02:46:23.260425+00:00 [running]> on host 9fdd53a423c8
[2025-06-04T02:46:27.690+0000] {taskinstance.py:2480} INFO - Exporting env vars: AIRFLOW_CTX_DAG_OWNER='data-team' AIRFLOW_CTX_DAG_ID='simple_etl_pipeline' AIRFLOW_CTX_TASK_ID='refresh_snowflake_stages' AIRFLOW_CTX_EXECUTION_DATE='2025-06-04T02:46:23.260425+00:00' AIRFLOW_CTX_TRY_NUMBER='1' AIRFLOW_CTX_DAG_RUN_ID='manual__2025-06-04T02:46:23.260425+00:00'
[2025-06-04T02:46:27.691+0000] {simple_etl_dag.py:48} INFO - Running script: /opt/***/workspace/create_snowflake_stages.py
[2025-06-04T02:46:30.802+0000] {simple_etl_dag.py:64} ERROR - Script failed: usage: create_snowflake_stages.py [-h] --role-arn ROLE_ARN
create_snowflake_stages.py: error: the following arguments are required: --role-arn

[2025-06-04T02:46:30.803+0000] {taskinstance.py:2698} ERROR - Task failed with exception
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/models/taskinstance.py", line 433, in _execute_task
    result = execute_callable(context=context, **execute_callable_kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/operators/python.py", line 199, in execute
    return_value = self.execute_callable()
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/operators/python.py", line 216, in execute_callable
    return self.python_callable(*self.op_args, **self.op_kwargs)
  File "/opt/airflow/dags/simple_etl_dag.py", line 113, in refresh_stages
    return run_python_script('create_snowflake_stages.py', **context)
  File "/opt/airflow/dags/simple_etl_dag.py", line 65, in run_python_script
    raise Exception(f"Script failed: {result.stderr}")
Exception: Script failed: usage: create_snowflake_stages.py [-h] --role-arn ROLE_ARN
create_snowflake_stages.py: error: the following arguments are required: --role-arn

[2025-06-04T02:46:30.817+0000] {taskinstance.py:1138} INFO - Marking task as UP_FOR_RETRY. dag_id=simple_etl_pipeline, task_id=refresh_snowflake_stages, execution_date=20250604T024623, start_date=20250604T024627, end_date=20250604T024630
[2025-06-04T02:46:30.832+0000] {standard_task_runner.py:107} ERROR - Failed to execute job 17 for task refresh_snowflake_stages (Script failed: usage: create_snowflake_stages.py [-h] --role-arn ROLE_ARN
create_snowflake_stages.py: error: the following arguments are required: --role-arn
; 165)
[2025-06-04T02:46:30.854+0000] {local_task_job_runner.py:234} INFO - Task exited with return code 1
[2025-06-04T02:46:30.880+0000] {taskinstance.py:3280} INFO - 0 downstream tasks scheduled from follow-on schedule check
