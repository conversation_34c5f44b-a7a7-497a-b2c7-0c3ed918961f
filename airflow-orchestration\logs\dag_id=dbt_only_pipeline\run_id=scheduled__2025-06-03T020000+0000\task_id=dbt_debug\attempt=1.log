[2025-06-04T09:42:54.624+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=non-requeueable deps ti=<TaskInstance: dbt_only_pipeline.dbt_debug scheduled__2025-06-03T02:00:00+00:00 [queued]>
[2025-06-04T09:42:54.634+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=requeueable deps ti=<TaskInstance: dbt_only_pipeline.dbt_debug scheduled__2025-06-03T02:00:00+00:00 [queued]>
[2025-06-04T09:42:54.635+0000] {taskinstance.py:2170} INFO - Starting attempt 1 of 3
[2025-06-04T09:42:54.652+0000] {taskinstance.py:2191} INFO - Executing <Task(PythonOperator): dbt_debug> on 2025-06-03 02:00:00+00:00
[2025-06-04T09:42:54.658+0000] {standard_task_runner.py:60} INFO - Started process 4171 to run task
[2025-06-04T09:42:54.662+0000] {standard_task_runner.py:87} INFO - Running: ['***', 'tasks', 'run', 'dbt_only_pipeline', 'dbt_debug', 'scheduled__2025-06-03T02:00:00+00:00', '--job-id', '105', '--raw', '--subdir', 'DAGS_FOLDER/dbt_only_dag.py', '--cfg-path', '/tmp/tmpz1bz8pj5']
[2025-06-04T09:42:54.667+0000] {standard_task_runner.py:88} INFO - Job 105: Subtask dbt_debug
[2025-06-04T09:42:54.728+0000] {task_command.py:423} INFO - Running <TaskInstance: dbt_only_pipeline.dbt_debug scheduled__2025-06-03T02:00:00+00:00 [running]> on host 8fb7c8dc5097
[2025-06-04T09:42:54.815+0000] {taskinstance.py:2480} INFO - Exporting env vars: AIRFLOW_CTX_DAG_OWNER='data-team' AIRFLOW_CTX_DAG_ID='dbt_only_pipeline' AIRFLOW_CTX_TASK_ID='dbt_debug' AIRFLOW_CTX_EXECUTION_DATE='2025-06-03T02:00:00+00:00' AIRFLOW_CTX_TRY_NUMBER='1' AIRFLOW_CTX_DAG_RUN_ID='scheduled__2025-06-03T02:00:00+00:00'
[2025-06-04T09:42:54.819+0000] {dbt_only_dag.py:135} INFO - 🔍 Running dbt debug to validate configuration...
[2025-06-04T09:42:54.820+0000] {dbt_only_dag.py:68} INFO - 🚀 Running dbt debug
[2025-06-04T09:42:54.821+0000] {dbt_only_dag.py:88} INFO - 📋 Executing: dbt debug
[2025-06-04T09:43:00.481+0000] {dbt_only_dag.py:101} INFO - 📄 stdout: [0m09:42:56  Running with dbt=1.9.6
[0m09:42:56  dbt version: 1.9.6
[0m09:42:56  python version: 3.10.13
[0m09:42:56  python path: /usr/local/bin/python
[0m09:42:56  os info: Linux-********-microsoft-standard-WSL2-x86_64-with-glibc2.36
[0m09:42:57  Using profiles dir at /opt/***/workspace/dbt_live
[0m09:42:57  Using profiles.yml file at /opt/***/workspace/dbt_live/profiles.yml
[0m09:42:57  Using dbt_project.yml file at /opt/***/workspace/dbt_live/dbt_project.yml
[0m09:42:57  adapter type: snowflake
[0m09:42:57  adapter version: 1.9.4
[0m09:42:57  Configuration:
[0m09:42:57    profiles.yml file [[32mOK found and valid[0m]
[0m09:42:57    dbt_project.yml file [[32mOK found and valid[0m]
[0m09:42:57  Required dependencies:
[0m09:42:57   - git [[32mOK found[0m]

[0m09:42:57  Connection:
[0m09:42:57    account: SVLFKJI-IX89869
[0m09:42:57    user: XINBINZHANG
[0m09:42:57    database: MYDB
[0m09:42:57    warehouse: COMPUTE_WH
[0m09:42:57    role: ACCOUNTADMIN
[0m09:42:57    schema: LIVE_DATA
[0m09:42:57    authenticator: None
[0m09:42:57    oauth_client_id: None
[0m09:42:57    query_tag: None
[0m09:42:57    client_session_keep_alive: False
[0m09:42:57    host: None
[0m09:42:57    port: None
[0m09:42:57    proxy_host: None
[0m09:42:57    proxy_port: None
[0m09:42:57    protocol: None
[0m09:42:57    connect_retries: 1
[0m09:42:57    connect_timeout: None
[0m09:42:57    retry_on_database_errors: False
[0m09:42:57    retry_all: False
[0m09:42:57    insecure_mode: False
[0m09:42:57    reuse_connections: True
[0m09:42:57  Registered adapter: snowflake=1.9.4
[0m09:42:58    Connection test: [[32mOK connection ok[0m]

[0m09:42:58  [32mAll checks passed![0m

[2025-06-04T09:43:00.482+0000] {dbt_only_dag.py:107} INFO - ✅ dbt debug completed successfully
[2025-06-04T09:43:00.484+0000] {python.py:201} INFO - Done. Returned value was: {'success': True, 'command': 'dbt debug', 'stdout': '\x1b[0m09:42:56  Running with dbt=1.9.6\n\x1b[0m09:42:56  dbt version: 1.9.6\n\x1b[0m09:42:56  python version: 3.10.13\n\x1b[0m09:42:56  python path: /usr/local/bin/python\n\x1b[0m09:42:56  os info: Linux-********-microsoft-standard-WSL2-x86_64-with-glibc2.36\n\x1b[0m09:42:57  Using profiles dir at /opt/***/workspace/dbt_live\n\x1b[0m09:42:57  Using profiles.yml file at /opt/***/workspace/dbt_live/profiles.yml\n\x1b[0m09:42:57  Using dbt_project.yml file at /opt/***/workspace/dbt_live/dbt_project.yml\n\x1b[0m09:42:57  adapter type: snowflake\n\x1b[0m09:42:57  adapter version: 1.9.4\n\x1b[0m09:42:57  Configuration:\n\x1b[0m09:42:57    profiles.yml file [\x1b[32mOK found and valid\x1b[0m]\n\x1b[0m09:42:57    dbt_project.yml file [\x1b[32mOK found and valid\x1b[0m]\n\x1b[0m09:42:57  Required dependencies:\n\x1b[0m09:42:57   - git [\x1b[32mOK found\x1b[0m]\n\n\x1b[0m09:42:57  Connection:\n\x1b[0m09:42:57    account: SVLFKJI-IX89869\n\x1b[0m09:42:57    user: XINBINZHANG\n\x1b[0m09:42:57    database: MYDB\n\x1b[0m09:42:57    warehouse: COMPUTE_WH\n\x1b[0m09:42:57    role: ACCOUNTADMIN\n\x1b[0m09:42:57    schema: LIVE_DATA\n\x1b[0m09:42:57    authenticator: None\n\x1b[0m09:42:57    oauth_client_id: None\n\x1b[0m09:42:57    query_tag: None\n\x1b[0m09:42:57    client_session_keep_alive: False\n\x1b[0m09:42:57    host: None\n\x1b[0m09:42:57    port: None\n\x1b[0m09:42:57    proxy_host: None\n\x1b[0m09:42:57    proxy_port: None\n\x1b[0m09:42:57    protocol: None\n\x1b[0m09:42:57    connect_retries: 1\n\x1b[0m09:42:57    connect_timeout: None\n\x1b[0m09:42:57    retry_on_database_errors: False\n\x1b[0m09:42:57    retry_all: False\n\x1b[0m09:42:57    insecure_mode: False\n\x1b[0m09:42:57    reuse_connections: True\n\x1b[0m09:42:57  Registered adapter: snowflake=1.9.4\n\x1b[0m09:42:58    Connection test: [\x1b[32mOK connection ok\x1b[0m]\n\n\x1b[0m09:42:58  \x1b[32mAll checks passed!\x1b[0m\n', 'stderr': ''}
[2025-06-04T09:43:00.509+0000] {taskinstance.py:1138} INFO - Marking task as SUCCESS. dag_id=dbt_only_pipeline, task_id=dbt_debug, execution_date=20250603T020000, start_date=20250604T094254, end_date=20250604T094300
[2025-06-04T09:43:00.573+0000] {local_task_job_runner.py:234} INFO - Task exited with return code 0
[2025-06-04T09:43:00.604+0000] {taskinstance.py:3280} INFO - 1 downstream tasks scheduled from follow-on schedule check
