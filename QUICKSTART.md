# Quick Start Guide - Experiment 2: Daily Data Pipeline

## 🚀 One-Command End-to-End Run

```bash
# Complete daily pipeline (generate data + process with dbt)
python run_end2end_pipeline.py

# Or for a specific date
python run_end2end_pipeline.py --date 2025-01-15
```

## 🧪 Quick Test

```bash
# Test all components
python test_daily_pipeline.py

# Test specific component
python test_daily_pipeline.py --test s3
```

## 📋 Manual Setup (Step by Step)

### 1. Prerequisites
```bash
# Verify prerequisites
aws --version
python --version
dbt --version

# Install dependencies
pip install -r requirements.txt
```

### 2. AWS Setup
```bash
# Set up AWS credentials for Snowflake
python setup_aws_credentials.py

# Note the Role ARN from output for next step
```

### 3. Snowflake Setup
```bash
# Create external stages (use Role ARN from step 2)
python create_snowflake_stages.py --role-arn "arn:aws:iam::YOUR_ACCOUNT:role/SnowflakeS3AccessRole"
```

### 4. Generate Daily Data
```bash
# Generate today's data
python daily_data_generator.py

# Generate data for specific date
python daily_data_generator.py --date 2025-01-15

# Generate multiple days (backfill)
python daily_data_generator.py --days 7 --backfill
```

### 5. Run dbt Pipeline
```bash
cd dbt_live
dbt debug  # Test connection
dbt run     # Run models
dbt test    # Run tests
cd ..
```

## 🔄 Daily Operations

### Manual Daily Run
```bash
# Generate and process today's data
python run_end2end_pipeline.py

# Save detailed results
python run_end2end_pipeline.py --save-results
```

### Scheduled Daily Run (Cron Example)
```bash
# Add to crontab for daily 2 AM execution
# crontab -e
0 2 * * * cd /path/to/experiment2 && python run_end2end_pipeline.py >> daily_pipeline.log 2>&1
```

### Backfill Historical Data
```bash
# Generate data for past 7 days
python daily_data_generator.py --days 7 --backfill

# Process each day with dbt
for i in {1..7}; do
    date=$(date -d "$i days ago" +%Y-%m-%d)
    python run_end2end_pipeline.py --date $date
done
```

## 📊 Verify Results

### Check S3 Data
```bash
aws s3 ls s3://lake-loader-input-365542662955-20250525-001439/live-data/ --recursive
```

### Check Snowflake Tables
```sql
-- In Snowflake
USE DATABASE MYDB;
USE SCHEMA LIVE_DATA;

SELECT COUNT(*) FROM EXT_LIVE_USERS;
SELECT COUNT(*) FROM EXT_LIVE_ORDERS;
SELECT COUNT(*) FROM EXT_LIVE_EVENTS;

-- Check dbt models
SELECT COUNT(*) FROM DIM_USERS;
```

## 🛠️ Configuration

### Key Configuration Files
- `config/live_pipeline_config.yml` - Main pipeline configuration
- `dbt_live/profiles.yml` - dbt Snowflake connection
- `aws_setup/trust_policy.json` - AWS IAM trust policy
- `aws_setup/s3_policy.json` - S3 access policy

### Customization
```yaml
# Edit config/live_pipeline_config.yml
generation:
  batch_size: 1000          # Records per batch
  users_per_batch: 100      # Users per batch
  orders_per_user: 3        # Average orders per user

scheduling:
  generation_interval_minutes: 5    # Data generation frequency
  dbt_run_interval_minutes: 10      # dbt processing frequency
```

## 🧪 Testing

### Test Individual Components
```bash
# Test AWS connection
python -c "import boto3; print('AWS OK')"

# Test Snowflake connection
cd dbt_live && dbt debug

# Test data generation
python live_data_generator.py --batches 1

# Test dbt models
cd dbt_live && dbt run --models staging
```

### Data Quality Checks
```bash
cd dbt_live
dbt test --models staging    # Test staging models
dbt test --models marts      # Test marts models
```

## 📈 Monitoring

### Pipeline Health
```sql
-- Data freshness (should be < 30 minutes)
SELECT MAX(generated_at) as latest_data FROM EXT_LIVE_USERS;

-- Record counts by batch
SELECT batch_id, COUNT(*) as records
FROM EXT_LIVE_USERS
GROUP BY batch_id
ORDER BY batch_id DESC;
```

### Performance Metrics
```bash
# Check S3 upload performance
python live_data_generator.py --batches 1 | grep "Upload"

# Check dbt performance
cd dbt_live && dbt run --log-level debug
```

## 🚨 Troubleshooting

### Common Issues

**AWS Credentials Error**
```bash
aws configure list
aws sts get-caller-identity
```

**Snowflake Connection Failed**
```bash
cd dbt_live && dbt debug --log-level debug
```

**S3 Access Denied**
```bash
aws s3 ls s3://lake-loader-input-365542662955-20250525-001439/
```

**dbt Models Failed**
```bash
cd dbt_live && dbt run --models staging --log-level debug
```

### Log Locations
- Data generation: Console output
- dbt logs: `dbt_live/logs/`
- AWS setup: Console output

## 🎯 Success Criteria

✅ **Pipeline Health Indicators:**
- Data generation: New files in S3 every 5 minutes
- Snowflake: External tables show fresh data
- dbt: All models run successfully
- Tests: All data quality tests pass

✅ **Performance Targets:**
- Data generation: < 30 seconds per batch
- S3 upload: < 10 seconds per file
- dbt processing: < 60 seconds total
- End-to-end latency: < 15 minutes

---
**Setup Time**: ~15-30 minutes
**Skill Level**: Intermediate
**Dependencies**: AWS CLI, Python 3.8+, dbt
