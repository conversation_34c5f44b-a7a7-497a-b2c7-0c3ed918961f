# Setup Guide: dbt Marts Only DAG

This guide will help you set up and use the new minimal dbt marts-only DAG.

## 📁 New Files Created

1. **`airflow-orchestration/dags/dbt_marts_only_dag.py`** - The minimal marts-only DAG
2. **`airflow-orchestration/dags/README_dbt_marts_only.md`** - Detailed documentation
3. **`SETUP_MARTS_ONLY_DAG.md`** - This setup guide

## 🎯 What This DAG Does

**Simple Focus**: Runs only dbt marts models - nothing else!

### Task Flow (Super Simple)
```
Start → Run Marts → Test Marts → Validate → End
```

### Key Features
- ✅ **Minimal overhead** - Only 5 tasks total
- ✅ **Fast execution** - 5-minute timeout, quick turnaround
- ✅ **Business logic focus** - Only marts layer
- ✅ **Simple debugging** - Easy to troubleshoot
- ✅ **Independent** - Doesn't depend on other pipeline components

## 🚀 Quick Setup

### 1. Verify DAG File
The DAG should be in place:
```
airflow-orchestration/dags/dbt_marts_only_dag.py
```

### 2. Restart Airflow
```bash
cd airflow-orchestration
docker-compose down
docker-compose up -d
```

### 3. Check Airflow UI
1. Go to http://localhost:8080
2. Look for `dbt_marts_only` DAG
3. Should show tags: `dbt`, `marts`, `business-logic`

## ⚙️ Configuration

### Schedule
- **Default**: Daily at 3:00 AM (after main pipeline)
- **Modify**: Edit `schedule_interval` in the DAG file

### Parameters
- `run_tests`: Run marts tests (default: true)
- `validate_results`: Validate execution (default: true)

## 🧪 Testing the DAG

### 1. Manual Test
1. Go to Airflow UI
2. Find `dbt_marts_only` DAG
3. Click "Trigger DAG"
4. Watch the simple flow execute

### 2. Expected Execution Time
- **Total time**: 2-5 minutes (depending on marts complexity)
- **Per task**: < 5 minutes timeout

### 3. What Should Happen
1. **dbt_run_marts**: Executes `dbt run --models marts`
2. **dbt_test_marts**: Executes `dbt test --models marts`
3. **validate_marts_results**: Checks execution success
4. **All tasks green**: Pipeline completes successfully

## 🔍 Monitoring

### Check Task Logs
1. Click on any task in Airflow UI
2. Select "Logs" to see execution details
3. Look for:
   - `🏪 Running dbt marts models...`
   - `🧪 Running marts model tests...`
   - `✅ Validating marts results...`

### Success Indicators
- All tasks show green checkmarks
- Logs show successful dbt execution
- No error messages in task logs
- Validation task reports successful models

## 🔧 When to Use This DAG

### Perfect For:
1. **Quick marts refresh** - When you only need to update business logic
2. **Testing marts changes** - Validating marts model modifications
3. **Independent execution** - When source data is already available
4. **Fast turnaround** - When you need quick results
5. **Debugging marts** - Isolating marts-specific issues

### Not Suitable For:
1. **Full pipeline** - When you need staging, monitoring, etc.
2. **Data ingestion** - Doesn't handle external tables or stages
3. **First-time setup** - Assumes source data is already available

## 🔄 Integration Options

### Option 1: Standalone Use
Run independently when you need marts refresh:
```bash
# Manual trigger
docker-compose exec airflow-webserver airflow dags trigger dbt_marts_only
```

### Option 2: After Main Pipeline
Trigger after your main data pipeline completes:
```python
# In your main DAG
trigger_marts = TriggerDagRunOperator(
    task_id='trigger_marts_refresh',
    trigger_dag_id='dbt_marts_only',
    dag=main_dag,
)
```

### Option 3: On-Demand
Use for ad-hoc business logic updates without running full pipeline.

## 🐛 Troubleshooting

### Common Issues

1. **"dbt project not found"**
   - **Cause**: Workspace not mounted properly
   - **Solution**: Check Docker volume mounts

2. **"Marts models fail"**
   - **Cause**: Source data not available
   - **Solution**: Ensure staging/external tables have data

3. **"Tests fail"**
   - **Cause**: Data quality issues
   - **Solution**: Check marts model logic and test definitions

### Debug Commands
```bash
# Test marts manually
docker-compose exec airflow-webserver dbt run --models marts --project-dir /opt/airflow/workspace/dbt_live

# Check what marts models exist
docker-compose exec airflow-webserver dbt ls --models marts --project-dir /opt/airflow/workspace/dbt_live
```

## 📊 Performance Expectations

### Typical Execution Times
- **Small marts** (2-3 models): 1-2 minutes
- **Medium marts** (5-10 models): 2-4 minutes
- **Large marts** (10+ models): 3-5 minutes

### If Execution is Slow
1. **Check Snowflake warehouse** - Ensure adequate compute
2. **Review model complexity** - Optimize heavy transformations
3. **Check dependencies** - Ensure efficient model ordering

## ✅ Success Checklist

- [ ] DAG appears in Airflow UI as `dbt_marts_only`
- [ ] Manual trigger works without errors
- [ ] All 5 tasks complete successfully
- [ ] Execution time is reasonable (< 5 minutes)
- [ ] Marts models are updated in Snowflake
- [ ] Tests pass without failures
- [ ] Logs show expected dbt output

## 🎉 Benefits Summary

This minimal DAG provides:

1. **Speed** - Fast execution without overhead
2. **Simplicity** - Easy to understand and debug
3. **Focus** - Only business logic, no distractions
4. **Flexibility** - Can run independently or integrated
5. **Efficiency** - Minimal resource usage

Perfect for when you just need to refresh your marts layer quickly! 🚀

## 🔄 Next Steps

1. **Test the DAG** to ensure it works with your marts models
2. **Adjust schedule** if needed for your use case
3. **Monitor performance** and optimize if necessary
4. **Integrate** with your existing workflow as needed

The marts-only DAG is ready to provide fast, focused execution of your business logic layer!
