#!/usr/bin/env python3
"""
Daily Pipeline Summary
Shows the configuration and expected data volumes for the daily pipeline.
"""

import yaml
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def show_pipeline_summary():
    """Show comprehensive pipeline summary."""
    
    # Load configuration
    try:
        with open('config/live_pipeline_config.yml', 'r') as f:
            config = yaml.safe_load(f)
    except FileNotFoundError:
        logger.error("❌ Configuration file not found")
        return
    
    print("=" * 80)
    print("📊 DAILY DATA PIPELINE SUMMARY")
    print("=" * 80)
    
    # Pipeline Overview
    print("\n🎯 PIPELINE OVERVIEW:")
    print(f"   Pipeline Type: Daily Batch Processing")
    print(f"   Target Schedule: {config['scheduling']['daily_generation_time']} daily")
    print(f"   Data Retention: {config['aws']['lifecycle_rules'][0]['days_to_delete']} days")
    
    # Data Volumes
    print("\n📈 DAILY DATA VOLUMES:")
    gen_config = config['generation']
    print(f"   👥 Users per day: {gen_config['daily_users']:,}")
    print(f"   🛒 Orders per day: {gen_config['daily_orders']:,}")
    print(f"   📱 Events per day: {gen_config['daily_events']:,}")
    print(f"   📊 Total records per day: {gen_config['daily_users'] + gen_config['daily_orders'] + gen_config['daily_events']:,}")
    
    # Monthly projections
    monthly_users = gen_config['daily_users'] * 30
    monthly_orders = gen_config['daily_orders'] * 30
    monthly_events = gen_config['daily_events'] * 30
    monthly_total = monthly_users + monthly_orders + monthly_events
    
    print(f"\n📅 MONTHLY PROJECTIONS:")
    print(f"   👥 Users per month: {monthly_users:,}")
    print(f"   🛒 Orders per month: {monthly_orders:,}")
    print(f"   📱 Events per month: {monthly_events:,}")
    print(f"   📊 Total records per month: {monthly_total:,}")
    
    # Storage Configuration
    print(f"\n💾 STORAGE CONFIGURATION:")
    s3_config = config['s3']
    print(f"   S3 Bucket: {s3_config['input_bucket']}")
    print(f"   Users Path: {s3_config['users_prefix']}")
    print(f"   Orders Path: {s3_config['orders_prefix']}")
    print(f"   Events Path: {s3_config['events_prefix']}")
    print(f"   Partitioning: {'Enabled' if s3_config['partition_by_date'] else 'Disabled'}")
    
    # Snowflake Configuration
    print(f"\n❄️ SNOWFLAKE CONFIGURATION:")
    sf_config = config['snowflake']
    print(f"   Account: {sf_config['account']}")
    print(f"   Database: {sf_config['database']}")
    print(f"   Schema: {sf_config['schema']}")
    print(f"   Warehouse: {sf_config['warehouse']}")
    
    # dbt Configuration
    print(f"\n🔧 DBT CONFIGURATION:")
    dbt_config = config['dbt']
    print(f"   Project: {dbt_config['project_name']}")
    print(f"   Target: {dbt_config['target']}")
    print(f"   Incremental Strategy: {dbt_config['incremental_strategy']}")
    print(f"   Lookback Days: {dbt_config['lookback_days']}")
    
    # Processing Schedule
    print(f"\n⏰ PROCESSING SCHEDULE:")
    schedule_config = config['scheduling']
    print(f"   Data Generation: {schedule_config['daily_generation_time']} daily")
    print(f"   dbt Processing: {schedule_config['daily_dbt_run_time']} daily")
    print(f"   Quality Checks: {schedule_config['data_quality_check_time']} daily")
    
    # Monitoring Configuration
    print(f"\n📊 MONITORING CONFIGURATION:")
    monitor_config = config['monitoring']
    print(f"   Alerts Enabled: {monitor_config['enable_alerts']}")
    print(f"   Min Records/Batch: {monitor_config['min_records_per_batch']:,}")
    print(f"   Max Null %: {monitor_config['max_null_percentage']}%")
    print(f"   Max Duplicate %: {monitor_config['max_duplicate_percentage']}%")
    
    # File Size Estimates
    print(f"\n📁 ESTIMATED FILE SIZES (per day):")
    
    # Rough estimates based on CSV structure
    avg_user_row_size = 200  # bytes
    avg_order_row_size = 100  # bytes
    avg_event_row_size = 150  # bytes
    
    users_size_mb = (gen_config['daily_users'] * avg_user_row_size) / (1024 * 1024)
    orders_size_mb = (gen_config['daily_orders'] * avg_order_row_size) / (1024 * 1024)
    events_size_mb = (gen_config['daily_events'] * avg_event_row_size) / (1024 * 1024)
    total_size_mb = users_size_mb + orders_size_mb + events_size_mb
    
    print(f"   Users CSV: ~{users_size_mb:.1f} MB")
    print(f"   Orders CSV: ~{orders_size_mb:.1f} MB")
    print(f"   Events CSV: ~{events_size_mb:.1f} MB")
    print(f"   Total per day: ~{total_size_mb:.1f} MB")
    print(f"   Total per month: ~{total_size_mb * 30:.1f} MB ({total_size_mb * 30 / 1024:.1f} GB)")
    
    # Performance Expectations
    print(f"\n⚡ PERFORMANCE EXPECTATIONS:")
    print(f"   Data Generation: ~5-10 minutes")
    print(f"   S3 Upload: ~2-5 minutes")
    print(f"   Snowflake Refresh: ~1-2 minutes")
    print(f"   dbt Processing: ~2-5 minutes")
    print(f"   Total Pipeline: ~10-20 minutes")
    
    # Available Commands
    print(f"\n🚀 AVAILABLE COMMANDS:")
    print(f"   Full Pipeline: python run_end2end_pipeline.py")
    print(f"   Data Only: python daily_data_generator.py")
    print(f"   Test Pipeline: python test_daily_pipeline.py")
    print(f"   Setup Pipeline: python setup_live_pipeline.py")
    
    # Data Quality Features
    print(f"\n✅ DATA QUALITY FEATURES:")
    print(f"   • Referential integrity (orders → users)")
    print(f"   • Business rule validation (positive amounts)")
    print(f"   • Freshness monitoring (< 24 hours)")
    print(f"   • Duplicate detection and prevention")
    print(f"   • Null value monitoring and alerts")
    print(f"   • Schema validation and evolution")
    
    # Architecture Benefits
    print(f"\n🏗️ ARCHITECTURE BENEFITS:")
    print(f"   • Scalable: Handle increasing data volumes")
    print(f"   • Reliable: Comprehensive error handling")
    print(f"   • Monitorable: Built-in quality checks")
    print(f"   • Cost-effective: Optimized S3 and Snowflake usage")
    print(f"   • Maintainable: Clear separation of concerns")
    print(f"   • Testable: Comprehensive test suite")
    
    print("\n" + "=" * 80)
    print(f"📅 Summary generated at: {datetime.now()}")
    print("=" * 80)

def main():
    """Main function."""
    show_pipeline_summary()

if __name__ == "__main__":
    main()
