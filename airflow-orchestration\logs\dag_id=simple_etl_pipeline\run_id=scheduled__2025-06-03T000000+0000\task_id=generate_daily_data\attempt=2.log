[2025-06-04T02:00:42.683+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=non-requeueable deps ti=<TaskInstance: simple_etl_pipeline.generate_daily_data scheduled__2025-06-03T00:00:00+00:00 [queued]>
[2025-06-04T02:00:42.694+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=requeueable deps ti=<TaskInstance: simple_etl_pipeline.generate_daily_data scheduled__2025-06-03T00:00:00+00:00 [queued]>
[2025-06-04T02:00:42.695+0000] {taskinstance.py:2170} INFO - Starting attempt 2 of 2
[2025-06-04T02:00:42.706+0000] {taskinstance.py:2191} INFO - Executing <Task(PythonOperator): generate_daily_data> on 2025-06-03 00:00:00+00:00
[2025-06-04T02:00:42.713+0000] {standard_task_runner.py:60} INFO - Started process 358 to run task
[2025-06-04T02:00:42.717+0000] {standard_task_runner.py:87} INFO - Running: ['***', 'tasks', 'run', 'simple_etl_pipeline', 'generate_daily_data', 'scheduled__2025-06-03T00:00:00+00:00', '--job-id', '4', '--raw', '--subdir', 'DAGS_FOLDER/simple_etl_dag.py', '--cfg-path', '/tmp/tmp5wo47nkx']
[2025-06-04T02:00:42.719+0000] {standard_task_runner.py:88} INFO - Job 4: Subtask generate_daily_data
[2025-06-04T02:00:42.924+0000] {task_command.py:423} INFO - Running <TaskInstance: simple_etl_pipeline.generate_daily_data scheduled__2025-06-03T00:00:00+00:00 [running]> on host 0c73d38e3af1
[2025-06-04T02:00:43.013+0000] {taskinstance.py:2480} INFO - Exporting env vars: AIRFLOW_CTX_DAG_OWNER='data-team' AIRFLOW_CTX_DAG_ID='simple_etl_pipeline' AIRFLOW_CTX_TASK_ID='generate_daily_data' AIRFLOW_CTX_EXECUTION_DATE='2025-06-03T00:00:00+00:00' AIRFLOW_CTX_TRY_NUMBER='2' AIRFLOW_CTX_DAG_RUN_ID='scheduled__2025-06-03T00:00:00+00:00'
[2025-06-04T02:00:43.014+0000] {simple_etl_dag.py:48} INFO - Running script: G:\github\S3 dbt-snowflake c360\experiment2/daily_data_generator.py
[2025-06-04T02:00:43.015+0000] {simple_etl_dag.py:51} ERROR - Script not found: G:\github\S3 dbt-snowflake c360\experiment2/daily_data_generator.py
[2025-06-04T02:00:43.016+0000] {taskinstance.py:2698} ERROR - Task failed with exception
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/models/taskinstance.py", line 433, in _execute_task
    result = execute_callable(context=context, **execute_callable_kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/operators/python.py", line 199, in execute
    return_value = self.execute_callable()
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/operators/python.py", line 216, in execute_callable
    return self.python_callable(*self.op_args, **self.op_kwargs)
  File "/opt/airflow/dags/simple_etl_dag.py", line 109, in generate_data
    return run_python_script('daily_data_generator.py', **context)
  File "/opt/airflow/dags/simple_etl_dag.py", line 52, in run_python_script
    raise FileNotFoundError(f"Script not found: {script_path}")
FileNotFoundError: Script not found: G:\github\S3 dbt-snowflake c360\experiment2/daily_data_generator.py
[2025-06-04T02:00:43.024+0000] {taskinstance.py:1138} INFO - Marking task as FAILED. dag_id=simple_etl_pipeline, task_id=generate_daily_data, execution_date=20250603T000000, start_date=20250604T020042, end_date=20250604T020043
[2025-06-04T02:00:43.036+0000] {standard_task_runner.py:107} ERROR - Failed to execute job 4 for task generate_daily_data (Script not found: G:\github\S3 dbt-snowflake c360\experiment2/daily_data_generator.py; 358)
[2025-06-04T02:00:43.063+0000] {local_task_job_runner.py:234} INFO - Task exited with return code 1
[2025-06-04T02:00:43.087+0000] {taskinstance.py:3280} INFO - 0 downstream tasks scheduled from follow-on schedule check
