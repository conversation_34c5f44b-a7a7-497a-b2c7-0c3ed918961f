# Experiment 2: Live Data Pipeline with S3 → Snowflake Integration

## 🎯 Objective
Build a live data pipeline that:
1. **Generates live CSV data** directly to S3 using modified data generator
2. **Sets up AWS credentials** for Snowflake S3 integration
3. **Creates Snowflake external stages** to read directly from S3
4. **Updates dbt models** to consume live data from external stages
5. **Implements incremental loading** for continuous data processing

## 🏗️ Architecture Overview
```
Live Data Generator → S3 Buckets → Snowflake External Stages → dbt Models → Analytics
```

## 📁 Project Structure
```
experiment2/
├── README.md
├── live_data_generator.py          # Modified from 01-load-data.py
├── setup_aws_credentials.py        # AWS credential setup for Snowflake
├── create_snowflake_stages.py      # External stages creation
├── setup_live_pipeline.py          # Complete pipeline setup
├── dbt_live/                       # dbt project for live data
│   ├── dbt_project.yml
│   ├── profiles.yml
│   ├── models/
│   │   ├── sources.yml
│   │   ├── schema.yml
│   │   ├── staging/
│   │   │   ├── stg_users.sql
│   │   │   ├── stg_orders.sql
│   │   │   └── stg_events.sql
│   │   ├── marts/
│   │   │   ├── dim_users.sql
│   │   │   ├── fact_orders.sql
│   │   │   ├── fact_events.sql
│   │   │   └── mart_customer_360.sql
│   │   └── incremental/
│   │       ├── inc_orders.sql
│   │       └── inc_events.sql
│   └── macros/
│       └── generate_schema_name.sql
├── aws_setup/
│   ├── create_iam_role.json
│   ├── trust_policy.json
│   └── s3_policy.json
├── monitoring/
│   ├── pipeline_monitor.py
│   └── data_quality_checks.py
└── config/
    ├── live_pipeline_config.yml
    └── aws_config.yml
```

## 🚀 Implementation Plan

### Phase 1: Live Data Generation
- Convert Databricks data generator to S3-compatible version
- Implement continuous data generation with timestamps
- Add data partitioning by date/hour

### Phase 2: AWS Integration
- Create IAM role for Snowflake S3 access
- Set up AWS credentials using AWS CLI
- Configure S3 bucket policies

### Phase 3: Snowflake External Stages
- Create file formats for CSV parsing
- Set up external stages pointing to S3
- Create external tables for direct querying

### Phase 4: dbt Live Pipeline
- Update models to use external stages
- Implement incremental loading strategies
- Add data freshness monitoring

### Phase 5: Monitoring & Quality
- Real-time pipeline monitoring
- Data quality checks
- Alerting for pipeline failures

## 📊 Expected Outcomes
- **Live data generation**: Continuous CSV creation to S3
- **Real-time processing**: dbt models process new data automatically
- **Scalable architecture**: Handle increasing data volumes
- **Cost optimization**: Efficient S3 and Snowflake usage
- **Data quality**: Comprehensive monitoring and validation

---
**Status**: In Development 🚧  
**Target Completion**: TBD  
**Dependencies**: Experiment 1 success ✅
