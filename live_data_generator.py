#!/usr/bin/env python3
"""
Live Data Generator for S3
Converts the original Databricks data generator to create live CSV files directly in S3.
Based on 01-load-raw-data/01-load-data.py but adapted for continuous S3 generation.
"""

import pandas as pd
import numpy as np
import boto3
import logging
import time
import uuid
from datetime import datetime, timed<PERSON>ta
from faker import Faker
import random
import io
import os
from concurrent.futures import ThreadPoolExecutor
import yaml

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class LiveDataGenerator:
    def __init__(self, config_file='config/live_pipeline_config.yml'):
        """Initialize the live data generator."""
        self.config = self._load_config(config_file)
        self.s3_client = self._create_s3_client()
        self.fake = Faker()
        Faker.seed(42)  # For reproducible data
        random.seed(42)
        np.random.seed(42)
        
        # S3 configuration
        self.bucket_name = self.config['s3']['input_bucket']
        self.users_prefix = self.config['s3']['users_prefix']
        self.orders_prefix = self.config['s3']['orders_prefix']
        self.events_prefix = self.config['s3']['events_prefix']
        
        # Data generation parameters
        self.batch_size = self.config['generation']['batch_size']
        self.users_per_batch = self.config['generation']['users_per_batch']
        self.orders_per_user = self.config['generation']['orders_per_user']
        self.events_per_order = self.config['generation']['events_per_order']
        
        logger.info(f"✅ Live Data Generator initialized for bucket: {self.bucket_name}")

    def _load_config(self, config_file):
        """Load configuration from YAML file."""
        try:
            with open(config_file, 'r') as f:
                config = yaml.safe_load(f)
            logger.info(f"✅ Configuration loaded from {config_file}")
            return config
        except FileNotFoundError:
            logger.error(f"❌ Configuration file {config_file} not found")
            # Return default configuration
            return self._get_default_config()

    def _get_default_config(self):
        """Get default configuration if file not found."""
        return {
            's3': {
                'input_bucket': 'lake-loader-input-365542662955-20250525-001439',
                'users_prefix': 'live-data/users',
                'orders_prefix': 'live-data/orders',
                'events_prefix': 'live-data/events'
            },
            'generation': {
                'batch_size': 1000,
                'users_per_batch': 100,
                'orders_per_user': 3,
                'events_per_order': 5
            }
        }

    def _create_s3_client(self):
        """Create S3 client using AWS CLI credentials."""
        try:
            s3_client = boto3.client('s3')
            # Test connection
            s3_client.list_buckets()
            logger.info("✅ S3 client created successfully")
            return s3_client
        except Exception as e:
            logger.error(f"❌ Failed to create S3 client: {e}")
            raise

    def generate_users_batch(self, batch_id, num_users=100):
        """Generate a batch of user data."""
        logger.info(f"🔄 Generating {num_users} users for batch {batch_id}")
        
        users_data = []
        current_time = datetime.now()
        
        for i in range(num_users):
            user_id = str(uuid.uuid4())
            creation_date = current_time - timedelta(days=random.randint(1, 365))
            last_activity = creation_date + timedelta(days=random.randint(0, 30))
            
            user = {
                'id': user_id,
                'firstname': self.fake.first_name(),
                'lastname': self.fake.last_name(),
                'email': self.fake.email(),
                'address': self.fake.address().replace('\n', ', '),
                'canal': random.choice(['WEBAPP', 'MOBILE', 'PHONE', None]),
                'country': random.choice(['FR', 'USA', 'SPAIN']),
                'creation_date': creation_date.strftime("%m-%d-%Y %H:%M:%S"),
                'last_activity_date': last_activity.strftime("%m-%d-%Y %H:%M:%S"),
                'gender': random.randint(0, 1),
                'age_group': random.randint(0, 10),
                'churn': random.choice([True, False]),
                'batch_id': batch_id,
                'generated_at': current_time.isoformat()
            }
            users_data.append(user)
        
        return pd.DataFrame(users_data)

    def generate_orders_batch(self, users_df, batch_id):
        """Generate orders for a batch of users."""
        logger.info(f"🔄 Generating orders for batch {batch_id}")
        
        orders_data = []
        current_time = datetime.now()
        
        for _, user in users_df.iterrows():
            # Generate 1-5 orders per user
            num_orders = np.random.poisson(self.orders_per_user)
            num_orders = max(1, min(num_orders, 8))  # Ensure 1-8 orders
            
            for order_num in range(num_orders):
                order_id = str(uuid.uuid4())
                # Orders happen after user creation
                user_creation = datetime.strptime(user['creation_date'], "%m-%d-%Y %H:%M:%S")
                order_date = user_creation + timedelta(days=random.randint(0, 30))
                
                order = {
                    'id': order_id,
                    'user_id': user['id'],
                    'transaction_date': order_date.strftime("%m-%d-%Y %H:%M:%S"),
                    'item_count': random.randint(1, 5),
                    'amount': round(random.uniform(10, 200), 2),
                    'batch_id': batch_id,
                    'generated_at': current_time.isoformat()
                }
                orders_data.append(order)
        
        return pd.DataFrame(orders_data)

    def generate_events_batch(self, orders_df, batch_id):
        """Generate events for a batch of orders."""
        logger.info(f"🔄 Generating events for batch {batch_id}")
        
        events_data = []
        current_time = datetime.now()
        
        for _, order in orders_df.iterrows():
            # Generate 3-7 events per order
            num_events = random.randint(3, 7)
            
            for event_num in range(num_events):
                event_id = str(uuid.uuid4())
                session_id = str(uuid.uuid4())
                
                # Events happen around order time
                order_date = datetime.strptime(order['transaction_date'], "%m-%d-%Y %H:%M:%S")
                event_date = order_date + timedelta(minutes=random.randint(-60, 60))
                
                event = {
                    'user_id': order['user_id'],
                    'event_id': event_id,
                    'platform': random.choice(['ios', 'android', 'web', None]),
                    'date': event_date.strftime("%m-%d-%Y %H:%M:%S"),
                    'action': random.choice(['view', 'click', 'log', 'purchase']),
                    'session_id': session_id,
                    'url': f"https://example.com/{self.fake.uri_path()}",
                    'batch_id': batch_id,
                    'generated_at': current_time.isoformat()
                }
                events_data.append(event)
        
        return pd.DataFrame(events_data)

    def upload_dataframe_to_s3(self, df, prefix, filename):
        """Upload DataFrame as CSV to S3."""
        try:
            # Convert DataFrame to CSV string
            csv_buffer = io.StringIO()
            df.to_csv(csv_buffer, index=False)
            csv_content = csv_buffer.getvalue()
            
            # Upload to S3
            s3_key = f"{prefix}/{filename}"
            self.s3_client.put_object(
                Bucket=self.bucket_name,
                Key=s3_key,
                Body=csv_content,
                ContentType='text/csv',
                ServerSideEncryption='AES256'
            )
            
            logger.info(f"✅ Uploaded {len(df)} records to s3://{self.bucket_name}/{s3_key}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to upload to S3: {e}")
            return False

    def generate_and_upload_batch(self, batch_id):
        """Generate and upload a complete batch of data."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        try:
            # Generate users
            users_df = self.generate_users_batch(batch_id, self.users_per_batch)
            users_filename = f"users_batch_{batch_id}_{timestamp}.csv"
            self.upload_dataframe_to_s3(users_df, self.users_prefix, users_filename)
            
            # Generate orders
            orders_df = self.generate_orders_batch(users_df, batch_id)
            orders_filename = f"orders_batch_{batch_id}_{timestamp}.csv"
            self.upload_dataframe_to_s3(orders_df, self.orders_prefix, orders_filename)
            
            # Generate events
            events_df = self.generate_events_batch(orders_df, batch_id)
            events_filename = f"events_batch_{batch_id}_{timestamp}.csv"
            self.upload_dataframe_to_s3(events_df, self.events_prefix, events_filename)
            
            logger.info(f"🎉 Batch {batch_id} completed successfully!")
            return {
                'batch_id': batch_id,
                'timestamp': timestamp,
                'users_count': len(users_df),
                'orders_count': len(orders_df),
                'events_count': len(events_df),
                'status': 'success'
            }
            
        except Exception as e:
            logger.error(f"❌ Batch {batch_id} failed: {e}")
            return {
                'batch_id': batch_id,
                'timestamp': timestamp,
                'status': 'failed',
                'error': str(e)
            }

    def run_continuous_generation(self, interval_minutes=5, max_batches=None):
        """Run continuous data generation."""
        logger.info(f"🚀 Starting continuous data generation (interval: {interval_minutes} minutes)")
        
        batch_id = 1
        results = []
        
        try:
            while True:
                if max_batches and batch_id > max_batches:
                    logger.info(f"✅ Reached maximum batches ({max_batches}), stopping")
                    break
                
                logger.info(f"📊 Starting batch {batch_id}")
                result = self.generate_and_upload_batch(batch_id)
                results.append(result)
                
                if result['status'] == 'success':
                    logger.info(f"✅ Batch {batch_id} completed: {result['users_count']} users, {result['orders_count']} orders, {result['events_count']} events")
                else:
                    logger.error(f"❌ Batch {batch_id} failed: {result.get('error', 'Unknown error')}")
                
                batch_id += 1
                
                if max_batches is None or batch_id <= max_batches:
                    logger.info(f"⏰ Waiting {interval_minutes} minutes before next batch...")
                    time.sleep(interval_minutes * 60)
                
        except KeyboardInterrupt:
            logger.info("🛑 Generation stopped by user")
        except Exception as e:
            logger.error(f"❌ Generation failed: {e}")
        
        return results

def main():
    """Main function for command line usage."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Live Data Generator for S3')
    parser.add_argument('--batches', type=int, default=3, help='Number of batches to generate')
    parser.add_argument('--interval', type=int, default=1, help='Interval between batches (minutes)')
    parser.add_argument('--continuous', action='store_true', help='Run continuously')
    
    args = parser.parse_args()
    
    generator = LiveDataGenerator()
    
    if args.continuous:
        results = generator.run_continuous_generation(interval_minutes=args.interval)
    else:
        results = generator.run_continuous_generation(
            interval_minutes=args.interval, 
            max_batches=args.batches
        )
    
    # Summary
    successful_batches = sum(1 for r in results if r['status'] == 'success')
    total_users = sum(r.get('users_count', 0) for r in results if r['status'] == 'success')
    total_orders = sum(r.get('orders_count', 0) for r in results if r['status'] == 'success')
    total_events = sum(r.get('events_count', 0) for r in results if r['status'] == 'success')
    
    logger.info(f"\n📊 GENERATION SUMMARY:")
    logger.info(f"✅ Successful batches: {successful_batches}/{len(results)}")
    logger.info(f"👥 Total users generated: {total_users:,}")
    logger.info(f"🛒 Total orders generated: {total_orders:,}")
    logger.info(f"📱 Total events generated: {total_events:,}")

if __name__ == "__main__":
    main()
