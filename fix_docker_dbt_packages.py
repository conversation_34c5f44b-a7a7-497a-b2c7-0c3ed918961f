#!/usr/bin/env python3
"""
Fix dbt packages in Docker container via Airflow task
"""

import os
import shutil
import subprocess
import logging
import time

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def fix_dbt_packages_in_container():
    """Fix dbt packages directly in the container environment"""
    
    # This function is designed to run inside the Airflow container
    dbt_project_path = "/opt/airflow/workspace/dbt_live"
    
    logger.info(f"🔧 Fixing dbt packages in container: {dbt_project_path}")
    
    if not os.path.exists(dbt_project_path):
        logger.error(f"❌ dbt project path not found: {dbt_project_path}")
        return False
    
    # Change to dbt project directory
    original_cwd = os.getcwd()
    
    try:
        os.chdir(dbt_project_path)
        logger.info(f"📁 Changed to directory: {os.getcwd()}")
        
        # Step 1: Remove all existing packages
        packages_path = "dbt_packages"
        if os.path.exists(packages_path):
            logger.info("🗑️ Removing existing dbt_packages directory...")
            try:
                shutil.rmtree(packages_path)
                logger.info("✅ Removed dbt_packages directory")
            except Exception as e:
                logger.warning(f"⚠️ Could not remove dbt_packages: {e}")
                
                # Try to remove individual directories
                try:
                    for item in os.listdir(packages_path):
                        item_path = os.path.join(packages_path, item)
                        if os.path.isdir(item_path):
                            shutil.rmtree(item_path)
                            logger.info(f"✅ Removed {item}")
                except Exception as e2:
                    logger.warning(f"⚠️ Individual cleanup failed: {e2}")
        
        # Step 2: Clean dbt artifacts
        logger.info("🧹 Cleaning dbt artifacts...")
        for artifact in ["target", "logs", "dbt_modules"]:
            if os.path.exists(artifact):
                try:
                    shutil.rmtree(artifact)
                    logger.info(f"✅ Removed {artifact}")
                except Exception as e:
                    logger.warning(f"⚠️ Could not remove {artifact}: {e}")
        
        # Step 3: Verify packages.yml exists
        if not os.path.exists("packages.yml"):
            logger.error("❌ packages.yml not found")
            return False
        
        logger.info("📋 packages.yml content:")
        with open("packages.yml", "r") as f:
            content = f.read()
            logger.info(content)
        
        # Step 4: Reinstall packages using dbt deps
        logger.info("📦 Reinstalling dbt packages...")
        try:
            # Use dbt deps command
            result = subprocess.run(
                ["dbt", "deps"],
                capture_output=True,
                text=True,
                timeout=180  # 3 minutes timeout
            )
            
            if result.returncode == 0:
                logger.info("✅ dbt deps completed successfully")
                logger.info(f"Output: {result.stdout}")
            else:
                logger.error(f"❌ dbt deps failed: {result.stderr}")
                logger.error(f"Return code: {result.returncode}")
                return False
                
        except subprocess.TimeoutExpired:
            logger.error("❌ dbt deps timed out")
            return False
        except FileNotFoundError:
            logger.error("❌ dbt command not found")
            return False
        except Exception as e:
            logger.error(f"❌ dbt deps failed: {e}")
            return False
        
        # Step 5: Verify packages
        logger.info("🔍 Verifying packages...")
        if os.path.exists(packages_path):
            packages = os.listdir(packages_path)
            logger.info(f"📦 Installed packages: {packages}")
            
            # Check for duplicate dbt_utils
            dbt_utils_packages = [p for p in packages if 'dbt' in p.lower() and 'utils' in p.lower()]
            if len(dbt_utils_packages) > 1:
                logger.error(f"❌ Multiple dbt_utils packages found: {dbt_utils_packages}")
                return False
            elif len(dbt_utils_packages) == 1:
                logger.info(f"✅ Single dbt_utils package found: {dbt_utils_packages[0]}")
            else:
                logger.warning("⚠️ No dbt_utils package found")
        else:
            logger.error("❌ dbt_packages directory not created")
            return False
        
        # Step 6: Test dbt compilation
        logger.info("🧪 Testing dbt compilation...")
        try:
            result = subprocess.run(
                ["dbt", "parse"],
                capture_output=True,
                text=True,
                timeout=120
            )
            
            if result.returncode == 0:
                logger.info("✅ dbt parse completed successfully")
                return True
            else:
                logger.error(f"❌ dbt parse failed: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"❌ dbt parse test failed: {e}")
            return False
    
    finally:
        os.chdir(original_cwd)

def run_fix_as_airflow_task(**context):
    """Airflow task wrapper for the fix"""
    logger.info("🚀 Starting dbt packages fix in Airflow container...")
    
    success = fix_dbt_packages_in_container()
    
    if success:
        logger.info("🎉 dbt packages fix completed successfully!")
        return "dbt packages fixed successfully"
    else:
        logger.error("❌ dbt packages fix failed")
        raise Exception("dbt packages fix failed")

if __name__ == "__main__":
    # For local testing
    logger.info("🧪 Testing dbt packages fix locally...")
    success = fix_dbt_packages_in_container()
    
    if success:
        logger.info("🎉 Local test passed!")
    else:
        logger.error("❌ Local test failed!")
        exit(1)
