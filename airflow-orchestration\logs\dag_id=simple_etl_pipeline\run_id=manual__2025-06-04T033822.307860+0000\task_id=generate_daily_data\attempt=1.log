[2025-06-04T03:38:24.200+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=non-requeueable deps ti=<TaskInstance: simple_etl_pipeline.generate_daily_data manual__2025-06-04T03:38:22.307860+00:00 [queued]>
[2025-06-04T03:38:24.210+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=requeueable deps ti=<TaskInstance: simple_etl_pipeline.generate_daily_data manual__2025-06-04T03:38:22.307860+00:00 [queued]>
[2025-06-04T03:38:24.211+0000] {taskinstance.py:2170} INFO - Starting attempt 1 of 2
[2025-06-04T03:38:24.227+0000] {taskinstance.py:2191} INFO - Executing <Task(PythonOperator): generate_daily_data> on 2025-06-04 03:38:22.307860+00:00
[2025-06-04T03:38:24.232+0000] {standard_task_runner.py:60} INFO - Started process 939 to run task
[2025-06-04T03:38:24.237+0000] {standard_task_runner.py:87} INFO - Running: ['***', 'tasks', 'run', 'simple_etl_pipeline', 'generate_daily_data', 'manual__2025-06-04T03:38:22.307860+00:00', '--job-id', '60', '--raw', '--subdir', 'DAGS_FOLDER/simple_etl_dag.py', '--cfg-path', '/tmp/tmpkwvc4l17']
[2025-06-04T03:38:24.238+0000] {standard_task_runner.py:88} INFO - Job 60: Subtask generate_daily_data
[2025-06-04T03:38:24.294+0000] {task_command.py:423} INFO - Running <TaskInstance: simple_etl_pipeline.generate_daily_data manual__2025-06-04T03:38:22.307860+00:00 [running]> on host 9fdd53a423c8
[2025-06-04T03:38:24.379+0000] {taskinstance.py:2480} INFO - Exporting env vars: AIRFLOW_CTX_DAG_OWNER='data-team' AIRFLOW_CTX_DAG_ID='simple_etl_pipeline' AIRFLOW_CTX_TASK_ID='generate_daily_data' AIRFLOW_CTX_EXECUTION_DATE='2025-06-04T03:38:22.307860+00:00' AIRFLOW_CTX_TRY_NUMBER='1' AIRFLOW_CTX_DAG_RUN_ID='manual__2025-06-04T03:38:22.307860+00:00'
[2025-06-04T03:38:24.382+0000] {simple_etl_dag.py:48} INFO - Running script: /opt/***/workspace/simple_data_generator.py
[2025-06-04T03:38:24.464+0000] {simple_etl_dag.py:67} INFO - Script completed successfully: 🚀 Starting simple data generation...
✅ Generated 100 users and 500 transactions
📁 Files created:
   - data/users_2025-06-04.json
   - data/transactions_2025-06-04.json
🎉 Data generation completed successfully!
📊 Summary: {'users_count': 100, 'transactions_count': 500, 'files_created': ['data/users_2025-06-04.json', 'data/transactions_2025-06-04.json']}

[2025-06-04T03:38:24.464+0000] {python.py:201} INFO - Done. Returned value was: 🚀 Starting simple data generation...
✅ Generated 100 users and 500 transactions
📁 Files created:
   - data/users_2025-06-04.json
   - data/transactions_2025-06-04.json
🎉 Data generation completed successfully!
📊 Summary: {'users_count': 100, 'transactions_count': 500, 'files_created': ['data/users_2025-06-04.json', 'data/transactions_2025-06-04.json']}

[2025-06-04T03:38:24.489+0000] {taskinstance.py:1138} INFO - Marking task as SUCCESS. dag_id=simple_etl_pipeline, task_id=generate_daily_data, execution_date=20250604T033822, start_date=20250604T033824, end_date=20250604T033824
[2025-06-04T03:38:24.532+0000] {local_task_job_runner.py:234} INFO - Task exited with return code 0
[2025-06-04T03:38:24.569+0000] {taskinstance.py:3280} INFO - 1 downstream tasks scheduled from follow-on schedule check
