
  
    

create or replace transient table MYDB.LIVE_DATA.etl_health_dashboard
    

    
    as (

-- ETL Health Check: Overall Dashboard Summary
-- Combines all health metrics into a single dashboard view

with latest_data_quality as (
    select
        'Data Quality' as health_category,
        avg(overall_quality_score) as avg_health_score,
        min(overall_quality_score) as min_health_score,
        max(overall_quality_score) as max_health_score,
        sum(total_rows) as metric_value,
        sum(data_quality_issues) as issue_count,
        max(check_timestamp) as latest_check_time
    from MYDB.LIVE_DATA.data_quality_health
    where date(check_timestamp) = current_date
),

combined_health as (
    select * from latest_data_quality
),

health_summary as (
    select
        health_category,
        avg_health_score,
        min_health_score,
        max_health_score,
        latest_check_time,
        metric_value as total_items,
        issue_count as total_issues,
        
        -- Health status
        case 
            when avg_health_score >= 95 then 'Excellent'
            when avg_health_score >= 85 then 'Good'
            when avg_health_score >= 70 then 'Fair'
            when avg_health_score >= 50 then 'Poor'
            else 'Critical'
        end as health_status,
        
        -- Trend indicator (simplified)
        case 
            when min_health_score = max_health_score then 'Stable'
            when max_health_score - min_health_score <= 10 then 'Stable'
            when max_health_score - min_health_score <= 20 then 'Variable'
            else 'Unstable'
        end as trend_indicator
        
    from combined_health
),

overall_summary as (
    select
        current_date as dashboard_date,
        current_timestamp() as dashboard_timestamp,
        avg(avg_health_score) as overall_health_score,
        sum(case when health_status = 'Excellent' then 1 else 0 end) as excellent_categories,
        sum(case when health_status = 'Good' then 1 else 0 end) as good_categories,
        sum(case when health_status = 'Fair' then 1 else 0 end) as fair_categories,
        sum(case when health_status = 'Poor' then 1 else 0 end) as poor_categories,
        sum(case when health_status = 'Critical' then 1 else 0 end) as critical_categories,
        max(case when health_status in ('Poor', 'Critical') then 1 else 0 end) as has_critical_issues,
        max(case when trend_indicator = 'Unstable' then 1 else 0 end) as has_unstable_trends,
        count(*) as total_categories_monitored
    from health_summary
)

-- Final output combining summary and details
select
    'OVERALL' as health_category,
    overall_health_score as avg_health_score,
    overall_health_score as min_health_score,
    overall_health_score as max_health_score,
    dashboard_timestamp as latest_check_time,
    total_categories_monitored as total_items,
    critical_categories as total_issues,
    
    case 
        when overall_health_score >= 95 then 'Excellent'
        when overall_health_score >= 85 then 'Good'
        when overall_health_score >= 70 then 'Fair'
        when overall_health_score >= 50 then 'Poor'
        else 'Critical'
    end as health_status,
    
    case 
        when has_unstable_trends = 1 then 'Unstable'
        when has_critical_issues = 1 then 'Variable'
        else 'Stable'
    end as trend_indicator
    
from overall_summary

union all

-- Individual category details
select
    health_category,
    avg_health_score,
    min_health_score,
    max_health_score,
    latest_check_time,
    total_items,
    total_issues,
    health_status,
    trend_indicator
    
from health_summary

order by 
    case health_category 
        when 'OVERALL' then 0
        when 'Data Quality' then 1
        when 'Pipeline Runtime' then 2
        when 'dbt Tests' then 3
        when 'Query Performance' then 4
        else 5
    end
    )
;


  