#!/usr/bin/env python3
"""
ETL Pipeline Alert Monitor
Checks for critical issues and sends alerts
"""

import snowflake.connector
import pandas as pd
import yaml
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from datetime import datetime, timedelta
import os

def load_snowflake_config():
    """Load Snowflake connection configuration"""
    config_path = os.path.join('dbt_live', 'profiles.yml')
    
    with open(config_path, 'r') as file:
        profiles = yaml.safe_load(file)
    
    live_config = profiles['live_c360']['outputs']['live']
    
    return {
        'account': live_config['account'],
        'user': live_config['user'],
        'password': live_config['password'],
        'warehouse': live_config['warehouse'],
        'database': live_config['database'],
        'schema': live_config['schema'],
        'role': live_config['role']
    }

def connect_to_snowflake():
    """Create Snowflake connection"""
    config = load_snowflake_config()
    
    conn = snowflake.connector.connect(
        account=config['account'],
        user=config['user'],
        password=config['password'],
        warehouse=config['warehouse'],
        database=config['database'],
        schema=config['schema'],
        role=config['role']
    )
    
    return conn

def check_critical_alerts():
    """Check for critical pipeline issues"""
    conn = connect_to_snowflake()
    alerts = []
    
    try:
        # Check for failed dbt runs in last 24 hours
        failed_runs_query = """
        SELECT 
            query_text,
            start_time,
            error_message,
            total_elapsed_time
        FROM snowflake.account_usage.query_history
        WHERE start_time >= current_timestamp() - interval '24 hours'
          AND database_name = 'MYDB'
          AND execution_status = 'FAIL'
          AND (query_text ILIKE '%dbt%' OR query_text ILIKE '%create table%')
        ORDER BY start_time DESC
        LIMIT 10
        """
        
        failed_runs = pd.read_sql(failed_runs_query, conn)
        
        if not failed_runs.empty:
            alerts.append({
                'type': 'CRITICAL',
                'category': 'Pipeline Failures',
                'count': len(failed_runs),
                'details': f"{len(failed_runs)} failed dbt runs in last 24 hours",
                'data': failed_runs
            })
        
        # Check for stale data (no updates in last 6 hours)
        stale_data_query = """
        SELECT 
            table_name,
            latest_data_timestamp,
            datediff('hour', latest_data_timestamp, current_timestamp()) as hours_stale
        FROM MYDB.LIVE_DATA.data_quality_health
        WHERE latest_data_timestamp < current_timestamp() - interval '6 hours'
        """
        
        stale_data = pd.read_sql(stale_data_query, conn)
        
        if not stale_data.empty:
            alerts.append({
                'type': 'WARNING',
                'category': 'Stale Data',
                'count': len(stale_data),
                'details': f"{len(stale_data)} tables with stale data",
                'data': stale_data
            })
        
        # Check for significant volume changes (>50%)
        volume_changes_query = """
        SELECT 
            table_name,
            total_rows,
            volume_change_pct
        FROM MYDB.LIVE_DATA.data_quality_health
        WHERE abs(volume_change_pct) > 50
        """
        
        volume_changes = pd.read_sql(volume_changes_query, conn)
        
        if not volume_changes.empty:
            alerts.append({
                'type': 'WARNING',
                'category': 'Volume Changes',
                'count': len(volume_changes),
                'details': f"{len(volume_changes)} tables with significant volume changes",
                'data': volume_changes
            })
        
        # Check for low health scores (<70)
        health_issues_query = """
        SELECT 
            health_category,
            avg_health_score,
            health_status
        FROM MYDB.LIVE_DATA.etl_health_dashboard
        WHERE avg_health_score < 70
        """
        
        health_issues = pd.read_sql(health_issues_query, conn)
        
        if not health_issues.empty:
            alerts.append({
                'type': 'CRITICAL',
                'category': 'Health Score',
                'count': len(health_issues),
                'details': f"{len(health_issues)} categories with poor health scores",
                'data': health_issues
            })
        
        return alerts
        
    finally:
        conn.close()

def format_alert_report(alerts):
    """Format alerts into a readable report"""
    if not alerts:
        return "✅ No critical alerts detected. Pipeline is healthy!"
    
    report = []
    report.append("🚨 ETL PIPELINE ALERTS")
    report.append("=" * 50)
    report.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report.append("")
    
    critical_count = sum(1 for alert in alerts if alert['type'] == 'CRITICAL')
    warning_count = sum(1 for alert in alerts if alert['type'] == 'WARNING')
    
    report.append(f"🔴 Critical Alerts: {critical_count}")
    report.append(f"🟡 Warning Alerts: {warning_count}")
    report.append("")
    
    for alert in alerts:
        icon = "🔴" if alert['type'] == 'CRITICAL' else "🟡"
        report.append(f"{icon} {alert['category'].upper()}")
        report.append(f"   {alert['details']}")
        
        if alert['category'] == 'Pipeline Failures':
            report.append("   Recent failures:")
            for _, row in alert['data'].head(3).iterrows():
                report.append(f"   • {row['START_TIME']}: {row['ERROR_MESSAGE'][:100]}...")
        
        elif alert['category'] == 'Stale Data':
            report.append("   Stale tables:")
            for _, row in alert['data'].iterrows():
                report.append(f"   • {row['TABLE_NAME']}: {row['HOURS_STALE']} hours old")
        
        elif alert['category'] == 'Volume Changes':
            report.append("   Volume changes:")
            for _, row in alert['data'].iterrows():
                report.append(f"   • {row['TABLE_NAME']}: {row['VOLUME_CHANGE_PCT']:+.1f}% change")
        
        elif alert['category'] == 'Health Score':
            report.append("   Poor health scores:")
            for _, row in alert['data'].iterrows():
                report.append(f"   • {row['HEALTH_CATEGORY']}: {row['AVG_HEALTH_SCORE']:.0f}% ({row['HEALTH_STATUS']})")
        
        report.append("")
    
    return "\n".join(report)

def send_email_alert(report, recipients=None):
    """Send email alert (placeholder - configure with your SMTP settings)"""
    if recipients is None:
        recipients = ["<EMAIL>"]  # Configure your email
    
    # Email configuration (update with your SMTP settings)
    smtp_server = "smtp.gmail.com"  # Update with your SMTP server
    smtp_port = 587
    sender_email = "<EMAIL>"  # Update with your sender email
    sender_password = "your_password"  # Use environment variable in production
    
    try:
        msg = MIMEMultipart()
        msg['From'] = sender_email
        msg['To'] = ", ".join(recipients)
        msg['Subject'] = "ETL Pipeline Alert"
        
        msg.attach(MIMEText(report, 'plain'))
        
        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(sender_email, sender_password)
        text = msg.as_string()
        server.sendmail(sender_email, recipients, text)
        server.quit()
        
        print("✅ Alert email sent successfully")
        
    except Exception as e:
        print(f"❌ Failed to send email alert: {str(e)}")

def main():
    """Main alert monitoring function"""
    print("🔍 Checking for ETL pipeline alerts...")
    
    try:
        alerts = check_critical_alerts()
        report = format_alert_report(alerts)
        
        print(report)
        
        # Send email if there are critical alerts
        critical_alerts = [a for a in alerts if a['type'] == 'CRITICAL']
        if critical_alerts:
            print("\n📧 Critical alerts detected - email notification would be sent")
            # Uncomment to enable email alerts:
            # send_email_alert(report)
        
    except Exception as e:
        error_msg = f"❌ Error checking alerts: {str(e)}"
        print(error_msg)
        # Uncomment to send error notifications:
        # send_email_alert(error_msg)

if __name__ == "__main__":
    main()
