#!/usr/bin/env python3
"""
ETL Health Dashboard Viewer
Displays the current health status of the ETL pipeline
"""

import snowflake.connector
import pandas as pd
import yaml
from datetime import datetime
import os

def load_snowflake_config():
    """Load Snowflake connection configuration"""
    config_path = os.path.join('dbt_live', 'profiles.yml')
    
    with open(config_path, 'r') as file:
        profiles = yaml.safe_load(file)
    
    # Extract connection details from dbt profiles
    live_config = profiles['live_c360']['outputs']['live']
    
    return {
        'account': live_config['account'],
        'user': live_config['user'],
        'password': live_config['password'],
        'warehouse': live_config['warehouse'],
        'database': live_config['database'],
        'schema': live_config['schema'],
        'role': live_config['role']
    }

def connect_to_snowflake():
    """Create Snowflake connection"""
    config = load_snowflake_config()
    
    conn = snowflake.connector.connect(
        account=config['account'],
        user=config['user'],
        password=config['password'],
        warehouse=config['warehouse'],
        database=config['database'],
        schema=config['schema'],
        role=config['role']
    )
    
    return conn

def get_health_dashboard_data():
    """Fetch ETL health dashboard data"""
    conn = connect_to_snowflake()
    
    try:
        # Get overall health summary
        dashboard_query = """
        SELECT 
            health_category,
            avg_health_score,
            total_items,
            total_issues,
            health_status,
            trend_indicator,
            latest_check_time
        FROM MYDB.LIVE_DATA.etl_health_dashboard
        ORDER BY 
            CASE health_category 
                WHEN 'OVERALL' THEN 0
                ELSE 1
            END,
            health_category
        """
        
        dashboard_df = pd.read_sql(dashboard_query, conn)
        
        # Get detailed data quality metrics
        data_quality_query = """
        SELECT 
            table_name,
            layer,
            total_rows,
            overall_quality_score,
            quality_status,
            freshness_status,
            volume_change_pct,
            latest_data_timestamp
        FROM MYDB.LIVE_DATA.data_quality_health
        ORDER BY layer, table_name
        """
        
        data_quality_df = pd.read_sql(data_quality_query, conn)
        
        return dashboard_df, data_quality_df
        
    finally:
        conn.close()

def format_health_score(score):
    """Format health score with color coding"""
    if score >= 95:
        return f"🟢 {score:.0f}"
    elif score >= 85:
        return f"🟡 {score:.0f}"
    elif score >= 70:
        return f"🟠 {score:.0f}"
    else:
        return f"🔴 {score:.0f}"

def format_status(status):
    """Format status with emojis"""
    status_map = {
        'Excellent': '🟢 Excellent',
        'Good': '🟡 Good', 
        'Fair': '🟠 Fair',
        'Poor': '🔴 Poor',
        'Critical': '🚨 Critical',
        'Fresh': '🟢 Fresh',
        'Acceptable': '🟡 Acceptable',
        'Stale': '🔴 Stale',
        'Normal': '🟢 Normal',
        'Moderate Change': '🟡 Moderate',
        'Significant Change': '🟠 Significant',
        'Extreme Change': '🔴 Extreme'
    }
    return status_map.get(status, status)

def display_dashboard():
    """Display the ETL health dashboard"""
    print("=" * 80)
    print("🏥 ETL HEALTH DASHBOARD")
    print("=" * 80)
    print(f"📅 Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    try:
        dashboard_df, data_quality_df = get_health_dashboard_data()
        
        # Display overall health summary
        print("📊 OVERALL HEALTH SUMMARY")
        print("-" * 40)
        
        for _, row in dashboard_df.iterrows():
            category = row['HEALTH_CATEGORY']
            score = row['AVG_HEALTH_SCORE']
            status = row['HEALTH_STATUS']
            items = row['TOTAL_ITEMS']
            issues = row['TOTAL_ISSUES']
            
            print(f"{category:20} | {format_health_score(score):8} | {format_status(status):15} | Items: {items:,} | Issues: {issues}")
        
        print()
        print("📋 DATA QUALITY DETAILS")
        print("-" * 60)
        
        for _, row in data_quality_df.iterrows():
            table = row['TABLE_NAME']
            layer = row['LAYER']
            rows = row['TOTAL_ROWS']
            quality_score = row['OVERALL_QUALITY_SCORE']
            quality_status = row['QUALITY_STATUS']
            freshness = row['FRESHNESS_STATUS']
            volume_change = row['VOLUME_CHANGE_PCT']
            
            print(f"{table:20} | {layer:8} | Rows: {rows:,} | {format_health_score(quality_score):8}")
            print(f"{'':20} | Quality: {format_status(quality_status):15} | Fresh: {format_status(freshness):15}")
            if volume_change != 0:
                print(f"{'':20} | Volume Change: {volume_change:+.1f}%")
            print()
        
        # Health recommendations
        print("💡 RECOMMENDATIONS")
        print("-" * 30)
        
        critical_issues = dashboard_df[dashboard_df['HEALTH_STATUS'].isin(['Poor', 'Critical'])]
        if not critical_issues.empty:
            print("🚨 CRITICAL ISSUES DETECTED:")
            for _, row in critical_issues.iterrows():
                print(f"   • {row['HEALTH_CATEGORY']}: {row['HEALTH_STATUS']} (Score: {row['AVG_HEALTH_SCORE']:.0f})")
        
        stale_data = data_quality_df[data_quality_df['FRESHNESS_STATUS'] == 'Stale']
        if not stale_data.empty:
            print("⏰ STALE DATA DETECTED:")
            for _, row in stale_data.iterrows():
                print(f"   • {row['TABLE_NAME']}: Data may be outdated")
        
        volume_changes = data_quality_df[abs(data_quality_df['VOLUME_CHANGE_PCT']) > 25]
        if not volume_changes.empty:
            print("📈 SIGNIFICANT VOLUME CHANGES:")
            for _, row in volume_changes.iterrows():
                print(f"   • {row['TABLE_NAME']}: {row['VOLUME_CHANGE_PCT']:+.1f}% change")
        
        if critical_issues.empty and stale_data.empty and volume_changes.empty:
            print("✅ No critical issues detected. Pipeline is healthy!")
        
    except Exception as e:
        print(f"❌ Error fetching dashboard data: {str(e)}")
        print("Make sure the monitoring models have been run successfully.")

if __name__ == "__main__":
    display_dashboard()
