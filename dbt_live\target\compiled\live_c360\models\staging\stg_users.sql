

-- Staging model for live users data
-- Cleans and standardizes raw user data from S3 external table

with source_data as (
    select * from MYDB.LIVE_DATA.ext_live_users
),

cleaned_data as (
    select
        -- Primary key
        id as user_id,
        
        -- Personal information (anonymized)
        sha1(email) as email_hash,
        initcap(trim(firstname)) as firstname,
        initcap(trim(lastname)) as lastname,
        trim(address) as address,
        upper(trim(canal)) as acquisition_channel,
        upper(trim(country)) as country,
        
        -- Dates
        try_to_timestamp(creation_date, 'MM-DD-YYYY HH24:MI:SS') as creation_date,
        try_to_timestamp(last_activity_date, 'MM-DD-YYYY HH24:MI:SS') as last_activity_date,
        
        -- Demographics
        case 
            when gender = 0 then 'F'
            when gender = 1 then 'M'
            else 'Unknown'
        end as gender,
        age_group,
        
        -- Behavioral flags
        churn as is_churned,
        
        -- Metadata
        batch_id,
        try_to_timestamp(generated_at) as generated_at,
        current_timestamp() as processed_at
        
    from source_data
    where id is not null  -- Filter out any null user IDs
),

final as (
    select
        *,
        -- Derived fields
        datediff('day', creation_date, current_timestamp()) as days_since_creation,
        datediff('day', last_activity_date, current_timestamp()) as days_since_last_activity,
        
        -- Data quality flags
        case 
            when creation_date > current_timestamp() then true
            else false
        end as has_future_creation_date,
        
        case 
            when last_activity_date < creation_date then true
            else false
        end as has_invalid_activity_date,
        
        -- Segmentation
        case 
            when days_since_last_activity <= 7 then 'Active'
            when days_since_last_activity <= 30 then 'Recent'
            when days_since_last_activity <= 90 then 'Dormant'
            else 'Inactive'
        end as activity_segment
        
    from cleaned_data
)

select * from final