# dbt Live C360 Project

This dbt project contains the data transformation models for the Live Customer 360 pipeline.

## Quick Start

### 1. Test Connection
```bash
dbt debug
```

### 2. Install Dependencies
```bash
dbt deps
```

### 3. Run Models
```bash
# Run all models
dbt run

# Run specific model groups
dbt run --models staging
dbt run --models marts
dbt run --models monitoring
```

### 4. Run Tests
```bash
dbt test
```

### 5. Generate and Serve Documentation

#### Option 1: Use Convenient Scripts
```bash
# PowerShell
.\serve_docs.ps1

# Command Prompt
serve_docs.bat
```

#### Option 2: Manual Commands
```bash
# Generate documentation
dbt docs generate

# Serve on port 8088 (to avoid conflict with Airflow on 8080)
dbt docs serve --port 8088
```

**📖 Documentation will be available at: http://localhost:8088**

## Port Configuration

- **Airflow**: http://localhost:8080 (airflow/airflow)
- **dbt docs**: http://localhost:8088 (configured to avoid conflicts)

## Project Structure

```
dbt_live/
├── models/
│   ├── staging/          # Raw data cleaning and standardization
│   ├── marts/           # Business logic and final tables
│   ├── monitoring/      # Data quality and health checks
│   └── incremental/     # Incremental loading models
├── tests/               # Data tests
├── macros/              # Reusable SQL macros
├── seeds/               # Static reference data
├── snapshots/           # SCD Type 2 tracking
├── profiles.yml         # Database connection settings
├── dbt_project.yml      # Project configuration
└── packages.yml         # dbt package dependencies
```

## Key Features

- **Snowflake Integration**: Direct connection to Snowflake data warehouse
- **Incremental Models**: Efficient processing of large datasets
- **Data Quality Tests**: Automated testing for data integrity
- **Documentation**: Auto-generated lineage and model documentation
- **Monitoring**: Built-in health checks and quality metrics

## Environment Targets

- **live**: Production environment (default)
- **dev**: Development environment for testing

## Common Commands

```bash
# Full refresh (rebuild all incremental models)
dbt run --full-refresh

# Run specific model and downstream dependencies
dbt run --models my_model+

# Run models with specific tags
dbt run --models tag:staging
dbt run --models tag:monitoring

# Test specific models
dbt test --models staging

# Compile without running
dbt compile

# Clean generated files
dbt clean
```

## Troubleshooting

### Connection Issues
1. Check `profiles.yml` credentials
2. Verify Snowflake account access
3. Run `dbt debug` for detailed diagnostics

### Port Conflicts
- If port 8088 is also in use, specify a different port:
  ```bash
  dbt docs serve --port 8089
  ```

### Performance Issues
- Use `--threads` to control parallelism:
  ```bash
  dbt run --threads 2
  ```
