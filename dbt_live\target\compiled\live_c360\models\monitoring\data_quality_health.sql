

-- ETL Health Check: Data Quality Monitoring
-- Tracks data quality metrics across all tables

with table_stats as (
    -- Users table quality
    select
        'stg_users' as table_name,
        'staging' as layer,
        count(*) as total_rows,
        count(distinct user_id) as unique_keys,
        sum(case when user_id is null then 1 else 0 end) as null_primary_keys,
        sum(case when email_hash is null then 1 else 0 end) as null_critical_fields,
        sum(case when has_future_creation_date then 1 else 0 end) as data_quality_issues,
        max(generated_at) as latest_data_timestamp,
        current_timestamp() as check_timestamp
    from MYDB.LIVE_DATA.stg_users
    
    union all
    
    -- Orders table quality
    select
        'stg_orders' as table_name,
        'staging' as layer,
        count(*) as total_rows,
        count(distinct order_id) as unique_keys,
        sum(case when order_id is null then 1 else 0 end) as null_primary_keys,
        sum(case when user_id is null or amount is null then 1 else 0 end) as null_critical_fields,
        sum(case when has_future_transaction_date or has_high_item_price then 1 else 0 end) as data_quality_issues,
        max(generated_at) as latest_data_timestamp,
        current_timestamp() as check_timestamp
    from MYDB.LIVE_DATA.stg_orders
    
    union all
    
    -- Events table quality
    select
        'stg_events' as table_name,
        'staging' as layer,
        count(*) as total_rows,
        count(distinct event_id) as unique_keys,
        sum(case when event_id is null then 1 else 0 end) as null_primary_keys,
        sum(case when user_id is null or session_id is null then 1 else 0 end) as null_critical_fields,
        sum(case when has_future_event_timestamp then 1 else 0 end) as data_quality_issues,
        max(generated_at) as latest_data_timestamp,
        current_timestamp() as check_timestamp
    from MYDB.LIVE_DATA.stg_events
    
    union all
    
    -- Dimension Users quality
    select
        'dim_users' as table_name,
        'marts' as layer,
        count(*) as total_rows,
        count(distinct user_id) as unique_keys,
        sum(case when user_id is null then 1 else 0 end) as null_primary_keys,
        sum(case when email_hash is null then 1 else 0 end) as null_critical_fields,
        sum(case when has_future_creation_date or has_invalid_activity_date then 1 else 0 end) as data_quality_issues,
        max(generated_at) as latest_data_timestamp,
        current_timestamp() as check_timestamp
    from MYDB.LIVE_DATA.dim_users
    
    union all
    
    -- Fact Orders quality
    select
        'fact_orders' as table_name,
        'marts' as layer,
        count(*) as total_rows,
        count(distinct order_id) as unique_keys,
        sum(case when order_id is null then 1 else 0 end) as null_primary_keys,
        sum(case when user_id is null or order_amount is null then 1 else 0 end) as null_critical_fields,
        0 as data_quality_issues,  -- No specific DQ flags in fact table yet
        max(transaction_date) as latest_data_timestamp,
        current_timestamp() as check_timestamp
    from MYDB.LIVE_DATA.fact_orders
),

quality_metrics as (
    select
        *,
        -- Calculate quality scores
        case when total_rows = unique_keys then 100 else round((unique_keys::float / total_rows) * 100, 2) end as uniqueness_score,
        case when null_primary_keys = 0 then 100 else round(((total_rows - null_primary_keys)::float / total_rows) * 100, 2) end as completeness_score,
        case when data_quality_issues = 0 then 100 else round(((total_rows - data_quality_issues)::float / total_rows) * 100, 2) end as validity_score,
        
        -- Freshness check (data should be within last 24 hours for live pipeline)
        case 
            when latest_data_timestamp >= current_timestamp() - interval '1 hour' then 100
            when latest_data_timestamp >= current_timestamp() - interval '6 hours' then 80
            when latest_data_timestamp >= current_timestamp() - interval '24 hours' then 60
            else 30
        end as freshness_score,
        
        -- Data volume check (flag significant changes)
        lag(total_rows) over (partition by table_name order by check_timestamp) as previous_row_count
        
    from table_stats
),

final_metrics as (
    select
        *,
        -- Volume change detection
        case 
            when previous_row_count is null then 0  -- First run
            when previous_row_count = 0 then 0  -- Avoid division by zero
            else round(((total_rows - previous_row_count)::float / previous_row_count) * 100, 2)
        end as volume_change_pct,
        
        -- Overall quality score
        round((uniqueness_score * 0.25 + completeness_score * 0.35 + validity_score * 0.25 + freshness_score * 0.15), 0) as overall_quality_score
        
    from quality_metrics
)

select
    table_name,
    layer,
    total_rows,
    unique_keys,
    null_primary_keys,
    null_critical_fields,
    data_quality_issues,
    latest_data_timestamp,
    uniqueness_score,
    completeness_score,
    validity_score,
    freshness_score,
    overall_quality_score,
    volume_change_pct,
    
    -- Quality status
    case 
        when overall_quality_score >= 95 then 'Excellent'
        when overall_quality_score >= 85 then 'Good'
        when overall_quality_score >= 70 then 'Fair'
        when overall_quality_score >= 50 then 'Poor'
        else 'Critical'
    end as quality_status,
    
    -- Volume change status
    case 
        when abs(volume_change_pct) <= 10 then 'Normal'
        when abs(volume_change_pct) <= 25 then 'Moderate Change'
        when abs(volume_change_pct) <= 50 then 'Significant Change'
        else 'Extreme Change'
    end as volume_status,
    
    -- Freshness status
    case 
        when freshness_score >= 80 then 'Fresh'
        when freshness_score >= 60 then 'Acceptable'
        else 'Stale'
    end as freshness_status,
    
    check_timestamp
    
from final_metrics
order by layer, table_name