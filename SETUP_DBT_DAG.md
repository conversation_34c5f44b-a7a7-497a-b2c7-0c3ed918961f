# Setup Guide: dbt-Only DAG

This guide will help you set up and use the new dbt-only DAG in your Airflow environment.

## 📁 Files Created

1. **`airflow-orchestration/dags/dbt_only_dag.py`** - The main DAG file
2. **`airflow-orchestration/dags/README_dbt_only_dag.md`** - Detailed documentation
3. **`fix_external_tables.py`** - Standalone script to fix external table issues
4. **`validate_dbt_dag_syntax.py`** - Syntax validation script
5. **`test_dbt_dag.py`** - Comprehensive test script
6. **`SETUP_DBT_DAG.md`** - This setup guide

## 🚀 Quick Setup

### 1. Verify DAG is in Place
The DAG file should already be in your Airflow DAGs directory:
```
airflow-orchestration/dags/dbt_only_dag.py
```

### 2. Restart Airflow (if running)
If Airflow is currently running, restart it to load the new DAG:

```bash
# Stop Airflow
cd airflow-orchestration
docker-compose down

# Start Airflow
docker-compose up -d

# Or use the convenience script
.\build-and-deploy.ps1  # Windows
./build-and-deploy.sh   # Linux/Mac
```

### 3. Verify DAG is Loaded
1. Open Airflow UI: http://localhost:8080
2. Login with: `airflow` / `airflow`
3. Look for `dbt_only_pipeline` in the DAG list
4. The DAG should show with tags: `dbt`, `transformations`, `data-quality`

## 🎯 DAG Features

### Core Capabilities
- ✅ **Pure dbt operations** - No data generation or external dependencies
- ✅ **Modular execution** - Staging → Marts → Monitoring flow
- ✅ **Comprehensive testing** - Tests at each stage
- ✅ **Health checks** - Connection validation and result verification
- ✅ **Documentation** - Optional dbt docs generation
- ✅ **Error handling** - Robust error handling with detailed logging

### Task Flow
```
Start → Debug → Deps → Refresh Stages → Marts → Test Marts → Monitoring → Test All → Validate → End
                                                                    ↓
                                                             Generate Docs (optional)
```

**Note**: Staging tasks have been removed per user request - the DAG now runs marts models directly.

### 🔧 External Table Fix
The DAG includes an automatic **stage refresh step** that fixes the common issue:
```
External table EXT_LIVE_EVENTS marked invalid. Stage S3_LIVE_EVENTS_STAGE dropped.
```

This happens when Snowflake external tables lose connection to their S3 stages. The DAG automatically refreshes all external tables before running staging models.

## 🔧 Configuration Options

### Schedule
- **Default**: Daily at 2:00 AM
- **Modify**: Edit `schedule_interval` in the DAG file

### Runtime Parameters
When triggering manually, you can set these parameters:
- `run_staging`: Run staging models (default: true)
- `run_marts`: Run marts models (default: true)  
- `run_monitoring`: Run monitoring models (default: true)
- `run_tests`: Run dbt tests (default: true)
- `generate_docs`: Generate documentation (default: false)
- `docs_port`: Port for docs server (default: 8088)

## 🧪 Testing the DAG

### 1. Manual Trigger Test
1. Go to Airflow UI
2. Find `dbt_only_pipeline` DAG
3. Click the "Play" button to trigger manually
4. Monitor the task execution in the Graph view

### 2. Individual Task Testing
You can test individual tasks by clicking on them and selecting "Run Task"

### 3. Command Line Testing
```bash
# Trigger the entire DAG
docker-compose exec airflow-webserver airflow dags trigger dbt_only_pipeline

# Test individual tasks
docker-compose exec airflow-webserver airflow tasks test dbt_only_pipeline dbt_debug 2024-01-01
```

## 🔍 Monitoring & Debugging

### Check Task Logs
1. Click on any task in the Airflow UI
2. Select "Logs" to see detailed execution information
3. Look for dbt output and any error messages

### Common Issues & Solutions

#### 1. DAG Not Appearing
- **Cause**: Syntax errors or import issues
- **Solution**: Check Airflow logs, validate syntax with our validation script

#### 2. dbt Command Not Found
- **Cause**: dbt not installed in Airflow container
- **Solution**: Ensure custom Docker image includes dbt-core and dbt-snowflake

#### 3. Connection Errors
- **Cause**: Invalid Snowflake credentials or network issues
- **Solution**: 
  - Check `dbt_live/profiles.yml` configuration
  - Run `dbt_debug` task to diagnose
  - Verify Snowflake credentials

#### 4. Model Failures
- **Cause**: Data issues, SQL errors, or missing dependencies
- **Solution**:
  - Check individual task logs
  - Review dbt logs in container
  - Validate source data

### Debug Commands
```bash
# Check dbt installation
docker-compose exec airflow-webserver dbt --version

# Test dbt connection
docker-compose exec airflow-webserver dbt debug --project-dir /opt/airflow/workspace/dbt_live

# Run dbt manually
docker-compose exec airflow-webserver dbt run --project-dir /opt/airflow/workspace/dbt_live

# Check workspace files
docker-compose exec airflow-webserver ls -la /opt/airflow/workspace/dbt_live/
```

## 🎨 Customization

### Adding New Model Groups
To add a new model group (e.g., `analytics`):

1. **Add task function** in `dbt_only_dag.py`:
```python
def dbt_run_analytics(**context):
    """Run analytics models"""
    return run_dbt_command('run', models='analytics', **context)
```

2. **Add task definition**:
```python
run_analytics_task = PythonOperator(
    task_id='dbt_run_analytics',
    python_callable=dbt_run_analytics,
    dag=dag,
)
```

3. **Update dependencies**:
```python
test_marts_task >> run_analytics_task >> run_monitoring_task
```

### Changing Schedule
Edit the DAG definition:
```python
schedule_interval='@hourly',     # Every hour
schedule_interval='0 */6 * * *', # Every 6 hours
schedule_interval=None,          # Manual only
```

## 📊 Integration with Other DAGs

### Triggering from Other DAGs
```python
from airflow.operators.trigger_dagrun import TriggerDagRunOperator

trigger_dbt = TriggerDagRunOperator(
    task_id='trigger_dbt_pipeline',
    trigger_dag_id='dbt_only_pipeline',
    dag=your_dag,
)
```

### Waiting for Other DAGs
```python
from airflow.sensors.external_task import ExternalTaskSensor

wait_for_ingestion = ExternalTaskSensor(
    task_id='wait_for_data_ingestion',
    external_dag_id='data_ingestion_dag',
    external_task_id='end_task',
    dag=dag,
)
```

## ✅ Success Checklist

- [ ] DAG appears in Airflow UI
- [ ] Manual trigger works without errors
- [ ] All tasks complete successfully
- [ ] dbt models are created in Snowflake
- [ ] dbt tests pass
- [ ] Logs show expected output
- [ ] Documentation generates (if enabled)

## 🆘 Getting Help

If you encounter issues:

1. **Check the logs** in Airflow UI for detailed error messages
2. **Run validation script**: `python validate_dbt_dag_syntax.py`
3. **Test dbt manually** in the container
4. **Review the README**: `airflow-orchestration/dags/README_dbt_only_dag.md`
5. **Check existing DAGs** for working patterns

## 🎉 Next Steps

Once the DAG is working:

1. **Schedule it** for regular execution
2. **Set up monitoring** and alerts
3. **Integrate** with other DAGs in your pipeline
4. **Customize** for your specific dbt models
5. **Document** any customizations for your team
