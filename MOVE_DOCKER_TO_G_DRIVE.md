# 💾 Moving Docker to G: Drive - Space Optimization Guide

**Date:** 2025-06-04  
**Issue:** Docker taking too much space on C: drive  
**Solution:** Move Docker data to G: drive  
**Status:** 📋 GUIDE READY  

## 🔍 **Current Situation**

### **✅ What's Working:**
- Docker Desktop: Fully functional
- Docker Airflow: Successfully deployed (airflow/airflow)
- All services: Running and healthy
- Web UI: Accessible at http://localhost:8080

### **⚠️ Issue:**
- Docker consuming significant space on C: drive
- Need to move Docker data to G: drive for space optimization

## 🛠️ **Solution Options**

### **Option 1: Move Docker Desktop Data Directory (Recommended)**

#### **Step 1: Stop Docker Services**
```powershell
# Stop Airflow services first
cd "G:\github\S3 dbt-snowflake c360\experiment2\airflow-orchestration"
$env:PATH += ";C:\Program Files\Docker\Docker\resources\bin"
docker compose down

# Stop Docker Desktop
Stop-Process -Name "Docker Desktop" -Force -ErrorAction SilentlyContinue
```

#### **Step 2: Close Docker Desktop Completely**
1. **Right-click Docker whale icon** in system tray
2. **Select "Quit Docker Desktop"**
3. **Wait for complete shutdown**

#### **Step 3: Move Docker Data**
```powershell
# Create new Docker directory on G: drive
New-Item -ItemType Directory -Force -Path "G:\Docker"

# Move Docker Desktop data
Move-Item "C:\Users\<USER>\AppData\Local\Docker" "G:\Docker\AppData" -Force

# Create symbolic link
New-Item -ItemType SymbolicLink -Path "C:\Users\<USER>\AppData\Local\Docker" -Target "G:\Docker\AppData"
```

#### **Step 4: Move WSL2 Data (Optional)**
```powershell
# Export WSL distributions
wsl --export docker-desktop "G:\Docker\docker-desktop.tar"
wsl --export docker-desktop-data "G:\Docker\docker-desktop-data.tar"

# Unregister old distributions
wsl --unregister docker-desktop
wsl --unregister docker-desktop-data

# Import to new location
wsl --import docker-desktop "G:\Docker\wsl\docker-desktop" "G:\Docker\docker-desktop.tar"
wsl --import docker-desktop-data "G:\Docker\wsl\docker-desktop-data" "G:\Docker\docker-desktop-data.tar"

# Clean up tar files
Remove-Item "G:\Docker\docker-desktop.tar", "G:\Docker\docker-desktop-data.tar"
```

### **Option 2: Docker Desktop Settings (Easier)**

#### **Step 1: Open Docker Desktop Settings**
1. **Start Docker Desktop**
2. **Click Settings (gear icon)**
3. **Go to Resources → Advanced**

#### **Step 2: Change Data Directory**
1. **Find "Disk image location"**
2. **Click "Browse"**
3. **Select G:\Docker**
4. **Click "Apply & Restart"**

### **Option 3: Fresh Install on G: Drive**

#### **Step 1: Backup Current Setup**
```powershell
# Export Airflow volumes
cd "G:\github\S3 dbt-snowflake c360\experiment2\airflow-orchestration"
$env:PATH += ";C:\Program Files\Docker\Docker\resources\bin"
docker compose down
docker volume ls
# Note volume names for backup if needed
```

#### **Step 2: Uninstall and Reinstall**
1. **Uninstall Docker Desktop** from Control Panel
2. **Download Docker Desktop installer**
3. **Install to G: drive** (if installer allows custom path)

## 🚀 **Recommended Approach: Option 2 (Settings)**

This is the safest and easiest method:

### **Step-by-Step Instructions:**

#### **1. Stop Airflow Services**
```powershell
cd "G:\github\S3 dbt-snowflake c360\experiment2\airflow-orchestration"
$env:PATH += ";C:\Program Files\Docker\Docker\resources\bin"
docker compose down
```

#### **2. Open Docker Desktop Settings**
- Click Docker whale icon in system tray
- Select "Settings"
- Go to "Resources" → "Advanced"

#### **3. Change Disk Image Location**
- Find "Disk image location" 
- Current: `C:\Users\<USER>\AppData\Local\Docker\wsl\data\ext4.vhdx`
- Click "Browse" and select: `G:\Docker`
- Click "Apply & Restart"

#### **4. Wait for Migration**
- Docker will automatically move data
- This may take 10-30 minutes depending on data size
- Docker Desktop will restart automatically

#### **5. Verify and Restart Airflow**
```powershell
# Test Docker is working
docker --version
docker info

# Restart Airflow
cd "G:\github\S3 dbt-snowflake c360\experiment2\airflow-orchestration"
docker compose up -d

# Verify services
docker compose ps
```

## 📊 **Expected Space Savings**

### **Typical Docker Space Usage:**
- **Docker Desktop**: 2-5 GB
- **WSL2 Distributions**: 1-3 GB each
- **Container Images**: 1-10 GB (depending on images)
- **Volumes**: Variable (your Airflow data)

### **Total Potential Savings**: 5-20 GB on C: drive

## ✅ **Verification Steps**

After moving Docker to G: drive:

### **1. Check Docker Location**
```powershell
# Check Docker Desktop settings
# Should show G:\Docker as data location

# Check WSL distributions
wsl --list --verbose
# Should show docker-desktop and docker-desktop-data
```

### **2. Test Docker Functionality**
```powershell
# Test basic Docker
docker run hello-world

# Test Airflow
cd "G:\github\S3 dbt-snowflake c360\experiment2\airflow-orchestration"
docker compose ps
# All services should be healthy

# Test web interface
# http://localhost:8080 should work with airflow/airflow
```

### **3. Check Space Usage**
```powershell
# Check C: drive space (should be freed up)
Get-WmiObject -Class Win32_LogicalDisk | Select-Object DeviceID, @{Name="Size(GB)";Expression={[math]::Round($_.Size/1GB,2)}}, @{Name="FreeSpace(GB)";Expression={[math]::Round($_.FreeSpace/1GB,2)}}

# Check G: drive usage (should show Docker data)
Get-ChildItem "G:\Docker" -Recurse | Measure-Object -Property Length -Sum
```

## 🔄 **Rollback Plan (If Needed)**

If something goes wrong:

### **Option 1: Revert Settings**
1. Open Docker Desktop Settings
2. Change disk image location back to C: drive
3. Apply & Restart

### **Option 2: Reinstall**
1. Uninstall Docker Desktop
2. Delete G:\Docker folder
3. Reinstall Docker Desktop to default location
4. Restore Airflow setup

## 📝 **Post-Migration Checklist**

- [ ] Docker Desktop starts successfully
- [ ] `docker --version` works
- [ ] `docker info` shows correct information
- [ ] WSL distributions are healthy
- [ ] Airflow services start: `docker compose up -d`
- [ ] All 6 Airflow services show "healthy"
- [ ] Web UI accessible: http://localhost:8080
- [ ] Login works: airflow/airflow
- [ ] DAGs are visible and functional
- [ ] C: drive space freed up
- [ ] G: drive shows Docker data

## 💡 **Additional Tips**

### **1. Regular Cleanup**
```powershell
# Clean up unused Docker resources
docker system prune -a

# Remove unused volumes
docker volume prune

# Remove unused images
docker image prune -a
```

### **2. Monitor Space Usage**
```powershell
# Check Docker space usage
docker system df

# Check WSL space usage
wsl --list --verbose
```

### **3. Backup Strategy**
- Export important Docker volumes before major changes
- Keep Airflow DAGs and configuration in version control
- Document custom Docker configurations

## 🎯 **Expected Outcome**

After successful migration:
- ✅ **C: drive**: 5-20 GB freed up
- ✅ **G: drive**: Docker data moved
- ✅ **Docker**: Fully functional
- ✅ **Airflow**: All services working
- ✅ **Performance**: No degradation
- ✅ **Functionality**: All features preserved

---

**Next Steps:**
1. Choose migration method (recommend Option 2 - Settings)
2. Follow step-by-step instructions
3. Verify functionality
4. Update documentation with new paths
