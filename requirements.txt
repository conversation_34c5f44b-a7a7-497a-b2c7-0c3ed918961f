# Live Data Pipeline Requirements

# Core data processing
pandas>=1.5.0
numpy>=1.24.0

# AWS integration
boto3>=1.26.0
awscli>=1.27.0

# Snowflake integration
snowflake-connector-python>=3.0.0
dbt-core>=1.6.0
dbt-snowflake>=1.6.0

# Data generation
faker>=19.0.0

# Configuration and utilities
pyyaml>=6.0
python-dotenv>=1.0.0

# Monitoring and logging
structlog>=23.0.0

# Optional: Data quality and testing
great-expectations>=0.17.0

# Optional: Scheduling and orchestration
apache-airflow>=2.7.0

# Development and testing
pytest>=7.0.0
pytest-cov>=4.0.0
