[2025-06-04T03:52:06.642+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=non-requeueable deps ti=<TaskInstance: simple_etl_pipeline.generate_daily_data manual__2025-06-04T03:52:05.226263+00:00 [queued]>
[2025-06-04T03:52:06.657+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=requeueable deps ti=<TaskInstance: simple_etl_pipeline.generate_daily_data manual__2025-06-04T03:52:05.226263+00:00 [queued]>
[2025-06-04T03:52:06.658+0000] {taskinstance.py:2170} INFO - Starting attempt 1 of 2
[2025-06-04T03:52:06.674+0000] {taskinstance.py:2191} INFO - Executing <Task(PythonOperator): generate_daily_data> on 2025-06-04 03:52:05.226263+00:00
[2025-06-04T03:52:06.681+0000] {standard_task_runner.py:60} INFO - Started process 1113 to run task
[2025-06-04T03:52:06.686+0000] {standard_task_runner.py:87} INFO - Running: ['***', 'tasks', 'run', 'simple_etl_pipeline', 'generate_daily_data', 'manual__2025-06-04T03:52:05.226263+00:00', '--job-id', '64', '--raw', '--subdir', 'DAGS_FOLDER/simple_etl_dag.py', '--cfg-path', '/tmp/tmp0q7g72kc']
[2025-06-04T03:52:06.688+0000] {standard_task_runner.py:88} INFO - Job 64: Subtask generate_daily_data
[2025-06-04T03:52:06.745+0000] {task_command.py:423} INFO - Running <TaskInstance: simple_etl_pipeline.generate_daily_data manual__2025-06-04T03:52:05.226263+00:00 [running]> on host 9fdd53a423c8
[2025-06-04T03:52:06.825+0000] {taskinstance.py:2480} INFO - Exporting env vars: AIRFLOW_CTX_DAG_OWNER='data-team' AIRFLOW_CTX_DAG_ID='simple_etl_pipeline' AIRFLOW_CTX_TASK_ID='generate_daily_data' AIRFLOW_CTX_EXECUTION_DATE='2025-06-04T03:52:05.226263+00:00' AIRFLOW_CTX_TRY_NUMBER='1' AIRFLOW_CTX_DAG_RUN_ID='manual__2025-06-04T03:52:05.226263+00:00'
[2025-06-04T03:52:06.826+0000] {simple_etl_dag.py:48} INFO - Running script: /opt/***/workspace/simple_data_generator.py
[2025-06-04T03:52:06.900+0000] {simple_etl_dag.py:67} INFO - Script completed successfully: 🚀 Starting simple data generation...
✅ Generated 100 users and 500 transactions
📁 Files created:
   - data/users_2025-06-04.json
   - data/transactions_2025-06-04.json
🎉 Data generation completed successfully!
📊 Summary: {'users_count': 100, 'transactions_count': 500, 'files_created': ['data/users_2025-06-04.json', 'data/transactions_2025-06-04.json']}

[2025-06-04T03:52:06.900+0000] {python.py:201} INFO - Done. Returned value was: 🚀 Starting simple data generation...
✅ Generated 100 users and 500 transactions
📁 Files created:
   - data/users_2025-06-04.json
   - data/transactions_2025-06-04.json
🎉 Data generation completed successfully!
📊 Summary: {'users_count': 100, 'transactions_count': 500, 'files_created': ['data/users_2025-06-04.json', 'data/transactions_2025-06-04.json']}

[2025-06-04T03:52:06.923+0000] {taskinstance.py:1138} INFO - Marking task as SUCCESS. dag_id=simple_etl_pipeline, task_id=generate_daily_data, execution_date=20250604T035205, start_date=20250604T035206, end_date=20250604T035206
[2025-06-04T03:52:06.978+0000] {local_task_job_runner.py:234} INFO - Task exited with return code 0
[2025-06-04T03:52:07.006+0000] {taskinstance.py:3280} INFO - 1 downstream tasks scheduled from follow-on schedule check
