# Live Data Pipeline Configuration

# S3 Configuration
s3:
  input_bucket: "lake-loader-input-365542662955-20250525-001439"
  users_prefix: "live-data/users"
  orders_prefix: "live-data/orders"
  events_prefix: "live-data/events"

  # File organization
  partition_by_date: true
  partition_format: "year=%Y/month=%m/day=%d"

# Data Generation Parameters
generation:
  # Daily generation settings
  daily_users: 5000      # New users per day
  daily_orders: 15000    # New orders per day
  daily_events: 75000    # New events per day

  # Batch processing
  batch_size: 1000
  users_per_batch: 1000   # Larger batches for daily processing
  orders_per_user: 3      # Average orders per user (Poisson distribution)
  events_per_order: 5     # Average events per order

  # Data quality settings
  null_probability: 0.02  # 2% chance of null values where applicable
  duplicate_probability: 0.001  # 0.1% chance of duplicate records

  # Daily file naming
  use_date_partitioning: true
  file_prefix_format: "daily_%Y%m%d"

# Snowflake Integration
snowflake:
  account: "SVLFKJI-IX89869"
  user: "XINBINZHANG"
  database: "MYDB"
  warehouse: "COMPUTE_WH"
  schema: "LIVE_DATA"
  role: "ACCOUNTADMIN"

  # External stage configuration
  stage_name: "S3_LIVE_STAGE"
  file_format_name: "CSV_LIVE_FORMAT"

# dbt Configuration
dbt:
  project_name: "live_c360"
  target: "live"
  models_path: "models"

  # Incremental loading settings
  incremental_strategy: "merge"
  unique_key: "id"
  lookback_days: 7  # How many days to look back for incremental loads

# Monitoring Configuration
monitoring:
  enable_alerts: true
  alert_email: "<EMAIL>"

  # Data quality thresholds
  min_records_per_batch: 50
  max_null_percentage: 5.0
  max_duplicate_percentage: 1.0

  # Performance thresholds
  max_generation_time_minutes: 10
  max_upload_time_minutes: 5

# Pipeline Scheduling
scheduling:
  # Daily processing schedule
  daily_generation_time: "02:00"  # 2 AM daily generation
  daily_dbt_run_time: "03:00"     # 3 AM daily dbt processing
  data_quality_check_time: "04:00" # 4 AM daily quality checks

  # For testing/development
  generation_interval_minutes: 1440  # 24 hours = 1440 minutes
  dbt_run_interval_minutes: 60       # Run dbt 1 hour after generation
  data_quality_check_interval_minutes: 120  # Quality checks 2 hours after generation

  # Batch processing windows
  peak_hours_start: 9  # 9 AM
  peak_hours_end: 17   # 5 PM
  peak_batch_size_multiplier: 1.5

# AWS Configuration
aws:
  region: "ap-southeast-2"

  # IAM role for Snowflake
  snowflake_role_name: "SnowflakeS3AccessRole"
  snowflake_policy_name: "SnowflakeS3AccessPolicy"

  # S3 bucket policies
  enable_versioning: true
  enable_encryption: true
  lifecycle_rules:
    - name: "archive_old_data"
      days_to_ia: 30
      days_to_glacier: 90
      days_to_delete: 365
