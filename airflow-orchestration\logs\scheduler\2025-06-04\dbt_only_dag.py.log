[2025-06-04T09:42:06.324+0000] {processor.py:161} INFO - Started process (PID=5408) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:42:06.325+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T09:42:06.330+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:42:06.329+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:42:06.378+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:42:06.668+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:42:06.668+0000] {override.py:1769} INFO - Created Permission View: can edit on DAG:dbt_only_pipeline
[2025-06-04T09:42:06.685+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:42:06.685+0000] {override.py:1769} INFO - Created Permission View: can delete on DAG:dbt_only_pipeline
[2025-06-04T09:42:06.696+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:42:06.695+0000] {override.py:1769} INFO - Created Permission View: can read on DAG:dbt_only_pipeline
[2025-06-04T09:42:06.697+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:42:06.697+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T09:42:06.715+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:42:06.715+0000] {dag.py:3058} INFO - Creating ORM DAG for dbt_only_pipeline
[2025-06-04T09:42:06.738+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:42:06.738+0000] {dag.py:3823} INFO - Setting next_dagrun for dbt_only_pipeline to 2025-06-03 02:00:00+00:00, run_after=2025-06-04 02:00:00+00:00
[2025-06-04T09:42:06.940+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.621 seconds
[2025-06-04T09:42:37.427+0000] {processor.py:161} INFO - Started process (PID=5418) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:42:37.428+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T09:42:37.431+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:42:37.430+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:42:37.478+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:42:37.619+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:42:37.619+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T09:42:37.647+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:42:37.647+0000] {dag.py:3823} INFO - Setting next_dagrun for dbt_only_pipeline to 2025-06-03 02:00:00+00:00, run_after=2025-06-04 02:00:00+00:00
[2025-06-04T09:42:37.667+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.246 seconds
[2025-06-04T09:43:08.075+0000] {processor.py:161} INFO - Started process (PID=5428) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:43:08.079+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T09:43:08.082+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:43:08.081+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:43:08.123+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:43:08.237+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:43:08.237+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T09:43:08.307+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.236 seconds
[2025-06-04T09:43:38.818+0000] {processor.py:161} INFO - Started process (PID=5439) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:43:38.819+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T09:43:38.822+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:43:38.822+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:43:38.871+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:43:38.979+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:43:38.978+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T09:43:39.020+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.209 seconds
[2025-06-04T09:44:09.295+0000] {processor.py:161} INFO - Started process (PID=5449) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:44:09.298+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T09:44:09.301+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:44:09.301+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:44:09.335+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:44:09.453+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:44:09.452+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T09:44:09.509+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.219 seconds
[2025-06-04T09:44:39.936+0000] {processor.py:161} INFO - Started process (PID=5460) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:44:39.938+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T09:44:39.940+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:44:39.940+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:44:39.974+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:44:40.075+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:44:40.075+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T09:44:40.117+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.185 seconds
[2025-06-04T09:45:10.480+0000] {processor.py:161} INFO - Started process (PID=5470) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:45:10.481+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T09:45:10.484+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:45:10.484+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:45:10.516+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:45:10.610+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:45:10.610+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T09:45:10.653+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.178 seconds
[2025-06-04T09:45:41.116+0000] {processor.py:161} INFO - Started process (PID=5480) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:45:41.118+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T09:45:41.121+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:45:41.121+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:45:41.161+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:45:41.256+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:45:41.256+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T09:45:41.304+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.192 seconds
[2025-06-04T09:46:11.433+0000] {processor.py:161} INFO - Started process (PID=5490) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:46:11.437+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T09:46:11.440+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:46:11.440+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:46:11.478+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:46:11.577+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:46:11.577+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T09:46:11.659+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.231 seconds
[2025-06-04T09:46:42.174+0000] {processor.py:161} INFO - Started process (PID=5500) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:46:42.177+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T09:46:42.180+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:46:42.180+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:46:42.215+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:46:42.316+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:46:42.315+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T09:46:42.360+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.191 seconds
[2025-06-04T09:47:12.785+0000] {processor.py:161} INFO - Started process (PID=5510) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:47:12.787+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T09:47:12.791+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:47:12.789+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:47:12.829+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:47:12.925+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:47:12.924+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T09:47:12.979+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.200 seconds
[2025-06-04T09:47:43.714+0000] {processor.py:161} INFO - Started process (PID=5520) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:47:43.716+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T09:47:43.718+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:47:43.718+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:47:43.757+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:47:43.854+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:47:43.854+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T09:47:43.904+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.194 seconds
[2025-06-04T09:48:14.426+0000] {processor.py:161} INFO - Started process (PID=5530) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:48:14.429+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T09:48:14.432+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:48:14.431+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:48:14.468+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:48:14.559+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:48:14.559+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T09:48:14.752+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.330 seconds
[2025-06-04T09:48:45.091+0000] {processor.py:161} INFO - Started process (PID=5541) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:48:45.093+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T09:48:45.095+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:48:45.095+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:48:45.132+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:48:45.237+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:48:45.237+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T09:48:45.444+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.357 seconds
[2025-06-04T09:49:15.699+0000] {processor.py:161} INFO - Started process (PID=5551) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:49:15.701+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T09:49:15.703+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:49:15.703+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:49:15.739+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:49:15.848+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:49:15.848+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T09:49:15.891+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.197 seconds
[2025-06-04T09:49:46.396+0000] {processor.py:161} INFO - Started process (PID=5561) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:49:46.401+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T09:49:46.406+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:49:46.406+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:49:46.633+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:49:46.743+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:49:46.743+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T09:49:46.781+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.391 seconds
[2025-06-04T09:50:17.143+0000] {processor.py:161} INFO - Started process (PID=5571) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:50:17.145+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T09:50:17.148+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:50:17.147+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:50:17.189+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:50:17.290+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:50:17.289+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T09:50:17.345+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.208 seconds
[2025-06-04T09:50:47.731+0000] {processor.py:161} INFO - Started process (PID=5581) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:50:47.736+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T09:50:47.739+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:50:47.738+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:50:47.776+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:50:47.872+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:50:47.871+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T09:50:47.916+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.189 seconds
[2025-06-04T09:51:12.227+0000] {processor.py:161} INFO - Started process (PID=5590) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:51:12.229+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T09:51:12.232+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:51:12.231+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:51:12.288+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:51:12.416+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:51:12.416+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T09:51:12.486+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.263 seconds
[2025-06-04T09:51:21.344+0000] {processor.py:161} INFO - Started process (PID=5592) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:51:21.346+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T09:51:21.349+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:51:21.348+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:51:21.413+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:51:21.658+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:51:21.658+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T09:51:21.727+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.388 seconds
[2025-06-04T09:51:31.666+0000] {processor.py:161} INFO - Started process (PID=5599) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:51:31.668+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T09:51:31.670+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:51:31.670+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:51:31.717+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:51:31.734+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:51:31.734+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T09:51:31.792+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.134 seconds
[2025-06-04T09:52:02.121+0000] {processor.py:161} INFO - Started process (PID=5609) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:52:02.122+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T09:52:02.125+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:52:02.124+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:52:02.157+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:52:02.348+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:52:02.347+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T09:52:02.393+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.277 seconds
[2025-06-04T09:52:32.736+0000] {processor.py:161} INFO - Started process (PID=5619) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:52:32.737+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T09:52:32.740+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:52:32.739+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:52:32.773+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:52:32.885+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:52:32.885+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T09:52:32.937+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.206 seconds
[2025-06-04T09:53:03.415+0000] {processor.py:161} INFO - Started process (PID=5629) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:53:03.416+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T09:53:03.419+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:53:03.418+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:53:03.448+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:53:03.542+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:53:03.542+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T09:53:03.602+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.192 seconds
[2025-06-04T09:53:34.064+0000] {processor.py:161} INFO - Started process (PID=5638) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:53:34.066+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T09:53:34.069+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:53:34.069+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:53:34.121+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:53:34.234+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:53:34.234+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T09:53:34.389+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.329 seconds
[2025-06-04T09:54:04.827+0000] {processor.py:161} INFO - Started process (PID=5648) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:54:04.828+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T09:54:04.832+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:54:04.831+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:54:04.867+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:54:05.020+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:54:05.020+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T09:54:05.062+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.240 seconds
[2025-06-04T09:54:35.804+0000] {processor.py:161} INFO - Started process (PID=5658) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:54:35.805+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T09:54:35.808+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:54:35.807+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:54:35.842+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:54:35.939+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:54:35.939+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T09:54:35.985+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.185 seconds
[2025-06-04T09:55:06.338+0000] {processor.py:161} INFO - Started process (PID=5667) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:55:06.340+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T09:55:06.343+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:55:06.343+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:55:06.383+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:55:06.479+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:55:06.478+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T09:55:06.523+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.190 seconds
[2025-06-04T09:55:37.035+0000] {processor.py:161} INFO - Started process (PID=5677) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:55:37.036+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T09:55:37.039+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:55:37.038+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:55:37.092+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:55:37.216+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:55:37.215+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T09:55:37.259+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.230 seconds
[2025-06-04T09:55:49.780+0000] {processor.py:161} INFO - Started process (PID=5680) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:55:49.804+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T09:55:49.813+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:55:49.812+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:55:49.948+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:55:49.925+0000] {dagbag.py:348} ERROR - Failed to import: /opt/airflow/dags/dbt_only_dag.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/models/dagbag.py", line 344, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/opt/airflow/dags/dbt_only_dag.py", line 347, in <module>
    python_callable=dbt_run_staging,
NameError: name 'dbt_run_staging' is not defined
[2025-06-04T09:55:49.949+0000] {processor.py:842} WARNING - No viable dags retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:55:49.996+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.232 seconds
[2025-06-04T09:56:02.162+0000] {processor.py:161} INFO - Started process (PID=5688) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:56:02.164+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T09:56:02.167+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:56:02.166+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:56:02.217+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:56:02.212+0000] {dagbag.py:348} ERROR - Failed to import: /opt/airflow/dags/dbt_only_dag.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/models/dagbag.py", line 344, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/opt/airflow/dags/dbt_only_dag.py", line 347, in <module>
    python_callable=dbt_run_staging,
NameError: name 'dbt_run_staging' is not defined
[2025-06-04T09:56:02.218+0000] {processor.py:842} WARNING - No viable dags retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:56:02.247+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.088 seconds
[2025-06-04T09:56:08.240+0000] {processor.py:161} INFO - Started process (PID=5689) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:56:08.242+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T09:56:08.245+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:56:08.244+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:56:08.285+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:56:08.281+0000] {dagbag.py:348} ERROR - Failed to import: /opt/airflow/dags/dbt_only_dag.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/models/dagbag.py", line 344, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/opt/airflow/dags/dbt_only_dag.py", line 366, in <module>
    python_callable=dbt_test_staging,
NameError: name 'dbt_test_staging' is not defined
[2025-06-04T09:56:08.287+0000] {processor.py:842} WARNING - No viable dags retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:56:08.309+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.075 seconds
[2025-06-04T09:56:15.397+0000] {processor.py:161} INFO - Started process (PID=5690) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:56:15.399+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T09:56:15.402+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:56:15.402+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:56:15.453+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:56:15.444+0000] {dagbag.py:348} ERROR - Failed to import: /opt/airflow/dags/dbt_only_dag.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/models/dagbag.py", line 344, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/opt/airflow/dags/dbt_only_dag.py", line 417, in <module>
    refresh_stages_task >> run_staging_task >> test_staging_task
NameError: name 'run_staging_task' is not defined
[2025-06-04T09:56:15.455+0000] {processor.py:842} WARNING - No viable dags retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:56:15.478+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.084 seconds
[2025-06-04T09:56:28.790+0000] {processor.py:161} INFO - Started process (PID=5700) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:56:28.792+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T09:56:28.794+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:56:28.794+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:56:28.835+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:56:29.005+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:56:29.005+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T09:56:29.057+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.271 seconds
[2025-06-04T09:56:59.224+0000] {processor.py:161} INFO - Started process (PID=5710) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:56:59.226+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T09:56:59.230+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:56:59.229+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:56:59.261+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:56:59.371+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:56:59.371+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T09:56:59.441+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.221 seconds
[2025-06-04T09:57:30.009+0000] {processor.py:161} INFO - Started process (PID=5720) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:57:30.022+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T09:57:30.046+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:57:30.045+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:57:30.201+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:57:30.423+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:57:30.423+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T09:57:30.473+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.478 seconds
[2025-06-04T09:58:00.670+0000] {processor.py:161} INFO - Started process (PID=5730) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:58:00.671+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T09:58:00.676+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:58:00.675+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:58:00.714+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:58:00.842+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:58:00.842+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T09:58:00.894+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.229 seconds
[2025-06-04T09:58:31.122+0000] {processor.py:161} INFO - Started process (PID=5740) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:58:31.124+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T09:58:31.127+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:58:31.126+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:58:31.159+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:58:31.249+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:58:31.249+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T09:58:31.295+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.178 seconds
[2025-06-04T09:58:48.467+0000] {processor.py:161} INFO - Started process (PID=5741) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:58:48.469+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T09:58:48.472+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:58:48.472+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:58:48.541+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:58:48.727+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:58:48.727+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T09:58:48.784+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.321 seconds
[2025-06-04T09:59:19.117+0000] {processor.py:161} INFO - Started process (PID=5751) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:59:19.119+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T09:59:19.122+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:59:19.122+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:59:19.157+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:59:19.253+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:59:19.252+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T09:59:19.301+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.188 seconds
[2025-06-04T09:59:50.020+0000] {processor.py:161} INFO - Started process (PID=5761) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:59:50.022+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T09:59:50.025+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:59:50.024+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:59:50.069+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T09:59:50.196+0000] {logging_mixin.py:188} INFO - [2025-06-04T09:59:50.196+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T09:59:50.273+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.256 seconds
[2025-06-04T10:00:20.509+0000] {processor.py:161} INFO - Started process (PID=5771) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:00:20.512+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:00:20.515+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:00:20.514+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:00:20.551+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:00:20.642+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:00:20.642+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:00:20.700+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.195 seconds
[2025-06-04T10:00:51.181+0000] {processor.py:161} INFO - Started process (PID=5781) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:00:51.182+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:00:51.187+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:00:51.187+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:00:51.219+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:00:51.309+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:00:51.308+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:00:51.358+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.181 seconds
[2025-06-04T10:01:21.793+0000] {processor.py:161} INFO - Started process (PID=5791) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:01:21.795+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:01:21.798+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:01:21.797+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:01:21.833+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:01:21.929+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:01:21.928+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:01:21.977+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.189 seconds
[2025-06-04T10:01:52.420+0000] {processor.py:161} INFO - Started process (PID=5801) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:01:52.422+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:01:52.424+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:01:52.424+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:01:52.476+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:01:52.654+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:01:52.654+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:01:52.709+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.294 seconds
[2025-06-04T10:02:23.177+0000] {processor.py:161} INFO - Started process (PID=5811) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:02:23.179+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:02:23.181+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:02:23.181+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:02:23.217+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:02:23.302+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:02:23.301+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:02:23.349+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.176 seconds
[2025-06-04T10:02:53.542+0000] {processor.py:161} INFO - Started process (PID=5821) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:02:53.544+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:02:53.547+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:02:53.547+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:02:53.580+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:02:53.671+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:02:53.670+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:02:53.716+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.178 seconds
[2025-06-04T10:03:24.148+0000] {processor.py:161} INFO - Started process (PID=5831) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:03:24.150+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:03:24.152+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:03:24.152+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:03:24.190+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:03:24.275+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:03:24.275+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:03:24.320+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.177 seconds
[2025-06-04T10:03:54.659+0000] {processor.py:161} INFO - Started process (PID=5841) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:03:54.660+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:03:54.663+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:03:54.662+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:03:54.699+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:03:54.787+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:03:54.787+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:03:54.832+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.178 seconds
[2025-06-04T10:04:25.397+0000] {processor.py:161} INFO - Started process (PID=5851) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:04:25.399+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:04:25.404+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:04:25.401+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:04:25.457+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:04:25.542+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:04:25.542+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:04:25.588+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.196 seconds
[2025-06-04T10:04:55.917+0000] {processor.py:161} INFO - Started process (PID=5861) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:04:55.919+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:04:55.922+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:04:55.922+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:04:55.983+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:04:56.094+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:04:56.093+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:04:56.142+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.232 seconds
[2025-06-04T10:05:26.568+0000] {processor.py:161} INFO - Started process (PID=5877) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:05:26.571+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:05:26.574+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:05:26.574+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:05:26.611+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:05:26.703+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:05:26.702+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:05:26.757+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.193 seconds
[2025-06-04T10:06:59.810+0000] {processor.py:161} INFO - Started process (PID=48) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:06:59.821+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:06:59.842+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:06:59.842+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:07:00.065+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:07:00.530+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:07:00.530+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:07:00.581+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.787 seconds
[2025-06-04T10:07:31.411+0000] {processor.py:161} INFO - Started process (PID=65) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:07:31.414+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:07:31.416+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:07:31.416+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:07:31.454+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:07:31.573+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:07:31.572+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:07:31.670+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.263 seconds
[2025-06-04T10:08:02.456+0000] {processor.py:161} INFO - Started process (PID=76) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:08:02.460+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:08:02.476+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:08:02.476+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:08:02.519+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:08:02.616+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:08:02.615+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:08:02.668+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.217 seconds
[2025-06-04T10:08:33.204+0000] {processor.py:161} INFO - Started process (PID=87) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:08:33.205+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:08:33.207+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:08:33.207+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:08:33.240+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:08:33.395+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:08:33.394+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:08:33.488+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.288 seconds
[2025-06-04T10:09:04.319+0000] {processor.py:161} INFO - Started process (PID=98) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:09:04.320+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:09:04.323+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:09:04.323+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:09:04.358+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:09:04.474+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:09:04.473+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:09:04.552+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.238 seconds
[2025-06-04T10:09:35.143+0000] {processor.py:161} INFO - Started process (PID=109) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:09:35.146+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:09:35.149+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:09:35.148+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:09:35.181+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:09:35.280+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:09:35.280+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:09:35.331+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.193 seconds
[2025-06-04T10:10:05.988+0000] {processor.py:161} INFO - Started process (PID=120) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:10:05.990+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:10:05.999+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:10:05.998+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:10:06.099+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:10:06.201+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:10:06.201+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:10:06.256+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.271 seconds
[2025-06-04T10:10:36.938+0000] {processor.py:161} INFO - Started process (PID=131) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:10:36.940+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:10:36.946+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:10:36.945+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:10:36.987+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:10:37.095+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:10:37.095+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:10:37.173+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.241 seconds
[2025-06-04T10:11:07.709+0000] {processor.py:161} INFO - Started process (PID=142) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:11:07.714+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:11:07.717+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:11:07.717+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:11:07.751+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:11:07.848+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:11:07.848+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:11:07.902+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.198 seconds
[2025-06-04T10:11:38.453+0000] {processor.py:161} INFO - Started process (PID=153) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:11:38.454+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:11:38.457+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:11:38.456+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:11:38.513+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:11:38.611+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:11:38.611+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:11:38.670+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.230 seconds
[2025-06-04T10:12:09.301+0000] {processor.py:161} INFO - Started process (PID=164) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:12:09.303+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:12:09.305+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:12:09.305+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:12:09.343+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:12:09.436+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:12:09.436+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:12:09.485+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.188 seconds
[2025-06-04T10:12:40.031+0000] {processor.py:161} INFO - Started process (PID=175) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:12:40.032+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:12:40.035+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:12:40.034+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:12:40.070+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:12:40.166+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:12:40.166+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:12:40.210+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.184 seconds
[2025-06-04T10:13:10.708+0000] {processor.py:161} INFO - Started process (PID=186) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:13:10.710+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:13:10.712+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:13:10.712+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:13:10.751+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:13:10.856+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:13:10.856+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:13:10.901+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.200 seconds
[2025-06-04T10:13:41.388+0000] {processor.py:161} INFO - Started process (PID=197) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:13:41.389+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:13:41.392+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:13:41.392+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:13:41.429+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:13:41.528+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:13:41.527+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:13:41.583+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.200 seconds
[2025-06-04T10:14:12.342+0000] {processor.py:161} INFO - Started process (PID=208) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:14:12.344+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:14:12.346+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:14:12.346+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:14:12.378+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:14:12.479+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:14:12.478+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:14:12.531+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.193 seconds
[2025-06-04T10:14:43.072+0000] {processor.py:161} INFO - Started process (PID=219) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:14:43.074+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:14:43.077+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:14:43.076+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:14:43.110+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:14:43.211+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:14:43.211+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:14:43.264+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.195 seconds
[2025-06-04T10:15:13.735+0000] {processor.py:161} INFO - Started process (PID=230) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:15:13.737+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:15:13.739+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:15:13.739+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:15:13.779+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:15:13.884+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:15:13.884+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:15:13.944+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.214 seconds
[2025-06-04T10:15:44.534+0000] {processor.py:161} INFO - Started process (PID=241) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:15:44.536+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:15:44.539+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:15:44.539+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:15:44.609+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:15:44.764+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:15:44.764+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:15:44.842+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.313 seconds
[2025-06-04T10:16:15.413+0000] {processor.py:161} INFO - Started process (PID=252) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:16:15.416+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:16:15.419+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:16:15.418+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:16:15.467+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:16:15.782+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:16:15.782+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:16:15.834+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.425 seconds
[2025-06-04T10:16:46.628+0000] {processor.py:161} INFO - Started process (PID=263) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:16:46.630+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:16:46.633+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:16:46.632+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:16:46.674+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:16:46.764+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:16:46.764+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:16:46.817+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.193 seconds
[2025-06-04T10:17:17.419+0000] {processor.py:161} INFO - Started process (PID=274) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:17:17.420+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:17:17.422+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:17:17.422+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:17:17.453+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:17:17.540+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:17:17.540+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:17:17.602+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.187 seconds
[2025-06-04T10:17:48.251+0000] {processor.py:161} INFO - Started process (PID=285) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:17:48.253+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:17:48.258+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:17:48.258+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:17:48.291+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:17:48.385+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:17:48.385+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:17:48.435+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.188 seconds
[2025-06-04T10:18:18.958+0000] {processor.py:161} INFO - Started process (PID=296) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:18:18.960+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:18:18.963+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:18:18.963+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:18:19.000+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:18:19.095+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:18:19.094+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:18:19.145+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.193 seconds
[2025-06-04T10:18:49.688+0000] {processor.py:161} INFO - Started process (PID=307) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:18:49.691+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:18:49.697+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:18:49.697+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:18:49.740+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:18:49.831+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:18:49.830+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:18:49.899+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.216 seconds
[2025-06-04T10:19:20.450+0000] {processor.py:161} INFO - Started process (PID=318) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:19:20.452+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:19:20.460+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:19:20.459+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:19:20.498+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:19:20.595+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:19:20.595+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:19:20.646+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.200 seconds
[2025-06-04T10:19:51.143+0000] {processor.py:161} INFO - Started process (PID=328) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:19:51.145+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:19:51.148+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:19:51.148+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:19:51.189+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:19:51.285+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:19:51.285+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:19:51.349+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.211 seconds
[2025-06-04T10:20:21.845+0000] {processor.py:161} INFO - Started process (PID=339) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:20:21.846+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:20:21.849+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:20:21.848+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:20:21.891+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:20:22.019+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:20:22.018+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:20:22.100+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.260 seconds
[2025-06-04T10:20:52.731+0000] {processor.py:161} INFO - Started process (PID=350) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:20:52.733+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:20:52.736+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:20:52.736+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:20:52.778+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:20:52.894+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:20:52.894+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:20:52.953+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.229 seconds
[2025-06-04T10:21:23.492+0000] {processor.py:161} INFO - Started process (PID=361) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:21:23.494+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:21:23.497+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:21:23.497+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:21:23.529+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:21:23.654+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:21:23.654+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:21:23.710+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.224 seconds
[2025-06-04T10:21:54.297+0000] {processor.py:161} INFO - Started process (PID=372) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:21:54.298+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:21:54.302+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:21:54.302+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:21:54.352+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:21:54.446+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:21:54.445+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:21:54.504+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.213 seconds
[2025-06-04T10:22:25.100+0000] {processor.py:161} INFO - Started process (PID=383) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:22:25.101+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:22:25.104+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:22:25.103+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:22:25.137+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:22:25.228+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:22:25.228+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:22:25.276+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.181 seconds
[2025-06-04T10:22:55.765+0000] {processor.py:161} INFO - Started process (PID=394) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:22:55.766+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:22:55.769+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:22:55.768+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:22:55.805+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:22:55.898+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:22:55.898+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:22:55.949+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.189 seconds
[2025-06-04T10:23:26.455+0000] {processor.py:161} INFO - Started process (PID=405) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:23:26.458+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:23:26.461+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:23:26.461+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:23:26.503+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:23:26.615+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:23:26.615+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:23:26.671+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.223 seconds
[2025-06-04T10:23:57.244+0000] {processor.py:161} INFO - Started process (PID=416) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:23:57.246+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:23:57.252+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:23:57.251+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:23:57.302+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:23:57.414+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:23:57.414+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:23:57.463+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.225 seconds
[2025-06-04T10:24:27.611+0000] {processor.py:161} INFO - Started process (PID=427) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:24:27.613+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:24:27.617+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:24:27.617+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:24:27.657+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:24:27.772+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:24:27.772+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:24:27.942+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.339 seconds
[2025-06-04T10:24:58.605+0000] {processor.py:161} INFO - Started process (PID=438) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:24:58.607+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:24:58.614+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:24:58.611+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:24:58.676+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:24:58.787+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:24:58.787+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:24:58.889+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.290 seconds
[2025-06-04T10:25:29.250+0000] {processor.py:161} INFO - Started process (PID=449) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:25:29.252+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:25:29.255+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:25:29.255+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:25:29.295+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:25:29.401+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:25:29.401+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:25:29.458+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.213 seconds
[2025-06-04T10:25:59.586+0000] {processor.py:161} INFO - Started process (PID=460) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:25:59.588+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:25:59.591+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:25:59.590+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:25:59.621+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:25:59.719+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:25:59.719+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:25:59.770+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.189 seconds
[2025-06-04T10:26:29.951+0000] {processor.py:161} INFO - Started process (PID=471) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:26:29.955+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:26:29.959+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:26:29.959+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:26:30.002+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:26:30.141+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:26:30.141+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:26:30.199+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.253 seconds
[2025-06-04T10:27:01.002+0000] {processor.py:161} INFO - Started process (PID=482) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:27:01.003+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:27:01.006+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:27:01.005+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:27:01.042+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:27:01.142+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:27:01.141+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:27:01.190+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.193 seconds
[2025-06-04T10:27:31.246+0000] {processor.py:161} INFO - Started process (PID=493) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:27:31.247+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:27:31.253+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:27:31.252+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:27:31.292+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:27:31.389+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:27:31.389+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:27:31.437+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.197 seconds
[2025-06-04T10:28:02.202+0000] {processor.py:161} INFO - Started process (PID=504) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:28:02.205+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:28:02.214+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:28:02.213+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:28:02.277+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:28:02.442+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:28:02.442+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:28:02.506+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.308 seconds
[2025-06-04T10:28:33.078+0000] {processor.py:161} INFO - Started process (PID=515) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:28:33.080+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:28:33.084+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:28:33.083+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:28:33.188+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:28:33.323+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:28:33.322+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:28:33.377+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.326 seconds
[2025-06-04T10:29:03.537+0000] {processor.py:161} INFO - Started process (PID=526) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:29:03.539+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:29:03.543+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:29:03.542+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:29:03.580+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:29:03.667+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:29:03.666+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:29:03.718+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.186 seconds
[2025-06-04T10:29:34.294+0000] {processor.py:161} INFO - Started process (PID=537) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:29:34.300+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:29:34.304+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:29:34.303+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:29:34.344+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:29:34.445+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:29:34.445+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:29:34.492+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.203 seconds
[2025-06-04T10:30:05.119+0000] {processor.py:161} INFO - Started process (PID=556) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:30:05.122+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:30:05.125+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:30:05.124+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:30:05.180+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:30:05.328+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:30:05.328+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:30:05.389+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.276 seconds
[2025-06-04T10:30:35.999+0000] {processor.py:161} INFO - Started process (PID=567) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:30:36.001+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:30:36.005+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:30:36.004+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:30:36.039+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:30:36.140+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:30:36.140+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:30:36.190+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.195 seconds
[2025-06-04T10:31:06.821+0000] {processor.py:161} INFO - Started process (PID=579) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:31:06.824+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:31:06.828+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:31:06.828+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:31:06.866+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:31:07.097+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:31:07.097+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:31:07.168+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.352 seconds
[2025-06-04T10:31:37.569+0000] {processor.py:161} INFO - Started process (PID=590) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:31:37.571+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:31:37.582+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:31:37.582+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:31:37.621+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:31:37.848+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:31:37.848+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:31:38.347+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.784 seconds
[2025-06-04T10:32:09.333+0000] {processor.py:161} INFO - Started process (PID=601) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:32:09.335+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:32:09.337+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:32:09.337+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:32:09.380+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:32:09.562+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:32:09.561+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:32:09.666+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.337 seconds
[2025-06-04T10:32:39.950+0000] {processor.py:161} INFO - Started process (PID=612) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:32:39.952+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:32:39.954+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:32:39.954+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:32:40.071+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:32:40.170+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:32:40.169+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:32:40.231+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.286 seconds
[2025-06-04T10:33:10.984+0000] {processor.py:161} INFO - Started process (PID=623) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:33:10.986+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:33:10.989+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:33:10.989+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:33:11.073+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:33:11.205+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:33:11.204+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:33:11.255+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.279 seconds
[2025-06-04T10:33:41.979+0000] {processor.py:161} INFO - Started process (PID=634) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:33:41.980+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:33:41.983+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:33:41.983+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:33:42.021+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:33:42.118+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:33:42.118+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:33:42.175+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.202 seconds
[2025-06-04T10:34:12.739+0000] {processor.py:161} INFO - Started process (PID=645) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:34:12.749+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:34:12.755+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:34:12.754+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:34:12.825+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:34:13.011+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:34:13.011+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:34:13.086+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.351 seconds
[2025-06-04T10:34:43.642+0000] {processor.py:161} INFO - Started process (PID=656) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:34:43.643+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:34:43.646+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:34:43.646+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:34:43.686+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:34:43.785+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:34:43.785+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:34:43.848+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.211 seconds
[2025-06-04T10:35:14.383+0000] {processor.py:161} INFO - Started process (PID=667) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:35:14.387+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:35:14.391+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:35:14.391+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:35:14.429+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:35:14.528+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:35:14.528+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:35:14.577+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.198 seconds
[2025-06-04T10:35:45.141+0000] {processor.py:161} INFO - Started process (PID=678) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:35:45.143+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:35:45.146+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:35:45.145+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:35:45.183+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:35:45.274+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:35:45.274+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:35:45.324+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.189 seconds
[2025-06-04T10:36:16.107+0000] {processor.py:161} INFO - Started process (PID=689) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:36:16.110+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:36:16.113+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:36:16.113+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:36:16.152+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:36:16.262+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:36:16.261+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:36:16.317+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.217 seconds
[2025-06-04T10:36:46.498+0000] {processor.py:161} INFO - Started process (PID=701) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:36:46.500+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:36:46.502+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:36:46.502+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:36:46.545+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:36:46.643+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:36:46.643+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:36:46.695+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.202 seconds
[2025-06-04T10:37:17.227+0000] {processor.py:161} INFO - Started process (PID=711) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:37:17.231+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:37:17.234+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:37:17.234+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:37:17.459+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:37:17.677+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:37:17.677+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:37:18.001+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.782 seconds
[2025-06-04T10:37:48.182+0000] {processor.py:161} INFO - Started process (PID=723) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:37:48.185+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:37:48.188+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:37:48.187+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:37:48.227+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:37:48.332+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:37:48.332+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:37:48.395+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.218 seconds
[2025-06-04T10:38:18.908+0000] {processor.py:161} INFO - Started process (PID=733) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:38:18.910+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:38:18.914+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:38:18.913+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:38:18.969+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:38:19.066+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:38:19.066+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:38:19.112+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.209 seconds
[2025-06-04T10:38:49.650+0000] {processor.py:161} INFO - Started process (PID=744) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:38:49.651+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:38:49.655+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:38:49.654+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:38:49.690+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:38:49.780+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:38:49.780+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:38:49.837+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.192 seconds
[2025-06-04T10:39:20.415+0000] {processor.py:161} INFO - Started process (PID=755) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:39:20.417+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:39:20.422+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:39:20.422+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:39:20.456+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:39:20.611+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:39:20.611+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:39:20.701+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.292 seconds
[2025-06-04T10:39:50.797+0000] {processor.py:161} INFO - Started process (PID=766) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:39:50.798+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:39:50.801+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:39:50.800+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:39:50.847+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:39:51.036+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:39:51.034+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:39:51.092+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.300 seconds
[2025-06-04T10:40:21.813+0000] {processor.py:161} INFO - Started process (PID=777) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:40:21.816+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:40:21.819+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:40:21.818+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:40:21.857+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:40:21.954+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:40:21.954+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:40:22.001+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.193 seconds
[2025-06-04T10:40:52.514+0000] {processor.py:161} INFO - Started process (PID=788) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:40:52.516+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:40:52.522+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:40:52.521+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:40:52.572+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:40:52.666+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:40:52.666+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:40:52.740+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.230 seconds
[2025-06-04T10:41:23.237+0000] {processor.py:161} INFO - Started process (PID=799) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:41:23.238+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:41:23.241+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:41:23.241+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:41:23.273+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:41:23.369+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:41:23.369+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:41:23.416+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.183 seconds
[2025-06-04T10:41:53.539+0000] {processor.py:161} INFO - Started process (PID=810) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:41:53.548+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:41:53.558+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:41:53.557+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:41:53.619+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:41:53.727+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:41:53.727+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:41:53.778+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.250 seconds
[2025-06-04T10:42:24.453+0000] {processor.py:161} INFO - Started process (PID=821) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:42:24.455+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:42:24.458+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:42:24.457+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:42:24.497+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:42:24.601+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:42:24.601+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:42:24.648+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.198 seconds
[2025-06-04T10:42:55.190+0000] {processor.py:161} INFO - Started process (PID=832) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:42:55.193+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:42:55.197+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:42:55.197+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:42:55.233+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:42:55.334+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:42:55.334+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:42:55.381+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.196 seconds
[2025-06-04T10:43:25.905+0000] {processor.py:161} INFO - Started process (PID=843) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:43:25.910+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:43:25.915+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:43:25.914+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:43:25.971+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:43:26.115+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:43:26.115+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:43:26.169+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.279 seconds
[2025-06-04T10:43:56.756+0000] {processor.py:161} INFO - Started process (PID=854) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:43:56.759+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:43:56.762+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:43:56.762+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:43:56.805+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:43:56.911+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:43:56.911+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:43:56.964+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.213 seconds
[2025-06-04T10:44:27.498+0000] {processor.py:161} INFO - Started process (PID=865) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:44:27.499+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:44:27.503+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:44:27.502+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:44:27.539+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:44:27.643+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:44:27.643+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:44:27.695+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.204 seconds
[2025-06-04T10:44:58.247+0000] {processor.py:161} INFO - Started process (PID=876) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:44:58.249+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:44:58.252+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:44:58.251+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:44:58.288+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:44:58.391+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:44:58.391+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:44:58.445+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.202 seconds
[2025-06-04T10:45:29.006+0000] {processor.py:161} INFO - Started process (PID=887) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:45:29.008+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:45:29.012+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:45:29.011+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:45:29.052+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:45:29.229+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:45:29.229+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:45:29.337+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.338 seconds
[2025-06-04T10:45:59.524+0000] {processor.py:161} INFO - Started process (PID=898) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:45:59.526+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:45:59.529+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:45:59.529+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:45:59.561+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:45:59.654+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:45:59.653+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:45:59.701+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.181 seconds
[2025-06-04T10:46:29.872+0000] {processor.py:161} INFO - Started process (PID=909) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:46:29.874+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:46:29.876+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:46:29.876+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:46:29.909+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:46:30.012+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:46:30.012+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:46:30.057+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.191 seconds
[2025-06-04T10:47:00.144+0000] {processor.py:161} INFO - Started process (PID=920) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:47:00.146+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:47:00.148+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:47:00.148+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:47:00.182+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:47:00.381+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:47:00.381+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:47:00.462+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.325 seconds
[2025-06-04T10:47:30.680+0000] {processor.py:161} INFO - Started process (PID=931) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:47:30.682+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:47:30.684+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:47:30.684+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:47:30.716+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:47:30.803+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:47:30.802+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:47:30.847+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.171 seconds
[2025-06-04T10:48:01.186+0000] {processor.py:161} INFO - Started process (PID=942) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:48:01.188+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:48:01.193+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:48:01.193+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:48:01.232+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:48:01.343+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:48:01.343+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:48:01.391+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.210 seconds
[2025-06-04T10:48:31.854+0000] {processor.py:161} INFO - Started process (PID=953) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:48:31.873+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:48:31.880+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:48:31.880+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:48:31.932+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:48:32.028+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:48:32.028+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:48:32.083+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.233 seconds
[2025-06-04T10:49:02.808+0000] {processor.py:161} INFO - Started process (PID=964) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:49:02.809+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:49:02.813+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:49:02.812+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:49:02.851+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:49:02.945+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:49:02.944+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:49:02.990+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.202 seconds
[2025-06-04T10:49:33.618+0000] {processor.py:161} INFO - Started process (PID=976) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:49:33.619+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:49:33.623+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:49:33.622+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:49:33.658+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:49:33.768+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:49:33.768+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:49:33.821+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.210 seconds
[2025-06-04T10:50:04.253+0000] {processor.py:161} INFO - Started process (PID=987) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:50:04.256+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:50:04.259+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:50:04.259+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:50:04.296+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:50:04.400+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:50:04.399+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:50:04.446+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.197 seconds
[2025-06-04T10:50:35.016+0000] {processor.py:161} INFO - Started process (PID=998) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:50:35.018+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:50:35.023+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:50:35.022+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:50:35.059+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:50:35.160+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:50:35.159+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:50:35.207+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.197 seconds
[2025-06-04T10:51:05.763+0000] {processor.py:161} INFO - Started process (PID=1009) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:51:05.765+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:51:05.772+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:51:05.768+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:51:05.811+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:51:05.919+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:51:05.918+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:51:05.964+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.207 seconds
[2025-06-04T10:51:36.612+0000] {processor.py:161} INFO - Started process (PID=1020) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:51:36.614+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:51:36.617+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:51:36.617+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:51:36.654+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:51:36.746+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:51:36.746+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:51:36.792+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.185 seconds
[2025-06-04T10:52:07.538+0000] {processor.py:161} INFO - Started process (PID=1037) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:52:07.540+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:52:07.544+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:52:07.544+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:52:07.585+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:52:07.691+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:52:07.691+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:52:07.750+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.218 seconds
[2025-06-04T10:52:38.231+0000] {processor.py:161} INFO - Started process (PID=1048) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:52:38.233+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:52:38.235+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:52:38.235+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:52:38.274+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:52:38.364+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:52:38.364+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:52:38.409+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.183 seconds
[2025-06-04T10:53:08.920+0000] {processor.py:161} INFO - Started process (PID=1059) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:53:08.921+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:53:08.924+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:53:08.923+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:53:08.958+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:53:09.052+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:53:09.052+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:53:09.101+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.185 seconds
[2025-06-04T10:53:39.762+0000] {processor.py:161} INFO - Started process (PID=1070) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:53:39.768+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:53:39.771+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:53:39.771+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:53:39.839+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:53:39.950+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:53:39.949+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:53:40.014+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.307 seconds
[2025-06-04T10:54:10.714+0000] {processor.py:161} INFO - Started process (PID=1081) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:54:10.716+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:54:10.723+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:54:10.723+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:54:10.763+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:54:10.867+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:54:10.867+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:54:10.912+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.204 seconds
[2025-06-04T10:54:41.491+0000] {processor.py:161} INFO - Started process (PID=1092) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:54:41.492+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:54:41.497+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:54:41.496+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:54:41.535+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:54:41.717+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:54:41.716+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:54:41.772+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.286 seconds
[2025-06-04T10:55:12.396+0000] {processor.py:161} INFO - Started process (PID=1103) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:55:12.401+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:55:12.404+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:55:12.403+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:55:12.444+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:55:12.562+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:55:12.562+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:55:12.616+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.224 seconds
[2025-06-04T10:55:42.728+0000] {processor.py:161} INFO - Started process (PID=1114) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:55:42.730+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:55:42.732+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:55:42.732+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:55:42.769+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:55:42.871+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:55:42.871+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:55:42.913+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.193 seconds
[2025-06-04T10:56:13.366+0000] {processor.py:161} INFO - Started process (PID=1125) to work on /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:56:13.368+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_only_dag.py for tasks to queue
[2025-06-04T10:56:13.370+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:56:13.370+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:56:13.434+0000] {processor.py:840} INFO - DAG(s) 'dbt_only_pipeline' retrieved from /opt/airflow/dags/dbt_only_dag.py
[2025-06-04T10:56:13.642+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:56:13.641+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:56:13.713+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_only_dag.py took 0.353 seconds
