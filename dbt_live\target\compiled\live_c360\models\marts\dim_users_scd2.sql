

with 

-- Get the latest user data from staging
source_data as (
    select 
        user_id,
        firstname,
        lastname,
        email_hash,
        address,
        acquisition_channel as canal,
        country,
        gender,
        age_group,
        creation_date,
        last_activity_date,
        is_churned as churn,
        batch_id,
        current_timestamp() as dbt_updated_at,
        -- Track when this record was processed
        current_timestamp() as processed_at
    from MYDB.LIVE_DATA.stg_users
    where user_id is not null
    qualify row_number() over (partition by user_id order by generated_at desc) = 1
    
    
    -- Only process users that are new or have changed since the last run
    and user_id in (
        select user_id
        from MYDB.LIVE_DATA.stg_users
        where generated_at > (
            select coalesce(max(dbt_valid_from), '1900-01-01'::timestamp)
            from MYDB.LIVE_DATA.dim_users_scd2
            where is_current_version = true
        )
    )
    
),

-- Get the current version of each user from the dimension
existing_dimension as (
    
    select
        user_sk,
        user_id,
        firstname,
        lastname,
        email,
        address,
        acquisition_channel,
        country,
        gender,
        age_group,
        creation_date,
        last_activity_date,
        churn,
        batch_id,
        dbt_valid_from,
        dbt_valid_to,
        is_current_version,
        dbt_change_type,
        current_timestamp() as processed_at  -- Add processed_at for consistency
    from MYDB.LIVE_DATA.dim_users_scd2
    where is_current_version = true
    
),

-- Identify new and changed records
changes_to_apply as (
    select
        s.*,
        case
            when e.user_sk is null then 'insert'
            when e.firstname != s.firstname 
              or e.lastname != s.lastname
              or e.email != s.email_hash
              or e.address != s.address
              or e.acquisition_channel != s.canal
              or e.country != s.country
              or e.gender != s.gender
              or e.age_group != s.age_group
              or e.churn != s.churn then 'update'
            else 'no_change'
        end as change_type
    from source_data s
    left join existing_dimension e 
        on s.user_id = e.user_id
)

-- For initial load, just insert all records


-- For incremental loads, handle inserts and updates
select * from (
    -- New or updated records
    select
        md5(cast(coalesce(cast(user_id as TEXT), '_dbt_utils_surrogate_key_null_') || '-' || coalesce(cast(dbt_updated_at as TEXT), '_dbt_utils_surrogate_key_null_') as TEXT)) as user_sk,
        user_id,
        firstname,
        lastname,
        email_hash as email,
        address,
        canal as acquisition_channel,
        country,
        gender,
        age_group,
        creation_date,
        last_activity_date,
        churn,
        batch_id,
        dbt_updated_at as dbt_valid_from,
        cast(null as timestamp_ntz) as dbt_valid_to,
        true as is_current_version,
        change_type as dbt_change_type,
        processed_at
    from changes_to_apply
    where change_type in ('insert', 'update')
    
    union all
    
    -- Expire old versions of updated records
    select
        e.user_sk,
        e.user_id,
        e.firstname,
        e.lastname,
        e.email,
        e.address,
        e.acquisition_channel,
        e.country,
        e.gender,
        e.age_group,
        e.creation_date,
        e.last_activity_date,
        e.churn,
        e.batch_id,
        e.dbt_valid_from,
        current_timestamp() as dbt_valid_to,
        false as is_current_version,
        'expire' as dbt_change_type,
        e.processed_at
    from existing_dimension e
    inner join changes_to_apply c 
        on e.user_id = c.user_id
        and c.change_type = 'update'
)

