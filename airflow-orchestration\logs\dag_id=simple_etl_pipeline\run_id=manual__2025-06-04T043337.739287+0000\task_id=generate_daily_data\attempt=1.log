[2025-06-04T04:33:39.339+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=non-requeueable deps ti=<TaskInstance: simple_etl_pipeline.generate_daily_data manual__2025-06-04T04:33:37.739287+00:00 [queued]>
[2025-06-04T04:33:39.350+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=requeueable deps ti=<TaskInstance: simple_etl_pipeline.generate_daily_data manual__2025-06-04T04:33:37.739287+00:00 [queued]>
[2025-06-04T04:33:39.350+0000] {taskinstance.py:2170} INFO - Starting attempt 1 of 2
[2025-06-04T04:33:39.367+0000] {taskinstance.py:2191} INFO - Executing <Task(PythonOperator): generate_daily_data> on 2025-06-04 04:33:37.739287+00:00
[2025-06-04T04:33:39.374+0000] {standard_task_runner.py:60} INFO - Started process 90 to run task
[2025-06-04T04:33:39.378+0000] {standard_task_runner.py:87} INFO - Running: ['***', 'tasks', 'run', 'simple_etl_pipeline', 'generate_daily_data', 'manual__2025-06-04T04:33:37.739287+00:00', '--job-id', '73', '--raw', '--subdir', 'DAGS_FOLDER/simple_etl_dag.py', '--cfg-path', '/tmp/tmpvl6t0vp_']
[2025-06-04T04:33:39.380+0000] {standard_task_runner.py:88} INFO - Job 73: Subtask generate_daily_data
[2025-06-04T04:33:39.432+0000] {task_command.py:423} INFO - Running <TaskInstance: simple_etl_pipeline.generate_daily_data manual__2025-06-04T04:33:37.739287+00:00 [running]> on host 8fb7c8dc5097
[2025-06-04T04:33:39.507+0000] {taskinstance.py:2480} INFO - Exporting env vars: AIRFLOW_CTX_DAG_OWNER='data-team' AIRFLOW_CTX_DAG_ID='simple_etl_pipeline' AIRFLOW_CTX_TASK_ID='generate_daily_data' AIRFLOW_CTX_EXECUTION_DATE='2025-06-04T04:33:37.739287+00:00' AIRFLOW_CTX_TRY_NUMBER='1' AIRFLOW_CTX_DAG_RUN_ID='manual__2025-06-04T04:33:37.739287+00:00'
[2025-06-04T04:33:39.508+0000] {simple_etl_dag.py:48} INFO - Running script: /opt/***/workspace/simple_data_generator.py
[2025-06-04T04:33:39.600+0000] {simple_etl_dag.py:67} INFO - Script completed successfully: 🚀 Starting simple data generation...
✅ Generated 100 users and 500 transactions
📁 Files created:
   - data/users_2025-06-04.json
   - data/transactions_2025-06-04.json
🎉 Data generation completed successfully!
📊 Summary: {'users_count': 100, 'transactions_count': 500, 'files_created': ['data/users_2025-06-04.json', 'data/transactions_2025-06-04.json']}

[2025-06-04T04:33:39.601+0000] {python.py:201} INFO - Done. Returned value was: 🚀 Starting simple data generation...
✅ Generated 100 users and 500 transactions
📁 Files created:
   - data/users_2025-06-04.json
   - data/transactions_2025-06-04.json
🎉 Data generation completed successfully!
📊 Summary: {'users_count': 100, 'transactions_count': 500, 'files_created': ['data/users_2025-06-04.json', 'data/transactions_2025-06-04.json']}

[2025-06-04T04:33:39.626+0000] {taskinstance.py:1138} INFO - Marking task as SUCCESS. dag_id=simple_etl_pipeline, task_id=generate_daily_data, execution_date=20250604T043337, start_date=20250604T043339, end_date=20250604T043339
[2025-06-04T04:33:39.673+0000] {local_task_job_runner.py:234} INFO - Task exited with return code 0
[2025-06-04T04:33:39.698+0000] {taskinstance.py:3280} INFO - 1 downstream tasks scheduled from follow-on schedule check
