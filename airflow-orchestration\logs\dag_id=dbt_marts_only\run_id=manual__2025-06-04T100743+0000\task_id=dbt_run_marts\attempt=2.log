[2025-06-04T10:12:28.067+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=non-requeueable deps ti=<TaskInstance: dbt_marts_only.dbt_run_marts manual__2025-06-04T10:07:43+00:00 [queued]>
[2025-06-04T10:12:28.077+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=requeueable deps ti=<TaskInstance: dbt_marts_only.dbt_run_marts manual__2025-06-04T10:07:43+00:00 [queued]>
[2025-06-04T10:12:28.078+0000] {taskinstance.py:2170} INFO - Starting attempt 2 of 2
[2025-06-04T10:12:28.090+0000] {taskinstance.py:2191} INFO - Executing <Task(PythonOperator): dbt_run_marts> on 2025-06-04 10:07:43+00:00
[2025-06-04T10:12:28.099+0000] {standard_task_runner.py:60} INFO - Started process 187 to run task
[2025-06-04T10:12:28.103+0000] {standard_task_runner.py:87} INFO - Running: ['***', 'tasks', 'run', 'dbt_marts_only', 'dbt_run_marts', 'manual__2025-06-04T10:07:43+00:00', '--job-id', '120', '--raw', '--subdir', 'DAGS_FOLDER/dbt_marts_only_dag.py', '--cfg-path', '/tmp/tmpxamb0qxa']
[2025-06-04T10:12:28.105+0000] {standard_task_runner.py:88} INFO - Job 120: Subtask dbt_run_marts
[2025-06-04T10:12:28.172+0000] {task_command.py:423} INFO - Running <TaskInstance: dbt_marts_only.dbt_run_marts manual__2025-06-04T10:07:43+00:00 [running]> on host bad0c90122e4
[2025-06-04T10:12:28.261+0000] {taskinstance.py:2480} INFO - Exporting env vars: AIRFLOW_CTX_DAG_OWNER='data-team' AIRFLOW_CTX_DAG_ID='dbt_marts_only' AIRFLOW_CTX_TASK_ID='dbt_run_marts' AIRFLOW_CTX_EXECUTION_DATE='2025-06-04T10:07:43+00:00' AIRFLOW_CTX_TRY_NUMBER='2' AIRFLOW_CTX_DAG_RUN_ID='manual__2025-06-04T10:07:43+00:00'
[2025-06-04T10:12:28.263+0000] {dbt_marts_only_dag.py:129} INFO - 🏪 Running dbt marts models...
[2025-06-04T10:12:28.263+0000] {dbt_marts_only_dag.py:62} INFO - 🚀 Running dbt run
[2025-06-04T10:12:28.265+0000] {dbt_marts_only_dag.py:82} INFO - 📋 Executing: dbt run --models marts
[2025-06-04T10:12:37.627+0000] {dbt_marts_only_dag.py:95} INFO - 📄 stdout: [0m10:12:30  Running with dbt=1.9.6
[0m10:12:31  Registered adapter: snowflake=1.9.4
[0m10:12:32  [[33mWARNING[0m]: Configuration paths exist in your dbt_project.yml file which do not apply to any resources.
There are 3 unused configuration paths:
- models.live_c360.incremental
- seeds.live_c360
- snapshots.live_c360
[0m10:12:32  Found 12 models, 21 data tests, 3 sources, 590 macros
[0m10:12:33  
[0m10:12:33  Concurrency: 4 threads (target='live')
[0m10:12:33  
[0m10:12:34  1 of 4 START sql table model LIVE_DATA.dim_users ............................... [RUN]
[0m10:12:34  2 of 4 START sql incremental model LIVE_DATA.dim_users_scd2 .................... [RUN]
[0m10:12:34  3 of 4 START sql table model LIVE_DATA.fact_orders ............................. [RUN]
[0m10:12:34  4 of 4 START sql view model LIVE_DATA_ANALYTICS.mv_fact_orders ................. [RUN]
[0m10:12:35  2 of 4 ERROR creating sql incremental model LIVE_DATA.dim_users_scd2 ........... [[31mERROR[0m in 0.53s]
[0m10:12:35  1 of 4 ERROR creating sql table model LIVE_DATA.dim_users ...................... [[31mERROR[0m in 0.53s]
[0m10:12:35  3 of 4 ERROR creating sql table model LIVE_DATA.fact_orders .................... [[31mERROR[0m in 0.61s]
[0m10:12:35  4 of 4 ERROR creating sql view model LIVE_DATA_ANALYTICS.mv_fact_orders ........ [[31mERROR[0m in 0.88s]
[0m10:12:35  
[0m10:12:35  Finished running 1 incremental model, 2 table models, 1 view model in 0 hours 0 minutes and 2.98 seconds (2.98s).
[0m10:12:36  
[0m10:12:36  [31mCompleted with 4 errors, 0 partial successes, and 0 warnings:[0m
[0m10:12:36  
[0m10:12:36    Database Error in model dim_users_scd2 (models/marts/dim_users_scd2.sql)
  091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
[0m10:12:36  
[0m10:12:36    Database Error in model dim_users (models/marts/dim_users.sql)
  091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
  compiled code at target/run/live_c360/models/marts/dim_users.sql
[0m10:12:36  
[0m10:12:36    Database Error in model fact_orders (models/marts/fact_orders.sql)
  091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/marts/fact_orders.sql
[0m10:12:36  
[0m10:12:36    Database Error in model mv_fact_orders (models/marts/mv_fact_orders.sql)
  091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/marts/mv_fact_orders.sql
[0m10:12:36  
[0m10:12:36  Done. PASS=0 WARN=0 ERROR=4 SKIP=0 TOTAL=4

[2025-06-04T10:12:37.627+0000] {dbt_marts_only_dag.py:110} ERROR - ❌ dbt run failed with return code 1
[2025-06-04T10:12:37.628+0000] {dbt_marts_only_dag.py:111} ERROR - ❌ Error output: 
[2025-06-04T10:12:37.630+0000] {dbt_marts_only_dag.py:120} ERROR - ❌ dbt run failed: dbt run failed with return code 1: 
[2025-06-04T10:12:37.631+0000] {taskinstance.py:2698} ERROR - Task failed with exception
Traceback (most recent call last):
  File "/opt/airflow/dags/dbt_marts_only_dag.py", line 112, in run_dbt_command
    raise Exception(f"{error_msg}: {result.stderr}")
Exception: dbt run failed with return code 1: 

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/models/taskinstance.py", line 433, in _execute_task
    result = execute_callable(context=context, **execute_callable_kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/operators/python.py", line 199, in execute
    return_value = self.execute_callable()
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/operators/python.py", line 216, in execute_callable
    return self.python_callable(*self.op_args, **self.op_kwargs)
  File "/opt/airflow/dags/dbt_marts_only_dag.py", line 130, in dbt_run_marts
    return run_dbt_command('run', models='marts', **context)
  File "/opt/airflow/dags/dbt_marts_only_dag.py", line 121, in run_dbt_command
    raise Exception(f"dbt {command_type} failed: {e}")
Exception: dbt run failed: dbt run failed with return code 1: 
[2025-06-04T10:12:37.645+0000] {taskinstance.py:1138} INFO - Marking task as FAILED. dag_id=dbt_marts_only, task_id=dbt_run_marts, execution_date=20250604T100743, start_date=20250604T101228, end_date=20250604T101237
[2025-06-04T10:12:37.659+0000] {standard_task_runner.py:107} ERROR - Failed to execute job 120 for task dbt_run_marts (dbt run failed: dbt run failed with return code 1: ; 187)
[2025-06-04T10:12:37.701+0000] {local_task_job_runner.py:234} INFO - Task exited with return code 1
[2025-06-04T10:12:37.725+0000] {taskinstance.py:3280} INFO - 0 downstream tasks scheduled from follow-on schedule check
