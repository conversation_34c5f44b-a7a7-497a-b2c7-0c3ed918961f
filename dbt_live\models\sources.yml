version: 2

sources:
  - name: live_external
    description: "External tables reading live data from S3"
    schema: LIVE_DATA
    tables:
      - name: ext_live_users
        description: "Live user data from S3 external table"
        external:
          location: "@S3_LIVE_USERS_STAGE"
          file_format: "CSV_LIVE_FORMAT"
          auto_refresh: true
        columns:
          - name: id
            description: "User ID"
            tests:
              - not_null
              - unique
          - name: firstname
            description: "User first name"
          - name: lastname
            description: "User last name"
          - name: email
            description: "User email address"
            tests:
              - not_null
          - name: address
            description: "User address"
          - name: canal
            description: "Acquisition channel"
          - name: country
            description: "User country"
          - name: creation_date
            description: "Account creation date"
          - name: last_activity_date
            description: "Last activity date"
          - name: gender
            description: "User gender (0/1)"
          - name: age_group
            description: "Age group category"
          - name: churn
            description: "Churn flag"
          - name: batch_id
            description: "Data generation batch ID"
          - name: generated_at
            description: "Timestamp when data was generated"
            tests:
              - not_null

      - name: ext_live_orders
        description: "Live order data from S3 external table"
        external:
          location: "@S3_LIVE_ORDERS_STAGE"
          file_format: "CSV_LIVE_FORMAT"
          auto_refresh: true
        columns:
          - name: id
            description: "Order ID"
            tests:
              - not_null
              - unique
          - name: user_id
            description: "User ID"
            tests:
              - not_null
              - relationships:
                  to: source('live_external', 'ext_live_users')
                  field: id
          - name: transaction_date
            description: "Transaction date"
          - name: item_count
            description: "Number of items"
            tests:
              - not_null
              - dbt_utils.accepted_range:
                  min_value: 1
                  max_value: 100
          - name: amount
            description: "Order amount"
            tests:
              - not_null
              - dbt_utils.accepted_range:
                  min_value: 0
                  inclusive: false
          - name: batch_id
            description: "Data generation batch ID"
          - name: generated_at
            description: "Timestamp when data was generated"
            tests:
              - not_null

      - name: ext_live_events
        description: "Live event data from S3 external table"
        external:
          location: "@S3_LIVE_EVENTS_STAGE"
          file_format: "CSV_LIVE_FORMAT"
          auto_refresh: true
        columns:
          - name: user_id
            description: "User ID"
            tests:
              - not_null
              - relationships:
                  to: source('live_external', 'ext_live_users')
                  field: id
          - name: event_id
            description: "Event ID"
            tests:
              - not_null
              - unique
          - name: platform
            description: "Platform (ios, android, web)"
            tests:
              - accepted_values:
                  values: ['ios', 'android', 'web', null]
          - name: date
            description: "Event date"
          - name: action
            description: "Action type"
            tests:
              - accepted_values:
                  values: ['view', 'click', 'log', 'purchase']
          - name: session_id
            description: "Session ID"
            tests:
              - not_null
          - name: url
            description: "URL visited"
          - name: batch_id
            description: "Data generation batch ID"
          - name: generated_at
            description: "Timestamp when data was generated"
            tests:
              - not_null

# Freshness tests for live data
  - name: live_data_freshness
    description: "Data freshness monitoring for live pipeline"
    freshness:
      warn_after: {count: 30, period: minute}
      error_after: {count: 60, period: minute}
    loaded_at_field: generated_at
