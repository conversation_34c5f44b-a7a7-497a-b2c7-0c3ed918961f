[2025-06-04T03:43:41.939+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=non-requeueable deps ti=<TaskInstance: simple_etl_pipeline.run_dbt_models manual__2025-06-04T03:38:22.307860+00:00 [queued]>
[2025-06-04T03:43:41.950+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=requeueable deps ti=<TaskInstance: simple_etl_pipeline.run_dbt_models manual__2025-06-04T03:38:22.307860+00:00 [queued]>
[2025-06-04T03:43:41.951+0000] {taskinstance.py:2170} INFO - Starting attempt 2 of 2
[2025-06-04T03:43:41.964+0000] {taskinstance.py:2191} INFO - Executing <Task(PythonOperator): run_dbt_models> on 2025-06-04 03:38:22.307860+00:00
[2025-06-04T03:43:41.972+0000] {standard_task_runner.py:60} INFO - Started process 1011 to run task
[2025-06-04T03:43:41.976+0000] {standard_task_runner.py:87} INFO - Running: ['***', 'tasks', 'run', 'simple_etl_pipeline', 'run_dbt_models', 'manual__2025-06-04T03:38:22.307860+00:00', '--job-id', '63', '--raw', '--subdir', 'DAGS_FOLDER/simple_etl_dag.py', '--cfg-path', '/tmp/tmpjkj16qg4']
[2025-06-04T03:43:41.978+0000] {standard_task_runner.py:88} INFO - Job 63: Subtask run_dbt_models
[2025-06-04T03:43:42.035+0000] {task_command.py:423} INFO - Running <TaskInstance: simple_etl_pipeline.run_dbt_models manual__2025-06-04T03:38:22.307860+00:00 [running]> on host 9fdd53a423c8
[2025-06-04T03:43:42.117+0000] {taskinstance.py:2480} INFO - Exporting env vars: AIRFLOW_CTX_DAG_OWNER='data-team' AIRFLOW_CTX_DAG_ID='simple_etl_pipeline' AIRFLOW_CTX_TASK_ID='run_dbt_models' AIRFLOW_CTX_EXECUTION_DATE='2025-06-04T03:38:22.307860+00:00' AIRFLOW_CTX_TRY_NUMBER='2' AIRFLOW_CTX_DAG_RUN_ID='manual__2025-06-04T03:38:22.307860+00:00'
[2025-06-04T03:43:42.118+0000] {simple_etl_dag.py:78} INFO - Running dbt command: dbt run
[2025-06-04T03:43:42.121+0000] {taskinstance.py:2698} ERROR - Task failed with exception
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/models/taskinstance.py", line 433, in _execute_task
    result = execute_callable(context=context, **execute_callable_kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/operators/python.py", line 199, in execute
    return_value = self.execute_callable()
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/operators/python.py", line 216, in execute_callable
    return self.python_callable(*self.op_args, **self.op_kwargs)
  File "/opt/airflow/dags/simple_etl_dag.py", line 142, in run_dbt_models
    return run_dbt_command('dbt run', **context)
  File "/opt/airflow/dags/simple_etl_dag.py", line 89, in run_dbt_command
    result = subprocess.run(
  File "/usr/local/lib/python3.10/subprocess.py", line 503, in run
    with Popen(*popenargs, **kwargs) as process:
  File "/usr/local/lib/python3.10/subprocess.py", line 971, in __init__
    self._execute_child(args, executable, preexec_fn, close_fds,
  File "/usr/local/lib/python3.10/subprocess.py", line 1863, in _execute_child
    raise child_exception_type(errno_num, err_msg, err_filename)
PermissionError: [Errno 13] Permission denied: 'dbt'
[2025-06-04T03:43:42.134+0000] {taskinstance.py:1138} INFO - Marking task as FAILED. dag_id=simple_etl_pipeline, task_id=run_dbt_models, execution_date=20250604T033822, start_date=20250604T034341, end_date=20250604T034342
[2025-06-04T03:43:42.149+0000] {standard_task_runner.py:107} ERROR - Failed to execute job 63 for task run_dbt_models ([Errno 13] Permission denied: 'dbt'; 1011)
[2025-06-04T03:43:42.188+0000] {local_task_job_runner.py:234} INFO - Task exited with return code 1
[2025-06-04T03:43:42.212+0000] {taskinstance.py:3280} INFO - 0 downstream tasks scheduled from follow-on schedule check
