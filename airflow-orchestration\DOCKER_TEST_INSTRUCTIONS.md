# 🐳 Docker Testing Instructions

## Step 1: Start Docker Desktop Manually

1. **Open Start Menu** and search for "Docker Desktop"
2. **Click on Docker Desktop** to start it
3. **Wait 2-5 minutes** for <PERSON><PERSON> to fully initialize
4. **Look for the Docker whale icon** in your system tray (bottom right)
5. **Click the whale icon** and ensure it says "Docker Desktop is running"

## Step 2: Test Docker from Command Line

Open **PowerShell as Administrator** and run these commands:

```powershell
# Add Docker to PATH
$env:PATH += ";C:\Program Files\Docker\Docker\resources\bin"

# Test Docker version
docker --version

# Test Docker info
docker info

# Test with hello-world container
docker run hello-world
```

## Step 3: If Docker is Working, Test Airflow Setup

Navigate to the airflow-orchestration folder and run:

```powershell
# Navigate to airflow folder
cd "G:\github\S3 dbt-snowflake c360\experiment2\airflow-orchestration"

# Add Docker to PATH
$env:PATH += ";C:\Program Files\Docker\Docker\resources\bin"

# Initialize Airflow (first time only)
docker compose up airflow-init

# Start Airflow services
docker compose up -d

# Check service status
docker compose ps
```

## Step 4: Access Airflow Web UI

1. **Open browser** and go to: http://localhost:8080
2. **Login with:**
   - Username: `airflow`
   - Password: `airflow`

## Step 5: Stop Airflow (when done testing)

```powershell
# Stop all services
docker compose down

# Remove volumes (optional - only if you want to reset everything)
docker compose down -v
```

## Troubleshooting Common Issues

### Issue 1: "Docker command not found"
**Solution:** Add Docker to PATH:
```powershell
$env:PATH += ";C:\Program Files\Docker\Docker\resources\bin"
```

### Issue 2: "Cannot connect to Docker daemon"
**Solutions:**
1. Restart Docker Desktop
2. Run PowerShell as Administrator
3. Check Windows Subsystem for Linux (WSL) is enabled

### Issue 3: "Port 8080 already in use"
**Solution:** Change port in docker-compose.yml:
```yaml
ports:
  - "8081:8080"  # Change 8080 to 8081
```

### Issue 4: Airflow initialization fails
**Solutions:**
1. Delete logs folder: `Remove-Item -Recurse -Force logs`
2. Run init again: `docker compose up airflow-init`

## Expected Results

### Successful Docker Test:
```
Docker version 28.1.1, build 4eba377
Hello from Docker!
This message shows that your installation appears to be working correctly.
```

### Successful Airflow Startup:
```
✅ airflow-webserver-1    running
✅ airflow-scheduler-1    running  
✅ airflow-worker-1       running
✅ postgres-1             running
✅ redis-1                running
```

### Airflow Web UI:
- Should load at http://localhost:8080
- Login page should appear
- After login, you should see the Airflow dashboard

## Next Steps After Successful Test

1. **Verify DAGs are loaded** (should see etl_pipeline_dag in the UI)
2. **Test a simple DAG run**
3. **Configure Snowflake connections** in Airflow UI
4. **Set up monitoring alerts**

## Need Help?

If you encounter issues:
1. Check Docker Desktop logs (Settings > Troubleshoot > Get support)
2. Restart your computer (sometimes needed for Docker)
3. Ensure Windows features are enabled:
   - Hyper-V
   - Windows Subsystem for Linux (WSL)
   - Virtual Machine Platform
