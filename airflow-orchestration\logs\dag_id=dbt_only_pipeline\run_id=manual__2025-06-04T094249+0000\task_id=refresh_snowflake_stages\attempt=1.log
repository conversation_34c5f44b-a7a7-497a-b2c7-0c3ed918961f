[2025-06-04T09:59:56.244+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=non-requeueable deps ti=<TaskInstance: dbt_only_pipeline.refresh_snowflake_stages manual__2025-06-04T09:42:49+00:00 [queued]>
[2025-06-04T09:59:56.255+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=requeueable deps ti=<TaskInstance: dbt_only_pipeline.refresh_snowflake_stages manual__2025-06-04T09:42:49+00:00 [queued]>
[2025-06-04T09:59:56.256+0000] {taskinstance.py:2170} INFO - Starting attempt 1 of 3
[2025-06-04T09:59:56.270+0000] {taskinstance.py:2191} INFO - Executing <Task(PythonOperator): refresh_snowflake_stages> on 2025-06-04 09:42:49+00:00
[2025-06-04T09:59:56.277+0000] {standard_task_runner.py:60} INFO - Started process 4498 to run task
[2025-06-04T09:59:56.282+0000] {standard_task_runner.py:87} INFO - Running: ['***', 'tasks', 'run', 'dbt_only_pipeline', 'refresh_snowflake_stages', 'manual__2025-06-04T09:42:49+00:00', '--job-id', '113', '--raw', '--subdir', 'DAGS_FOLDER/dbt_only_dag.py', '--cfg-path', '/tmp/tmpxlpm3zct']
[2025-06-04T09:59:56.284+0000] {standard_task_runner.py:88} INFO - Job 113: Subtask refresh_snowflake_stages
[2025-06-04T09:59:56.350+0000] {task_command.py:423} INFO - Running <TaskInstance: dbt_only_pipeline.refresh_snowflake_stages manual__2025-06-04T09:42:49+00:00 [running]> on host 8fb7c8dc5097
[2025-06-04T09:59:56.439+0000] {taskinstance.py:2480} INFO - Exporting env vars: AIRFLOW_CTX_DAG_OWNER='data-team' AIRFLOW_CTX_DAG_ID='dbt_only_pipeline' AIRFLOW_CTX_TASK_ID='refresh_snowflake_stages' AIRFLOW_CTX_EXECUTION_DATE='2025-06-04T09:42:49+00:00' AIRFLOW_CTX_TRY_NUMBER='1' AIRFLOW_CTX_DAG_RUN_ID='manual__2025-06-04T09:42:49+00:00'
[2025-06-04T09:59:56.440+0000] {dbt_only_dag.py:145} INFO - 🔄 Refreshing Snowflake external stages...
[2025-06-04T09:59:57.690+0000] {dbt_only_dag.py:169} INFO - 🔗 Connecting to Snowflake: SVLFKJI-IX89869
[2025-06-04T09:59:57.691+0000] {connection.py:486} INFO - Snowflake Connector for Python Version: 3.15.0, Python Version: 3.10.13, Platform: Linux-********-microsoft-standard-WSL2-x86_64-with-glibc2.36
[2025-06-04T09:59:57.692+0000] {connection.py:1391} INFO - Connecting to GLOBAL Snowflake domain
[2025-06-04T10:00:02.890+0000] {dbt_only_dag.py:184} INFO - 🔄 Refreshing external table: EXT_LIVE_USERS
[2025-06-04T10:00:02.989+0000] {dbt_only_dag.py:207} ERROR - ❌ Failed to refresh EXT_LIVE_USERS: 091093 (55000): 01bccd38-3204-7fc5-0002-4ad6000631de: External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
[2025-06-04T10:00:02.990+0000] {dbt_only_dag.py:184} INFO - 🔄 Refreshing external table: EXT_LIVE_ORDERS
[2025-06-04T10:00:03.172+0000] {dbt_only_dag.py:207} ERROR - ❌ Failed to refresh EXT_LIVE_ORDERS: 091093 (55000): 01bccd38-3204-802b-0002-4ad600068102: External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
[2025-06-04T10:00:03.172+0000] {dbt_only_dag.py:184} INFO - 🔄 Refreshing external table: EXT_LIVE_EVENTS
[2025-06-04T10:00:03.320+0000] {dbt_only_dag.py:207} ERROR - ❌ Failed to refresh EXT_LIVE_EVENTS: 091093 (55000): 01bccd38-3204-800f-0002-4ad6000650ce: External table EXT_LIVE_EVENTS marked invalid. Stage S3_LIVE_EVENTS_STAGE dropped.
[2025-06-04T10:00:03.399+0000] {dbt_only_dag.py:220} ERROR - ❌ No external tables were successfully refreshed
[2025-06-04T10:00:03.400+0000] {dbt_only_dag.py:224} ERROR - ❌ Stage refresh failed: Stage refresh failed for all tables: {'EXT_LIVE_USERS': {'error': '091093 (55000): 01bccd38-3204-7fc5-0002-4ad6000631de: External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.'}, 'EXT_LIVE_ORDERS': {'error': '091093 (55000): 01bccd38-3204-802b-0002-4ad600068102: External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.'}, 'EXT_LIVE_EVENTS': {'error': '091093 (55000): 01bccd38-3204-800f-0002-4ad6000650ce: External table EXT_LIVE_EVENTS marked invalid. Stage S3_LIVE_EVENTS_STAGE dropped.'}}
[2025-06-04T10:00:03.401+0000] {taskinstance.py:2698} ERROR - Task failed with exception
Traceback (most recent call last):
  File "/opt/airflow/dags/dbt_only_dag.py", line 221, in refresh_snowflake_stages
    raise Exception(f"Stage refresh failed for all tables: {refresh_results}")
Exception: Stage refresh failed for all tables: {'EXT_LIVE_USERS': {'error': '091093 (55000): 01bccd38-3204-7fc5-0002-4ad6000631de: External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.'}, 'EXT_LIVE_ORDERS': {'error': '091093 (55000): 01bccd38-3204-802b-0002-4ad600068102: External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.'}, 'EXT_LIVE_EVENTS': {'error': '091093 (55000): 01bccd38-3204-800f-0002-4ad6000650ce: External table EXT_LIVE_EVENTS marked invalid. Stage S3_LIVE_EVENTS_STAGE dropped.'}}

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/models/taskinstance.py", line 433, in _execute_task
    result = execute_callable(context=context, **execute_callable_kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/operators/python.py", line 199, in execute
    return_value = self.execute_callable()
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/operators/python.py", line 216, in execute_callable
    return self.python_callable(*self.op_args, **self.op_kwargs)
  File "/opt/airflow/dags/dbt_only_dag.py", line 225, in refresh_snowflake_stages
    raise Exception(f"Stage refresh failed: {e}")
Exception: Stage refresh failed: Stage refresh failed for all tables: {'EXT_LIVE_USERS': {'error': '091093 (55000): 01bccd38-3204-7fc5-0002-4ad6000631de: External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.'}, 'EXT_LIVE_ORDERS': {'error': '091093 (55000): 01bccd38-3204-802b-0002-4ad600068102: External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.'}, 'EXT_LIVE_EVENTS': {'error': '091093 (55000): 01bccd38-3204-800f-0002-4ad6000650ce: External table EXT_LIVE_EVENTS marked invalid. Stage S3_LIVE_EVENTS_STAGE dropped.'}}
[2025-06-04T10:00:03.413+0000] {taskinstance.py:1138} INFO - Marking task as UP_FOR_RETRY. dag_id=dbt_only_pipeline, task_id=refresh_snowflake_stages, execution_date=20250604T094249, start_date=20250604T095956, end_date=20250604T100003
[2025-06-04T10:00:03.428+0000] {standard_task_runner.py:107} ERROR - Failed to execute job 113 for task refresh_snowflake_stages (Stage refresh failed: Stage refresh failed for all tables: {'EXT_LIVE_USERS': {'error': '091093 (55000): 01bccd38-3204-7fc5-0002-4ad6000631de: External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.'}, 'EXT_LIVE_ORDERS': {'error': '091093 (55000): 01bccd38-3204-802b-0002-4ad600068102: External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.'}, 'EXT_LIVE_EVENTS': {'error': '091093 (55000): 01bccd38-3204-800f-0002-4ad6000650ce: External table EXT_LIVE_EVENTS marked invalid. Stage S3_LIVE_EVENTS_STAGE dropped.'}}; 4498)
[2025-06-04T10:00:03.480+0000] {local_task_job_runner.py:234} INFO - Task exited with return code 1
[2025-06-04T10:00:03.507+0000] {taskinstance.py:3280} INFO - 0 downstream tasks scheduled from follow-on schedule check
