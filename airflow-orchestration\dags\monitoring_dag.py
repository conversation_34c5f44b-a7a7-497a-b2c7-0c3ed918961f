"""
ETL Monitoring DAG
Runs health checks and monitoring more frequently than the main pipeline
"""

from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.operators.dummy import DummyOperator
from airflow.utils.dates import days_ago
import os
import sys
import subprocess
import logging

# Add workspace to Python path for imports
sys.path.append('/opt/airflow/workspace')

# Default arguments for the DAG
default_args = {
    'owner': 'data-team',
    'depends_on_past': False,
    'start_date': days_ago(1),
    'email_on_failure': True,
    'email_on_retry': False,
    'retries': 1,
    'retry_delay': timedelta(minutes=2),
    'catchup': False,
}

# DAG definition
dag = DAG(
    'etl_monitoring_hourly',
    default_args=default_args,
    description='Hourly ETL Health Monitoring',
    schedule_interval='0 * * * *',  # Every hour
    max_active_runs=1,
    tags=['monitoring', 'health-check', 'alerts'],
)

def update_monitoring_models(**context):
    """Update monitoring models with latest data"""
    logging.info("Updating monitoring models...")
    
    os.chdir('/opt/airflow/workspace/dbt_live')
    
    # Run only the monitoring models
    result = subprocess.run([
        'dbt', 'run', '--models', 'monitoring'
    ], capture_output=True, text=True)
    
    if result.returncode != 0:
        logging.error(f"Monitoring models update failed: {result.stderr}")
        raise Exception(f"Monitoring models update failed: {result.stderr}")
    
    logging.info(f"Monitoring models updated: {result.stdout}")
    return "Monitoring models updated"

def check_pipeline_health(**context):
    """Check overall pipeline health"""
    logging.info("Checking pipeline health...")
    
    os.chdir('/opt/airflow/workspace')
    
    result = subprocess.run([
        'python', 'show_etl_health_dashboard.py'
    ], capture_output=True, text=True)
    
    if result.returncode != 0:
        logging.error(f"Health check failed: {result.stderr}")
        raise Exception(f"Health check failed: {result.stderr}")
    
    # Parse output to check for critical issues
    output = result.stdout
    if "Critical" in output or "Poor" in output:
        logging.warning("Critical issues detected in pipeline health!")
        # Could trigger alerts here
    
    logging.info(f"Health check completed: {output}")
    return "Health check completed"

def run_alert_monitoring(**context):
    """Run alert monitoring and notifications"""
    logging.info("Running alert monitoring...")
    
    os.chdir('/opt/airflow/workspace')
    
    result = subprocess.run([
        'python', 'monitor_pipeline_alerts.py'
    ], capture_output=True, text=True)
    
    # Alert monitoring might return warnings, which is OK
    if result.returncode != 0:
        logging.warning(f"Alert monitoring had issues: {result.stderr}")
    
    # Check if critical alerts were found
    output = result.stdout
    if "CRITICAL" in output:
        logging.error("Critical alerts detected!")
        # Could trigger immediate notifications here
    
    logging.info(f"Alert monitoring completed: {output}")
    return "Alert monitoring completed"

def generate_monitoring_report(**context):
    """Generate comprehensive monitoring report (runs less frequently)"""
    # Only run this during business hours
    current_hour = datetime.now().hour
    if current_hour < 8 or current_hour > 18:
        logging.info("Skipping report generation outside business hours")
        return "Report generation skipped"
    
    logging.info("Generating monitoring report...")
    
    os.chdir('/opt/airflow/workspace')
    
    result = subprocess.run([
        'python', 'generate_monitoring_report.py'
    ], capture_output=True, text=True)
    
    if result.returncode != 0:
        logging.error(f"Report generation failed: {result.stderr}")
        raise Exception(f"Report generation failed: {result.stderr}")
    
    logging.info(f"Monitoring report generated: {result.stdout}")
    return "Monitoring report generated"

# Task definitions
start_monitoring = DummyOperator(
    task_id='start_monitoring',
    dag=dag,
)

update_models_task = PythonOperator(
    task_id='update_monitoring_models',
    python_callable=update_monitoring_models,
    dag=dag,
)

health_check_task = PythonOperator(
    task_id='check_pipeline_health',
    python_callable=check_pipeline_health,
    dag=dag,
)

alert_monitoring_task = PythonOperator(
    task_id='run_alert_monitoring',
    python_callable=run_alert_monitoring,
    dag=dag,
)

report_task = PythonOperator(
    task_id='generate_monitoring_report',
    python_callable=generate_monitoring_report,
    dag=dag,
)

end_monitoring = DummyOperator(
    task_id='end_monitoring',
    dag=dag,
)

# Task dependencies
start_monitoring >> update_models_task >> [health_check_task, alert_monitoring_task]
[health_check_task, alert_monitoring_task] >> report_task >> end_monitoring
