[2025-06-04T09:50:05.647+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=non-requeueable deps ti=<TaskInstance: dbt_only_pipeline.dbt_run_staging manual__2025-06-04T09:42:49+00:00 [queued]>
[2025-06-04T09:50:05.657+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=requeueable deps ti=<TaskInstance: dbt_only_pipeline.dbt_run_staging manual__2025-06-04T09:42:49+00:00 [queued]>
[2025-06-04T09:50:05.658+0000] {taskinstance.py:2170} INFO - Starting attempt 1 of 3
[2025-06-04T09:50:05.671+0000] {taskinstance.py:2191} INFO - Executing <Task(PythonOperator): dbt_run_staging> on 2025-06-04 09:42:49+00:00
[2025-06-04T09:50:05.677+0000] {standard_task_runner.py:60} INFO - Started process 4352 to run task
[2025-06-04T09:50:05.683+0000] {standard_task_runner.py:87} INFO - Running: ['***', 'tasks', 'run', 'dbt_only_pipeline', 'dbt_run_staging', 'manual__2025-06-04T09:42:49+00:00', '--job-id', '112', '--raw', '--subdir', 'DAGS_FOLDER/dbt_only_dag.py', '--cfg-path', '/tmp/tmpc2ombinv']
[2025-06-04T09:50:05.685+0000] {standard_task_runner.py:88} INFO - Job 112: Subtask dbt_run_staging
[2025-06-04T09:50:05.742+0000] {task_command.py:423} INFO - Running <TaskInstance: dbt_only_pipeline.dbt_run_staging manual__2025-06-04T09:42:49+00:00 [running]> on host 8fb7c8dc5097
[2025-06-04T09:50:05.847+0000] {taskinstance.py:2480} INFO - Exporting env vars: AIRFLOW_CTX_DAG_OWNER='data-team' AIRFLOW_CTX_DAG_ID='dbt_only_pipeline' AIRFLOW_CTX_TASK_ID='dbt_run_staging' AIRFLOW_CTX_EXECUTION_DATE='2025-06-04T09:42:49+00:00' AIRFLOW_CTX_TRY_NUMBER='1' AIRFLOW_CTX_DAG_RUN_ID='manual__2025-06-04T09:42:49+00:00'
[2025-06-04T09:50:05.848+0000] {dbt_only_dag.py:145} INFO - 🏗️ Running staging models...
[2025-06-04T09:50:05.850+0000] {dbt_only_dag.py:68} INFO - 🚀 Running dbt run
[2025-06-04T09:50:05.852+0000] {dbt_only_dag.py:88} INFO - 📋 Executing: dbt run --models staging
[2025-06-04T09:50:14.093+0000] {dbt_only_dag.py:101} INFO - 📄 stdout: [0m09:50:07  Running with dbt=1.9.6
[0m09:50:08  Registered adapter: snowflake=1.9.4
[0m09:50:10  [[33mWARNING[0m]: Configuration paths exist in your dbt_project.yml file which do not apply to any resources.
There are 3 unused configuration paths:
- models.live_c360.incremental
- seeds.live_c360
- snapshots.live_c360
[0m09:50:10  Found 12 models, 21 data tests, 3 sources, 590 macros
[0m09:50:10  
[0m09:50:10  Concurrency: 4 threads (target='live')
[0m09:50:10  
[0m09:50:11  1 of 3 START sql view model LIVE_DATA.stg_events ............................... [RUN]
[0m09:50:11  2 of 3 START sql view model LIVE_DATA.stg_orders ............................... [RUN]
[0m09:50:11  3 of 3 START sql view model LIVE_DATA.stg_users ................................ [RUN]
[0m09:50:12  3 of 3 ERROR creating sql view model LIVE_DATA.stg_users ....................... [[31mERROR[0m in 0.22s]
[0m09:50:12  2 of 3 ERROR creating sql view model LIVE_DATA.stg_orders ...................... [[31mERROR[0m in 0.24s]
[0m09:50:12  1 of 3 ERROR creating sql view model LIVE_DATA.stg_events ...................... [[31mERROR[0m in 0.27s]
[0m09:50:12  
[0m09:50:12  Finished running 3 view models in 0 hours 0 minutes and 2.04 seconds (2.04s).
[0m09:50:12  
[0m09:50:12  [31mCompleted with 3 errors, 0 partial successes, and 0 warnings:[0m
[0m09:50:12  
[0m09:50:12    Database Error in model stg_users (models/staging/stg_users.sql)
  091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_users.sql
[0m09:50:12  
[0m09:50:12    Database Error in model stg_orders (models/staging/stg_orders.sql)
  091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_orders.sql
[0m09:50:12  
[0m09:50:12    Database Error in model stg_events (models/staging/stg_events.sql)
  091093 (55000): External table EXT_LIVE_EVENTS marked invalid. Stage S3_LIVE_EVENTS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_events.sql
[0m09:50:12  
[0m09:50:12  Done. PASS=0 WARN=0 ERROR=3 SKIP=0 TOTAL=3

[2025-06-04T09:50:14.094+0000] {dbt_only_dag.py:116} ERROR - ❌ dbt run failed with return code 1
[2025-06-04T09:50:14.095+0000] {dbt_only_dag.py:117} ERROR - ❌ Error output: 
[2025-06-04T09:50:14.095+0000] {dbt_only_dag.py:126} ERROR - ❌ dbt run failed: dbt run failed with return code 1: 
[2025-06-04T09:50:14.096+0000] {taskinstance.py:2698} ERROR - Task failed with exception
Traceback (most recent call last):
  File "/opt/airflow/dags/dbt_only_dag.py", line 118, in run_dbt_command
    raise Exception(f"{error_msg}: {result.stderr}")
Exception: dbt run failed with return code 1: 

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/models/taskinstance.py", line 433, in _execute_task
    result = execute_callable(context=context, **execute_callable_kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/operators/python.py", line 199, in execute
    return_value = self.execute_callable()
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/operators/python.py", line 216, in execute_callable
    return self.python_callable(*self.op_args, **self.op_kwargs)
  File "/opt/airflow/dags/dbt_only_dag.py", line 146, in dbt_run_staging
    return run_dbt_command('run', models='staging', **context)
  File "/opt/airflow/dags/dbt_only_dag.py", line 127, in run_dbt_command
    raise Exception(f"dbt {command_type} failed: {e}")
Exception: dbt run failed: dbt run failed with return code 1: 
[2025-06-04T09:50:14.109+0000] {taskinstance.py:1138} INFO - Marking task as UP_FOR_RETRY. dag_id=dbt_only_pipeline, task_id=dbt_run_staging, execution_date=20250604T094249, start_date=20250604T095005, end_date=20250604T095014
[2025-06-04T09:50:14.123+0000] {standard_task_runner.py:107} ERROR - Failed to execute job 112 for task dbt_run_staging (dbt run failed: dbt run failed with return code 1: ; 4352)
[2025-06-04T09:50:14.175+0000] {local_task_job_runner.py:234} INFO - Task exited with return code 1
[2025-06-04T09:50:14.208+0000] {taskinstance.py:3280} INFO - 0 downstream tasks scheduled from follow-on schedule check
