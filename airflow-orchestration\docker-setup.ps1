# Airflow Docker Setup Script
Write-Host "🐳 Setting up Docker environment for Airflow..." -ForegroundColor Cyan

# Add Docker to PATH for this session
$env:PATH += ";C:\Program Files\Docker\Docker\resources\bin"

# Check if Docker is running
Write-Host "📋 Checking Docker status..." -ForegroundColor Yellow
try {
    $dockerVersion = docker --version
    Write-Host "✅ Docker is running: $dockerVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker is not running. Please start Docker Desktop first." -ForegroundColor Red
    exit 1
}

# Check Docker Compose
try {
    $composeVersion = docker compose version
    Write-Host "✅ Docker Compose is available: $composeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker Compose is not available." -ForegroundColor Red
    exit 1
}

# Check if this is first time setup
if (-not (Test-Path "logs\scheduler")) {
    Write-Host "🚀 First time setup - Initializing Airflow..." -ForegroundColor Yellow
    
    # Create necessary directories
    New-Item -ItemType Directory -Force -Path "logs", "plugins", "config" | Out-Null
    
    # Initialize Airflow
    Write-Host "⏳ Running Airflow initialization (this may take a few minutes)..." -ForegroundColor Yellow
    docker compose up airflow-init
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Airflow initialization completed successfully!" -ForegroundColor Green
    } else {
        Write-Host "❌ Airflow initialization failed!" -ForegroundColor Red
        exit 1
    }
} else {
    Write-Host "✅ Airflow already initialized" -ForegroundColor Green
}

Write-Host ""
Write-Host "🎉 Docker setup complete!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Next steps:" -ForegroundColor Cyan
Write-Host "  1. Start Airflow: docker compose up -d" -ForegroundColor White
Write-Host "  2. Stop Airflow: docker compose down" -ForegroundColor White
Write-Host "  3. View logs: docker compose logs -f" -ForegroundColor White
Write-Host ""
Write-Host "🌐 Airflow Web UI: http://localhost:8080" -ForegroundColor Cyan
Write-Host "👤 Username: airflow" -ForegroundColor White
Write-Host "🔑 Password: airflow" -ForegroundColor White
Write-Host ""
Write-Host "💡 Tip: Use 'docker compose ps' to check service status" -ForegroundColor Yellow
