[2025-06-04T03:34:54.128+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=non-requeueable deps ti=<TaskInstance: etl_monitoring_hourly.update_monitoring_models scheduled__2025-06-03T17:00:00+00:00 [queued]>
[2025-06-04T03:34:54.142+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=requeueable deps ti=<TaskInstance: etl_monitoring_hourly.update_monitoring_models scheduled__2025-06-03T17:00:00+00:00 [queued]>
[2025-06-04T03:34:54.142+0000] {taskinstance.py:2170} INFO - Starting attempt 2 of 2
[2025-06-04T03:34:54.159+0000] {taskinstance.py:2191} INFO - Executing <Task(PythonOperator): update_monitoring_models> on 2025-06-03 17:00:00+00:00
[2025-06-04T03:34:54.166+0000] {standard_task_runner.py:60} INFO - Started process 885 to run task
[2025-06-04T03:34:54.170+0000] {standard_task_runner.py:87} INFO - Running: ['***', 'tasks', 'run', 'etl_monitoring_hourly', 'update_monitoring_models', 'scheduled__2025-06-03T17:00:00+00:00', '--job-id', '56', '--raw', '--subdir', 'DAGS_FOLDER/monitoring_dag.py', '--cfg-path', '/tmp/tmp_x2xet4n']
[2025-06-04T03:34:54.171+0000] {standard_task_runner.py:88} INFO - Job 56: Subtask update_monitoring_models
[2025-06-04T03:34:54.238+0000] {task_command.py:423} INFO - Running <TaskInstance: etl_monitoring_hourly.update_monitoring_models scheduled__2025-06-03T17:00:00+00:00 [running]> on host 9fdd53a423c8
[2025-06-04T03:34:54.327+0000] {taskinstance.py:2480} INFO - Exporting env vars: AIRFLOW_CTX_DAG_OWNER='data-team' AIRFLOW_CTX_DAG_ID='etl_monitoring_hourly' AIRFLOW_CTX_TASK_ID='update_monitoring_models' AIRFLOW_CTX_EXECUTION_DATE='2025-06-03T17:00:00+00:00' AIRFLOW_CTX_TRY_NUMBER='2' AIRFLOW_CTX_DAG_RUN_ID='scheduled__2025-06-03T17:00:00+00:00'
[2025-06-04T03:34:54.328+0000] {monitoring_dag.py:43} INFO - Updating monitoring models...
[2025-06-04T03:34:54.331+0000] {taskinstance.py:2698} ERROR - Task failed with exception
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/models/taskinstance.py", line 433, in _execute_task
    result = execute_callable(context=context, **execute_callable_kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/operators/python.py", line 199, in execute
    return_value = self.execute_callable()
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/operators/python.py", line 216, in execute_callable
    return self.python_callable(*self.op_args, **self.op_kwargs)
  File "/opt/airflow/dags/monitoring_dag.py", line 48, in update_monitoring_models
    result = subprocess.run([
  File "/usr/local/lib/python3.10/subprocess.py", line 503, in run
    with Popen(*popenargs, **kwargs) as process:
  File "/usr/local/lib/python3.10/subprocess.py", line 971, in __init__
    self._execute_child(args, executable, preexec_fn, close_fds,
  File "/usr/local/lib/python3.10/subprocess.py", line 1863, in _execute_child
    raise child_exception_type(errno_num, err_msg, err_filename)
PermissionError: [Errno 13] Permission denied: 'dbt'
[2025-06-04T03:34:54.346+0000] {taskinstance.py:1138} INFO - Marking task as FAILED. dag_id=etl_monitoring_hourly, task_id=update_monitoring_models, execution_date=20250603T170000, start_date=20250604T033454, end_date=20250604T033454
[2025-06-04T03:34:54.360+0000] {standard_task_runner.py:107} ERROR - Failed to execute job 56 for task update_monitoring_models ([Errno 13] Permission denied: 'dbt'; 885)
[2025-06-04T03:34:54.383+0000] {local_task_job_runner.py:234} INFO - Task exited with return code 1
[2025-06-04T03:34:54.405+0000] {taskinstance.py:3280} INFO - 0 downstream tasks scheduled from follow-on schedule check
