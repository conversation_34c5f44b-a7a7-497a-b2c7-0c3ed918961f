# 🚀 How to Run End-to-End Pipeline Successfully

This guide documents the complete process to run the end-to-end data pipeline from S3 → Snowflake → dbt successfully.

## 📋 Prerequisites Checklist

### ✅ **Required Software & Accounts**
- [ ] Python 3.11+ with conda/pip
- [ ] AWS CLI configured with credentials
- [ ] Snowflake account with ACCOUNTADMIN role
- [ ] Git repository cloned locally

### ✅ **Required Python Packages**
```bash
pip install pandas numpy pyyaml boto3 snowflake-connector-python dbt-core dbt-snowflake faker structlog
```

### ✅ **AWS Configuration**
```bash
# Verify AWS credentials are configured
aws configure list
aws s3 ls  # Should list your buckets
```

### ✅ **Snowflake Configuration**
- Account: `SVLFKJI-IX89869`
- User: `XINBINZHANG`
- Database: `MYDB`
- Schema: `LIVE_DATA`
- Warehouse: `COMPUTE_WH`
- Role: `ACCOUNTADMIN`

## 🔧 One-Time Setup Steps

### 1. **Create Snowflake External Tables** (CRITICAL)
```bash
cd experiment2
python create_external_tables_simple.py
```

**Expected Output:**
```
✅ Connected to Snowflake successfully
✅ File format created
✅ External stages created
✅ EXT_LIVE_USERS created
✅ EXT_LIVE_ORDERS created
✅ EXT_LIVE_EVENTS created
✅ EXT_LIVE_USERS: 5,000 records
✅ EXT_LIVE_ORDERS: 15,000 records
✅ EXT_LIVE_EVENTS: 75,000 records
🎉 External tables setup completed successfully!
```

### 2. **Verify Configuration Files**
```bash
# Check config file exists
ls config/live_pipeline_config.yml

# Check dbt profiles
ls dbt_live/profiles.yml
```

## 🚀 Running the End-to-End Pipeline

### **Command to Run:**
```bash
cd experiment2
python run_end2end_pipeline.py --save-results
```

### **Expected Execution Flow:**

#### **Step 1: Data Generation** (30-40 seconds)
```
📊 STEP 1: Generating daily data...
🔄 Generating 5,000 users for 2025-05-26
✅ Uploaded 5,000 records to s3://bucket/live-data/users/daily_users_20250526.csv
🔄 Generating 15,000 orders for 2025-05-26
✅ Uploaded 15,000 records to s3://bucket/live-data/orders/daily_orders_20250526.csv
🔄 Generating 75,000 events for 2025-05-26
✅ Uploaded 75,000 records to s3://bucket/live-data/events/daily_events_20250526.csv
✅ Step 1 completed: 95,000 records generated
```

#### **Step 2: S3 Verification** (5 seconds)
```
🔍 STEP 2: Verifying S3 data...
✅ users: 1.07 MB
✅ orders: 2.08 MB
✅ events: 15.57 MB
✅ Step 2 completed: All files verified in S3
```

#### **Step 3: Snowflake Refresh** (10 seconds)
```
❄️ STEP 3: Refreshing Snowflake external stages...
✅ EXT_LIVE_USERS: 5,000 → 5,000 (+0)
✅ EXT_LIVE_ORDERS: 15,000 → 15,000 (+0)
✅ EXT_LIVE_EVENTS: 75,000 → 75,000 (+0)
✅ Step 3 completed: Snowflake stages refreshed
```

#### **Step 4: dbt Pipeline** (30 seconds)
```
🔧 STEP 4: Running dbt pipeline...
Running dbt models...
✅ dbt run successful
Running dbt tests...
✅ dbt tests passed
✅ Step 4 completed: dbt pipeline executed
```

#### **Step 5: Validation** (5 seconds)
```
✅ STEP 5: Validating results...
📊 EXT_LIVE_USERS: 5,000 records
📊 EXT_LIVE_ORDERS: 15,000 records
📊 EXT_LIVE_EVENTS: 75,000 records
📊 STG_USERS: 5,000 records
📊 STG_ORDERS: 15,000 records
📊 STG_EVENTS: 75,000 records
📊 DIM_USERS: 5,000 records
✅ Step 5 completed: Results validated
```

#### **Final Summary:**
```
============================================================
📋 END-TO-END PIPELINE SUMMARY
============================================================
⏰ Start time: 2025-05-26 22:12:48.879549
⏰ End time: 2025-05-26 22:13:56.287330
⏱️ Duration: 1.12 minutes
📅 Target date: 2025-05-26
📊 Records generated: 95,000
🎉 END-TO-END PIPELINE COMPLETED SUCCESSFULLY!
📄 Results saved to pipeline_results_20250526_221356.yaml
```

## 🚨 Troubleshooting Common Issues

### **Issue 1: "No module named 'faker'"**
```bash
# Solution:
pip install faker
```

### **Issue 2: "Object 'EXT_LIVE_USERS' does not exist"**
```bash
# Solution: Run the external tables setup
cd experiment2
python create_external_tables_simple.py
```

### **Issue 3: AWS credentials not found**
```bash
# Solution: Configure AWS CLI
aws configure
# Enter your Access Key ID, Secret Access Key, and region
```

### **Issue 4: Snowflake connection failed**
```bash
# Solution: Check credentials in config/live_pipeline_config.yml
# Verify account, user, database, schema settings
```

### **Issue 5: dbt run failed**
```bash
# Solution: Check dbt profiles and models
cd dbt_live
dbt debug  # Check connection
dbt run --models stg_users  # Test individual model
```

## 📊 Verifying Success

### **1. Check Results File**
```bash
# View the latest results
ls -la pipeline_results_*.yaml
cat pipeline_results_20250526_221356.yaml
```

### **2. Query Snowflake Directly**
```sql
-- Connect to Snowflake and run:
USE DATABASE MYDB;
USE SCHEMA LIVE_DATA;

-- Check external tables
SELECT COUNT(*) FROM EXT_LIVE_USERS;   -- Should be 5,000
SELECT COUNT(*) FROM EXT_LIVE_ORDERS;  -- Should be 15,000
SELECT COUNT(*) FROM EXT_LIVE_EVENTS;  -- Should be 75,000

-- Check dbt models
SELECT COUNT(*) FROM STG_USERS;        -- Should be 5,000
SELECT COUNT(*) FROM STG_ORDERS;       -- Should be 15,000
SELECT COUNT(*) FROM STG_EVENTS;       -- Should be 75,000
SELECT COUNT(*) FROM DIM_USERS;        -- Should be 5,000
```

### **3. Check S3 Files**
```bash
# List files in S3
aws s3 ls s3://lake-loader-input-365542662955-20250525-001439/live-data/ --recursive
```

## 🔄 Running Pipeline Daily

### **Manual Daily Run**
```bash
cd experiment2
python run_end2end_pipeline.py --save-results
```

### **Automated Daily Run Options**

#### **Option 1: Python Scheduler**
```bash
cd orchestration
python python_scheduler.py --time 06:00
```

#### **Option 2: Windows Task Scheduler**
```bash
# Use: orchestration/windows_task_scheduler.bat
# Schedule in Windows Task Scheduler for daily execution
```

#### **Option 3: Apache Airflow**
```bash
cd orchestration/airflow
./setup_airflow.sh
# Access: http://localhost:9090
```

## 📈 Performance Benchmarks

### **Typical Execution Times:**
- **Total Duration**: 1-2 minutes
- **Data Generation**: 30-40 seconds
- **S3 Upload**: 5-10 seconds
- **Snowflake Refresh**: 5-10 seconds
- **dbt Processing**: 20-30 seconds
- **Validation**: 5 seconds

### **Data Volumes:**
- **Users**: 5,000 records (~1 MB)
- **Orders**: 15,000 records (~2 MB)
- **Events**: 75,000 records (~15 MB)
- **Total**: 95,000 records (~18 MB)

## 🎯 Success Criteria

✅ **Pipeline is successful when:**
- All 5 steps complete without errors
- Return code is 0
- Results file shows `success: true`
- All record counts match expected values
- dbt tests pass
- Snowflake tables contain data

## 📝 Maintenance Notes

### **Regular Tasks:**
- Monitor S3 storage costs
- Check Snowflake credit usage
- Review dbt test results
- Clean up old result files

### **Scaling Considerations:**
- Increase data volumes in `config/live_pipeline_config.yml`
- Add more dbt models as needed
- Consider partitioning for larger datasets
- Monitor execution times and optimize

## 🏆 Achievement Unlocked!

**You now have a production-ready modern data stack:**
- ✅ S3 for data lake storage
- ✅ Snowflake for cloud data warehouse
- ✅ dbt for data transformation
- ✅ Automated end-to-end pipeline
- ✅ Data quality testing
- ✅ Monitoring and validation

**This is enterprise-grade data engineering!** 🚀
