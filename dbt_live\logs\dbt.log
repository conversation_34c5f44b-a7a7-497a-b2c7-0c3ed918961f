[0m06:22:31.228952 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x798877f3a770>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x798876e7d930>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x798876e7df30>]}


============================== 06:22:31.240214 | 71df748e-7f96-4227-82fd-46c4f2eb7969 ==============================
[0m06:22:31.240214 [info ] [MainThread]: Running with dbt=1.9.6
[0m06:22:31.241997 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'log_cache_events': 'False', 'write_json': 'True', 'partial_parse': 'True', 'cache_selected_only': 'False', 'warn_error': 'None', 'debug': 'False', 'version_check': 'True', 'log_path': '/opt/airflow/workspace/dbt_live/logs', 'fail_fast': 'False', 'profiles_dir': '/opt/airflow/workspace/dbt_live', 'use_colors': 'True', 'use_experimental_parser': 'False', 'no_print': 'None', 'quiet': 'False', 'empty': 'None', 'log_format': 'default', 'static_parser': 'True', 'introspect': 'True', 'warn_error_options': 'WarnErrorOptions(include=[], exclude=[])', 'target_path': 'None', 'invocation_command': 'dbt deps', 'send_anonymous_usage_stats': 'True'}
[0m06:22:31.678122 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'project_id', 'label': '71df748e-7f96-4227-82fd-46c4f2eb7969', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x79887925dc00>]}
[0m06:22:31.720954 [debug] [MainThread]: Set downloads directory='/tmp/dbt-downloads-13l8jyz_'
[0m06:22:31.722294 [debug] [MainThread]: Making package index registry request: GET https://hub.getdbt.com/api/v1/index.json
[0m06:22:31.851601 [debug] [MainThread]: Response from registry index: GET https://hub.getdbt.com/api/v1/index.json 200
[0m06:22:31.855858 [debug] [MainThread]: Making package registry request: GET https://hub.getdbt.com/api/v1/dbt-labs/dbt_utils.json
[0m06:22:31.953924 [debug] [MainThread]: Response from registry: GET https://hub.getdbt.com/api/v1/dbt-labs/dbt_utils.json 200
[0m06:22:31.964141 [info ] [MainThread]: Installing dbt-labs/dbt_utils
[0m06:22:36.363004 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7081f10c6860>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7081f0008790>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7081f000b310>]}


============================== 06:22:36.369817 | 134c2024-6326-4965-b7b9-acbf4f1c9b6e ==============================
[0m06:22:36.369817 [info ] [MainThread]: Running with dbt=1.9.6
[0m06:22:36.372546 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'log_cache_events': 'False', 'write_json': 'True', 'partial_parse': 'True', 'cache_selected_only': 'False', 'profiles_dir': '/opt/airflow/workspace/dbt_live', 'version_check': 'True', 'warn_error': 'None', 'log_path': '/opt/airflow/workspace/dbt_live/logs', 'debug': 'False', 'fail_fast': 'False', 'use_colors': 'True', 'use_experimental_parser': 'False', 'no_print': 'None', 'quiet': 'False', 'empty': 'False', 'warn_error_options': 'WarnErrorOptions(include=[], exclude=[])', 'introspect': 'True', 'log_format': 'default', 'static_parser': 'True', 'target_path': 'None', 'invocation_command': 'dbt run', 'send_anonymous_usage_stats': 'True'}
[0m06:22:36.805387 [info ] [MainThread]: Installed from version 1.1.1
[0m06:22:36.806598 [info ] [MainThread]: Updated version available: 1.3.0
[0m06:22:36.807846 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'package', 'label': '71df748e-7f96-4227-82fd-46c4f2eb7969', 'property_': 'install', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x798877bab580>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x798876cb79a0>]}
[0m06:22:36.809239 [info ] [MainThread]: 
[0m06:22:36.810463 [info ] [MainThread]: Updates available for packages: ['dbt-labs/dbt_utils']                 
Update your versions in packages.yml, then run dbt deps
[0m06:22:36.814366 [debug] [MainThread]: Resource report: {"command_name": "deps", "command_success": true, "command_wall_clock_time": 5.758381, "process_in_blocks": "0", "process_kernel_time": 1.013611, "process_mem_max_rss": "94880", "process_out_blocks": "200", "process_user_time": 3.531802}
[0m06:22:36.815980 [debug] [MainThread]: Command `dbt deps` succeeded at 06:22:36.815752 after 5.76 seconds
[0m06:22:36.817310 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x798877f3a770>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x798876ebc820>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x798877ba8610>]}
[0m06:22:36.818524 [debug] [MainThread]: Flushing usage events
[0m06:22:38.468020 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'project_id', 'label': '134c2024-6326-4965-b7b9-acbf4f1c9b6e', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7081f000bfa0>]}
[0m06:22:38.555808 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'adapter_info', 'label': '134c2024-6326-4965-b7b9-acbf4f1c9b6e', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7081dd46a3b0>]}
[0m06:22:38.558975 [info ] [MainThread]: Registered adapter: snowflake=1.9.4
[0m06:22:38.688526 [debug] [MainThread]: An error was encountered while trying to flush usage events
[0m06:22:40.150013 [debug] [MainThread]: checksum: dd6dc1e5178459e3de3bf2eeb7c86bed4be2266c311fce8c15d86eb4ff94e7ad, vars: {}, profile: , target: , version: 1.9.6
[0m06:22:40.156361 [info ] [MainThread]: Unable to do partial parsing because saved manifest not found. Starting full parse.
[0m06:22:40.159410 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'partial_parser', 'label': '134c2024-6326-4965-b7b9-acbf4f1c9b6e', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7081dd30f130>]}
[0m06:22:42.431300 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7903102827d0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x79030feefdf0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x79030f181c90>]}


============================== 06:22:42.448060 | e8e8d900-9afe-473d-a898-2ffdf34ccbbc ==============================
[0m06:22:42.448060 [info ] [MainThread]: Running with dbt=1.9.6
[0m06:22:42.451672 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'write_json': 'True', 'log_cache_events': 'False', 'partial_parse': 'True', 'cache_selected_only': 'False', 'warn_error': 'None', 'version_check': 'True', 'profiles_dir': '/opt/airflow/workspace/dbt_live', 'log_path': '/opt/airflow/workspace/dbt_live/logs', 'debug': 'False', 'fail_fast': 'False', 'use_colors': 'True', 'use_experimental_parser': 'False', 'empty': 'None', 'quiet': 'False', 'no_print': 'None', 'warn_error_options': 'WarnErrorOptions(include=[], exclude=[])', 'invocation_command': 'dbt parse', 'static_parser': 'True', 'log_format': 'default', 'target_path': 'None', 'introspect': 'True', 'send_anonymous_usage_stats': 'True'}
[0m06:22:43.697574 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'project_id', 'label': 'e8e8d900-9afe-473d-a898-2ffdf34ccbbc', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7902fc677a60>]}
[0m06:22:43.852079 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'adapter_info', 'label': 'e8e8d900-9afe-473d-a898-2ffdf34ccbbc', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x79030fef0fa0>]}
[0m06:22:43.857393 [info ] [MainThread]: Registered adapter: snowflake=1.9.4
[0m06:22:44.167469 [debug] [MainThread]: checksum: dd6dc1e5178459e3de3bf2eeb7c86bed4be2266c311fce8c15d86eb4ff94e7ad, vars: {}, profile: , target: , version: 1.9.6
[0m06:22:44.173950 [info ] [MainThread]: Unable to do partial parsing because saved manifest not found. Starting full parse.
[0m06:22:44.174939 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'partial_parser', 'label': 'e8e8d900-9afe-473d-a898-2ffdf34ccbbc', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7902fc37b070>]}
[0m06:22:44.196623 [warn ] [MainThread]: [[33mWARNING[0m]: Configuration paths exist in your dbt_project.yml file which do not apply to any resources.
There are 3 unused configuration paths:
- models.live_c360.incremental
- seeds.live_c360
- snapshots.live_c360
[0m06:22:44.218299 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'load_project', 'label': '134c2024-6326-4965-b7b9-acbf4f1c9b6e', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7081dc6533a0>]}
[0m06:22:44.456438 [debug] [MainThread]: Wrote artifact WritableManifest to /opt/airflow/workspace/dbt_live/target/manifest.json
[0m06:22:44.461359 [debug] [MainThread]: Wrote artifact SemanticManifest to /opt/airflow/workspace/dbt_live/target/semantic_manifest.json
[0m06:22:44.491447 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'resource_counts', 'label': '134c2024-6326-4965-b7b9-acbf4f1c9b6e', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7081dd00cc10>]}
[0m06:22:44.492601 [info ] [MainThread]: Found 12 models, 21 data tests, 3 sources, 590 macros
[0m06:22:44.493514 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'runnable_timing', 'label': '134c2024-6326-4965-b7b9-acbf4f1c9b6e', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7081dd00ebc0>]}
[0m06:22:44.496996 [info ] [MainThread]: 
[0m06:22:44.499132 [info ] [MainThread]: Concurrency: 4 threads (target='live')
[0m06:22:44.500485 [info ] [MainThread]: 
[0m06:22:44.501694 [debug] [MainThread]: Acquiring new snowflake connection 'master'
[0m06:22:44.508501 [debug] [ThreadPool]: Acquiring new snowflake connection 'list_MYDB'
[0m06:22:44.509608 [debug] [ThreadPool]: Acquiring new snowflake connection 'list_MYDB'
[0m06:22:44.571788 [debug] [ThreadPool]: Using snowflake connection "list_MYDB"
[0m06:22:44.573259 [debug] [ThreadPool]: Using snowflake connection "list_MYDB"
[0m06:22:44.580458 [debug] [ThreadPool]: On list_MYDB: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB"} */
show terse schemas in database MYDB
    limit 10000
[0m06:22:44.577723 [debug] [ThreadPool]: On list_MYDB: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB"} */
show terse schemas in database MYDB
    limit 10000
[0m06:22:44.582976 [debug] [ThreadPool]: Opening a new connection, currently in state init
[0m06:22:44.587614 [debug] [ThreadPool]: Opening a new connection, currently in state init
[0m06:22:45.144315 [debug] [ThreadPool]: SQL status: SUCCESS 6 in 0.557 seconds
[0m06:22:45.225367 [debug] [ThreadPool]: SQL status: SUCCESS 6 in 0.643 seconds
[0m06:22:45.231662 [debug] [ThreadPool]: Re-using an available connection from the pool (formerly list_MYDB, now list_MYDB_LIVE_DATA_ANALYTICS)
[0m06:22:45.232628 [debug] [ThreadPool]: Re-using an available connection from the pool (formerly list_MYDB, now list_MYDB_LIVE_DATA)
[0m06:22:45.233498 [debug] [ThreadPool]: Acquiring new snowflake connection 'list_MYDB_LIVE_DATA_dbt_test__audit'
[0m06:22:45.247412 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_ANALYTICS"
[0m06:22:45.252269 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA"
[0m06:22:45.255923 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_dbt_test__audit"
[0m06:22:45.257401 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_ANALYTICS: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_ANALYTICS"} */
alter session set quoted_identifiers_ignore_case = false;
[0m06:22:45.258566 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA"} */
alter session set quoted_identifiers_ignore_case = false;
[0m06:22:45.260735 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_dbt_test__audit: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_dbt_test__audit"} */
alter session set quoted_identifiers_ignore_case = false;
[0m06:22:45.268896 [debug] [ThreadPool]: Opening a new connection, currently in state init
[0m06:22:45.344486 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.083 seconds
[0m06:22:45.359930 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_ANALYTICS"
[0m06:22:45.361197 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.095 seconds
[0m06:22:45.362752 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_ANALYTICS: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_ANALYTICS"} */
show objects in MYDB.LIVE_DATA_ANALYTICS
    limit 10000
    
;
[0m06:22:45.365621 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA"
[0m06:22:45.370384 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA"} */
show objects in MYDB.LIVE_DATA
    limit 10000
    
;
[0m06:22:45.448888 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.082 seconds
[0m06:22:45.452546 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_ANALYTICS"
[0m06:22:45.453844 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_ANALYTICS: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_ANALYTICS"} */
alter session unset quoted_identifiers_ignore_case;
[0m06:22:45.494796 [debug] [ThreadPool]: SQL status: SUCCESS 14 in 0.122 seconds
[0m06:22:45.499002 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA"
[0m06:22:45.501175 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA"} */
alter session unset quoted_identifiers_ignore_case;
[0m06:22:45.545862 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.090 seconds
[0m06:22:45.588887 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.087 seconds
[0m06:22:45.852221 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.583 seconds
[0m06:22:45.855043 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_dbt_test__audit"
[0m06:22:45.857282 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_dbt_test__audit: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_dbt_test__audit"} */
show objects in MYDB.LIVE_DATA_dbt_test__audit
    limit 10000
    
;
[0m06:22:45.973218 [debug] [ThreadPool]: SQL status: SUCCESS 21 in 0.114 seconds
[0m06:22:45.979223 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_dbt_test__audit"
[0m06:22:45.981401 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_dbt_test__audit: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_dbt_test__audit"} */
alter session unset quoted_identifiers_ignore_case;
[0m06:22:46.034228 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.052 seconds
[0m06:22:46.044818 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'runnable_timing', 'label': '134c2024-6326-4965-b7b9-acbf4f1c9b6e', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7081dd4cad10>]}
[0m06:22:46.051315 [debug] [Thread-1 (]: Began running node model.live_c360.dbt_test_health
[0m06:22:46.052186 [debug] [Thread-2 (]: Began running node model.live_c360.pipeline_runtime_health
[0m06:22:46.052848 [debug] [Thread-3 (]: Began running node model.live_c360.query_history_health
[0m06:22:46.058354 [info ] [Thread-3 (]: 3 of 12 START sql table model LIVE_DATA.query_history_health ................... [RUN]
[0m06:22:46.054854 [info ] [Thread-1 (]: 1 of 12 START sql table model LIVE_DATA.dbt_test_health ........................ [RUN]
[0m06:22:46.056438 [info ] [Thread-2 (]: 2 of 12 START sql table model LIVE_DATA.pipeline_runtime_health ................ [RUN]
[0m06:22:46.053459 [debug] [Thread-4 (]: Began running node model.live_c360.stg_events
[0m06:22:46.059707 [debug] [Thread-3 (]: Re-using an available connection from the pool (formerly list_MYDB_LIVE_DATA_ANALYTICS, now model.live_c360.query_history_health)
[0m06:22:46.061408 [debug] [Thread-1 (]: Re-using an available connection from the pool (formerly list_MYDB_LIVE_DATA, now model.live_c360.dbt_test_health)
[0m06:22:46.062723 [debug] [Thread-2 (]: Re-using an available connection from the pool (formerly list_MYDB_LIVE_DATA_dbt_test__audit, now model.live_c360.pipeline_runtime_health)
[0m06:22:46.065142 [info ] [Thread-4 (]: 4 of 12 START sql view model LIVE_DATA.stg_events .............................. [RUN]
[0m06:22:46.067569 [debug] [Thread-3 (]: Began compiling node model.live_c360.query_history_health
[0m06:22:46.069308 [debug] [Thread-1 (]: Began compiling node model.live_c360.dbt_test_health
[0m06:22:46.070788 [debug] [Thread-2 (]: Began compiling node model.live_c360.pipeline_runtime_health
[0m06:22:46.072377 [debug] [Thread-4 (]: Acquiring new snowflake connection 'model.live_c360.stg_events'
[0m06:22:46.130582 [debug] [Thread-4 (]: Began compiling node model.live_c360.stg_events
[0m06:22:46.110174 [debug] [Thread-1 (]: Writing injected SQL for node "model.live_c360.dbt_test_health"
[0m06:22:46.125705 [debug] [Thread-2 (]: Writing injected SQL for node "model.live_c360.pipeline_runtime_health"
[0m06:22:46.090125 [debug] [Thread-3 (]: Writing injected SQL for node "model.live_c360.query_history_health"
[0m06:22:46.136320 [debug] [Thread-4 (]: Writing injected SQL for node "model.live_c360.stg_events"
[0m06:22:46.149772 [debug] [Thread-3 (]: Began executing node model.live_c360.query_history_health
[0m06:22:46.163522 [debug] [Thread-1 (]: Began executing node model.live_c360.dbt_test_health
[0m06:22:46.176261 [debug] [Thread-4 (]: Began executing node model.live_c360.stg_events
[0m06:22:46.191892 [debug] [Thread-2 (]: Began executing node model.live_c360.pipeline_runtime_health
[0m06:22:46.197433 [debug] [Thread-3 (]: Writing runtime sql for node "model.live_c360.query_history_health"
[0m06:22:46.201545 [debug] [Thread-1 (]: Writing runtime sql for node "model.live_c360.dbt_test_health"
[0m06:22:46.230629 [debug] [Thread-2 (]: Writing runtime sql for node "model.live_c360.pipeline_runtime_health"
[0m06:22:46.244115 [debug] [Thread-4 (]: Writing runtime sql for node "model.live_c360.stg_events"
[0m06:22:46.282309 [debug] [Thread-2 (]: Using snowflake connection "model.live_c360.pipeline_runtime_health"
[0m06:22:46.293942 [debug] [Thread-2 (]: On model.live_c360.pipeline_runtime_health: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "node_id": "model.live_c360.pipeline_runtime_health"} */
create or replace transient table MYDB.LIVE_DATA.pipeline_runtime_health
    

    
    as (

-- ETL Health Check: Pipeline Runtime Monitoring
-- Tracks pipeline execution times, success rates, and performance trends

with dbt_run_history as (
    -- Extract dbt-specific queries from query history
    select
        query_id,
        query_text,
        start_time,
        end_time,
        total_elapsed_time,
        execution_status,
        error_message,
        warehouse_name,
        user_name,
        
        -- Extract model name from dbt queries
        case 
            when query_text ilike '%create or replace%view%stg_%' then 
                regexp_substr(query_text, 'view\\s+[^.]+\\.([^\\s]+)', 1, 1, 'i', 1)
            when query_text ilike '%create or replace%table%dim_%' then 
                regexp_substr(query_text, 'table\\s+[^.]+\\.([^\\s]+)', 1, 1, 'i', 1)
            when query_text ilike '%create or replace%table%fact_%' then 
                regexp_substr(query_text, 'table\\s+[^.]+\\.([^\\s]+)', 1, 1, 'i', 1)
            when query_text ilike '%merge into%' then 
                regexp_substr(query_text, 'merge\\s+into\\s+[^.]+\\.([^\\s]+)', 1, 1, 'i', 1)
            else 'unknown'
        end as model_name,
        
        -- Categorize model types
        case 
            when query_text ilike '%stg_%' then 'staging'
            when query_text ilike '%dim_%' then 'dimension'
            when query_text ilike '%fact_%' then 'fact'
            when query_text ilike '%mv_%' then 'materialized_view'
            else 'other'
        end as model_type,
        
        date(start_time) as execution_date,
        extract(hour from start_time) as execution_hour
        
    from snowflake.account_usage.query_history
    where start_time >= current_date - 7
      and database_name = 'MYDB'
      and (query_text ilike '%dbt%' or query_text ilike '%create%table%' or query_text ilike '%create%view%')
      and query_text not ilike '%information_schema%'
      and query_text not ilike '%show%'
),

pipeline_runs as (
    -- Group queries into pipeline runs (by date and hour)
    select
        execution_date,
        execution_hour,
        model_type,
        warehouse_name,
        
        -- Run metrics
        count(*) as models_executed,
        sum(case when execution_status = 'SUCCESS' then 1 else 0 end) as successful_models,
        sum(case when execution_status != 'SUCCESS' then 1 else 0 end) as failed_models,
        
        -- Timing metrics
        sum(total_elapsed_time) as total_pipeline_time_ms,
        avg(total_elapsed_time) as avg_model_time_ms,
        max(total_elapsed_time) as max_model_time_ms,
        min(start_time) as pipeline_start_time,
        max(end_time) as pipeline_end_time,
        
        -- Calculate actual pipeline duration
        datediff('millisecond', min(start_time), max(end_time)) as pipeline_duration_ms,
        
        -- Error details
        listagg(distinct error_message, '; ') within group (order by error_message) as error_messages
        
    from dbt_run_history
    where model_name != 'unknown'
    group by execution_date, execution_hour, model_type, warehouse_name
),

model_performance as (
    -- Individual model performance tracking
    select
        model_name,
        model_type,
        execution_date,
        
        -- Performance metrics
        count(*) as execution_count,
        avg(total_elapsed_time) as avg_execution_time_ms,
        max(total_elapsed_time) as max_execution_time_ms,
        min(total_elapsed_time) as min_execution_time_ms,
        stddev(total_elapsed_time) as stddev_execution_time_ms,
        
        -- Success rate
        round((sum(case when execution_status = 'SUCCESS' then 1 else 0 end)::float / count(*)) * 100, 2) as success_rate,
        
        -- Latest execution
        max(start_time) as latest_execution_time,
        
        -- Performance trend (compare to previous day)
        lag(avg(total_elapsed_time)) over (partition by model_name order by execution_date) as prev_day_avg_time
        
    from dbt_run_history
    where model_name != 'unknown'
    group by model_name, model_type, execution_date
),

health_metrics as (
    select
        pr.*,
        
        -- Success rate
        round((successful_models::float / models_executed) * 100, 2) as pipeline_success_rate,
        
        -- Performance scores
        case 
            when avg_model_time_ms <= 30000 then 100  -- < 30 seconds
            when avg_model_time_ms <= 120000 then 80  -- < 2 minutes
            when avg_model_time_ms <= 300000 then 60  -- < 5 minutes
            else 30
        end as performance_score,
        
        case 
            when pipeline_success_rate = 100 then 100
            when pipeline_success_rate >= 95 then 90
            when pipeline_success_rate >= 90 then 80
            when pipeline_success_rate >= 80 then 70
            else 50
        end as reliability_score,
        
        -- Duration score (based on total pipeline time)
        case 
            when pipeline_duration_ms <= 300000 then 100  -- < 5 minutes
            when pipeline_duration_ms <= 600000 then 80   -- < 10 minutes
            when pipeline_duration_ms <= 1200000 then 60  -- < 20 minutes
            else 30
        end as duration_score
        
    from pipeline_runs pr
)

select
    execution_date,
    execution_hour,
    model_type,
    warehouse_name,
    models_executed,
    successful_models,
    failed_models,
    pipeline_success_rate,
    total_pipeline_time_ms,
    avg_model_time_ms,
    max_model_time_ms,
    pipeline_duration_ms,
    pipeline_start_time,
    pipeline_end_time,
    performance_score,
    reliability_score,
    duration_score,
    
    -- Overall pipeline health score
    round((reliability_score * 0.5 + performance_score * 0.3 + duration_score * 0.2), 0) as overall_pipeline_health_score,
    
    -- Pipeline status
    case 
        when failed_models = 0 and performance_score >= 80 then 'Healthy'
        when failed_models = 0 and performance_score >= 60 then 'Good'
        when failed_models <= 1 and performance_score >= 60 then 'Fair'
        when failed_models <= 2 then 'Poor'
        else 'Critical'
    end as pipeline_status,
    
    error_messages,
    current_timestamp() as health_check_timestamp
    
from health_metrics
order by execution_date desc, execution_hour desc, model_type
    )
;
[0m06:22:46.301937 [debug] [Thread-3 (]: Using snowflake connection "model.live_c360.query_history_health"
[0m06:22:46.315959 [debug] [Thread-1 (]: Using snowflake connection "model.live_c360.dbt_test_health"
[0m06:22:46.325731 [debug] [Thread-1 (]: On model.live_c360.dbt_test_health: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "node_id": "model.live_c360.dbt_test_health"} */
create or replace transient table MYDB.LIVE_DATA.dbt_test_health
    

    
    as (

-- ETL Health Check: dbt Test Results Monitoring
-- Tracks dbt test execution and results

with test_metadata as (
    -- This would typically come from dbt artifacts or a custom logging solution
    -- For now, we'll create a mock structure based on our known tests
    select 'source_not_null_live_external_ext_live_users_id' as test_name,
           'source' as test_type,
           'ext_live_users' as model_name,
           'id' as column_name,
           'not_null' as test_category,
           'pass' as status,
           0 as failures,
           current_timestamp() - interval '1 hour' as execution_time
    
    union all
    
    select 'source_unique_live_external_ext_live_users_id' as test_name,
           'source' as test_type,
           'ext_live_users' as model_name,
           'id' as column_name,
           'unique' as test_category,
           'pass' as status,
           0 as failures,
           current_timestamp() - interval '1 hour' as execution_time
    
    union all
    
    select 'source_not_null_live_external_ext_live_orders_id' as test_name,
           'source' as test_type,
           'ext_live_orders' as model_name,
           'id' as column_name,
           'not_null' as test_category,
           'pass' as status,
           0 as failures,
           current_timestamp() - interval '1 hour' as execution_time
    
    union all
    
    select 'source_relationships_live_external_ext_live_orders_user_id' as test_name,
           'source' as test_type,
           'ext_live_orders' as model_name,
           'user_id' as column_name,
           'relationships' as test_category,
           'pass' as status,
           0 as failures,
           current_timestamp() - interval '1 hour' as execution_time
    
    union all
    
    select 'source_accepted_values_live_external_ext_live_events_action' as test_name,
           'source' as test_type,
           'ext_live_events' as model_name,
           'action' as column_name,
           'accepted_values' as test_category,
           'pass' as status,
           0 as failures,
           current_timestamp() - interval '1 hour' as execution_time
),

test_summary as (
    select
        date(execution_time) as test_date,
        test_type,
        model_name,
        test_category,
        
        -- Test counts
        count(*) as total_tests,
        sum(case when status = 'pass' then 1 else 0 end) as passed_tests,
        sum(case when status = 'fail' then 1 else 0 end) as failed_tests,
        sum(case when status = 'warn' then 1 else 0 end) as warning_tests,
        sum(case when status = 'skip' then 1 else 0 end) as skipped_tests,
        
        -- Failure details
        sum(failures) as total_failures,
        max(execution_time) as latest_execution,
        
        -- Calculate pass rate
        round((sum(case when status = 'pass' then 1 else 0 end)::float / count(*)) * 100, 2) as pass_rate
        
    from test_metadata
    group by date(execution_time), test_type, model_name, test_category
),

model_coverage as (
    -- Calculate test coverage per model
    select
        model_name,
        count(distinct test_category) as test_types_covered,
        count(*) as total_tests_on_model,
        
        -- Check for essential test coverage
        max(case when test_category = 'not_null' then 1 else 0 end) as has_not_null_tests,
        max(case when test_category = 'unique' then 1 else 0 end) as has_unique_tests,
        max(case when test_category = 'relationships' then 1 else 0 end) as has_relationship_tests,
        max(case when test_category = 'accepted_values' then 1 else 0 end) as has_accepted_values_tests
        
    from test_metadata
    group by model_name
),

health_scores as (
    select
        ts.*,
        mc.test_types_covered,
        mc.total_tests_on_model,
        mc.has_not_null_tests,
        mc.has_unique_tests,
        mc.has_relationship_tests,
        mc.has_accepted_values_tests,
        
        -- Test reliability score
        case 
            when pass_rate = 100 then 100
            when pass_rate >= 95 then 90
            when pass_rate >= 90 then 80
            when pass_rate >= 80 then 70
            else 50
        end as reliability_score,
        
        -- Test coverage score
        case 
            when test_types_covered >= 4 then 100
            when test_types_covered >= 3 then 80
            when test_types_covered >= 2 then 60
            when test_types_covered >= 1 then 40
            else 20
        end as coverage_score
        
    from test_summary ts
    left join model_coverage mc on ts.model_name = mc.model_name
)

select
    test_date,
    test_type,
    model_name,
    test_category,
    total_tests,
    passed_tests,
    failed_tests,
    warning_tests,
    skipped_tests,
    total_failures,
    pass_rate,
    test_types_covered,
    total_tests_on_model,
    has_not_null_tests,
    has_unique_tests,
    has_relationship_tests,
    has_accepted_values_tests,
    reliability_score,
    coverage_score,
    
    -- Overall test health score
    round((reliability_score * 0.7 + coverage_score * 0.3), 0) as overall_test_health_score,
    
    -- Test status
    case 
        when pass_rate = 100 and coverage_score >= 80 then 'Excellent'
        when pass_rate >= 95 and coverage_score >= 60 then 'Good'
        when pass_rate >= 90 and coverage_score >= 40 then 'Fair'
        when pass_rate >= 80 then 'Poor'
        else 'Critical'
    end as test_health_status,
    
    latest_execution,
    current_timestamp() as health_check_timestamp
    
from health_scores
order by test_date desc, model_name, test_category
    )
;
[0m06:22:46.323519 [debug] [Thread-3 (]: On model.live_c360.query_history_health: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "node_id": "model.live_c360.query_history_health"} */
create or replace transient table MYDB.LIVE_DATA.query_history_health
    

    
    as (

-- ETL Health Check: Query History Monitoring
-- Tracks query performance, failures, and runtime metrics

with query_history as (
    select
        query_id,
        query_text,
        database_name,
        schema_name,
        query_type,
        session_id,
        user_name,
        role_name,
        warehouse_name,
        warehouse_size,
        warehouse_type,
        cluster_number,
        query_tag,
        execution_status,
        error_code,
        error_message,
        start_time,
        end_time,
        total_elapsed_time,
        bytes_scanned,
        percentage_scanned_from_cache,
        bytes_written,
        bytes_written_to_result,
        bytes_read_from_result,
        rows_produced,
        rows_inserted,
        rows_updated,
        rows_deleted,
        rows_unloaded,
        bytes_deleted,
        partitions_scanned,
        partitions_total,
        bytes_spilled_to_local_storage,
        bytes_spilled_to_remote_storage,
        bytes_sent_over_the_network,
        compilation_time,
        execution_time,
        queued_provisioning_time,
        queued_repair_time,
        queued_overload_time,
        transaction_blocked_time,
        outbound_data_transfer_cloud,
        outbound_data_transfer_region,
        outbound_data_transfer_bytes,
        inbound_data_transfer_cloud,
        inbound_data_transfer_region,
        inbound_data_transfer_bytes,
        list_external_files_time,
        credits_used_cloud_services
    from snowflake.account_usage.query_history
    where start_time >= current_date - 7  -- Last 7 days
      and database_name = 'MYDB'  -- Focus on our database
),

etl_queries as (
    select
        *,
        -- Categorize queries
        case 
            when query_text ilike '%dbt%' then 'dbt'
            when query_text ilike '%insert%' or query_text ilike '%update%' or query_text ilike '%merge%' then 'ETL_DML'
            when query_text ilike '%create table%' or query_text ilike '%create view%' then 'ETL_DDL'
            when query_text ilike '%copy into%' then 'Data_Load'
            when query_text ilike '%select%' and query_text not ilike '%insert%' then 'Analytics'
            else 'Other'
        end as query_category,
        
        -- Performance flags
        case when total_elapsed_time > 300000 then true else false end as is_long_running,  -- > 5 minutes
        case when execution_status = 'FAIL' then true else false end as is_failed,
        case when bytes_spilled_to_local_storage > 0 or bytes_spilled_to_remote_storage > 0 then true else false end as has_spill,
        case when percentage_scanned_from_cache < 50 then true else false end as low_cache_hit,
        
        -- Time segments
        date(start_time) as execution_date,
        extract(hour from start_time) as execution_hour,
        case 
            when extract(hour from start_time) between 6 and 11 then 'Morning'
            when extract(hour from start_time) between 12 and 17 then 'Afternoon'
            when extract(hour from start_time) between 18 and 22 then 'Evening'
            else 'Night'
        end as time_segment
        
    from query_history
),

health_metrics as (
    select
        execution_date,
        query_category,
        time_segment,
        warehouse_name,
        
        -- Count metrics
        count(*) as total_queries,
        sum(case when is_failed then 1 else 0 end) as failed_queries,
        sum(case when is_long_running then 1 else 0 end) as long_running_queries,
        sum(case when has_spill then 1 else 0 end) as queries_with_spill,
        sum(case when low_cache_hit then 1 else 0 end) as low_cache_queries,
        
        -- Performance metrics
        avg(total_elapsed_time) as avg_elapsed_time_ms,
        max(total_elapsed_time) as max_elapsed_time_ms,
        percentile_cont(0.95) within group (order by total_elapsed_time) as p95_elapsed_time_ms,
        
        -- Data metrics
        sum(bytes_scanned) as total_bytes_scanned,
        sum(rows_produced) as total_rows_produced,
        avg(percentage_scanned_from_cache) as avg_cache_hit_rate,
        
        -- Cost metrics
        sum(credits_used_cloud_services) as total_credits_used,
        
        -- Latest execution
        max(start_time) as latest_execution_time
        
    from etl_queries
    group by execution_date, query_category, time_segment, warehouse_name
)

select
    *,
    -- Health scores (0-100)
    case 
        when failed_queries = 0 then 100
        when failed_queries::float / total_queries <= 0.01 then 95
        when failed_queries::float / total_queries <= 0.05 then 80
        when failed_queries::float / total_queries <= 0.10 then 60
        else 30
    end as reliability_score,
    
    case 
        when avg_elapsed_time_ms <= 30000 then 100  -- < 30 seconds
        when avg_elapsed_time_ms <= 120000 then 80  -- < 2 minutes
        when avg_elapsed_time_ms <= 300000 then 60  -- < 5 minutes
        else 30
    end as performance_score,
    
    case 
        when avg_cache_hit_rate >= 80 then 100
        when avg_cache_hit_rate >= 60 then 80
        when avg_cache_hit_rate >= 40 then 60
        else 30
    end as efficiency_score,
    
    -- Overall health score
    round((
        (case when failed_queries = 0 then 100 when failed_queries::float / total_queries <= 0.01 then 95 when failed_queries::float / total_queries <= 0.05 then 80 when failed_queries::float / total_queries <= 0.10 then 60 else 30 end) * 0.4 +
        (case when avg_elapsed_time_ms <= 30000 then 100 when avg_elapsed_time_ms <= 120000 then 80 when avg_elapsed_time_ms <= 300000 then 60 else 30 end) * 0.3 +
        (case when avg_cache_hit_rate >= 80 then 100 when avg_cache_hit_rate >= 60 then 80 when avg_cache_hit_rate >= 40 then 60 else 30 end) * 0.3
    ), 0) as overall_health_score,
    
    current_timestamp() as health_check_timestamp
    
from health_metrics
order by execution_date desc, query_category, warehouse_name
    )
;
[0m06:22:46.318604 [debug] [Thread-4 (]: Using snowflake connection "model.live_c360.stg_events"
[0m06:22:46.357793 [debug] [Thread-4 (]: On model.live_c360.stg_events: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "node_id": "model.live_c360.stg_events"} */
create or replace   view MYDB.LIVE_DATA.stg_events
  
   as (
    

-- Staging model for live events data
-- Cleans and standardizes raw event data from S3 external table

with source_data as (
    select * from MYDB.LIVE_DATA.ext_live_events
),

cleaned_data as (
    select
        -- Primary key
        event_id,
        user_id,
        session_id,
        
        -- Event details
        try_to_timestamp(date, 'MM-DD-YYYY HH24:MI:SS') as event_timestamp,
        lower(trim(platform)) as platform,
        lower(trim(action)) as action,
        trim(url) as url,
        
        -- Metadata
        batch_id,
        try_to_timestamp(generated_at) as generated_at,
        current_timestamp() as processed_at
        
    from source_data
    where event_id is not null  -- Filter out any null event IDs
      and user_id is not null   -- Filter out events without user IDs
),

url_parsed as (
    select
        *,
        -- URL parsing
        case 
            when url like '%/product/%' then 'Product Page'
            when url like '%/cart%' then 'Cart'
            when url like '%/checkout%' then 'Checkout'
            when url like '%/search%' then 'Search'
            when url like '%/category/%' then 'Category'
            when url like '%/home%' or url = 'https://example.com/' then 'Home'
            else 'Other'
        end as page_type,
        
        -- Extract path from URL
        regexp_substr(url, 'https?://[^/]+(/.*)', 1, 1, 'e', 1) as url_path
        
    from cleaned_data
),

final as (
    select
        *,
        -- Date extractions
        date(event_timestamp) as event_date,
        extract(year from event_timestamp) as event_year,
        extract(month from event_timestamp) as event_month,
        extract(day from event_timestamp) as event_day,
        extract(hour from event_timestamp) as event_hour,
        dayname(event_timestamp) as event_day_name,
        
        -- Time-based segments
        case 
            when extract(hour from event_timestamp) between 6 and 11 then 'Morning'
            when extract(hour from event_timestamp) between 12 and 17 then 'Afternoon'
            when extract(hour from event_timestamp) between 18 and 22 then 'Evening'
            else 'Night'
        end as time_of_day_segment,
        
        -- Platform standardization
        case 
            when platform in ('ios', 'android') then 'Mobile'
            when platform = 'web' then 'Web'
            else 'Unknown'
        end as device_category,
        
        -- Action categorization
        case 
            when action in ('view', 'click') then 'Engagement'
            when action = 'log' then 'Authentication'
            when action = 'purchase' then 'Conversion'
            else 'Other'
        end as action_category,
        
        -- Funnel stage
        case 
            when action = 'view' and page_type = 'Home' then 'Awareness'
            when action = 'view' and page_type in ('Category', 'Search') then 'Interest'
            when action = 'view' and page_type = 'Product Page' then 'Consideration'
            when action = 'click' and page_type = 'Cart' then 'Intent'
            when action = 'purchase' then 'Purchase'
            else 'Other'
        end as funnel_stage,
        
        -- Data quality flags
        case 
            when event_timestamp > current_timestamp() then true
            else false
        end as has_future_event_timestamp,
        
        -- Recency
        datediff('minute', event_timestamp, current_timestamp()) as minutes_since_event,
        datediff('hour', event_timestamp, current_timestamp()) as hours_since_event,
        datediff('day', event_timestamp, current_timestamp()) as days_since_event
        
    from url_parsed
)

select * from final
  );
[0m06:22:46.364700 [debug] [Thread-4 (]: Opening a new connection, currently in state init
[0m06:22:46.754115 [debug] [Thread-4 (]: Snowflake adapter: Snowflake query id: 01bccc5e-3204-7f80-0002-4ad60006710a
[0m06:22:46.755413 [debug] [Thread-4 (]: Snowflake adapter: Snowflake error: 091093 (55000): External table EXT_LIVE_EVENTS marked invalid. Stage S3_LIVE_EVENTS_STAGE dropped.
[0m06:22:46.757865 [debug] [Thread-4 (]: Database Error in model stg_events (models/staging/stg_events.sql)
  091093 (55000): External table EXT_LIVE_EVENTS marked invalid. Stage S3_LIVE_EVENTS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_events.sql
[0m06:22:46.760730 [debug] [Thread-4 (]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '134c2024-6326-4965-b7b9-acbf4f1c9b6e', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7081c6ddce80>]}
[0m06:22:46.763205 [error] [Thread-4 (]: 4 of 12 ERROR creating sql view model LIVE_DATA.stg_events ..................... [[31mERROR[0m in 0.69s]
[0m06:22:46.766677 [debug] [Thread-4 (]: Finished running node model.live_c360.stg_events
[0m06:22:46.769705 [debug] [Thread-4 (]: Began running node model.live_c360.stg_orders
[0m06:22:46.772168 [debug] [Thread-7 (]: Marking all children of 'model.live_c360.stg_events' to be skipped because of status 'error'.  Reason: Database Error in model stg_events (models/staging/stg_events.sql)
  091093 (55000): External table EXT_LIVE_EVENTS marked invalid. Stage S3_LIVE_EVENTS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_events.sql.
[0m06:22:46.778526 [info ] [Thread-4 (]: 5 of 12 START sql view model LIVE_DATA.stg_orders .............................. [RUN]
[0m06:22:46.807589 [debug] [Thread-4 (]: Re-using an available connection from the pool (formerly model.live_c360.stg_events, now model.live_c360.stg_orders)
[0m06:22:46.815340 [debug] [Thread-4 (]: Began compiling node model.live_c360.stg_orders
[0m06:22:46.823293 [debug] [Thread-4 (]: Writing injected SQL for node "model.live_c360.stg_orders"
[0m06:22:46.838431 [debug] [Thread-4 (]: Began executing node model.live_c360.stg_orders
[0m06:22:46.844360 [debug] [Thread-4 (]: Writing runtime sql for node "model.live_c360.stg_orders"
[0m06:22:46.863451 [debug] [Thread-4 (]: Using snowflake connection "model.live_c360.stg_orders"
[0m06:22:46.869153 [debug] [Thread-4 (]: On model.live_c360.stg_orders: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "node_id": "model.live_c360.stg_orders"} */
create or replace   view MYDB.LIVE_DATA.stg_orders
  
   as (
    

-- Staging model for live orders data
-- Cleans and standardizes raw order data from S3 external table

with source_data as (
    select * from MYDB.LIVE_DATA.ext_live_orders
),

cleaned_data as (
    select
        -- Primary key
        id as order_id,
        user_id,
        
        -- Transaction details
        try_to_timestamp(transaction_date, 'MM-DD-YYYY HH24:MI:SS') as transaction_date,
        item_count,
        amount,
        
        -- Metadata
        batch_id,
        try_to_timestamp(generated_at) as generated_at,
        current_timestamp() as processed_at
        
    from source_data
    where id is not null  -- Filter out any null order IDs
      and user_id is not null  -- Filter out orders without user IDs
      and amount > 0  -- Filter out invalid amounts
),

final as (
    select
        *,
        -- Derived fields
        round(amount / item_count, 2) as avg_item_price,
        
        -- Date extractions
        date(transaction_date) as transaction_date_only,
        extract(year from transaction_date) as transaction_year,
        extract(month from transaction_date) as transaction_month,
        extract(day from transaction_date) as transaction_day,
        extract(hour from transaction_date) as transaction_hour,
        dayname(transaction_date) as transaction_day_name,
        
        -- Time-based segments
        case 
            when extract(hour from transaction_date) between 6 and 11 then 'Morning'
            when extract(hour from transaction_date) between 12 and 17 then 'Afternoon'
            when extract(hour from transaction_date) between 18 and 22 then 'Evening'
            else 'Night'
        end as time_of_day_segment,
        
        -- Order size segments
        case 
            when amount < 25 then 'Small'
            when amount < 75 then 'Medium'
            when amount < 150 then 'Large'
            else 'Extra Large'
        end as order_size_segment,
        
        -- Item quantity segments
        case 
            when item_count = 1 then 'Single Item'
            when item_count <= 3 then 'Few Items'
            when item_count <= 5 then 'Multiple Items'
            else 'Bulk Order'
        end as quantity_segment,
        
        -- Data quality flags
        case 
            when transaction_date > current_timestamp() then true
            else false
        end as has_future_transaction_date,
        
        case 
            when avg_item_price > 500 then true  -- Flag unusually expensive items
            else false
        end as has_high_item_price,
        
        -- Recency
        datediff('day', transaction_date, current_timestamp()) as days_since_transaction
        
    from cleaned_data
)

select * from final
  );
[0m06:22:47.037105 [debug] [Thread-4 (]: Snowflake adapter: Snowflake query id: 01bccc5e-3204-7fc5-0002-4ad60006319e
[0m06:22:47.038489 [debug] [Thread-4 (]: Snowflake adapter: Snowflake error: 091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
[0m06:22:47.040866 [debug] [Thread-4 (]: Database Error in model stg_orders (models/staging/stg_orders.sql)
  091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_orders.sql
[0m06:22:47.042184 [debug] [Thread-4 (]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '134c2024-6326-4965-b7b9-acbf4f1c9b6e', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7081f11588e0>]}
[0m06:22:47.043806 [error] [Thread-4 (]: 5 of 12 ERROR creating sql view model LIVE_DATA.stg_orders ..................... [[31mERROR[0m in 0.24s]
[0m06:22:47.046111 [debug] [Thread-4 (]: Finished running node model.live_c360.stg_orders
[0m06:22:47.047456 [debug] [Thread-4 (]: Began running node model.live_c360.stg_users
[0m06:22:47.048303 [debug] [Thread-7 (]: Marking all children of 'model.live_c360.stg_orders' to be skipped because of status 'error'.  Reason: Database Error in model stg_orders (models/staging/stg_orders.sql)
  091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_orders.sql.
[0m06:22:47.049561 [info ] [Thread-4 (]: 6 of 12 START sql view model LIVE_DATA.stg_users ............................... [RUN]
[0m06:22:47.051999 [debug] [Thread-4 (]: Re-using an available connection from the pool (formerly model.live_c360.stg_orders, now model.live_c360.stg_users)
[0m06:22:47.053372 [debug] [Thread-4 (]: Began compiling node model.live_c360.stg_users
[0m06:22:47.059566 [debug] [Thread-4 (]: Writing injected SQL for node "model.live_c360.stg_users"
[0m06:22:47.067317 [debug] [Thread-4 (]: Began executing node model.live_c360.stg_users
[0m06:22:47.072856 [debug] [Thread-4 (]: Writing runtime sql for node "model.live_c360.stg_users"
[0m06:22:47.081907 [debug] [Thread-4 (]: Using snowflake connection "model.live_c360.stg_users"
[0m06:22:47.083089 [debug] [Thread-4 (]: On model.live_c360.stg_users: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "node_id": "model.live_c360.stg_users"} */
create or replace   view MYDB.LIVE_DATA.stg_users
  
   as (
    

-- Staging model for live users data
-- Cleans and standardizes raw user data from S3 external table

with source_data as (
    select * from MYDB.LIVE_DATA.ext_live_users
),

cleaned_data as (
    select
        -- Primary key
        id as user_id,
        
        -- Personal information (anonymized)
        sha1(email) as email_hash,
        initcap(trim(firstname)) as firstname,
        initcap(trim(lastname)) as lastname,
        trim(address) as address,
        upper(trim(canal)) as acquisition_channel,
        upper(trim(country)) as country,
        
        -- Dates
        try_to_timestamp(creation_date, 'MM-DD-YYYY HH24:MI:SS') as creation_date,
        try_to_timestamp(last_activity_date, 'MM-DD-YYYY HH24:MI:SS') as last_activity_date,
        
        -- Demographics
        case 
            when gender = 0 then 'F'
            when gender = 1 then 'M'
            else 'Unknown'
        end as gender,
        age_group,
        
        -- Behavioral flags
        churn as is_churned,
        
        -- Metadata
        batch_id,
        try_to_timestamp(generated_at) as generated_at,
        current_timestamp() as processed_at
        
    from source_data
    where id is not null  -- Filter out any null user IDs
),

final as (
    select
        *,
        -- Derived fields
        datediff('day', creation_date, current_timestamp()) as days_since_creation,
        datediff('day', last_activity_date, current_timestamp()) as days_since_last_activity,
        
        -- Data quality flags
        case 
            when creation_date > current_timestamp() then true
            else false
        end as has_future_creation_date,
        
        case 
            when last_activity_date < creation_date then true
            else false
        end as has_invalid_activity_date,
        
        -- Segmentation
        case 
            when days_since_last_activity <= 7 then 'Active'
            when days_since_last_activity <= 30 then 'Recent'
            when days_since_last_activity <= 90 then 'Dormant'
            else 'Inactive'
        end as activity_segment
        
    from cleaned_data
)

select * from final
  );
[0m06:22:47.197680 [debug] [Thread-4 (]: Snowflake adapter: Snowflake query id: 01bccc5e-3204-802a-0002-4ad600069086
[0m06:22:47.199441 [debug] [Thread-4 (]: Snowflake adapter: Snowflake error: 091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
[0m06:22:47.207647 [debug] [Thread-4 (]: Database Error in model stg_users (models/staging/stg_users.sql)
  091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_users.sql
[0m06:22:47.209082 [debug] [Thread-4 (]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '134c2024-6326-4965-b7b9-acbf4f1c9b6e', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7081c6ddc2e0>]}
[0m06:22:47.211797 [error] [Thread-4 (]: 6 of 12 ERROR creating sql view model LIVE_DATA.stg_users ...................... [[31mERROR[0m in 0.16s]
[0m06:22:47.216808 [debug] [Thread-4 (]: Finished running node model.live_c360.stg_users
[0m06:22:47.218201 [debug] [Thread-4 (]: Began running node model.live_c360.fact_orders
[0m06:22:47.220422 [debug] [Thread-7 (]: Marking all children of 'model.live_c360.stg_users' to be skipped because of status 'error'.  Reason: Database Error in model stg_users (models/staging/stg_users.sql)
  091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_users.sql.
[0m06:22:47.229506 [info ] [Thread-4 (]: 7 of 12 SKIP relation LIVE_DATA.fact_orders .................................... [[33mSKIP[0m]
[0m06:22:47.238014 [debug] [Thread-4 (]: Finished running node model.live_c360.fact_orders
[0m06:22:47.239350 [debug] [Thread-4 (]: Began running node model.live_c360.mv_fact_orders
[0m06:22:47.240574 [info ] [Thread-4 (]: 8 of 12 SKIP relation LIVE_DATA_ANALYTICS.mv_fact_orders ....................... [[33mSKIP[0m]
[0m06:22:47.241725 [debug] [Thread-4 (]: Finished running node model.live_c360.mv_fact_orders
[0m06:22:47.242843 [debug] [Thread-4 (]: Began running node model.live_c360.dim_users
[0m06:22:47.244291 [info ] [Thread-4 (]: 9 of 12 SKIP relation LIVE_DATA.dim_users ...................................... [[33mSKIP[0m]
[0m06:22:47.245589 [debug] [Thread-4 (]: Finished running node model.live_c360.dim_users
[0m06:22:47.250614 [debug] [Thread-4 (]: Began running node model.live_c360.dim_users_scd2
[0m06:22:47.253198 [info ] [Thread-4 (]: 10 of 12 SKIP relation LIVE_DATA.dim_users_scd2 ................................ [[33mSKIP[0m]
[0m06:22:47.254545 [debug] [Thread-4 (]: Finished running node model.live_c360.dim_users_scd2
[0m06:22:47.255655 [debug] [Thread-4 (]: Began running node model.live_c360.data_quality_health
[0m06:22:47.256811 [info ] [Thread-4 (]: 11 of 12 SKIP relation LIVE_DATA.data_quality_health ........................... [[33mSKIP[0m]
[0m06:22:47.257968 [debug] [Thread-4 (]: Finished running node model.live_c360.data_quality_health
[0m06:22:47.259207 [debug] [Thread-4 (]: Began running node model.live_c360.etl_health_dashboard
[0m06:22:47.260376 [info ] [Thread-4 (]: 12 of 12 SKIP relation LIVE_DATA.etl_health_dashboard .......................... [[33mSKIP[0m]
[0m06:22:47.263790 [debug] [Thread-4 (]: Finished running node model.live_c360.etl_health_dashboard
[0m06:22:47.468859 [debug] [Thread-1 (]: SQL status: SUCCESS 1 in 1.132 seconds
[0m06:22:47.502279 [debug] [Thread-1 (]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '134c2024-6326-4965-b7b9-acbf4f1c9b6e', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7081f2496920>]}
[0m06:22:47.503949 [info ] [Thread-1 (]: 1 of 12 OK created sql table model LIVE_DATA.dbt_test_health ................... [[32mSUCCESS 1[0m in 1.44s]
[0m06:22:47.505566 [debug] [Thread-1 (]: Finished running node model.live_c360.dbt_test_health
[0m06:22:48.173187 [warn ] [MainThread]: [[33mWARNING[0m]: Configuration paths exist in your dbt_project.yml file which do not apply to any resources.
There are 3 unused configuration paths:
- models.live_c360.incremental
- seeds.live_c360
- snapshots.live_c360
[0m06:22:48.196279 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'load_project', 'label': 'e8e8d900-9afe-473d-a898-2ffdf34ccbbc', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7902f7ffb1f0>]}
[0m06:22:48.204289 [info ] [MainThread]: Performance info: /opt/airflow/workspace/dbt_live/target/perf_info.json
[0m06:22:48.370171 [debug] [MainThread]: Wrote artifact WritableManifest to /opt/airflow/workspace/dbt_live/target/manifest.json
[0m06:22:48.375613 [debug] [MainThread]: Wrote artifact SemanticManifest to /opt/airflow/workspace/dbt_live/target/semantic_manifest.json
[0m06:22:48.377279 [debug] [MainThread]: Resource report: {"command_name": "parse", "command_success": true, "command_wall_clock_time": 6.117766, "process_in_blocks": "0", "process_kernel_time": 0.582691, "process_mem_max_rss": "193444", "process_out_blocks": "0", "process_user_time": 6.774345}
[0m06:22:48.378453 [debug] [MainThread]: Command `dbt parse` succeeded at 06:22:48.378309 after 6.12 seconds
[0m06:22:48.381171 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7903102827d0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7902f7f08070>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7902f7f0a3b0>]}
[0m06:22:48.382330 [debug] [MainThread]: Flushing usage events
[0m06:22:49.275048 [debug] [MainThread]: An error was encountered while trying to flush usage events
[0m06:22:49.508458 [debug] [Thread-3 (]: SQL status: SUCCESS 1 in 3.157 seconds
[0m06:22:49.512483 [debug] [Thread-3 (]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '134c2024-6326-4965-b7b9-acbf4f1c9b6e', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7081c6d8fbe0>]}
[0m06:22:49.515046 [info ] [Thread-3 (]: 3 of 12 OK created sql table model LIVE_DATA.query_history_health .............. [[32mSUCCESS 1[0m in 3.45s]
[0m06:22:49.516448 [debug] [Thread-3 (]: Finished running node model.live_c360.query_history_health
[0m06:22:49.521200 [debug] [Thread-2 (]: SQL status: SUCCESS 1 in 3.201 seconds
[0m06:22:49.525536 [debug] [Thread-2 (]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '134c2024-6326-4965-b7b9-acbf4f1c9b6e', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7081c446cdf0>]}
[0m06:22:49.527081 [info ] [Thread-2 (]: 2 of 12 OK created sql table model LIVE_DATA.pipeline_runtime_health ........... [[32mSUCCESS 1[0m in 3.46s]
[0m06:22:49.529446 [debug] [Thread-2 (]: Finished running node model.live_c360.pipeline_runtime_health
[0m06:22:49.532581 [debug] [MainThread]: Connection 'master' was properly closed.
[0m06:22:49.533640 [debug] [MainThread]: Connection 'model.live_c360.dbt_test_health' was left open.
[0m06:22:49.534657 [debug] [MainThread]: On model.live_c360.dbt_test_health: Close
[0m06:22:49.586895 [debug] [MainThread]: Connection 'model.live_c360.query_history_health' was left open.
[0m06:22:49.588694 [debug] [MainThread]: On model.live_c360.query_history_health: Close
[0m06:22:49.649453 [debug] [MainThread]: Connection 'model.live_c360.pipeline_runtime_health' was left open.
[0m06:22:49.651060 [debug] [MainThread]: On model.live_c360.pipeline_runtime_health: Close
[0m06:22:49.712371 [debug] [MainThread]: Connection 'model.live_c360.stg_users' was left open.
[0m06:22:49.713846 [debug] [MainThread]: On model.live_c360.stg_users: Close
[0m06:22:49.777096 [info ] [MainThread]: 
[0m06:22:49.778633 [info ] [MainThread]: Finished running 1 incremental model, 7 table models, 4 view models in 0 hours 0 minutes and 5.28 seconds (5.28s).
[0m06:22:49.781175 [debug] [MainThread]: Command end result
[0m06:22:49.851736 [debug] [MainThread]: Wrote artifact WritableManifest to /opt/airflow/workspace/dbt_live/target/manifest.json
[0m06:22:49.858801 [debug] [MainThread]: Wrote artifact SemanticManifest to /opt/airflow/workspace/dbt_live/target/semantic_manifest.json
[0m06:22:49.871586 [debug] [MainThread]: Wrote artifact RunExecutionResult to /opt/airflow/workspace/dbt_live/target/run_results.json
[0m06:22:49.872717 [info ] [MainThread]: 
[0m06:22:49.873695 [info ] [MainThread]: [31mCompleted with 3 errors, 0 partial successes, and 0 warnings:[0m
[0m06:22:49.874688 [info ] [MainThread]: 
[0m06:22:49.875705 [error] [MainThread]:   Database Error in model stg_events (models/staging/stg_events.sql)
  091093 (55000): External table EXT_LIVE_EVENTS marked invalid. Stage S3_LIVE_EVENTS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_events.sql
[0m06:22:49.876884 [info ] [MainThread]: 
[0m06:22:49.877892 [error] [MainThread]:   Database Error in model stg_orders (models/staging/stg_orders.sql)
  091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_orders.sql
[0m06:22:49.879002 [info ] [MainThread]: 
[0m06:22:49.880134 [error] [MainThread]:   Database Error in model stg_users (models/staging/stg_users.sql)
  091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_users.sql
[0m06:22:49.881088 [info ] [MainThread]: 
[0m06:22:49.881976 [info ] [MainThread]: Done. PASS=3 WARN=0 ERROR=3 SKIP=6 TOTAL=12
[0m06:22:49.883620 [debug] [MainThread]: Resource report: {"command_name": "run", "command_success": false, "command_wall_clock_time": 11.329986, "process_in_blocks": "0", "process_kernel_time": 1.060249, "process_mem_max_rss": "212892", "process_out_blocks": "16", "process_user_time": 8.389608}
[0m06:22:49.886820 [debug] [MainThread]: Command `dbt run` failed at 06:22:49.886642 after 11.33 seconds
[0m06:22:49.887997 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7081f10c6860>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7081f0f23430>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7081c43d06d0>]}
[0m06:22:49.889059 [debug] [MainThread]: Flushing usage events
[0m06:22:50.965155 [debug] [MainThread]: An error was encountered while trying to flush usage events
[0m06:27:53.625032 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7947ed79a890>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7947ec6dc370>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7947ec6dca00>]}


============================== 06:27:53.631914 | 74dcd8a1-9abc-4250-b219-2842a0a70add ==============================
[0m06:27:53.631914 [info ] [MainThread]: Running with dbt=1.9.6
[0m06:27:53.633260 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'write_json': 'True', 'log_cache_events': 'False', 'partial_parse': 'True', 'cache_selected_only': 'False', 'profiles_dir': '/opt/airflow/workspace/dbt_live', 'fail_fast': 'False', 'debug': 'False', 'log_path': '/opt/airflow/workspace/dbt_live/logs', 'version_check': 'True', 'warn_error': 'None', 'use_colors': 'True', 'use_experimental_parser': 'False', 'no_print': 'None', 'quiet': 'False', 'empty': 'False', 'log_format': 'default', 'invocation_command': 'dbt run', 'warn_error_options': 'WarnErrorOptions(include=[], exclude=[])', 'static_parser': 'True', 'target_path': 'None', 'introspect': 'True', 'send_anonymous_usage_stats': 'True'}
[0m06:27:54.448237 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'project_id', 'label': '74dcd8a1-9abc-4250-b219-2842a0a70add', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7947ec6dd570>]}
[0m06:27:54.531286 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'adapter_info', 'label': '74dcd8a1-9abc-4250-b219-2842a0a70add', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7947ee82d330>]}
[0m06:27:54.533186 [info ] [MainThread]: Registered adapter: snowflake=1.9.4
[0m06:27:54.709963 [debug] [MainThread]: checksum: dd6dc1e5178459e3de3bf2eeb7c86bed4be2266c311fce8c15d86eb4ff94e7ad, vars: {}, profile: , target: , version: 1.9.6
[0m06:27:55.837728 [debug] [MainThread]: Partial parsing enabled: 0 files deleted, 0 files added, 0 files changed.
[0m06:27:55.839203 [debug] [MainThread]: Partial parsing enabled, no changes found, skipping parsing
[0m06:27:55.847771 [warn ] [MainThread]: [[33mWARNING[0m]: Configuration paths exist in your dbt_project.yml file which do not apply to any resources.
There are 3 unused configuration paths:
- models.live_c360.incremental
- seeds.live_c360
- snapshots.live_c360
[0m06:27:55.909506 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'load_project', 'label': '74dcd8a1-9abc-4250-b219-2842a0a70add', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7947d5660130>]}
[0m06:27:56.076671 [debug] [MainThread]: Wrote artifact WritableManifest to /opt/airflow/workspace/dbt_live/target/manifest.json
[0m06:27:56.084486 [debug] [MainThread]: Wrote artifact SemanticManifest to /opt/airflow/workspace/dbt_live/target/semantic_manifest.json
[0m06:27:56.116376 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'resource_counts', 'label': '74dcd8a1-9abc-4250-b219-2842a0a70add', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7947d54ac2e0>]}
[0m06:27:56.117734 [info ] [MainThread]: Found 12 models, 21 data tests, 3 sources, 590 macros
[0m06:27:56.118878 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'runnable_timing', 'label': '74dcd8a1-9abc-4250-b219-2842a0a70add', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7947d54ac3d0>]}
[0m06:27:56.122293 [info ] [MainThread]: 
[0m06:27:56.123598 [info ] [MainThread]: Concurrency: 4 threads (target='live')
[0m06:27:56.124611 [info ] [MainThread]: 
[0m06:27:56.125948 [debug] [MainThread]: Acquiring new snowflake connection 'master'
[0m06:27:56.134575 [debug] [ThreadPool]: Acquiring new snowflake connection 'list_MYDB'
[0m06:27:56.135493 [debug] [ThreadPool]: Acquiring new snowflake connection 'list_MYDB'
[0m06:27:56.188420 [debug] [ThreadPool]: Using snowflake connection "list_MYDB"
[0m06:27:56.189179 [debug] [ThreadPool]: Using snowflake connection "list_MYDB"
[0m06:27:56.190145 [debug] [ThreadPool]: On list_MYDB: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB"} */
show terse schemas in database MYDB
    limit 10000
[0m06:27:56.193127 [debug] [ThreadPool]: On list_MYDB: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB"} */
show terse schemas in database MYDB
    limit 10000
[0m06:27:56.194431 [debug] [ThreadPool]: Opening a new connection, currently in state init
[0m06:27:56.195934 [debug] [ThreadPool]: Opening a new connection, currently in state init
[0m06:27:56.836389 [debug] [ThreadPool]: SQL status: SUCCESS 6 in 0.642 seconds
[0m06:27:56.863153 [debug] [ThreadPool]: SQL status: SUCCESS 6 in 0.667 seconds
[0m06:27:56.871943 [debug] [ThreadPool]: Re-using an available connection from the pool (formerly list_MYDB, now list_MYDB_LIVE_DATA_dbt_test__audit)
[0m06:27:56.873797 [debug] [ThreadPool]: Re-using an available connection from the pool (formerly list_MYDB, now list_MYDB_LIVE_DATA)
[0m06:27:56.877515 [debug] [ThreadPool]: Acquiring new snowflake connection 'list_MYDB_LIVE_DATA_ANALYTICS'
[0m06:27:56.898820 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_dbt_test__audit"
[0m06:27:56.902828 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA"
[0m06:27:56.913444 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_ANALYTICS"
[0m06:27:56.914506 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_dbt_test__audit: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_dbt_test__audit"} */
alter session set quoted_identifiers_ignore_case = false;
[0m06:27:56.915587 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA"} */
alter session set quoted_identifiers_ignore_case = false;
[0m06:27:56.916585 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_ANALYTICS: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_ANALYTICS"} */
alter session set quoted_identifiers_ignore_case = false;
[0m06:27:56.922867 [debug] [ThreadPool]: Opening a new connection, currently in state init
[0m06:27:56.994222 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.077 seconds
[0m06:27:57.000606 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_dbt_test__audit"
[0m06:27:57.002155 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_dbt_test__audit: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_dbt_test__audit"} */
show objects in MYDB.LIVE_DATA_dbt_test__audit
    limit 10000
    
;
[0m06:27:57.052825 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.133 seconds
[0m06:27:57.055802 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA"
[0m06:27:57.057079 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA"} */
show objects in MYDB.LIVE_DATA
    limit 10000
    
;
[0m06:27:57.118466 [debug] [ThreadPool]: SQL status: SUCCESS 21 in 0.115 seconds
[0m06:27:57.122415 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_dbt_test__audit"
[0m06:27:57.125260 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_dbt_test__audit: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_dbt_test__audit"} */
alter session unset quoted_identifiers_ignore_case;
[0m06:27:57.166143 [debug] [ThreadPool]: SQL status: SUCCESS 14 in 0.108 seconds
[0m06:27:57.169363 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA"
[0m06:27:57.171899 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA"} */
alter session unset quoted_identifiers_ignore_case;
[0m06:27:57.207321 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.081 seconds
[0m06:27:57.266258 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.093 seconds
[0m06:27:57.414901 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.492 seconds
[0m06:27:57.417650 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_ANALYTICS"
[0m06:27:57.420846 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_ANALYTICS: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_ANALYTICS"} */
show objects in MYDB.LIVE_DATA_ANALYTICS
    limit 10000
    
;
[0m06:27:57.505085 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.082 seconds
[0m06:27:57.508201 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_ANALYTICS"
[0m06:27:57.509603 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_ANALYTICS: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_ANALYTICS"} */
alter session unset quoted_identifiers_ignore_case;
[0m06:27:57.566693 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.056 seconds
[0m06:27:57.570875 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'runnable_timing', 'label': '74dcd8a1-9abc-4250-b219-2842a0a70add', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7947ee8ef8e0>]}
[0m06:27:57.578067 [debug] [Thread-1 (]: Began running node model.live_c360.dbt_test_health
[0m06:27:57.578838 [debug] [Thread-2 (]: Began running node model.live_c360.pipeline_runtime_health
[0m06:27:57.579564 [debug] [Thread-3 (]: Began running node model.live_c360.query_history_health
[0m06:27:57.580258 [debug] [Thread-4 (]: Began running node model.live_c360.stg_events
[0m06:27:57.581472 [info ] [Thread-1 (]: 1 of 12 START sql table model LIVE_DATA.dbt_test_health ........................ [RUN]
[0m06:27:57.583452 [info ] [Thread-2 (]: 2 of 12 START sql table model LIVE_DATA.pipeline_runtime_health ................ [RUN]
[0m06:27:57.585287 [info ] [Thread-3 (]: 3 of 12 START sql table model LIVE_DATA.query_history_health ................... [RUN]
[0m06:27:57.587355 [info ] [Thread-4 (]: 4 of 12 START sql view model LIVE_DATA.stg_events .............................. [RUN]
[0m06:27:57.590839 [debug] [Thread-1 (]: Re-using an available connection from the pool (formerly list_MYDB_LIVE_DATA_ANALYTICS, now model.live_c360.dbt_test_health)
[0m06:27:57.592522 [debug] [Thread-2 (]: Re-using an available connection from the pool (formerly list_MYDB_LIVE_DATA, now model.live_c360.pipeline_runtime_health)
[0m06:27:57.594350 [debug] [Thread-3 (]: Re-using an available connection from the pool (formerly list_MYDB_LIVE_DATA_dbt_test__audit, now model.live_c360.query_history_health)
[0m06:27:57.596827 [debug] [Thread-4 (]: Acquiring new snowflake connection 'model.live_c360.stg_events'
[0m06:27:57.598614 [debug] [Thread-1 (]: Began compiling node model.live_c360.dbt_test_health
[0m06:27:57.600457 [debug] [Thread-2 (]: Began compiling node model.live_c360.pipeline_runtime_health
[0m06:27:57.602081 [debug] [Thread-3 (]: Began compiling node model.live_c360.query_history_health
[0m06:27:57.603372 [debug] [Thread-4 (]: Began compiling node model.live_c360.stg_events
[0m06:27:57.618210 [debug] [Thread-1 (]: Writing injected SQL for node "model.live_c360.dbt_test_health"
[0m06:27:57.624932 [debug] [Thread-2 (]: Writing injected SQL for node "model.live_c360.pipeline_runtime_health"
[0m06:27:57.631564 [debug] [Thread-3 (]: Writing injected SQL for node "model.live_c360.query_history_health"
[0m06:27:57.642971 [debug] [Thread-4 (]: Writing injected SQL for node "model.live_c360.stg_events"
[0m06:27:57.656658 [debug] [Thread-2 (]: Began executing node model.live_c360.pipeline_runtime_health
[0m06:27:57.659579 [debug] [Thread-3 (]: Began executing node model.live_c360.query_history_health
[0m06:27:57.669477 [debug] [Thread-1 (]: Began executing node model.live_c360.dbt_test_health
[0m06:27:57.692328 [debug] [Thread-4 (]: Began executing node model.live_c360.stg_events
[0m06:27:57.708780 [debug] [Thread-2 (]: Writing runtime sql for node "model.live_c360.pipeline_runtime_health"
[0m06:27:57.713335 [debug] [Thread-3 (]: Writing runtime sql for node "model.live_c360.query_history_health"
[0m06:27:57.720963 [debug] [Thread-1 (]: Writing runtime sql for node "model.live_c360.dbt_test_health"
[0m06:27:57.751112 [debug] [Thread-4 (]: Writing runtime sql for node "model.live_c360.stg_events"
[0m06:27:57.770063 [debug] [Thread-2 (]: Using snowflake connection "model.live_c360.pipeline_runtime_health"
[0m06:27:57.766358 [debug] [Thread-1 (]: Using snowflake connection "model.live_c360.dbt_test_health"
[0m06:27:57.777070 [debug] [Thread-2 (]: On model.live_c360.pipeline_runtime_health: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "node_id": "model.live_c360.pipeline_runtime_health"} */
create or replace transient table MYDB.LIVE_DATA.pipeline_runtime_health
    

    
    as (

-- ETL Health Check: Pipeline Runtime Monitoring
-- Tracks pipeline execution times, success rates, and performance trends

with dbt_run_history as (
    -- Extract dbt-specific queries from query history
    select
        query_id,
        query_text,
        start_time,
        end_time,
        total_elapsed_time,
        execution_status,
        error_message,
        warehouse_name,
        user_name,
        
        -- Extract model name from dbt queries
        case 
            when query_text ilike '%create or replace%view%stg_%' then 
                regexp_substr(query_text, 'view\\s+[^.]+\\.([^\\s]+)', 1, 1, 'i', 1)
            when query_text ilike '%create or replace%table%dim_%' then 
                regexp_substr(query_text, 'table\\s+[^.]+\\.([^\\s]+)', 1, 1, 'i', 1)
            when query_text ilike '%create or replace%table%fact_%' then 
                regexp_substr(query_text, 'table\\s+[^.]+\\.([^\\s]+)', 1, 1, 'i', 1)
            when query_text ilike '%merge into%' then 
                regexp_substr(query_text, 'merge\\s+into\\s+[^.]+\\.([^\\s]+)', 1, 1, 'i', 1)
            else 'unknown'
        end as model_name,
        
        -- Categorize model types
        case 
            when query_text ilike '%stg_%' then 'staging'
            when query_text ilike '%dim_%' then 'dimension'
            when query_text ilike '%fact_%' then 'fact'
            when query_text ilike '%mv_%' then 'materialized_view'
            else 'other'
        end as model_type,
        
        date(start_time) as execution_date,
        extract(hour from start_time) as execution_hour
        
    from snowflake.account_usage.query_history
    where start_time >= current_date - 7
      and database_name = 'MYDB'
      and (query_text ilike '%dbt%' or query_text ilike '%create%table%' or query_text ilike '%create%view%')
      and query_text not ilike '%information_schema%'
      and query_text not ilike '%show%'
),

pipeline_runs as (
    -- Group queries into pipeline runs (by date and hour)
    select
        execution_date,
        execution_hour,
        model_type,
        warehouse_name,
        
        -- Run metrics
        count(*) as models_executed,
        sum(case when execution_status = 'SUCCESS' then 1 else 0 end) as successful_models,
        sum(case when execution_status != 'SUCCESS' then 1 else 0 end) as failed_models,
        
        -- Timing metrics
        sum(total_elapsed_time) as total_pipeline_time_ms,
        avg(total_elapsed_time) as avg_model_time_ms,
        max(total_elapsed_time) as max_model_time_ms,
        min(start_time) as pipeline_start_time,
        max(end_time) as pipeline_end_time,
        
        -- Calculate actual pipeline duration
        datediff('millisecond', min(start_time), max(end_time)) as pipeline_duration_ms,
        
        -- Error details
        listagg(distinct error_message, '; ') within group (order by error_message) as error_messages
        
    from dbt_run_history
    where model_name != 'unknown'
    group by execution_date, execution_hour, model_type, warehouse_name
),

model_performance as (
    -- Individual model performance tracking
    select
        model_name,
        model_type,
        execution_date,
        
        -- Performance metrics
        count(*) as execution_count,
        avg(total_elapsed_time) as avg_execution_time_ms,
        max(total_elapsed_time) as max_execution_time_ms,
        min(total_elapsed_time) as min_execution_time_ms,
        stddev(total_elapsed_time) as stddev_execution_time_ms,
        
        -- Success rate
        round((sum(case when execution_status = 'SUCCESS' then 1 else 0 end)::float / count(*)) * 100, 2) as success_rate,
        
        -- Latest execution
        max(start_time) as latest_execution_time,
        
        -- Performance trend (compare to previous day)
        lag(avg(total_elapsed_time)) over (partition by model_name order by execution_date) as prev_day_avg_time
        
    from dbt_run_history
    where model_name != 'unknown'
    group by model_name, model_type, execution_date
),

health_metrics as (
    select
        pr.*,
        
        -- Success rate
        round((successful_models::float / models_executed) * 100, 2) as pipeline_success_rate,
        
        -- Performance scores
        case 
            when avg_model_time_ms <= 30000 then 100  -- < 30 seconds
            when avg_model_time_ms <= 120000 then 80  -- < 2 minutes
            when avg_model_time_ms <= 300000 then 60  -- < 5 minutes
            else 30
        end as performance_score,
        
        case 
            when pipeline_success_rate = 100 then 100
            when pipeline_success_rate >= 95 then 90
            when pipeline_success_rate >= 90 then 80
            when pipeline_success_rate >= 80 then 70
            else 50
        end as reliability_score,
        
        -- Duration score (based on total pipeline time)
        case 
            when pipeline_duration_ms <= 300000 then 100  -- < 5 minutes
            when pipeline_duration_ms <= 600000 then 80   -- < 10 minutes
            when pipeline_duration_ms <= 1200000 then 60  -- < 20 minutes
            else 30
        end as duration_score
        
    from pipeline_runs pr
)

select
    execution_date,
    execution_hour,
    model_type,
    warehouse_name,
    models_executed,
    successful_models,
    failed_models,
    pipeline_success_rate,
    total_pipeline_time_ms,
    avg_model_time_ms,
    max_model_time_ms,
    pipeline_duration_ms,
    pipeline_start_time,
    pipeline_end_time,
    performance_score,
    reliability_score,
    duration_score,
    
    -- Overall pipeline health score
    round((reliability_score * 0.5 + performance_score * 0.3 + duration_score * 0.2), 0) as overall_pipeline_health_score,
    
    -- Pipeline status
    case 
        when failed_models = 0 and performance_score >= 80 then 'Healthy'
        when failed_models = 0 and performance_score >= 60 then 'Good'
        when failed_models <= 1 and performance_score >= 60 then 'Fair'
        when failed_models <= 2 then 'Poor'
        else 'Critical'
    end as pipeline_status,
    
    error_messages,
    current_timestamp() as health_check_timestamp
    
from health_metrics
order by execution_date desc, execution_hour desc, model_type
    )
;
[0m06:27:57.776203 [debug] [Thread-3 (]: Using snowflake connection "model.live_c360.query_history_health"
[0m06:27:57.779775 [debug] [Thread-1 (]: On model.live_c360.dbt_test_health: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "node_id": "model.live_c360.dbt_test_health"} */
create or replace transient table MYDB.LIVE_DATA.dbt_test_health
    

    
    as (

-- ETL Health Check: dbt Test Results Monitoring
-- Tracks dbt test execution and results

with test_metadata as (
    -- This would typically come from dbt artifacts or a custom logging solution
    -- For now, we'll create a mock structure based on our known tests
    select 'source_not_null_live_external_ext_live_users_id' as test_name,
           'source' as test_type,
           'ext_live_users' as model_name,
           'id' as column_name,
           'not_null' as test_category,
           'pass' as status,
           0 as failures,
           current_timestamp() - interval '1 hour' as execution_time
    
    union all
    
    select 'source_unique_live_external_ext_live_users_id' as test_name,
           'source' as test_type,
           'ext_live_users' as model_name,
           'id' as column_name,
           'unique' as test_category,
           'pass' as status,
           0 as failures,
           current_timestamp() - interval '1 hour' as execution_time
    
    union all
    
    select 'source_not_null_live_external_ext_live_orders_id' as test_name,
           'source' as test_type,
           'ext_live_orders' as model_name,
           'id' as column_name,
           'not_null' as test_category,
           'pass' as status,
           0 as failures,
           current_timestamp() - interval '1 hour' as execution_time
    
    union all
    
    select 'source_relationships_live_external_ext_live_orders_user_id' as test_name,
           'source' as test_type,
           'ext_live_orders' as model_name,
           'user_id' as column_name,
           'relationships' as test_category,
           'pass' as status,
           0 as failures,
           current_timestamp() - interval '1 hour' as execution_time
    
    union all
    
    select 'source_accepted_values_live_external_ext_live_events_action' as test_name,
           'source' as test_type,
           'ext_live_events' as model_name,
           'action' as column_name,
           'accepted_values' as test_category,
           'pass' as status,
           0 as failures,
           current_timestamp() - interval '1 hour' as execution_time
),

test_summary as (
    select
        date(execution_time) as test_date,
        test_type,
        model_name,
        test_category,
        
        -- Test counts
        count(*) as total_tests,
        sum(case when status = 'pass' then 1 else 0 end) as passed_tests,
        sum(case when status = 'fail' then 1 else 0 end) as failed_tests,
        sum(case when status = 'warn' then 1 else 0 end) as warning_tests,
        sum(case when status = 'skip' then 1 else 0 end) as skipped_tests,
        
        -- Failure details
        sum(failures) as total_failures,
        max(execution_time) as latest_execution,
        
        -- Calculate pass rate
        round((sum(case when status = 'pass' then 1 else 0 end)::float / count(*)) * 100, 2) as pass_rate
        
    from test_metadata
    group by date(execution_time), test_type, model_name, test_category
),

model_coverage as (
    -- Calculate test coverage per model
    select
        model_name,
        count(distinct test_category) as test_types_covered,
        count(*) as total_tests_on_model,
        
        -- Check for essential test coverage
        max(case when test_category = 'not_null' then 1 else 0 end) as has_not_null_tests,
        max(case when test_category = 'unique' then 1 else 0 end) as has_unique_tests,
        max(case when test_category = 'relationships' then 1 else 0 end) as has_relationship_tests,
        max(case when test_category = 'accepted_values' then 1 else 0 end) as has_accepted_values_tests
        
    from test_metadata
    group by model_name
),

health_scores as (
    select
        ts.*,
        mc.test_types_covered,
        mc.total_tests_on_model,
        mc.has_not_null_tests,
        mc.has_unique_tests,
        mc.has_relationship_tests,
        mc.has_accepted_values_tests,
        
        -- Test reliability score
        case 
            when pass_rate = 100 then 100
            when pass_rate >= 95 then 90
            when pass_rate >= 90 then 80
            when pass_rate >= 80 then 70
            else 50
        end as reliability_score,
        
        -- Test coverage score
        case 
            when test_types_covered >= 4 then 100
            when test_types_covered >= 3 then 80
            when test_types_covered >= 2 then 60
            when test_types_covered >= 1 then 40
            else 20
        end as coverage_score
        
    from test_summary ts
    left join model_coverage mc on ts.model_name = mc.model_name
)

select
    test_date,
    test_type,
    model_name,
    test_category,
    total_tests,
    passed_tests,
    failed_tests,
    warning_tests,
    skipped_tests,
    total_failures,
    pass_rate,
    test_types_covered,
    total_tests_on_model,
    has_not_null_tests,
    has_unique_tests,
    has_relationship_tests,
    has_accepted_values_tests,
    reliability_score,
    coverage_score,
    
    -- Overall test health score
    round((reliability_score * 0.7 + coverage_score * 0.3), 0) as overall_test_health_score,
    
    -- Test status
    case 
        when pass_rate = 100 and coverage_score >= 80 then 'Excellent'
        when pass_rate >= 95 and coverage_score >= 60 then 'Good'
        when pass_rate >= 90 and coverage_score >= 40 then 'Fair'
        when pass_rate >= 80 then 'Poor'
        else 'Critical'
    end as test_health_status,
    
    latest_execution,
    current_timestamp() as health_check_timestamp
    
from health_scores
order by test_date desc, model_name, test_category
    )
;
[0m06:27:57.783605 [debug] [Thread-4 (]: Using snowflake connection "model.live_c360.stg_events"
[0m06:27:57.786866 [debug] [Thread-3 (]: On model.live_c360.query_history_health: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "node_id": "model.live_c360.query_history_health"} */
create or replace transient table MYDB.LIVE_DATA.query_history_health
    

    
    as (

-- ETL Health Check: Query History Monitoring
-- Tracks query performance, failures, and runtime metrics

with query_history as (
    select
        query_id,
        query_text,
        database_name,
        schema_name,
        query_type,
        session_id,
        user_name,
        role_name,
        warehouse_name,
        warehouse_size,
        warehouse_type,
        cluster_number,
        query_tag,
        execution_status,
        error_code,
        error_message,
        start_time,
        end_time,
        total_elapsed_time,
        bytes_scanned,
        percentage_scanned_from_cache,
        bytes_written,
        bytes_written_to_result,
        bytes_read_from_result,
        rows_produced,
        rows_inserted,
        rows_updated,
        rows_deleted,
        rows_unloaded,
        bytes_deleted,
        partitions_scanned,
        partitions_total,
        bytes_spilled_to_local_storage,
        bytes_spilled_to_remote_storage,
        bytes_sent_over_the_network,
        compilation_time,
        execution_time,
        queued_provisioning_time,
        queued_repair_time,
        queued_overload_time,
        transaction_blocked_time,
        outbound_data_transfer_cloud,
        outbound_data_transfer_region,
        outbound_data_transfer_bytes,
        inbound_data_transfer_cloud,
        inbound_data_transfer_region,
        inbound_data_transfer_bytes,
        list_external_files_time,
        credits_used_cloud_services
    from snowflake.account_usage.query_history
    where start_time >= current_date - 7  -- Last 7 days
      and database_name = 'MYDB'  -- Focus on our database
),

etl_queries as (
    select
        *,
        -- Categorize queries
        case 
            when query_text ilike '%dbt%' then 'dbt'
            when query_text ilike '%insert%' or query_text ilike '%update%' or query_text ilike '%merge%' then 'ETL_DML'
            when query_text ilike '%create table%' or query_text ilike '%create view%' then 'ETL_DDL'
            when query_text ilike '%copy into%' then 'Data_Load'
            when query_text ilike '%select%' and query_text not ilike '%insert%' then 'Analytics'
            else 'Other'
        end as query_category,
        
        -- Performance flags
        case when total_elapsed_time > 300000 then true else false end as is_long_running,  -- > 5 minutes
        case when execution_status = 'FAIL' then true else false end as is_failed,
        case when bytes_spilled_to_local_storage > 0 or bytes_spilled_to_remote_storage > 0 then true else false end as has_spill,
        case when percentage_scanned_from_cache < 50 then true else false end as low_cache_hit,
        
        -- Time segments
        date(start_time) as execution_date,
        extract(hour from start_time) as execution_hour,
        case 
            when extract(hour from start_time) between 6 and 11 then 'Morning'
            when extract(hour from start_time) between 12 and 17 then 'Afternoon'
            when extract(hour from start_time) between 18 and 22 then 'Evening'
            else 'Night'
        end as time_segment
        
    from query_history
),

health_metrics as (
    select
        execution_date,
        query_category,
        time_segment,
        warehouse_name,
        
        -- Count metrics
        count(*) as total_queries,
        sum(case when is_failed then 1 else 0 end) as failed_queries,
        sum(case when is_long_running then 1 else 0 end) as long_running_queries,
        sum(case when has_spill then 1 else 0 end) as queries_with_spill,
        sum(case when low_cache_hit then 1 else 0 end) as low_cache_queries,
        
        -- Performance metrics
        avg(total_elapsed_time) as avg_elapsed_time_ms,
        max(total_elapsed_time) as max_elapsed_time_ms,
        percentile_cont(0.95) within group (order by total_elapsed_time) as p95_elapsed_time_ms,
        
        -- Data metrics
        sum(bytes_scanned) as total_bytes_scanned,
        sum(rows_produced) as total_rows_produced,
        avg(percentage_scanned_from_cache) as avg_cache_hit_rate,
        
        -- Cost metrics
        sum(credits_used_cloud_services) as total_credits_used,
        
        -- Latest execution
        max(start_time) as latest_execution_time
        
    from etl_queries
    group by execution_date, query_category, time_segment, warehouse_name
)

select
    *,
    -- Health scores (0-100)
    case 
        when failed_queries = 0 then 100
        when failed_queries::float / total_queries <= 0.01 then 95
        when failed_queries::float / total_queries <= 0.05 then 80
        when failed_queries::float / total_queries <= 0.10 then 60
        else 30
    end as reliability_score,
    
    case 
        when avg_elapsed_time_ms <= 30000 then 100  -- < 30 seconds
        when avg_elapsed_time_ms <= 120000 then 80  -- < 2 minutes
        when avg_elapsed_time_ms <= 300000 then 60  -- < 5 minutes
        else 30
    end as performance_score,
    
    case 
        when avg_cache_hit_rate >= 80 then 100
        when avg_cache_hit_rate >= 60 then 80
        when avg_cache_hit_rate >= 40 then 60
        else 30
    end as efficiency_score,
    
    -- Overall health score
    round((
        (case when failed_queries = 0 then 100 when failed_queries::float / total_queries <= 0.01 then 95 when failed_queries::float / total_queries <= 0.05 then 80 when failed_queries::float / total_queries <= 0.10 then 60 else 30 end) * 0.4 +
        (case when avg_elapsed_time_ms <= 30000 then 100 when avg_elapsed_time_ms <= 120000 then 80 when avg_elapsed_time_ms <= 300000 then 60 else 30 end) * 0.3 +
        (case when avg_cache_hit_rate >= 80 then 100 when avg_cache_hit_rate >= 60 then 80 when avg_cache_hit_rate >= 40 then 60 else 30 end) * 0.3
    ), 0) as overall_health_score,
    
    current_timestamp() as health_check_timestamp
    
from health_metrics
order by execution_date desc, query_category, warehouse_name
    )
;
[0m06:27:57.792689 [debug] [Thread-4 (]: On model.live_c360.stg_events: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "node_id": "model.live_c360.stg_events"} */
create or replace   view MYDB.LIVE_DATA.stg_events
  
   as (
    

-- Staging model for live events data
-- Cleans and standardizes raw event data from S3 external table

with source_data as (
    select * from MYDB.LIVE_DATA.ext_live_events
),

cleaned_data as (
    select
        -- Primary key
        event_id,
        user_id,
        session_id,
        
        -- Event details
        try_to_timestamp(date, 'MM-DD-YYYY HH24:MI:SS') as event_timestamp,
        lower(trim(platform)) as platform,
        lower(trim(action)) as action,
        trim(url) as url,
        
        -- Metadata
        batch_id,
        try_to_timestamp(generated_at) as generated_at,
        current_timestamp() as processed_at
        
    from source_data
    where event_id is not null  -- Filter out any null event IDs
      and user_id is not null   -- Filter out events without user IDs
),

url_parsed as (
    select
        *,
        -- URL parsing
        case 
            when url like '%/product/%' then 'Product Page'
            when url like '%/cart%' then 'Cart'
            when url like '%/checkout%' then 'Checkout'
            when url like '%/search%' then 'Search'
            when url like '%/category/%' then 'Category'
            when url like '%/home%' or url = 'https://example.com/' then 'Home'
            else 'Other'
        end as page_type,
        
        -- Extract path from URL
        regexp_substr(url, 'https?://[^/]+(/.*)', 1, 1, 'e', 1) as url_path
        
    from cleaned_data
),

final as (
    select
        *,
        -- Date extractions
        date(event_timestamp) as event_date,
        extract(year from event_timestamp) as event_year,
        extract(month from event_timestamp) as event_month,
        extract(day from event_timestamp) as event_day,
        extract(hour from event_timestamp) as event_hour,
        dayname(event_timestamp) as event_day_name,
        
        -- Time-based segments
        case 
            when extract(hour from event_timestamp) between 6 and 11 then 'Morning'
            when extract(hour from event_timestamp) between 12 and 17 then 'Afternoon'
            when extract(hour from event_timestamp) between 18 and 22 then 'Evening'
            else 'Night'
        end as time_of_day_segment,
        
        -- Platform standardization
        case 
            when platform in ('ios', 'android') then 'Mobile'
            when platform = 'web' then 'Web'
            else 'Unknown'
        end as device_category,
        
        -- Action categorization
        case 
            when action in ('view', 'click') then 'Engagement'
            when action = 'log' then 'Authentication'
            when action = 'purchase' then 'Conversion'
            else 'Other'
        end as action_category,
        
        -- Funnel stage
        case 
            when action = 'view' and page_type = 'Home' then 'Awareness'
            when action = 'view' and page_type in ('Category', 'Search') then 'Interest'
            when action = 'view' and page_type = 'Product Page' then 'Consideration'
            when action = 'click' and page_type = 'Cart' then 'Intent'
            when action = 'purchase' then 'Purchase'
            else 'Other'
        end as funnel_stage,
        
        -- Data quality flags
        case 
            when event_timestamp > current_timestamp() then true
            else false
        end as has_future_event_timestamp,
        
        -- Recency
        datediff('minute', event_timestamp, current_timestamp()) as minutes_since_event,
        datediff('hour', event_timestamp, current_timestamp()) as hours_since_event,
        datediff('day', event_timestamp, current_timestamp()) as days_since_event
        
    from url_parsed
)

select * from final
  );
[0m06:27:57.795947 [debug] [Thread-4 (]: Opening a new connection, currently in state init
[0m06:27:58.163983 [debug] [Thread-4 (]: Snowflake adapter: Snowflake query id: 01bccc64-3204-802a-0002-4ad600069092
[0m06:27:58.168236 [debug] [Thread-4 (]: Snowflake adapter: Snowflake error: 091093 (55000): External table EXT_LIVE_EVENTS marked invalid. Stage S3_LIVE_EVENTS_STAGE dropped.
[0m06:27:58.173146 [debug] [Thread-4 (]: Database Error in model stg_events (models/staging/stg_events.sql)
  091093 (55000): External table EXT_LIVE_EVENTS marked invalid. Stage S3_LIVE_EVENTS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_events.sql
[0m06:27:58.175858 [debug] [Thread-4 (]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '74dcd8a1-9abc-4250-b219-2842a0a70add', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7947d563cca0>]}
[0m06:27:58.177363 [error] [Thread-4 (]: 4 of 12 ERROR creating sql view model LIVE_DATA.stg_events ..................... [[31mERROR[0m in 0.58s]
[0m06:27:58.178910 [debug] [Thread-4 (]: Finished running node model.live_c360.stg_events
[0m06:27:58.184499 [debug] [Thread-4 (]: Began running node model.live_c360.stg_orders
[0m06:27:58.185737 [debug] [Thread-7 (]: Marking all children of 'model.live_c360.stg_events' to be skipped because of status 'error'.  Reason: Database Error in model stg_events (models/staging/stg_events.sql)
  091093 (55000): External table EXT_LIVE_EVENTS marked invalid. Stage S3_LIVE_EVENTS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_events.sql.
[0m06:27:58.187003 [info ] [Thread-4 (]: 5 of 12 START sql view model LIVE_DATA.stg_orders .............................. [RUN]
[0m06:27:58.189930 [debug] [Thread-4 (]: Re-using an available connection from the pool (formerly model.live_c360.stg_events, now model.live_c360.stg_orders)
[0m06:27:58.190935 [debug] [Thread-4 (]: Began compiling node model.live_c360.stg_orders
[0m06:27:58.196541 [debug] [Thread-4 (]: Writing injected SQL for node "model.live_c360.stg_orders"
[0m06:27:58.204240 [debug] [Thread-4 (]: Began executing node model.live_c360.stg_orders
[0m06:27:58.209538 [debug] [Thread-4 (]: Writing runtime sql for node "model.live_c360.stg_orders"
[0m06:27:58.219401 [debug] [Thread-4 (]: Using snowflake connection "model.live_c360.stg_orders"
[0m06:27:58.220453 [debug] [Thread-4 (]: On model.live_c360.stg_orders: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "node_id": "model.live_c360.stg_orders"} */
create or replace   view MYDB.LIVE_DATA.stg_orders
  
   as (
    

-- Staging model for live orders data
-- Cleans and standardizes raw order data from S3 external table

with source_data as (
    select * from MYDB.LIVE_DATA.ext_live_orders
),

cleaned_data as (
    select
        -- Primary key
        id as order_id,
        user_id,
        
        -- Transaction details
        try_to_timestamp(transaction_date, 'MM-DD-YYYY HH24:MI:SS') as transaction_date,
        item_count,
        amount,
        
        -- Metadata
        batch_id,
        try_to_timestamp(generated_at) as generated_at,
        current_timestamp() as processed_at
        
    from source_data
    where id is not null  -- Filter out any null order IDs
      and user_id is not null  -- Filter out orders without user IDs
      and amount > 0  -- Filter out invalid amounts
),

final as (
    select
        *,
        -- Derived fields
        round(amount / item_count, 2) as avg_item_price,
        
        -- Date extractions
        date(transaction_date) as transaction_date_only,
        extract(year from transaction_date) as transaction_year,
        extract(month from transaction_date) as transaction_month,
        extract(day from transaction_date) as transaction_day,
        extract(hour from transaction_date) as transaction_hour,
        dayname(transaction_date) as transaction_day_name,
        
        -- Time-based segments
        case 
            when extract(hour from transaction_date) between 6 and 11 then 'Morning'
            when extract(hour from transaction_date) between 12 and 17 then 'Afternoon'
            when extract(hour from transaction_date) between 18 and 22 then 'Evening'
            else 'Night'
        end as time_of_day_segment,
        
        -- Order size segments
        case 
            when amount < 25 then 'Small'
            when amount < 75 then 'Medium'
            when amount < 150 then 'Large'
            else 'Extra Large'
        end as order_size_segment,
        
        -- Item quantity segments
        case 
            when item_count = 1 then 'Single Item'
            when item_count <= 3 then 'Few Items'
            when item_count <= 5 then 'Multiple Items'
            else 'Bulk Order'
        end as quantity_segment,
        
        -- Data quality flags
        case 
            when transaction_date > current_timestamp() then true
            else false
        end as has_future_transaction_date,
        
        case 
            when avg_item_price > 500 then true  -- Flag unusually expensive items
            else false
        end as has_high_item_price,
        
        -- Recency
        datediff('day', transaction_date, current_timestamp()) as days_since_transaction
        
    from cleaned_data
)

select * from final
  );
[0m06:27:58.330044 [debug] [Thread-4 (]: Snowflake adapter: Snowflake query id: 01bccc64-3204-7f69-0002-4ad60005d1b2
[0m06:27:58.331647 [debug] [Thread-4 (]: Snowflake adapter: Snowflake error: 091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
[0m06:27:58.334037 [debug] [Thread-4 (]: Database Error in model stg_orders (models/staging/stg_orders.sql)
  091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_orders.sql
[0m06:27:58.337431 [debug] [Thread-4 (]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '74dcd8a1-9abc-4250-b219-2842a0a70add', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7947ed833c40>]}
[0m06:27:58.339237 [error] [Thread-4 (]: 5 of 12 ERROR creating sql view model LIVE_DATA.stg_orders ..................... [[31mERROR[0m in 0.15s]
[0m06:27:58.340787 [debug] [Thread-4 (]: Finished running node model.live_c360.stg_orders
[0m06:27:58.342073 [debug] [Thread-4 (]: Began running node model.live_c360.stg_users
[0m06:27:58.342766 [debug] [Thread-7 (]: Marking all children of 'model.live_c360.stg_orders' to be skipped because of status 'error'.  Reason: Database Error in model stg_orders (models/staging/stg_orders.sql)
  091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_orders.sql.
[0m06:27:58.343898 [info ] [Thread-4 (]: 6 of 12 START sql view model LIVE_DATA.stg_users ............................... [RUN]
[0m06:27:58.346359 [debug] [Thread-4 (]: Re-using an available connection from the pool (formerly model.live_c360.stg_orders, now model.live_c360.stg_users)
[0m06:27:58.348117 [debug] [Thread-4 (]: Began compiling node model.live_c360.stg_users
[0m06:27:58.353779 [debug] [Thread-4 (]: Writing injected SQL for node "model.live_c360.stg_users"
[0m06:27:58.362342 [debug] [Thread-4 (]: Began executing node model.live_c360.stg_users
[0m06:27:58.442218 [debug] [Thread-4 (]: Writing runtime sql for node "model.live_c360.stg_users"
[0m06:27:58.456143 [debug] [Thread-4 (]: Using snowflake connection "model.live_c360.stg_users"
[0m06:27:58.457498 [debug] [Thread-4 (]: On model.live_c360.stg_users: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "node_id": "model.live_c360.stg_users"} */
create or replace   view MYDB.LIVE_DATA.stg_users
  
   as (
    

-- Staging model for live users data
-- Cleans and standardizes raw user data from S3 external table

with source_data as (
    select * from MYDB.LIVE_DATA.ext_live_users
),

cleaned_data as (
    select
        -- Primary key
        id as user_id,
        
        -- Personal information (anonymized)
        sha1(email) as email_hash,
        initcap(trim(firstname)) as firstname,
        initcap(trim(lastname)) as lastname,
        trim(address) as address,
        upper(trim(canal)) as acquisition_channel,
        upper(trim(country)) as country,
        
        -- Dates
        try_to_timestamp(creation_date, 'MM-DD-YYYY HH24:MI:SS') as creation_date,
        try_to_timestamp(last_activity_date, 'MM-DD-YYYY HH24:MI:SS') as last_activity_date,
        
        -- Demographics
        case 
            when gender = 0 then 'F'
            when gender = 1 then 'M'
            else 'Unknown'
        end as gender,
        age_group,
        
        -- Behavioral flags
        churn as is_churned,
        
        -- Metadata
        batch_id,
        try_to_timestamp(generated_at) as generated_at,
        current_timestamp() as processed_at
        
    from source_data
    where id is not null  -- Filter out any null user IDs
),

final as (
    select
        *,
        -- Derived fields
        datediff('day', creation_date, current_timestamp()) as days_since_creation,
        datediff('day', last_activity_date, current_timestamp()) as days_since_last_activity,
        
        -- Data quality flags
        case 
            when creation_date > current_timestamp() then true
            else false
        end as has_future_creation_date,
        
        case 
            when last_activity_date < creation_date then true
            else false
        end as has_invalid_activity_date,
        
        -- Segmentation
        case 
            when days_since_last_activity <= 7 then 'Active'
            when days_since_last_activity <= 30 then 'Recent'
            when days_since_last_activity <= 90 then 'Dormant'
            else 'Inactive'
        end as activity_segment
        
    from cleaned_data
)

select * from final
  );
[0m06:27:58.550973 [debug] [Thread-1 (]: SQL status: SUCCESS 1 in 0.762 seconds
[0m06:27:58.587153 [debug] [Thread-1 (]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '74dcd8a1-9abc-4250-b219-2842a0a70add', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7947eeb9e980>]}
[0m06:27:58.588068 [debug] [Thread-4 (]: Snowflake adapter: Snowflake query id: 01bccc64-3204-7f80-0002-4ad60006710e
[0m06:27:58.589592 [info ] [Thread-1 (]: 1 of 12 OK created sql table model LIVE_DATA.dbt_test_health ................... [[32mSUCCESS 1[0m in 1.00s]
[0m06:27:58.590883 [debug] [Thread-4 (]: Snowflake adapter: Snowflake error: 091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
[0m06:27:58.592390 [debug] [Thread-1 (]: Finished running node model.live_c360.dbt_test_health
[0m06:27:58.594450 [debug] [Thread-4 (]: Database Error in model stg_users (models/staging/stg_users.sql)
  091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_users.sql
[0m06:27:58.595628 [debug] [Thread-1 (]: Began running node model.live_c360.fact_orders
[0m06:27:58.597097 [debug] [Thread-4 (]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '74dcd8a1-9abc-4250-b219-2842a0a70add', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7947cd40fa90>]}
[0m06:27:58.598620 [info ] [Thread-1 (]: 7 of 12 SKIP relation LIVE_DATA.fact_orders .................................... [[33mSKIP[0m]
[0m06:27:58.602183 [error] [Thread-4 (]: 6 of 12 ERROR creating sql view model LIVE_DATA.stg_users ...................... [[31mERROR[0m in 0.25s]
[0m06:27:58.603920 [debug] [Thread-1 (]: Finished running node model.live_c360.fact_orders
[0m06:27:58.605582 [debug] [Thread-4 (]: Finished running node model.live_c360.stg_users
[0m06:27:58.606975 [debug] [Thread-1 (]: Began running node model.live_c360.mv_fact_orders
[0m06:27:58.609023 [debug] [Thread-7 (]: Marking all children of 'model.live_c360.stg_users' to be skipped because of status 'error'.  Reason: Database Error in model stg_users (models/staging/stg_users.sql)
  091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_users.sql.
[0m06:27:58.610337 [info ] [Thread-1 (]: 8 of 12 SKIP relation LIVE_DATA_ANALYTICS.mv_fact_orders ....................... [[33mSKIP[0m]
[0m06:27:58.612479 [debug] [Thread-4 (]: Began running node model.live_c360.dim_users
[0m06:27:58.613381 [debug] [Thread-1 (]: Finished running node model.live_c360.mv_fact_orders
[0m06:27:58.616229 [info ] [Thread-4 (]: 9 of 12 SKIP relation LIVE_DATA.dim_users ...................................... [[33mSKIP[0m]
[0m06:27:58.617798 [debug] [Thread-1 (]: Began running node model.live_c360.dim_users_scd2
[0m06:27:58.619099 [debug] [Thread-4 (]: Finished running node model.live_c360.dim_users
[0m06:27:58.620550 [info ] [Thread-1 (]: 10 of 12 SKIP relation LIVE_DATA.dim_users_scd2 ................................ [[33mSKIP[0m]
[0m06:27:58.622467 [debug] [Thread-4 (]: Began running node model.live_c360.data_quality_health
[0m06:27:58.623349 [debug] [Thread-1 (]: Finished running node model.live_c360.dim_users_scd2
[0m06:27:58.624471 [info ] [Thread-4 (]: 11 of 12 SKIP relation LIVE_DATA.data_quality_health ........................... [[33mSKIP[0m]
[0m06:27:58.627014 [debug] [Thread-4 (]: Finished running node model.live_c360.data_quality_health
[0m06:27:58.628952 [debug] [Thread-1 (]: Began running node model.live_c360.etl_health_dashboard
[0m06:27:58.632103 [info ] [Thread-1 (]: 12 of 12 SKIP relation LIVE_DATA.etl_health_dashboard .......................... [[33mSKIP[0m]
[0m06:27:58.633510 [debug] [Thread-1 (]: Finished running node model.live_c360.etl_health_dashboard
[0m06:28:00.260532 [debug] [Thread-3 (]: SQL status: SUCCESS 1 in 2.466 seconds
[0m06:28:00.270615 [debug] [Thread-3 (]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '74dcd8a1-9abc-4250-b219-2842a0a70add', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7947ee8ef8e0>]}
[0m06:28:00.273645 [info ] [Thread-3 (]: 3 of 12 OK created sql table model LIVE_DATA.query_history_health .............. [[32mSUCCESS 1[0m in 2.68s]
[0m06:28:00.275760 [debug] [Thread-3 (]: Finished running node model.live_c360.query_history_health
[0m06:28:00.344131 [debug] [Thread-2 (]: SQL status: SUCCESS 1 in 2.559 seconds
[0m06:28:00.348495 [debug] [Thread-2 (]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '74dcd8a1-9abc-4250-b219-2842a0a70add', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7947cf598100>]}
[0m06:28:00.350222 [info ] [Thread-2 (]: 2 of 12 OK created sql table model LIVE_DATA.pipeline_runtime_health ........... [[32mSUCCESS 1[0m in 2.76s]
[0m06:28:00.352148 [debug] [Thread-2 (]: Finished running node model.live_c360.pipeline_runtime_health
[0m06:28:00.355164 [debug] [MainThread]: Connection 'master' was properly closed.
[0m06:28:00.357856 [debug] [MainThread]: Connection 'model.live_c360.query_history_health' was left open.
[0m06:28:00.359040 [debug] [MainThread]: On model.live_c360.query_history_health: Close
[0m06:28:00.417168 [debug] [MainThread]: Connection 'model.live_c360.pipeline_runtime_health' was left open.
[0m06:28:00.420512 [debug] [MainThread]: On model.live_c360.pipeline_runtime_health: Close
[0m06:28:00.499851 [debug] [MainThread]: Connection 'model.live_c360.dbt_test_health' was left open.
[0m06:28:00.501424 [debug] [MainThread]: On model.live_c360.dbt_test_health: Close
[0m06:28:00.566236 [debug] [MainThread]: Connection 'model.live_c360.stg_users' was left open.
[0m06:28:00.567947 [debug] [MainThread]: On model.live_c360.stg_users: Close
[0m06:28:00.633158 [info ] [MainThread]: 
[0m06:28:00.634720 [info ] [MainThread]: Finished running 1 incremental model, 7 table models, 4 view models in 0 hours 0 minutes and 4.51 seconds (4.51s).
[0m06:28:00.638035 [debug] [MainThread]: Command end result
[0m06:28:00.712557 [debug] [MainThread]: Wrote artifact WritableManifest to /opt/airflow/workspace/dbt_live/target/manifest.json
[0m06:28:00.721562 [debug] [MainThread]: Wrote artifact SemanticManifest to /opt/airflow/workspace/dbt_live/target/semantic_manifest.json
[0m06:28:00.738253 [debug] [MainThread]: Wrote artifact RunExecutionResult to /opt/airflow/workspace/dbt_live/target/run_results.json
[0m06:28:00.739686 [info ] [MainThread]: 
[0m06:28:00.741079 [info ] [MainThread]: [31mCompleted with 3 errors, 0 partial successes, and 0 warnings:[0m
[0m06:28:00.742409 [info ] [MainThread]: 
[0m06:28:00.743803 [error] [MainThread]:   Database Error in model stg_events (models/staging/stg_events.sql)
  091093 (55000): External table EXT_LIVE_EVENTS marked invalid. Stage S3_LIVE_EVENTS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_events.sql
[0m06:28:00.745094 [info ] [MainThread]: 
[0m06:28:00.747678 [error] [MainThread]:   Database Error in model stg_orders (models/staging/stg_orders.sql)
  091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_orders.sql
[0m06:28:00.749031 [info ] [MainThread]: 
[0m06:28:00.750177 [error] [MainThread]:   Database Error in model stg_users (models/staging/stg_users.sql)
  091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_users.sql
[0m06:28:00.755905 [info ] [MainThread]: 
[0m06:28:00.757407 [info ] [MainThread]: Done. PASS=3 WARN=0 ERROR=3 SKIP=6 TOTAL=12
[0m06:28:00.760081 [debug] [MainThread]: Resource report: {"command_name": "run", "command_success": false, "command_wall_clock_time": 7.2262216, "process_in_blocks": "0", "process_kernel_time": 0.538925, "process_mem_max_rss": "205884", "process_out_blocks": "16", "process_user_time": 4.111482}
[0m06:28:00.764525 [debug] [MainThread]: Command `dbt run` failed at 06:28:00.764203 after 7.23 seconds
[0m06:28:00.766294 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7947ed79a890>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7947ed607280>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7947d55d28c0>]}
[0m06:28:00.768007 [debug] [MainThread]: Flushing usage events
[0m06:28:04.260290 [debug] [MainThread]: An error was encountered while trying to flush usage events
[0m09:42:56.681275 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7071c75e2860>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7071c724e950>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7071c64d0910>]}


============================== 09:42:56.689985 | 77ec1173-9a43-4127-8222-88a87cd80ac6 ==============================
[0m09:42:56.689985 [info ] [MainThread]: Running with dbt=1.9.6
[0m09:42:56.691594 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'write_json': 'True', 'log_cache_events': 'False', 'partial_parse': 'True', 'cache_selected_only': 'False', 'profiles_dir': '/opt/airflow/workspace/dbt_live', 'version_check': 'True', 'fail_fast': 'False', 'log_path': '/opt/airflow/workspace/dbt_live/logs', 'debug': 'False', 'warn_error': 'None', 'use_colors': 'True', 'use_experimental_parser': 'False', 'empty': 'None', 'quiet': 'False', 'no_print': 'None', 'warn_error_options': 'WarnErrorOptions(include=[], exclude=[])', 'invocation_command': 'dbt debug', 'introspect': 'True', 'log_format': 'default', 'target_path': 'None', 'static_parser': 'True', 'send_anonymous_usage_stats': 'True'}
[0m09:42:56.703167 [info ] [MainThread]: dbt version: 1.9.6
[0m09:42:56.704746 [info ] [MainThread]: python version: 3.10.13
[0m09:42:56.706269 [info ] [MainThread]: python path: /usr/local/bin/python
[0m09:42:56.707578 [info ] [MainThread]: os info: Linux-6.6.87.1-microsoft-standard-WSL2-x86_64-with-glibc2.36
[0m09:42:57.395052 [info ] [MainThread]: Using profiles dir at /opt/airflow/workspace/dbt_live
[0m09:42:57.397036 [info ] [MainThread]: Using profiles.yml file at /opt/airflow/workspace/dbt_live/profiles.yml
[0m09:42:57.399689 [info ] [MainThread]: Using dbt_project.yml file at /opt/airflow/workspace/dbt_live/dbt_project.yml
[0m09:42:57.401414 [info ] [MainThread]: adapter type: snowflake
[0m09:42:57.402881 [info ] [MainThread]: adapter version: 1.9.4
[0m09:42:57.542267 [info ] [MainThread]: Configuration:
[0m09:42:57.544217 [info ] [MainThread]:   profiles.yml file [[32mOK found and valid[0m]
[0m09:42:57.545584 [info ] [MainThread]:   dbt_project.yml file [[32mOK found and valid[0m]
[0m09:42:57.547326 [info ] [MainThread]: Required dependencies:
[0m09:42:57.549393 [debug] [MainThread]: Executing "git --help"
[0m09:42:57.671306 [debug] [MainThread]: STDOUT: "b"usage: git [-v | --version] [-h | --help] [-C <path>] [-c <name>=<value>]\n           [--exec-path[=<path>]] [--html-path] [--man-path] [--info-path]\n           [-p | --paginate | -P | --no-pager] [--no-replace-objects] [--bare]\n           [--git-dir=<path>] [--work-tree=<path>] [--namespace=<name>]\n           [--super-prefix=<path>] [--config-env=<name>=<envvar>]\n           <command> [<args>]\n\nThese are common Git commands used in various situations:\n\nstart a working area (see also: git help tutorial)\n   clone     Clone a repository into a new directory\n   init      Create an empty Git repository or reinitialize an existing one\n\nwork on the current change (see also: git help everyday)\n   add       Add file contents to the index\n   mv        Move or rename a file, a directory, or a symlink\n   restore   Restore working tree files\n   rm        Remove files from the working tree and from the index\n\nexamine the history and state (see also: git help revisions)\n   bisect    Use binary search to find the commit that introduced a bug\n   diff      Show changes between commits, commit and working tree, etc\n   grep      Print lines matching a pattern\n   log       Show commit logs\n   show      Show various types of objects\n   status    Show the working tree status\n\ngrow, mark and tweak your common history\n   branch    List, create, or delete branches\n   commit    Record changes to the repository\n   merge     Join two or more development histories together\n   rebase    Reapply commits on top of another base tip\n   reset     Reset current HEAD to the specified state\n   switch    Switch branches\n   tag       Create, list, delete or verify a tag object signed with GPG\n\ncollaborate (see also: git help workflows)\n   fetch     Download objects and refs from another repository\n   pull      Fetch from and integrate with another repository or a local branch\n   push      Update remote refs along with associated objects\n\n'git help -a' and 'git help -g' list available subcommands and some\nconcept guides. See 'git help <command>' or 'git help <concept>'\nto read about a specific subcommand or concept.\nSee 'git help git' for an overview of the system.\n""
[0m09:42:57.673052 [debug] [MainThread]: STDERR: "b''"
[0m09:42:57.674276 [info ] [MainThread]:  - git [[32mOK found[0m]

[0m09:42:57.675493 [info ] [MainThread]: Connection:
[0m09:42:57.676866 [info ] [MainThread]:   account: SVLFKJI-IX89869
[0m09:42:57.678375 [info ] [MainThread]:   user: XINBINZHANG
[0m09:42:57.679567 [info ] [MainThread]:   database: MYDB
[0m09:42:57.680683 [info ] [MainThread]:   warehouse: COMPUTE_WH
[0m09:42:57.683067 [info ] [MainThread]:   role: ACCOUNTADMIN
[0m09:42:57.684966 [info ] [MainThread]:   schema: LIVE_DATA
[0m09:42:57.686526 [info ] [MainThread]:   authenticator: None
[0m09:42:57.687735 [info ] [MainThread]:   oauth_client_id: None
[0m09:42:57.688882 [info ] [MainThread]:   query_tag: None
[0m09:42:57.689984 [info ] [MainThread]:   client_session_keep_alive: False
[0m09:42:57.691009 [info ] [MainThread]:   host: None
[0m09:42:57.692180 [info ] [MainThread]:   port: None
[0m09:42:57.693329 [info ] [MainThread]:   proxy_host: None
[0m09:42:57.694884 [info ] [MainThread]:   proxy_port: None
[0m09:42:57.695976 [info ] [MainThread]:   protocol: None
[0m09:42:57.696987 [info ] [MainThread]:   connect_retries: 1
[0m09:42:57.701190 [info ] [MainThread]:   connect_timeout: None
[0m09:42:57.702993 [info ] [MainThread]:   retry_on_database_errors: False
[0m09:42:57.704404 [info ] [MainThread]:   retry_all: False
[0m09:42:57.705754 [info ] [MainThread]:   insecure_mode: False
[0m09:42:57.706933 [info ] [MainThread]:   reuse_connections: True
[0m09:42:57.708275 [info ] [MainThread]: Registered adapter: snowflake=1.9.4
[0m09:42:57.884063 [debug] [MainThread]: Acquiring new snowflake connection 'debug'
[0m09:42:57.987326 [debug] [MainThread]: Using snowflake connection "debug"
[0m09:42:57.988862 [debug] [MainThread]: On debug: select 1 as id
[0m09:42:57.990054 [debug] [MainThread]: Opening a new connection, currently in state init
[0m09:42:58.753707 [debug] [MainThread]: SQL status: SUCCESS 1 in 0.764 seconds
[0m09:42:58.757105 [info ] [MainThread]:   Connection test: [[32mOK connection ok[0m]

[0m09:42:58.758904 [info ] [MainThread]: [32mAll checks passed![0m
[0m09:42:58.761690 [debug] [MainThread]: Resource report: {"command_name": "debug", "command_success": true, "command_wall_clock_time": 2.1725512, "process_in_blocks": "0", "process_kernel_time": 0.519223, "process_mem_max_rss": "190136", "process_out_blocks": "16", "process_user_time": 2.783836}
[0m09:42:58.763649 [debug] [MainThread]: Command `dbt debug` succeeded at 09:42:58.763482 after 2.17 seconds
[0m09:42:58.765247 [debug] [MainThread]: Connection 'debug' was left open.
[0m09:42:58.767483 [debug] [MainThread]: On debug: Close
[0m09:42:58.888442 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7071c75e2860>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7071af788100>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7071b411b5b0>]}
[0m09:42:58.890876 [debug] [MainThread]: Flushing usage events
[0m09:42:59.888020 [debug] [MainThread]: An error was encountered while trying to flush usage events
[0m09:43:03.487101 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x72673c5ca920>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x72673b3c9780>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x72673b3cb400>]}


============================== 09:43:03.495991 | 931dae6b-4e99-4f8a-a1fc-712c2d81fa57 ==============================
[0m09:43:03.495991 [info ] [MainThread]: Running with dbt=1.9.6
[0m09:43:03.498790 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'write_json': 'True', 'log_cache_events': 'False', 'partial_parse': 'True', 'cache_selected_only': 'False', 'warn_error': 'None', 'debug': 'False', 'version_check': 'True', 'log_path': '/opt/airflow/workspace/dbt_live/logs', 'fail_fast': 'False', 'profiles_dir': '/opt/airflow/workspace/dbt_live', 'use_colors': 'True', 'use_experimental_parser': 'False', 'empty': 'None', 'quiet': 'False', 'no_print': 'None', 'log_format': 'default', 'invocation_command': 'dbt deps', 'warn_error_options': 'WarnErrorOptions(include=[], exclude=[])', 'static_parser': 'True', 'target_path': 'None', 'introspect': 'True', 'send_anonymous_usage_stats': 'True'}
[0m09:43:03.691127 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'project_id', 'label': '931dae6b-4e99-4f8a-a1fc-712c2d81fa57', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x72673d83e560>]}
[0m09:43:04.068735 [debug] [MainThread]: Set downloads directory='/tmp/dbt-downloads-fldryela'
[0m09:43:04.069887 [debug] [MainThread]: Making package index registry request: GET https://hub.getdbt.com/api/v1/index.json
[0m09:43:04.658282 [debug] [MainThread]: Response from registry index: GET https://hub.getdbt.com/api/v1/index.json 200
[0m09:43:04.661941 [debug] [MainThread]: Making package registry request: GET https://hub.getdbt.com/api/v1/dbt-labs/dbt_utils.json
[0m09:43:04.772097 [debug] [MainThread]: Response from registry: GET https://hub.getdbt.com/api/v1/dbt-labs/dbt_utils.json 200
[0m09:43:04.780388 [info ] [MainThread]: Installing dbt-labs/dbt_utils
[0m09:43:09.667080 [info ] [MainThread]: Installed from version 1.1.1
[0m09:43:09.668203 [info ] [MainThread]: Updated version available: 1.3.0
[0m09:43:09.669268 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'package', 'label': '931dae6b-4e99-4f8a-a1fc-712c2d81fa57', 'property_': 'install', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x72673c23d6c0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x72673b2439a0>]}
[0m09:43:09.670330 [info ] [MainThread]: 
[0m09:43:09.671281 [info ] [MainThread]: Updates available for packages: ['dbt-labs/dbt_utils']                 
Update your versions in packages.yml, then run dbt deps
[0m09:43:09.673169 [debug] [MainThread]: Resource report: {"command_name": "deps", "command_success": true, "command_wall_clock_time": 6.293293, "process_in_blocks": "0", "process_kernel_time": 0.510522, "process_mem_max_rss": "94712", "process_out_blocks": "200", "process_user_time": 2.434474}
[0m09:43:09.674510 [debug] [MainThread]: Command `dbt deps` succeeded at 09:43:09.674343 after 6.29 seconds
[0m09:43:09.675731 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x72673c5ca920>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x72673c662350>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x72673c24b040>]}
[0m09:43:09.677323 [debug] [MainThread]: Flushing usage events
[0m09:43:10.645116 [debug] [MainThread]: An error was encountered while trying to flush usage events
[0m09:43:14.358125 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x782287a6a8c0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7822876dec50>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x782286972170>]}


============================== 09:43:14.366030 | 08ced0c0-898b-434a-b218-6560f31e3b9c ==============================
[0m09:43:14.366030 [info ] [MainThread]: Running with dbt=1.9.6
[0m09:43:14.369373 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'log_cache_events': 'False', 'write_json': 'True', 'partial_parse': 'True', 'cache_selected_only': 'False', 'warn_error': 'None', 'debug': 'False', 'fail_fast': 'False', 'log_path': '/opt/airflow/workspace/dbt_live/logs', 'version_check': 'True', 'profiles_dir': '/opt/airflow/workspace/dbt_live', 'use_colors': 'True', 'use_experimental_parser': 'False', 'empty': 'False', 'quiet': 'False', 'no_print': 'None', 'warn_error_options': 'WarnErrorOptions(include=[], exclude=[])', 'introspect': 'True', 'log_format': 'default', 'static_parser': 'True', 'target_path': 'None', 'invocation_command': 'dbt run --models staging', 'send_anonymous_usage_stats': 'True'}
[0m09:43:15.677159 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'project_id', 'label': '08ced0c0-898b-434a-b218-6560f31e3b9c', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7822876e1090>]}
[0m09:43:15.750034 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'adapter_info', 'label': '08ced0c0-898b-434a-b218-6560f31e3b9c', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7822877aba90>]}
[0m09:43:15.751796 [info ] [MainThread]: Registered adapter: snowflake=1.9.4
[0m09:43:16.009810 [debug] [MainThread]: checksum: dd6dc1e5178459e3de3bf2eeb7c86bed4be2266c311fce8c15d86eb4ff94e7ad, vars: {}, profile: , target: , version: 1.9.6
[0m09:43:17.397685 [debug] [MainThread]: Partial parsing enabled: 0 files deleted, 0 files added, 0 files changed.
[0m09:43:17.399568 [debug] [MainThread]: Partial parsing enabled, no changes found, skipping parsing
[0m09:43:17.409130 [warn ] [MainThread]: [[33mWARNING[0m]: Configuration paths exist in your dbt_project.yml file which do not apply to any resources.
There are 3 unused configuration paths:
- models.live_c360.incremental
- seeds.live_c360
- snapshots.live_c360
[0m09:43:17.487798 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'load_project', 'label': '08ced0c0-898b-434a-b218-6560f31e3b9c', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x78226f8ec130>]}
[0m09:43:17.681401 [debug] [MainThread]: Wrote artifact WritableManifest to /opt/airflow/workspace/dbt_live/target/manifest.json
[0m09:43:17.687097 [debug] [MainThread]: Wrote artifact SemanticManifest to /opt/airflow/workspace/dbt_live/target/semantic_manifest.json
[0m09:43:17.718602 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'resource_counts', 'label': '08ced0c0-898b-434a-b218-6560f31e3b9c', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x78226f738340>]}
[0m09:43:17.719907 [info ] [MainThread]: Found 12 models, 21 data tests, 3 sources, 590 macros
[0m09:43:17.720967 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'runnable_timing', 'label': '08ced0c0-898b-434a-b218-6560f31e3b9c', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x78226f738430>]}
[0m09:43:17.723438 [info ] [MainThread]: 
[0m09:43:17.725383 [info ] [MainThread]: Concurrency: 4 threads (target='live')
[0m09:43:17.726719 [info ] [MainThread]: 
[0m09:43:17.730428 [debug] [MainThread]: Acquiring new snowflake connection 'master'
[0m09:43:17.736958 [debug] [ThreadPool]: Acquiring new snowflake connection 'list_MYDB'
[0m09:43:17.793626 [debug] [ThreadPool]: Using snowflake connection "list_MYDB"
[0m09:43:17.796638 [debug] [ThreadPool]: On list_MYDB: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB"} */
show terse schemas in database MYDB
    limit 10000
[0m09:43:17.797722 [debug] [ThreadPool]: Opening a new connection, currently in state init
[0m09:43:18.534097 [debug] [ThreadPool]: SQL status: SUCCESS 6 in 0.736 seconds
[0m09:43:18.541459 [debug] [ThreadPool]: Re-using an available connection from the pool (formerly list_MYDB, now list_MYDB_LIVE_DATA_ANALYTICS)
[0m09:43:18.542560 [debug] [ThreadPool]: Acquiring new snowflake connection 'list_MYDB_LIVE_DATA'
[0m09:43:18.543942 [debug] [ThreadPool]: Acquiring new snowflake connection 'list_MYDB_LIVE_DATA_dbt_test__audit'
[0m09:43:18.560420 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_ANALYTICS"
[0m09:43:18.566699 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA"
[0m09:43:18.570763 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_dbt_test__audit"
[0m09:43:18.572271 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_ANALYTICS: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_ANALYTICS"} */
alter session set quoted_identifiers_ignore_case = false;
[0m09:43:18.573667 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA"} */
alter session set quoted_identifiers_ignore_case = false;
[0m09:43:18.574884 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_dbt_test__audit: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_dbt_test__audit"} */
alter session set quoted_identifiers_ignore_case = false;
[0m09:43:18.579822 [debug] [ThreadPool]: Opening a new connection, currently in state init
[0m09:43:18.581244 [debug] [ThreadPool]: Opening a new connection, currently in state init
[0m09:43:18.817617 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.241 seconds
[0m09:43:18.823948 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_ANALYTICS"
[0m09:43:18.827035 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_ANALYTICS: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_ANALYTICS"} */
show objects in MYDB.LIVE_DATA_ANALYTICS
    limit 10000
    
;
[0m09:43:19.005192 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.174 seconds
[0m09:43:19.008251 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_ANALYTICS"
[0m09:43:19.009677 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_ANALYTICS: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_ANALYTICS"} */
alter session unset quoted_identifiers_ignore_case;
[0m09:43:19.079940 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.499 seconds
[0m09:43:19.083397 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.504 seconds
[0m09:43:19.085864 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_dbt_test__audit"
[0m09:43:19.089302 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA"
[0m09:43:19.091282 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_dbt_test__audit: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_dbt_test__audit"} */
show objects in MYDB.LIVE_DATA_dbt_test__audit
    limit 10000
    
;
[0m09:43:19.092731 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA"} */
show objects in MYDB.LIVE_DATA
    limit 10000
    
;
[0m09:43:19.156166 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.145 seconds
[0m09:43:19.245941 [debug] [ThreadPool]: SQL status: SUCCESS 21 in 0.152 seconds
[0m09:43:19.249279 [debug] [ThreadPool]: SQL status: SUCCESS 14 in 0.150 seconds
[0m09:43:19.252589 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_dbt_test__audit"
[0m09:43:19.255883 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA"
[0m09:43:19.257806 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_dbt_test__audit: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_dbt_test__audit"} */
alter session unset quoted_identifiers_ignore_case;
[0m09:43:19.259430 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA"} */
alter session unset quoted_identifiers_ignore_case;
[0m09:43:19.443997 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.183 seconds
[0m09:43:19.461838 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.197 seconds
[0m09:43:19.470663 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'runnable_timing', 'label': '08ced0c0-898b-434a-b218-6560f31e3b9c', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x78226fd69ed0>]}
[0m09:43:19.478869 [debug] [Thread-1 (]: Began running node model.live_c360.stg_events
[0m09:43:19.479979 [debug] [Thread-2 (]: Began running node model.live_c360.stg_orders
[0m09:43:19.480961 [debug] [Thread-3 (]: Began running node model.live_c360.stg_users
[0m09:43:19.486441 [info ] [Thread-3 (]: 3 of 3 START sql view model LIVE_DATA.stg_users ................................ [RUN]
[0m09:43:19.484758 [info ] [Thread-2 (]: 2 of 3 START sql view model LIVE_DATA.stg_orders ............................... [RUN]
[0m09:43:19.483190 [info ] [Thread-1 (]: 1 of 3 START sql view model LIVE_DATA.stg_events ............................... [RUN]
[0m09:43:19.491218 [debug] [Thread-1 (]: Re-using an available connection from the pool (formerly list_MYDB_LIVE_DATA, now model.live_c360.stg_events)
[0m09:43:19.489539 [debug] [Thread-2 (]: Re-using an available connection from the pool (formerly list_MYDB_LIVE_DATA_dbt_test__audit, now model.live_c360.stg_orders)
[0m09:43:19.488034 [debug] [Thread-3 (]: Re-using an available connection from the pool (formerly list_MYDB_LIVE_DATA_ANALYTICS, now model.live_c360.stg_users)
[0m09:43:19.497495 [debug] [Thread-3 (]: Began compiling node model.live_c360.stg_users
[0m09:43:19.493794 [debug] [Thread-2 (]: Began compiling node model.live_c360.stg_orders
[0m09:43:19.492458 [debug] [Thread-1 (]: Began compiling node model.live_c360.stg_events
[0m09:43:19.509000 [debug] [Thread-3 (]: Writing injected SQL for node "model.live_c360.stg_users"
[0m09:43:19.518737 [debug] [Thread-2 (]: Writing injected SQL for node "model.live_c360.stg_orders"
[0m09:43:19.525339 [debug] [Thread-1 (]: Writing injected SQL for node "model.live_c360.stg_events"
[0m09:43:19.536475 [debug] [Thread-3 (]: Began executing node model.live_c360.stg_users
[0m09:43:19.537468 [debug] [Thread-1 (]: Began executing node model.live_c360.stg_events
[0m09:43:19.546131 [debug] [Thread-2 (]: Began executing node model.live_c360.stg_orders
[0m09:43:19.608714 [debug] [Thread-1 (]: Writing runtime sql for node "model.live_c360.stg_events"
[0m09:43:19.613779 [debug] [Thread-2 (]: Writing runtime sql for node "model.live_c360.stg_orders"
[0m09:43:19.616611 [debug] [Thread-3 (]: Writing runtime sql for node "model.live_c360.stg_users"
[0m09:43:19.625221 [debug] [Thread-1 (]: Using snowflake connection "model.live_c360.stg_events"
[0m09:43:19.629154 [debug] [Thread-2 (]: Using snowflake connection "model.live_c360.stg_orders"
[0m09:43:19.632889 [debug] [Thread-1 (]: On model.live_c360.stg_events: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "node_id": "model.live_c360.stg_events"} */
create or replace   view MYDB.LIVE_DATA.stg_events
  
   as (
    

-- Staging model for live events data
-- Cleans and standardizes raw event data from S3 external table

with source_data as (
    select * from MYDB.LIVE_DATA.ext_live_events
),

cleaned_data as (
    select
        -- Primary key
        event_id,
        user_id,
        session_id,
        
        -- Event details
        try_to_timestamp(date, 'MM-DD-YYYY HH24:MI:SS') as event_timestamp,
        lower(trim(platform)) as platform,
        lower(trim(action)) as action,
        trim(url) as url,
        
        -- Metadata
        batch_id,
        try_to_timestamp(generated_at) as generated_at,
        current_timestamp() as processed_at
        
    from source_data
    where event_id is not null  -- Filter out any null event IDs
      and user_id is not null   -- Filter out events without user IDs
),

url_parsed as (
    select
        *,
        -- URL parsing
        case 
            when url like '%/product/%' then 'Product Page'
            when url like '%/cart%' then 'Cart'
            when url like '%/checkout%' then 'Checkout'
            when url like '%/search%' then 'Search'
            when url like '%/category/%' then 'Category'
            when url like '%/home%' or url = 'https://example.com/' then 'Home'
            else 'Other'
        end as page_type,
        
        -- Extract path from URL
        regexp_substr(url, 'https?://[^/]+(/.*)', 1, 1, 'e', 1) as url_path
        
    from cleaned_data
),

final as (
    select
        *,
        -- Date extractions
        date(event_timestamp) as event_date,
        extract(year from event_timestamp) as event_year,
        extract(month from event_timestamp) as event_month,
        extract(day from event_timestamp) as event_day,
        extract(hour from event_timestamp) as event_hour,
        dayname(event_timestamp) as event_day_name,
        
        -- Time-based segments
        case 
            when extract(hour from event_timestamp) between 6 and 11 then 'Morning'
            when extract(hour from event_timestamp) between 12 and 17 then 'Afternoon'
            when extract(hour from event_timestamp) between 18 and 22 then 'Evening'
            else 'Night'
        end as time_of_day_segment,
        
        -- Platform standardization
        case 
            when platform in ('ios', 'android') then 'Mobile'
            when platform = 'web' then 'Web'
            else 'Unknown'
        end as device_category,
        
        -- Action categorization
        case 
            when action in ('view', 'click') then 'Engagement'
            when action = 'log' then 'Authentication'
            when action = 'purchase' then 'Conversion'
            else 'Other'
        end as action_category,
        
        -- Funnel stage
        case 
            when action = 'view' and page_type = 'Home' then 'Awareness'
            when action = 'view' and page_type in ('Category', 'Search') then 'Interest'
            when action = 'view' and page_type = 'Product Page' then 'Consideration'
            when action = 'click' and page_type = 'Cart' then 'Intent'
            when action = 'purchase' then 'Purchase'
            else 'Other'
        end as funnel_stage,
        
        -- Data quality flags
        case 
            when event_timestamp > current_timestamp() then true
            else false
        end as has_future_event_timestamp,
        
        -- Recency
        datediff('minute', event_timestamp, current_timestamp()) as minutes_since_event,
        datediff('hour', event_timestamp, current_timestamp()) as hours_since_event,
        datediff('day', event_timestamp, current_timestamp()) as days_since_event
        
    from url_parsed
)

select * from final
  );
[0m09:43:19.634710 [debug] [Thread-2 (]: On model.live_c360.stg_orders: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "node_id": "model.live_c360.stg_orders"} */
create or replace   view MYDB.LIVE_DATA.stg_orders
  
   as (
    

-- Staging model for live orders data
-- Cleans and standardizes raw order data from S3 external table

with source_data as (
    select * from MYDB.LIVE_DATA.ext_live_orders
),

cleaned_data as (
    select
        -- Primary key
        id as order_id,
        user_id,
        
        -- Transaction details
        try_to_timestamp(transaction_date, 'MM-DD-YYYY HH24:MI:SS') as transaction_date,
        item_count,
        amount,
        
        -- Metadata
        batch_id,
        try_to_timestamp(generated_at) as generated_at,
        current_timestamp() as processed_at
        
    from source_data
    where id is not null  -- Filter out any null order IDs
      and user_id is not null  -- Filter out orders without user IDs
      and amount > 0  -- Filter out invalid amounts
),

final as (
    select
        *,
        -- Derived fields
        round(amount / item_count, 2) as avg_item_price,
        
        -- Date extractions
        date(transaction_date) as transaction_date_only,
        extract(year from transaction_date) as transaction_year,
        extract(month from transaction_date) as transaction_month,
        extract(day from transaction_date) as transaction_day,
        extract(hour from transaction_date) as transaction_hour,
        dayname(transaction_date) as transaction_day_name,
        
        -- Time-based segments
        case 
            when extract(hour from transaction_date) between 6 and 11 then 'Morning'
            when extract(hour from transaction_date) between 12 and 17 then 'Afternoon'
            when extract(hour from transaction_date) between 18 and 22 then 'Evening'
            else 'Night'
        end as time_of_day_segment,
        
        -- Order size segments
        case 
            when amount < 25 then 'Small'
            when amount < 75 then 'Medium'
            when amount < 150 then 'Large'
            else 'Extra Large'
        end as order_size_segment,
        
        -- Item quantity segments
        case 
            when item_count = 1 then 'Single Item'
            when item_count <= 3 then 'Few Items'
            when item_count <= 5 then 'Multiple Items'
            else 'Bulk Order'
        end as quantity_segment,
        
        -- Data quality flags
        case 
            when transaction_date > current_timestamp() then true
            else false
        end as has_future_transaction_date,
        
        case 
            when avg_item_price > 500 then true  -- Flag unusually expensive items
            else false
        end as has_high_item_price,
        
        -- Recency
        datediff('day', transaction_date, current_timestamp()) as days_since_transaction
        
    from cleaned_data
)

select * from final
  );
[0m09:43:19.642880 [debug] [Thread-3 (]: Using snowflake connection "model.live_c360.stg_users"
[0m09:43:19.644740 [debug] [Thread-3 (]: On model.live_c360.stg_users: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "node_id": "model.live_c360.stg_users"} */
create or replace   view MYDB.LIVE_DATA.stg_users
  
   as (
    

-- Staging model for live users data
-- Cleans and standardizes raw user data from S3 external table

with source_data as (
    select * from MYDB.LIVE_DATA.ext_live_users
),

cleaned_data as (
    select
        -- Primary key
        id as user_id,
        
        -- Personal information (anonymized)
        sha1(email) as email_hash,
        initcap(trim(firstname)) as firstname,
        initcap(trim(lastname)) as lastname,
        trim(address) as address,
        upper(trim(canal)) as acquisition_channel,
        upper(trim(country)) as country,
        
        -- Dates
        try_to_timestamp(creation_date, 'MM-DD-YYYY HH24:MI:SS') as creation_date,
        try_to_timestamp(last_activity_date, 'MM-DD-YYYY HH24:MI:SS') as last_activity_date,
        
        -- Demographics
        case 
            when gender = 0 then 'F'
            when gender = 1 then 'M'
            else 'Unknown'
        end as gender,
        age_group,
        
        -- Behavioral flags
        churn as is_churned,
        
        -- Metadata
        batch_id,
        try_to_timestamp(generated_at) as generated_at,
        current_timestamp() as processed_at
        
    from source_data
    where id is not null  -- Filter out any null user IDs
),

final as (
    select
        *,
        -- Derived fields
        datediff('day', creation_date, current_timestamp()) as days_since_creation,
        datediff('day', last_activity_date, current_timestamp()) as days_since_last_activity,
        
        -- Data quality flags
        case 
            when creation_date > current_timestamp() then true
            else false
        end as has_future_creation_date,
        
        case 
            when last_activity_date < creation_date then true
            else false
        end as has_invalid_activity_date,
        
        -- Segmentation
        case 
            when days_since_last_activity <= 7 then 'Active'
            when days_since_last_activity <= 30 then 'Recent'
            when days_since_last_activity <= 90 then 'Dormant'
            else 'Inactive'
        end as activity_segment
        
    from cleaned_data
)

select * from final
  );
[0m09:43:19.809762 [debug] [Thread-1 (]: Snowflake adapter: Snowflake query id: 01bccd27-3204-800f-0002-4ad6000650ae
[0m09:43:19.811042 [debug] [Thread-2 (]: Snowflake adapter: Snowflake query id: 01bccd27-3204-800f-0002-4ad6000650b2
[0m09:43:19.812236 [debug] [Thread-3 (]: Snowflake adapter: Snowflake query id: 01bccd27-3204-7fc5-0002-4ad6000631ae
[0m09:43:19.813581 [debug] [Thread-1 (]: Snowflake adapter: Snowflake error: 091093 (55000): External table EXT_LIVE_EVENTS marked invalid. Stage S3_LIVE_EVENTS_STAGE dropped.
[0m09:43:19.815318 [debug] [Thread-2 (]: Snowflake adapter: Snowflake error: 091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
[0m09:43:19.816724 [debug] [Thread-3 (]: Snowflake adapter: Snowflake error: 091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
[0m09:43:19.819298 [debug] [Thread-1 (]: Database Error in model stg_events (models/staging/stg_events.sql)
  091093 (55000): External table EXT_LIVE_EVENTS marked invalid. Stage S3_LIVE_EVENTS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_events.sql
[0m09:43:19.821570 [debug] [Thread-2 (]: Database Error in model stg_orders (models/staging/stg_orders.sql)
  091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_orders.sql
[0m09:43:19.823377 [debug] [Thread-3 (]: Database Error in model stg_users (models/staging/stg_users.sql)
  091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_users.sql
[0m09:43:19.826379 [debug] [Thread-1 (]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '08ced0c0-898b-434a-b218-6560f31e3b9c', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x78226f74ef50>]}
[0m09:43:19.827899 [debug] [Thread-2 (]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '08ced0c0-898b-434a-b218-6560f31e3b9c', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x78226d81bfa0>]}
[0m09:43:19.830488 [debug] [Thread-3 (]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '08ced0c0-898b-434a-b218-6560f31e3b9c', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x78226d8c2650>]}
[0m09:43:19.832681 [error] [Thread-1 (]: 1 of 3 ERROR creating sql view model LIVE_DATA.stg_events ...................... [[31mERROR[0m in 0.33s]
[0m09:43:19.835121 [error] [Thread-2 (]: 2 of 3 ERROR creating sql view model LIVE_DATA.stg_orders ...................... [[31mERROR[0m in 0.34s]
[0m09:43:19.837047 [error] [Thread-3 (]: 3 of 3 ERROR creating sql view model LIVE_DATA.stg_users ....................... [[31mERROR[0m in 0.34s]
[0m09:43:19.838807 [debug] [Thread-1 (]: Finished running node model.live_c360.stg_events
[0m09:43:19.840530 [debug] [Thread-2 (]: Finished running node model.live_c360.stg_orders
[0m09:43:19.842259 [debug] [Thread-3 (]: Finished running node model.live_c360.stg_users
[0m09:43:19.844291 [debug] [Thread-7 (]: Marking all children of 'model.live_c360.stg_events' to be skipped because of status 'error'.  Reason: Database Error in model stg_events (models/staging/stg_events.sql)
  091093 (55000): External table EXT_LIVE_EVENTS marked invalid. Stage S3_LIVE_EVENTS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_events.sql.
[0m09:43:19.849269 [debug] [Thread-7 (]: Marking all children of 'model.live_c360.stg_orders' to be skipped because of status 'error'.  Reason: Database Error in model stg_orders (models/staging/stg_orders.sql)
  091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_orders.sql.
[0m09:43:19.850800 [debug] [Thread-7 (]: Marking all children of 'model.live_c360.stg_users' to be skipped because of status 'error'.  Reason: Database Error in model stg_users (models/staging/stg_users.sql)
  091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_users.sql.
[0m09:43:19.853445 [debug] [MainThread]: Connection 'master' was properly closed.
[0m09:43:19.855027 [debug] [MainThread]: Connection 'model.live_c360.stg_users' was left open.
[0m09:43:19.856429 [debug] [MainThread]: On model.live_c360.stg_users: Close
[0m09:43:19.928467 [debug] [MainThread]: Connection 'model.live_c360.stg_events' was left open.
[0m09:43:19.930820 [debug] [MainThread]: On model.live_c360.stg_events: Close
[0m09:43:20.010192 [debug] [MainThread]: Connection 'model.live_c360.stg_orders' was left open.
[0m09:43:20.011906 [debug] [MainThread]: On model.live_c360.stg_orders: Close
[0m09:43:20.078516 [info ] [MainThread]: 
[0m09:43:20.080714 [info ] [MainThread]: Finished running 3 view models in 0 hours 0 minutes and 2.35 seconds (2.35s).
[0m09:43:20.083232 [debug] [MainThread]: Command end result
[0m09:43:20.153058 [debug] [MainThread]: Wrote artifact WritableManifest to /opt/airflow/workspace/dbt_live/target/manifest.json
[0m09:43:20.158656 [debug] [MainThread]: Wrote artifact SemanticManifest to /opt/airflow/workspace/dbt_live/target/semantic_manifest.json
[0m09:43:20.171828 [debug] [MainThread]: Wrote artifact RunExecutionResult to /opt/airflow/workspace/dbt_live/target/run_results.json
[0m09:43:20.173002 [info ] [MainThread]: 
[0m09:43:20.174134 [info ] [MainThread]: [31mCompleted with 3 errors, 0 partial successes, and 0 warnings:[0m
[0m09:43:20.175266 [info ] [MainThread]: 
[0m09:43:20.176468 [error] [MainThread]:   Database Error in model stg_events (models/staging/stg_events.sql)
  091093 (55000): External table EXT_LIVE_EVENTS marked invalid. Stage S3_LIVE_EVENTS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_events.sql
[0m09:43:20.177678 [info ] [MainThread]: 
[0m09:43:20.180035 [error] [MainThread]:   Database Error in model stg_orders (models/staging/stg_orders.sql)
  091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_orders.sql
[0m09:43:20.181397 [info ] [MainThread]: 
[0m09:43:20.182597 [error] [MainThread]:   Database Error in model stg_users (models/staging/stg_users.sql)
  091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_users.sql
[0m09:43:20.183729 [info ] [MainThread]: 
[0m09:43:20.184880 [info ] [MainThread]: Done. PASS=0 WARN=0 ERROR=3 SKIP=0 TOTAL=3
[0m09:43:20.186664 [debug] [MainThread]: Resource report: {"command_name": "run", "command_success": false, "command_wall_clock_time": 5.9534564, "process_in_blocks": "0", "process_kernel_time": 0.839369, "process_mem_max_rss": "204208", "process_out_blocks": "16", "process_user_time": 5.130912}
[0m09:43:20.188213 [debug] [MainThread]: Command `dbt run` failed at 09:43:20.188042 after 5.96 seconds
[0m09:43:20.189484 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x782287a6a8c0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7822876dd210>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x78226c6fd390>]}
[0m09:43:20.190718 [debug] [MainThread]: Flushing usage events
[0m09:43:21.169473 [debug] [MainThread]: An error was encountered while trying to flush usage events
[0m09:46:24.824291 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7f20e85e6860>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7f20e752a170>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7f20e75297e0>]}


============================== 09:46:24.831607 | a69937aa-50f3-48c6-b499-e8a56a859c30 ==============================
[0m09:46:24.831607 [info ] [MainThread]: Running with dbt=1.9.6
[0m09:46:24.833208 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'write_json': 'True', 'log_cache_events': 'False', 'partial_parse': 'True', 'cache_selected_only': 'False', 'warn_error': 'None', 'version_check': 'True', 'fail_fast': 'False', 'log_path': '/opt/airflow/workspace/dbt_live/logs', 'profiles_dir': '/opt/airflow/workspace/dbt_live', 'debug': 'False', 'use_colors': 'True', 'use_experimental_parser': 'False', 'no_print': 'None', 'quiet': 'False', 'empty': 'False', 'warn_error_options': 'WarnErrorOptions(include=[], exclude=[])', 'invocation_command': 'dbt run --models staging', 'introspect': 'True', 'static_parser': 'True', 'target_path': 'None', 'log_format': 'default', 'send_anonymous_usage_stats': 'True'}
[0m09:46:25.714858 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'project_id', 'label': 'a69937aa-50f3-48c6-b499-e8a56a859c30', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7f20e7528c70>]}
[0m09:46:25.792715 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'adapter_info', 'label': 'a69937aa-50f3-48c6-b499-e8a56a859c30', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7f20e96c8b80>]}
[0m09:46:25.794911 [info ] [MainThread]: Registered adapter: snowflake=1.9.4
[0m09:46:25.982148 [debug] [MainThread]: checksum: dd6dc1e5178459e3de3bf2eeb7c86bed4be2266c311fce8c15d86eb4ff94e7ad, vars: {}, profile: , target: , version: 1.9.6
[0m09:46:27.172747 [debug] [MainThread]: Partial parsing enabled: 0 files deleted, 0 files added, 0 files changed.
[0m09:46:27.174415 [debug] [MainThread]: Partial parsing enabled, no changes found, skipping parsing
[0m09:46:27.187080 [warn ] [MainThread]: [[33mWARNING[0m]: Configuration paths exist in your dbt_project.yml file which do not apply to any resources.
There are 3 unused configuration paths:
- models.live_c360.incremental
- seeds.live_c360
- snapshots.live_c360
[0m09:46:27.249645 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'load_project', 'label': 'a69937aa-50f3-48c6-b499-e8a56a859c30', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7f20d44b4130>]}
[0m09:46:27.438894 [debug] [MainThread]: Wrote artifact WritableManifest to /opt/airflow/workspace/dbt_live/target/manifest.json
[0m09:46:27.447267 [debug] [MainThread]: Wrote artifact SemanticManifest to /opt/airflow/workspace/dbt_live/target/semantic_manifest.json
[0m09:46:27.481662 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'resource_counts', 'label': 'a69937aa-50f3-48c6-b499-e8a56a859c30', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7f20d43002e0>]}
[0m09:46:27.483385 [info ] [MainThread]: Found 12 models, 21 data tests, 3 sources, 590 macros
[0m09:46:27.484678 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'runnable_timing', 'label': 'a69937aa-50f3-48c6-b499-e8a56a859c30', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7f20d43003d0>]}
[0m09:46:27.487303 [info ] [MainThread]: 
[0m09:46:27.488638 [info ] [MainThread]: Concurrency: 4 threads (target='live')
[0m09:46:27.489763 [info ] [MainThread]: 
[0m09:46:27.491156 [debug] [MainThread]: Acquiring new snowflake connection 'master'
[0m09:46:27.499293 [debug] [ThreadPool]: Acquiring new snowflake connection 'list_MYDB'
[0m09:46:27.551014 [debug] [ThreadPool]: Using snowflake connection "list_MYDB"
[0m09:46:27.552817 [debug] [ThreadPool]: On list_MYDB: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB"} */
show terse schemas in database MYDB
    limit 10000
[0m09:46:27.554547 [debug] [ThreadPool]: Opening a new connection, currently in state init
[0m09:46:28.130990 [debug] [ThreadPool]: SQL status: SUCCESS 6 in 0.576 seconds
[0m09:46:28.137788 [debug] [ThreadPool]: Re-using an available connection from the pool (formerly list_MYDB, now list_MYDB_LIVE_DATA_ANALYTICS)
[0m09:46:28.139015 [debug] [ThreadPool]: Acquiring new snowflake connection 'list_MYDB_LIVE_DATA_dbt_test__audit'
[0m09:46:28.140389 [debug] [ThreadPool]: Acquiring new snowflake connection 'list_MYDB_LIVE_DATA'
[0m09:46:28.154143 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_ANALYTICS"
[0m09:46:28.157773 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_dbt_test__audit"
[0m09:46:28.162534 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA"
[0m09:46:28.164291 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_ANALYTICS: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_ANALYTICS"} */
alter session set quoted_identifiers_ignore_case = false;
[0m09:46:28.165815 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_dbt_test__audit: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_dbt_test__audit"} */
alter session set quoted_identifiers_ignore_case = false;
[0m09:46:28.167259 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA"} */
alter session set quoted_identifiers_ignore_case = false;
[0m09:46:28.170950 [debug] [ThreadPool]: Opening a new connection, currently in state init
[0m09:46:28.172530 [debug] [ThreadPool]: Opening a new connection, currently in state init
[0m09:46:28.301195 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.133 seconds
[0m09:46:28.315129 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_ANALYTICS"
[0m09:46:28.324056 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_ANALYTICS: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_ANALYTICS"} */
show objects in MYDB.LIVE_DATA_ANALYTICS
    limit 10000
    
;
[0m09:46:28.421178 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.093 seconds
[0m09:46:28.424863 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_ANALYTICS"
[0m09:46:28.426082 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_ANALYTICS: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_ANALYTICS"} */
alter session unset quoted_identifiers_ignore_case;
[0m09:46:28.505895 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.077 seconds
[0m09:46:28.635840 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.465 seconds
[0m09:46:28.638794 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_dbt_test__audit"
[0m09:46:28.640034 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_dbt_test__audit: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_dbt_test__audit"} */
show objects in MYDB.LIVE_DATA_dbt_test__audit
    limit 10000
    
;
[0m09:46:28.652985 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.480 seconds
[0m09:46:28.655868 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA"
[0m09:46:28.657104 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA"} */
show objects in MYDB.LIVE_DATA
    limit 10000
    
;
[0m09:46:28.758139 [debug] [ThreadPool]: SQL status: SUCCESS 21 in 0.117 seconds
[0m09:46:28.764080 [debug] [ThreadPool]: SQL status: SUCCESS 14 in 0.106 seconds
[0m09:46:28.767515 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_dbt_test__audit"
[0m09:46:28.771526 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA"
[0m09:46:28.773178 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_dbt_test__audit: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_dbt_test__audit"} */
alter session unset quoted_identifiers_ignore_case;
[0m09:46:28.775262 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA"} */
alter session unset quoted_identifiers_ignore_case;
[0m09:46:28.838016 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.059 seconds
[0m09:46:28.854466 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.071 seconds
[0m09:46:28.861763 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'runnable_timing', 'label': 'a69937aa-50f3-48c6-b499-e8a56a859c30', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7f20d4931e70>]}
[0m09:46:28.869464 [debug] [Thread-1 (]: Began running node model.live_c360.stg_events
[0m09:46:28.870136 [debug] [Thread-2 (]: Began running node model.live_c360.stg_orders
[0m09:46:28.870729 [debug] [Thread-3 (]: Began running node model.live_c360.stg_users
[0m09:46:28.872569 [info ] [Thread-1 (]: 1 of 3 START sql view model LIVE_DATA.stg_events ............................... [RUN]
[0m09:46:28.874256 [info ] [Thread-2 (]: 2 of 3 START sql view model LIVE_DATA.stg_orders ............................... [RUN]
[0m09:46:28.879718 [info ] [Thread-3 (]: 3 of 3 START sql view model LIVE_DATA.stg_users ................................ [RUN]
[0m09:46:28.881282 [debug] [Thread-1 (]: Re-using an available connection from the pool (formerly list_MYDB_LIVE_DATA, now model.live_c360.stg_events)
[0m09:46:28.882663 [debug] [Thread-2 (]: Re-using an available connection from the pool (formerly list_MYDB_LIVE_DATA_dbt_test__audit, now model.live_c360.stg_orders)
[0m09:46:28.884141 [debug] [Thread-3 (]: Re-using an available connection from the pool (formerly list_MYDB_LIVE_DATA_ANALYTICS, now model.live_c360.stg_users)
[0m09:46:28.885373 [debug] [Thread-1 (]: Began compiling node model.live_c360.stg_events
[0m09:46:28.886702 [debug] [Thread-2 (]: Began compiling node model.live_c360.stg_orders
[0m09:46:28.888054 [debug] [Thread-3 (]: Began compiling node model.live_c360.stg_users
[0m09:46:28.906756 [debug] [Thread-1 (]: Writing injected SQL for node "model.live_c360.stg_events"
[0m09:46:28.912630 [debug] [Thread-2 (]: Writing injected SQL for node "model.live_c360.stg_orders"
[0m09:46:28.918079 [debug] [Thread-3 (]: Writing injected SQL for node "model.live_c360.stg_users"
[0m09:46:28.926313 [debug] [Thread-1 (]: Began executing node model.live_c360.stg_events
[0m09:46:28.927257 [debug] [Thread-3 (]: Began executing node model.live_c360.stg_users
[0m09:46:28.935048 [debug] [Thread-2 (]: Began executing node model.live_c360.stg_orders
[0m09:46:29.008984 [debug] [Thread-3 (]: Writing runtime sql for node "model.live_c360.stg_users"
[0m09:46:29.025606 [debug] [Thread-1 (]: Writing runtime sql for node "model.live_c360.stg_events"
[0m09:46:29.028794 [debug] [Thread-2 (]: Writing runtime sql for node "model.live_c360.stg_orders"
[0m09:46:29.095327 [debug] [Thread-1 (]: Using snowflake connection "model.live_c360.stg_events"
[0m09:46:29.116803 [debug] [Thread-2 (]: Using snowflake connection "model.live_c360.stg_orders"
[0m09:46:29.126022 [debug] [Thread-1 (]: On model.live_c360.stg_events: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "node_id": "model.live_c360.stg_events"} */
create or replace   view MYDB.LIVE_DATA.stg_events
  
   as (
    

-- Staging model for live events data
-- Cleans and standardizes raw event data from S3 external table

with source_data as (
    select * from MYDB.LIVE_DATA.ext_live_events
),

cleaned_data as (
    select
        -- Primary key
        event_id,
        user_id,
        session_id,
        
        -- Event details
        try_to_timestamp(date, 'MM-DD-YYYY HH24:MI:SS') as event_timestamp,
        lower(trim(platform)) as platform,
        lower(trim(action)) as action,
        trim(url) as url,
        
        -- Metadata
        batch_id,
        try_to_timestamp(generated_at) as generated_at,
        current_timestamp() as processed_at
        
    from source_data
    where event_id is not null  -- Filter out any null event IDs
      and user_id is not null   -- Filter out events without user IDs
),

url_parsed as (
    select
        *,
        -- URL parsing
        case 
            when url like '%/product/%' then 'Product Page'
            when url like '%/cart%' then 'Cart'
            when url like '%/checkout%' then 'Checkout'
            when url like '%/search%' then 'Search'
            when url like '%/category/%' then 'Category'
            when url like '%/home%' or url = 'https://example.com/' then 'Home'
            else 'Other'
        end as page_type,
        
        -- Extract path from URL
        regexp_substr(url, 'https?://[^/]+(/.*)', 1, 1, 'e', 1) as url_path
        
    from cleaned_data
),

final as (
    select
        *,
        -- Date extractions
        date(event_timestamp) as event_date,
        extract(year from event_timestamp) as event_year,
        extract(month from event_timestamp) as event_month,
        extract(day from event_timestamp) as event_day,
        extract(hour from event_timestamp) as event_hour,
        dayname(event_timestamp) as event_day_name,
        
        -- Time-based segments
        case 
            when extract(hour from event_timestamp) between 6 and 11 then 'Morning'
            when extract(hour from event_timestamp) between 12 and 17 then 'Afternoon'
            when extract(hour from event_timestamp) between 18 and 22 then 'Evening'
            else 'Night'
        end as time_of_day_segment,
        
        -- Platform standardization
        case 
            when platform in ('ios', 'android') then 'Mobile'
            when platform = 'web' then 'Web'
            else 'Unknown'
        end as device_category,
        
        -- Action categorization
        case 
            when action in ('view', 'click') then 'Engagement'
            when action = 'log' then 'Authentication'
            when action = 'purchase' then 'Conversion'
            else 'Other'
        end as action_category,
        
        -- Funnel stage
        case 
            when action = 'view' and page_type = 'Home' then 'Awareness'
            when action = 'view' and page_type in ('Category', 'Search') then 'Interest'
            when action = 'view' and page_type = 'Product Page' then 'Consideration'
            when action = 'click' and page_type = 'Cart' then 'Intent'
            when action = 'purchase' then 'Purchase'
            else 'Other'
        end as funnel_stage,
        
        -- Data quality flags
        case 
            when event_timestamp > current_timestamp() then true
            else false
        end as has_future_event_timestamp,
        
        -- Recency
        datediff('minute', event_timestamp, current_timestamp()) as minutes_since_event,
        datediff('hour', event_timestamp, current_timestamp()) as hours_since_event,
        datediff('day', event_timestamp, current_timestamp()) as days_since_event
        
    from url_parsed
)

select * from final
  );
[0m09:46:29.136189 [debug] [Thread-2 (]: On model.live_c360.stg_orders: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "node_id": "model.live_c360.stg_orders"} */
create or replace   view MYDB.LIVE_DATA.stg_orders
  
   as (
    

-- Staging model for live orders data
-- Cleans and standardizes raw order data from S3 external table

with source_data as (
    select * from MYDB.LIVE_DATA.ext_live_orders
),

cleaned_data as (
    select
        -- Primary key
        id as order_id,
        user_id,
        
        -- Transaction details
        try_to_timestamp(transaction_date, 'MM-DD-YYYY HH24:MI:SS') as transaction_date,
        item_count,
        amount,
        
        -- Metadata
        batch_id,
        try_to_timestamp(generated_at) as generated_at,
        current_timestamp() as processed_at
        
    from source_data
    where id is not null  -- Filter out any null order IDs
      and user_id is not null  -- Filter out orders without user IDs
      and amount > 0  -- Filter out invalid amounts
),

final as (
    select
        *,
        -- Derived fields
        round(amount / item_count, 2) as avg_item_price,
        
        -- Date extractions
        date(transaction_date) as transaction_date_only,
        extract(year from transaction_date) as transaction_year,
        extract(month from transaction_date) as transaction_month,
        extract(day from transaction_date) as transaction_day,
        extract(hour from transaction_date) as transaction_hour,
        dayname(transaction_date) as transaction_day_name,
        
        -- Time-based segments
        case 
            when extract(hour from transaction_date) between 6 and 11 then 'Morning'
            when extract(hour from transaction_date) between 12 and 17 then 'Afternoon'
            when extract(hour from transaction_date) between 18 and 22 then 'Evening'
            else 'Night'
        end as time_of_day_segment,
        
        -- Order size segments
        case 
            when amount < 25 then 'Small'
            when amount < 75 then 'Medium'
            when amount < 150 then 'Large'
            else 'Extra Large'
        end as order_size_segment,
        
        -- Item quantity segments
        case 
            when item_count = 1 then 'Single Item'
            when item_count <= 3 then 'Few Items'
            when item_count <= 5 then 'Multiple Items'
            else 'Bulk Order'
        end as quantity_segment,
        
        -- Data quality flags
        case 
            when transaction_date > current_timestamp() then true
            else false
        end as has_future_transaction_date,
        
        case 
            when avg_item_price > 500 then true  -- Flag unusually expensive items
            else false
        end as has_high_item_price,
        
        -- Recency
        datediff('day', transaction_date, current_timestamp()) as days_since_transaction
        
    from cleaned_data
)

select * from final
  );
[0m09:46:29.150838 [debug] [Thread-3 (]: Using snowflake connection "model.live_c360.stg_users"
[0m09:46:29.158226 [debug] [Thread-3 (]: On model.live_c360.stg_users: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "node_id": "model.live_c360.stg_users"} */
create or replace   view MYDB.LIVE_DATA.stg_users
  
   as (
    

-- Staging model for live users data
-- Cleans and standardizes raw user data from S3 external table

with source_data as (
    select * from MYDB.LIVE_DATA.ext_live_users
),

cleaned_data as (
    select
        -- Primary key
        id as user_id,
        
        -- Personal information (anonymized)
        sha1(email) as email_hash,
        initcap(trim(firstname)) as firstname,
        initcap(trim(lastname)) as lastname,
        trim(address) as address,
        upper(trim(canal)) as acquisition_channel,
        upper(trim(country)) as country,
        
        -- Dates
        try_to_timestamp(creation_date, 'MM-DD-YYYY HH24:MI:SS') as creation_date,
        try_to_timestamp(last_activity_date, 'MM-DD-YYYY HH24:MI:SS') as last_activity_date,
        
        -- Demographics
        case 
            when gender = 0 then 'F'
            when gender = 1 then 'M'
            else 'Unknown'
        end as gender,
        age_group,
        
        -- Behavioral flags
        churn as is_churned,
        
        -- Metadata
        batch_id,
        try_to_timestamp(generated_at) as generated_at,
        current_timestamp() as processed_at
        
    from source_data
    where id is not null  -- Filter out any null user IDs
),

final as (
    select
        *,
        -- Derived fields
        datediff('day', creation_date, current_timestamp()) as days_since_creation,
        datediff('day', last_activity_date, current_timestamp()) as days_since_last_activity,
        
        -- Data quality flags
        case 
            when creation_date > current_timestamp() then true
            else false
        end as has_future_creation_date,
        
        case 
            when last_activity_date < creation_date then true
            else false
        end as has_invalid_activity_date,
        
        -- Segmentation
        case 
            when days_since_last_activity <= 7 then 'Active'
            when days_since_last_activity <= 30 then 'Recent'
            when days_since_last_activity <= 90 then 'Dormant'
            else 'Inactive'
        end as activity_segment
        
    from cleaned_data
)

select * from final
  );
[0m09:46:29.287512 [debug] [Thread-1 (]: Snowflake adapter: Snowflake query id: 01bccd2a-3204-7eab-0002-4ad6000611fa
[0m09:46:29.289939 [debug] [Thread-1 (]: Snowflake adapter: Snowflake error: 091093 (55000): External table EXT_LIVE_EVENTS marked invalid. Stage S3_LIVE_EVENTS_STAGE dropped.
[0m09:46:29.292880 [debug] [Thread-1 (]: Database Error in model stg_events (models/staging/stg_events.sql)
  091093 (55000): External table EXT_LIVE_EVENTS marked invalid. Stage S3_LIVE_EVENTS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_events.sql
[0m09:46:29.295175 [debug] [Thread-2 (]: Snowflake adapter: Snowflake query id: 01bccd2a-3204-802b-0002-4ad6000680da
[0m09:46:29.299875 [debug] [Thread-1 (]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': 'a69937aa-50f3-48c6-b499-e8a56a859c30', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7f20ce3be950>]}
[0m09:46:29.301040 [debug] [Thread-3 (]: Snowflake adapter: Snowflake query id: 01bccd2a-3204-7f69-0002-4ad60005d1be
[0m09:46:29.301995 [debug] [Thread-2 (]: Snowflake adapter: Snowflake error: 091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
[0m09:46:29.304233 [error] [Thread-1 (]: 1 of 3 ERROR creating sql view model LIVE_DATA.stg_events ...................... [[31mERROR[0m in 0.42s]
[0m09:46:29.305493 [debug] [Thread-3 (]: Snowflake adapter: Snowflake error: 091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
[0m09:46:29.307806 [debug] [Thread-2 (]: Database Error in model stg_orders (models/staging/stg_orders.sql)
  091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_orders.sql
[0m09:46:29.309039 [debug] [Thread-1 (]: Finished running node model.live_c360.stg_events
[0m09:46:29.312967 [debug] [Thread-3 (]: Database Error in model stg_users (models/staging/stg_users.sql)
  091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_users.sql
[0m09:46:29.314657 [debug] [Thread-2 (]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': 'a69937aa-50f3-48c6-b499-e8a56a859c30', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7f20cc1fef50>]}
[0m09:46:29.316382 [debug] [Thread-7 (]: Marking all children of 'model.live_c360.stg_events' to be skipped because of status 'error'.  Reason: Database Error in model stg_events (models/staging/stg_events.sql)
  091093 (55000): External table EXT_LIVE_EVENTS marked invalid. Stage S3_LIVE_EVENTS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_events.sql.
[0m09:46:29.317745 [debug] [Thread-3 (]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': 'a69937aa-50f3-48c6-b499-e8a56a859c30', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7f20cc1ff9a0>]}
[0m09:46:29.319395 [error] [Thread-2 (]: 2 of 3 ERROR creating sql view model LIVE_DATA.stg_orders ...................... [[31mERROR[0m in 0.43s]
[0m09:46:29.322485 [error] [Thread-3 (]: 3 of 3 ERROR creating sql view model LIVE_DATA.stg_users ....................... [[31mERROR[0m in 0.43s]
[0m09:46:29.324020 [debug] [Thread-2 (]: Finished running node model.live_c360.stg_orders
[0m09:46:29.325670 [debug] [Thread-3 (]: Finished running node model.live_c360.stg_users
[0m09:46:29.329128 [debug] [Thread-7 (]: Marking all children of 'model.live_c360.stg_orders' to be skipped because of status 'error'.  Reason: Database Error in model stg_orders (models/staging/stg_orders.sql)
  091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_orders.sql.
[0m09:46:29.331150 [debug] [Thread-7 (]: Marking all children of 'model.live_c360.stg_users' to be skipped because of status 'error'.  Reason: Database Error in model stg_users (models/staging/stg_users.sql)
  091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_users.sql.
[0m09:46:29.334285 [debug] [MainThread]: Connection 'master' was properly closed.
[0m09:46:29.335922 [debug] [MainThread]: Connection 'model.live_c360.stg_users' was left open.
[0m09:46:29.337130 [debug] [MainThread]: On model.live_c360.stg_users: Close
[0m09:46:30.032049 [debug] [MainThread]: Connection 'model.live_c360.stg_orders' was left open.
[0m09:46:30.033416 [debug] [MainThread]: On model.live_c360.stg_orders: Close
[0m09:46:30.108370 [debug] [MainThread]: Connection 'model.live_c360.stg_events' was left open.
[0m09:46:30.109833 [debug] [MainThread]: On model.live_c360.stg_events: Close
[0m09:46:30.185495 [info ] [MainThread]: 
[0m09:46:30.187223 [info ] [MainThread]: Finished running 3 view models in 0 hours 0 minutes and 2.69 seconds (2.69s).
[0m09:46:30.190652 [debug] [MainThread]: Command end result
[0m09:46:30.326154 [debug] [MainThread]: Wrote artifact WritableManifest to /opt/airflow/workspace/dbt_live/target/manifest.json
[0m09:46:30.332886 [debug] [MainThread]: Wrote artifact SemanticManifest to /opt/airflow/workspace/dbt_live/target/semantic_manifest.json
[0m09:46:30.353123 [debug] [MainThread]: Wrote artifact RunExecutionResult to /opt/airflow/workspace/dbt_live/target/run_results.json
[0m09:46:30.354305 [info ] [MainThread]: 
[0m09:46:30.356461 [info ] [MainThread]: [31mCompleted with 3 errors, 0 partial successes, and 0 warnings:[0m
[0m09:46:30.358442 [info ] [MainThread]: 
[0m09:46:30.362838 [error] [MainThread]:   Database Error in model stg_events (models/staging/stg_events.sql)
  091093 (55000): External table EXT_LIVE_EVENTS marked invalid. Stage S3_LIVE_EVENTS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_events.sql
[0m09:46:30.364698 [info ] [MainThread]: 
[0m09:46:30.366259 [error] [MainThread]:   Database Error in model stg_orders (models/staging/stg_orders.sql)
  091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_orders.sql
[0m09:46:30.368226 [info ] [MainThread]: 
[0m09:46:30.370931 [error] [MainThread]:   Database Error in model stg_users (models/staging/stg_users.sql)
  091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_users.sql
[0m09:46:30.372079 [info ] [MainThread]: 
[0m09:46:30.373148 [info ] [MainThread]: Done. PASS=0 WARN=0 ERROR=3 SKIP=0 TOTAL=3
[0m09:46:30.374977 [debug] [MainThread]: Resource report: {"command_name": "run", "command_success": false, "command_wall_clock_time": 5.6446824, "process_in_blocks": "0", "process_kernel_time": 0.640496, "process_mem_max_rss": "204604", "process_out_blocks": "16", "process_user_time": 3.786225}
[0m09:46:30.377016 [debug] [MainThread]: Command `dbt run` failed at 09:46:30.376789 after 5.65 seconds
[0m09:46:30.380358 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7f20e85e6860>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7f20e8451e40>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7f20ce3d6050>]}
[0m09:46:30.381502 [debug] [MainThread]: Flushing usage events
[0m09:46:31.351506 [debug] [MainThread]: An error was encountered while trying to flush usage events
[0m09:49:35.078121 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x72b044d2e920>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x72b0449a29e0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x72b0442ee6e0>]}


============================== 09:49:35.085374 | 23a97560-d17d-4d90-9da0-987538473b6d ==============================
[0m09:49:35.085374 [info ] [MainThread]: Running with dbt=1.9.6
[0m09:49:35.086822 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'log_cache_events': 'False', 'write_json': 'True', 'partial_parse': 'True', 'cache_selected_only': 'False', 'profiles_dir': '/opt/airflow/workspace/dbt_live', 'debug': 'False', 'fail_fast': 'False', 'log_path': '/opt/airflow/workspace/dbt_live/logs', 'version_check': 'True', 'warn_error': 'None', 'use_colors': 'True', 'use_experimental_parser': 'False', 'no_print': 'None', 'quiet': 'False', 'empty': 'False', 'warn_error_options': 'WarnErrorOptions(include=[], exclude=[])', 'static_parser': 'True', 'introspect': 'True', 'invocation_command': 'dbt run --models staging', 'target_path': 'None', 'log_format': 'default', 'send_anonymous_usage_stats': 'True'}
[0m09:49:35.909235 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'project_id', 'label': '23a97560-d17d-4d90-9da0-987538473b6d', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x72b043c62650>]}
[0m09:49:35.985526 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'adapter_info', 'label': '23a97560-d17d-4d90-9da0-987538473b6d', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x72b0310f4d00>]}
[0m09:49:35.987508 [info ] [MainThread]: Registered adapter: snowflake=1.9.4
[0m09:49:36.178219 [debug] [MainThread]: checksum: dd6dc1e5178459e3de3bf2eeb7c86bed4be2266c311fce8c15d86eb4ff94e7ad, vars: {}, profile: , target: , version: 1.9.6
[0m09:49:37.439425 [debug] [MainThread]: Partial parsing enabled: 0 files deleted, 0 files added, 0 files changed.
[0m09:49:37.441091 [debug] [MainThread]: Partial parsing enabled, no changes found, skipping parsing
[0m09:49:37.451756 [warn ] [MainThread]: [[33mWARNING[0m]: Configuration paths exist in your dbt_project.yml file which do not apply to any resources.
There are 3 unused configuration paths:
- models.live_c360.incremental
- seeds.live_c360
- snapshots.live_c360
[0m09:49:37.515979 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'load_project', 'label': '23a97560-d17d-4d90-9da0-987538473b6d', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x72b030a08130>]}
[0m09:49:37.683175 [debug] [MainThread]: Wrote artifact WritableManifest to /opt/airflow/workspace/dbt_live/target/manifest.json
[0m09:49:37.688042 [debug] [MainThread]: Wrote artifact SemanticManifest to /opt/airflow/workspace/dbt_live/target/semantic_manifest.json
[0m09:49:37.719028 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'resource_counts', 'label': '23a97560-d17d-4d90-9da0-987538473b6d', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x72b030a54370>]}
[0m09:49:37.720184 [info ] [MainThread]: Found 12 models, 21 data tests, 3 sources, 590 macros
[0m09:49:37.721153 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'runnable_timing', 'label': '23a97560-d17d-4d90-9da0-987538473b6d', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x72b030a54460>]}
[0m09:49:37.723688 [info ] [MainThread]: 
[0m09:49:37.724736 [info ] [MainThread]: Concurrency: 4 threads (target='live')
[0m09:49:37.725608 [info ] [MainThread]: 
[0m09:49:37.726709 [debug] [MainThread]: Acquiring new snowflake connection 'master'
[0m09:49:37.736470 [debug] [ThreadPool]: Acquiring new snowflake connection 'list_MYDB'
[0m09:49:37.801540 [debug] [ThreadPool]: Using snowflake connection "list_MYDB"
[0m09:49:37.802608 [debug] [ThreadPool]: On list_MYDB: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB"} */
show terse schemas in database MYDB
    limit 10000
[0m09:49:37.803490 [debug] [ThreadPool]: Opening a new connection, currently in state init
[0m09:49:38.399548 [debug] [ThreadPool]: SQL status: SUCCESS 6 in 0.596 seconds
[0m09:49:38.406651 [debug] [ThreadPool]: Re-using an available connection from the pool (formerly list_MYDB, now list_MYDB_LIVE_DATA_dbt_test__audit)
[0m09:49:38.407917 [debug] [ThreadPool]: Acquiring new snowflake connection 'list_MYDB_LIVE_DATA_ANALYTICS'
[0m09:49:38.409021 [debug] [ThreadPool]: Acquiring new snowflake connection 'list_MYDB_LIVE_DATA'
[0m09:49:38.423403 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_dbt_test__audit"
[0m09:49:38.427050 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_ANALYTICS"
[0m09:49:38.432159 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA"
[0m09:49:38.433949 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_dbt_test__audit: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_dbt_test__audit"} */
alter session set quoted_identifiers_ignore_case = false;
[0m09:49:38.435357 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_ANALYTICS: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_ANALYTICS"} */
alter session set quoted_identifiers_ignore_case = false;
[0m09:49:38.436791 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA"} */
alter session set quoted_identifiers_ignore_case = false;
[0m09:49:38.440326 [debug] [ThreadPool]: Opening a new connection, currently in state init
[0m09:49:38.442717 [debug] [ThreadPool]: Opening a new connection, currently in state init
[0m09:49:38.527752 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.090 seconds
[0m09:49:38.538338 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_dbt_test__audit"
[0m09:49:38.539783 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_dbt_test__audit: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_dbt_test__audit"} */
show objects in MYDB.LIVE_DATA_dbt_test__audit
    limit 10000
    
;
[0m09:49:38.645971 [debug] [ThreadPool]: SQL status: SUCCESS 21 in 0.105 seconds
[0m09:49:38.651778 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_dbt_test__audit"
[0m09:49:38.653433 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_dbt_test__audit: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_dbt_test__audit"} */
alter session unset quoted_identifiers_ignore_case;
[0m09:49:38.751488 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.096 seconds
[0m09:49:38.769662 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.329 seconds
[0m09:49:38.772988 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_ANALYTICS"
[0m09:49:38.774532 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_ANALYTICS: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_ANALYTICS"} */
show objects in MYDB.LIVE_DATA_ANALYTICS
    limit 10000
    
;
[0m09:49:38.877577 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.102 seconds
[0m09:49:38.882245 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_ANALYTICS"
[0m09:49:38.883734 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_ANALYTICS: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_ANALYTICS"} */
alter session unset quoted_identifiers_ignore_case;
[0m09:49:38.896704 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.454 seconds
[0m09:49:38.900736 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA"
[0m09:49:38.902247 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA"} */
show objects in MYDB.LIVE_DATA
    limit 10000
    
;
[0m09:49:38.968413 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.083 seconds
[0m09:49:39.034309 [debug] [ThreadPool]: SQL status: SUCCESS 14 in 0.131 seconds
[0m09:49:39.038172 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA"
[0m09:49:39.040355 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA"} */
alter session unset quoted_identifiers_ignore_case;
[0m09:49:39.117222 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.075 seconds
[0m09:49:39.125695 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'runnable_timing', 'label': '23a97560-d17d-4d90-9da0-987538473b6d', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x72b0460a1780>]}
[0m09:49:39.134080 [debug] [Thread-1 (]: Began running node model.live_c360.stg_events
[0m09:49:39.134847 [debug] [Thread-2 (]: Began running node model.live_c360.stg_orders
[0m09:49:39.135667 [debug] [Thread-3 (]: Began running node model.live_c360.stg_users
[0m09:49:39.137812 [info ] [Thread-1 (]: 1 of 3 START sql view model LIVE_DATA.stg_events ............................... [RUN]
[0m09:49:39.140662 [info ] [Thread-2 (]: 2 of 3 START sql view model LIVE_DATA.stg_orders ............................... [RUN]
[0m09:49:39.144051 [info ] [Thread-3 (]: 3 of 3 START sql view model LIVE_DATA.stg_users ................................ [RUN]
[0m09:49:39.151041 [debug] [Thread-3 (]: Re-using an available connection from the pool (formerly list_MYDB_LIVE_DATA_dbt_test__audit, now model.live_c360.stg_users)
[0m09:49:39.152266 [debug] [Thread-3 (]: Began compiling node model.live_c360.stg_users
[0m09:49:39.145786 [debug] [Thread-1 (]: Re-using an available connection from the pool (formerly list_MYDB_LIVE_DATA_ANALYTICS, now model.live_c360.stg_events)
[0m09:49:39.167132 [debug] [Thread-1 (]: Began compiling node model.live_c360.stg_events
[0m09:49:39.165888 [debug] [Thread-3 (]: Writing injected SQL for node "model.live_c360.stg_users"
[0m09:49:39.148619 [debug] [Thread-2 (]: Re-using an available connection from the pool (formerly list_MYDB_LIVE_DATA, now model.live_c360.stg_orders)
[0m09:49:39.183666 [debug] [Thread-2 (]: Began compiling node model.live_c360.stg_orders
[0m09:49:39.172814 [debug] [Thread-1 (]: Writing injected SQL for node "model.live_c360.stg_events"
[0m09:49:39.189419 [debug] [Thread-2 (]: Writing injected SQL for node "model.live_c360.stg_orders"
[0m09:49:39.195620 [debug] [Thread-3 (]: Began executing node model.live_c360.stg_users
[0m09:49:39.196640 [debug] [Thread-1 (]: Began executing node model.live_c360.stg_events
[0m09:49:39.204359 [debug] [Thread-2 (]: Began executing node model.live_c360.stg_orders
[0m09:49:39.243590 [debug] [Thread-3 (]: Writing runtime sql for node "model.live_c360.stg_users"
[0m09:49:39.245925 [debug] [Thread-1 (]: Writing runtime sql for node "model.live_c360.stg_events"
[0m09:49:39.251875 [debug] [Thread-2 (]: Writing runtime sql for node "model.live_c360.stg_orders"
[0m09:49:39.259434 [debug] [Thread-2 (]: Using snowflake connection "model.live_c360.stg_orders"
[0m09:49:39.263185 [debug] [Thread-1 (]: Using snowflake connection "model.live_c360.stg_events"
[0m09:49:39.266150 [debug] [Thread-2 (]: On model.live_c360.stg_orders: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "node_id": "model.live_c360.stg_orders"} */
create or replace   view MYDB.LIVE_DATA.stg_orders
  
   as (
    

-- Staging model for live orders data
-- Cleans and standardizes raw order data from S3 external table

with source_data as (
    select * from MYDB.LIVE_DATA.ext_live_orders
),

cleaned_data as (
    select
        -- Primary key
        id as order_id,
        user_id,
        
        -- Transaction details
        try_to_timestamp(transaction_date, 'MM-DD-YYYY HH24:MI:SS') as transaction_date,
        item_count,
        amount,
        
        -- Metadata
        batch_id,
        try_to_timestamp(generated_at) as generated_at,
        current_timestamp() as processed_at
        
    from source_data
    where id is not null  -- Filter out any null order IDs
      and user_id is not null  -- Filter out orders without user IDs
      and amount > 0  -- Filter out invalid amounts
),

final as (
    select
        *,
        -- Derived fields
        round(amount / item_count, 2) as avg_item_price,
        
        -- Date extractions
        date(transaction_date) as transaction_date_only,
        extract(year from transaction_date) as transaction_year,
        extract(month from transaction_date) as transaction_month,
        extract(day from transaction_date) as transaction_day,
        extract(hour from transaction_date) as transaction_hour,
        dayname(transaction_date) as transaction_day_name,
        
        -- Time-based segments
        case 
            when extract(hour from transaction_date) between 6 and 11 then 'Morning'
            when extract(hour from transaction_date) between 12 and 17 then 'Afternoon'
            when extract(hour from transaction_date) between 18 and 22 then 'Evening'
            else 'Night'
        end as time_of_day_segment,
        
        -- Order size segments
        case 
            when amount < 25 then 'Small'
            when amount < 75 then 'Medium'
            when amount < 150 then 'Large'
            else 'Extra Large'
        end as order_size_segment,
        
        -- Item quantity segments
        case 
            when item_count = 1 then 'Single Item'
            when item_count <= 3 then 'Few Items'
            when item_count <= 5 then 'Multiple Items'
            else 'Bulk Order'
        end as quantity_segment,
        
        -- Data quality flags
        case 
            when transaction_date > current_timestamp() then true
            else false
        end as has_future_transaction_date,
        
        case 
            when avg_item_price > 500 then true  -- Flag unusually expensive items
            else false
        end as has_high_item_price,
        
        -- Recency
        datediff('day', transaction_date, current_timestamp()) as days_since_transaction
        
    from cleaned_data
)

select * from final
  );
[0m09:49:39.270058 [debug] [Thread-3 (]: Using snowflake connection "model.live_c360.stg_users"
[0m09:49:39.271134 [debug] [Thread-1 (]: On model.live_c360.stg_events: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "node_id": "model.live_c360.stg_events"} */
create or replace   view MYDB.LIVE_DATA.stg_events
  
   as (
    

-- Staging model for live events data
-- Cleans and standardizes raw event data from S3 external table

with source_data as (
    select * from MYDB.LIVE_DATA.ext_live_events
),

cleaned_data as (
    select
        -- Primary key
        event_id,
        user_id,
        session_id,
        
        -- Event details
        try_to_timestamp(date, 'MM-DD-YYYY HH24:MI:SS') as event_timestamp,
        lower(trim(platform)) as platform,
        lower(trim(action)) as action,
        trim(url) as url,
        
        -- Metadata
        batch_id,
        try_to_timestamp(generated_at) as generated_at,
        current_timestamp() as processed_at
        
    from source_data
    where event_id is not null  -- Filter out any null event IDs
      and user_id is not null   -- Filter out events without user IDs
),

url_parsed as (
    select
        *,
        -- URL parsing
        case 
            when url like '%/product/%' then 'Product Page'
            when url like '%/cart%' then 'Cart'
            when url like '%/checkout%' then 'Checkout'
            when url like '%/search%' then 'Search'
            when url like '%/category/%' then 'Category'
            when url like '%/home%' or url = 'https://example.com/' then 'Home'
            else 'Other'
        end as page_type,
        
        -- Extract path from URL
        regexp_substr(url, 'https?://[^/]+(/.*)', 1, 1, 'e', 1) as url_path
        
    from cleaned_data
),

final as (
    select
        *,
        -- Date extractions
        date(event_timestamp) as event_date,
        extract(year from event_timestamp) as event_year,
        extract(month from event_timestamp) as event_month,
        extract(day from event_timestamp) as event_day,
        extract(hour from event_timestamp) as event_hour,
        dayname(event_timestamp) as event_day_name,
        
        -- Time-based segments
        case 
            when extract(hour from event_timestamp) between 6 and 11 then 'Morning'
            when extract(hour from event_timestamp) between 12 and 17 then 'Afternoon'
            when extract(hour from event_timestamp) between 18 and 22 then 'Evening'
            else 'Night'
        end as time_of_day_segment,
        
        -- Platform standardization
        case 
            when platform in ('ios', 'android') then 'Mobile'
            when platform = 'web' then 'Web'
            else 'Unknown'
        end as device_category,
        
        -- Action categorization
        case 
            when action in ('view', 'click') then 'Engagement'
            when action = 'log' then 'Authentication'
            when action = 'purchase' then 'Conversion'
            else 'Other'
        end as action_category,
        
        -- Funnel stage
        case 
            when action = 'view' and page_type = 'Home' then 'Awareness'
            when action = 'view' and page_type in ('Category', 'Search') then 'Interest'
            when action = 'view' and page_type = 'Product Page' then 'Consideration'
            when action = 'click' and page_type = 'Cart' then 'Intent'
            when action = 'purchase' then 'Purchase'
            else 'Other'
        end as funnel_stage,
        
        -- Data quality flags
        case 
            when event_timestamp > current_timestamp() then true
            else false
        end as has_future_event_timestamp,
        
        -- Recency
        datediff('minute', event_timestamp, current_timestamp()) as minutes_since_event,
        datediff('hour', event_timestamp, current_timestamp()) as hours_since_event,
        datediff('day', event_timestamp, current_timestamp()) as days_since_event
        
    from url_parsed
)

select * from final
  );
[0m09:49:39.275147 [debug] [Thread-3 (]: On model.live_c360.stg_users: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "node_id": "model.live_c360.stg_users"} */
create or replace   view MYDB.LIVE_DATA.stg_users
  
   as (
    

-- Staging model for live users data
-- Cleans and standardizes raw user data from S3 external table

with source_data as (
    select * from MYDB.LIVE_DATA.ext_live_users
),

cleaned_data as (
    select
        -- Primary key
        id as user_id,
        
        -- Personal information (anonymized)
        sha1(email) as email_hash,
        initcap(trim(firstname)) as firstname,
        initcap(trim(lastname)) as lastname,
        trim(address) as address,
        upper(trim(canal)) as acquisition_channel,
        upper(trim(country)) as country,
        
        -- Dates
        try_to_timestamp(creation_date, 'MM-DD-YYYY HH24:MI:SS') as creation_date,
        try_to_timestamp(last_activity_date, 'MM-DD-YYYY HH24:MI:SS') as last_activity_date,
        
        -- Demographics
        case 
            when gender = 0 then 'F'
            when gender = 1 then 'M'
            else 'Unknown'
        end as gender,
        age_group,
        
        -- Behavioral flags
        churn as is_churned,
        
        -- Metadata
        batch_id,
        try_to_timestamp(generated_at) as generated_at,
        current_timestamp() as processed_at
        
    from source_data
    where id is not null  -- Filter out any null user IDs
),

final as (
    select
        *,
        -- Derived fields
        datediff('day', creation_date, current_timestamp()) as days_since_creation,
        datediff('day', last_activity_date, current_timestamp()) as days_since_last_activity,
        
        -- Data quality flags
        case 
            when creation_date > current_timestamp() then true
            else false
        end as has_future_creation_date,
        
        case 
            when last_activity_date < creation_date then true
            else false
        end as has_invalid_activity_date,
        
        -- Segmentation
        case 
            when days_since_last_activity <= 7 then 'Active'
            when days_since_last_activity <= 30 then 'Recent'
            when days_since_last_activity <= 90 then 'Dormant'
            else 'Inactive'
        end as activity_segment
        
    from cleaned_data
)

select * from final
  );
[0m09:49:39.395407 [debug] [Thread-2 (]: Snowflake adapter: Snowflake query id: 01bccd2d-3204-7eab-0002-4ad600061206
[0m09:49:39.397303 [debug] [Thread-1 (]: Snowflake adapter: Snowflake query id: 01bccd2d-3204-800f-0002-4ad6000650be
[0m09:49:39.398631 [debug] [Thread-3 (]: Snowflake adapter: Snowflake query id: 01bccd2d-3204-802a-0002-4ad60006909a
[0m09:49:39.399753 [debug] [Thread-2 (]: Snowflake adapter: Snowflake error: 091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
[0m09:49:39.401233 [debug] [Thread-1 (]: Snowflake adapter: Snowflake error: 091093 (55000): External table EXT_LIVE_EVENTS marked invalid. Stage S3_LIVE_EVENTS_STAGE dropped.
[0m09:49:39.402645 [debug] [Thread-3 (]: Snowflake adapter: Snowflake error: 091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
[0m09:49:39.405357 [debug] [Thread-2 (]: Database Error in model stg_orders (models/staging/stg_orders.sql)
  091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_orders.sql
[0m09:49:39.407316 [debug] [Thread-1 (]: Database Error in model stg_events (models/staging/stg_events.sql)
  091093 (55000): External table EXT_LIVE_EVENTS marked invalid. Stage S3_LIVE_EVENTS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_events.sql
[0m09:49:39.410170 [debug] [Thread-3 (]: Database Error in model stg_users (models/staging/stg_users.sql)
  091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_users.sql
[0m09:49:39.413148 [debug] [Thread-2 (]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '23a97560-d17d-4d90-9da0-987538473b6d', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x72b0460a1780>]}
[0m09:49:39.415821 [debug] [Thread-1 (]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '23a97560-d17d-4d90-9da0-987538473b6d', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x72b030a54d90>]}
[0m09:49:39.417889 [debug] [Thread-3 (]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '23a97560-d17d-4d90-9da0-987538473b6d', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x72b02aa3bd60>]}
[0m09:49:39.420090 [error] [Thread-2 (]: 2 of 3 ERROR creating sql view model LIVE_DATA.stg_orders ...................... [[31mERROR[0m in 0.26s]
[0m09:49:39.421956 [error] [Thread-1 (]: 1 of 3 ERROR creating sql view model LIVE_DATA.stg_events ...................... [[31mERROR[0m in 0.27s]
[0m09:49:39.423699 [error] [Thread-3 (]: 3 of 3 ERROR creating sql view model LIVE_DATA.stg_users ....................... [[31mERROR[0m in 0.27s]
[0m09:49:39.425675 [debug] [Thread-2 (]: Finished running node model.live_c360.stg_orders
[0m09:49:39.427442 [debug] [Thread-1 (]: Finished running node model.live_c360.stg_events
[0m09:49:39.429235 [debug] [Thread-3 (]: Finished running node model.live_c360.stg_users
[0m09:49:39.432084 [debug] [Thread-7 (]: Marking all children of 'model.live_c360.stg_orders' to be skipped because of status 'error'.  Reason: Database Error in model stg_orders (models/staging/stg_orders.sql)
  091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_orders.sql.
[0m09:49:39.435961 [debug] [Thread-7 (]: Marking all children of 'model.live_c360.stg_events' to be skipped because of status 'error'.  Reason: Database Error in model stg_events (models/staging/stg_events.sql)
  091093 (55000): External table EXT_LIVE_EVENTS marked invalid. Stage S3_LIVE_EVENTS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_events.sql.
[0m09:49:39.437784 [debug] [Thread-7 (]: Marking all children of 'model.live_c360.stg_users' to be skipped because of status 'error'.  Reason: Database Error in model stg_users (models/staging/stg_users.sql)
  091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_users.sql.
[0m09:49:39.440911 [debug] [MainThread]: Connection 'master' was properly closed.
[0m09:49:39.442404 [debug] [MainThread]: Connection 'model.live_c360.stg_users' was left open.
[0m09:49:39.443689 [debug] [MainThread]: On model.live_c360.stg_users: Close
[0m09:49:39.510094 [debug] [MainThread]: Connection 'model.live_c360.stg_events' was left open.
[0m09:49:39.512121 [debug] [MainThread]: On model.live_c360.stg_events: Close
[0m09:49:39.606165 [debug] [MainThread]: Connection 'model.live_c360.stg_orders' was left open.
[0m09:49:39.608116 [debug] [MainThread]: On model.live_c360.stg_orders: Close
[0m09:49:39.683247 [info ] [MainThread]: 
[0m09:49:39.684883 [info ] [MainThread]: Finished running 3 view models in 0 hours 0 minutes and 1.96 seconds (1.96s).
[0m09:49:39.687200 [debug] [MainThread]: Command end result
[0m09:49:39.791434 [debug] [MainThread]: Wrote artifact WritableManifest to /opt/airflow/workspace/dbt_live/target/manifest.json
[0m09:49:39.799118 [debug] [MainThread]: Wrote artifact SemanticManifest to /opt/airflow/workspace/dbt_live/target/semantic_manifest.json
[0m09:49:39.811868 [debug] [MainThread]: Wrote artifact RunExecutionResult to /opt/airflow/workspace/dbt_live/target/run_results.json
[0m09:49:39.812834 [info ] [MainThread]: 
[0m09:49:39.815311 [info ] [MainThread]: [31mCompleted with 3 errors, 0 partial successes, and 0 warnings:[0m
[0m09:49:39.816533 [info ] [MainThread]: 
[0m09:49:39.817639 [error] [MainThread]:   Database Error in model stg_orders (models/staging/stg_orders.sql)
  091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_orders.sql
[0m09:49:39.818961 [info ] [MainThread]: 
[0m09:49:39.820672 [error] [MainThread]:   Database Error in model stg_events (models/staging/stg_events.sql)
  091093 (55000): External table EXT_LIVE_EVENTS marked invalid. Stage S3_LIVE_EVENTS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_events.sql
[0m09:49:39.822408 [info ] [MainThread]: 
[0m09:49:39.824011 [error] [MainThread]:   Database Error in model stg_users (models/staging/stg_users.sql)
  091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_users.sql
[0m09:49:39.824978 [info ] [MainThread]: 
[0m09:49:39.825870 [info ] [MainThread]: Done. PASS=0 WARN=0 ERROR=3 SKIP=0 TOTAL=3
[0m09:49:39.827412 [debug] [MainThread]: Resource report: {"command_name": "run", "command_success": false, "command_wall_clock_time": 4.845692, "process_in_blocks": "0", "process_kernel_time": 0.63686, "process_mem_max_rss": "203572", "process_out_blocks": "16", "process_user_time": 3.653986}
[0m09:49:39.828627 [debug] [MainThread]: Command `dbt run` failed at 09:49:39.828391 after 4.85 seconds
[0m09:49:39.829522 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x72b044d2e920>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x72b045119cf0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x72b02bbfff70>]}
[0m09:49:39.830472 [debug] [MainThread]: Flushing usage events
[0m09:49:40.809072 [debug] [MainThread]: An error was encountered while trying to flush usage events
[0m09:49:52.932249 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7c49057428c0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7c49053b2c50>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7c49046476a0>]}


============================== 09:49:52.939792 | 06c63a39-0e6b-462d-8e69-771cb5b89d03 ==============================
[0m09:49:52.939792 [info ] [MainThread]: Running with dbt=1.9.6
[0m09:49:52.940967 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'log_cache_events': 'False', 'write_json': 'True', 'partial_parse': 'True', 'cache_selected_only': 'False', 'profiles_dir': '/opt/airflow/workspace/dbt_live', 'fail_fast': 'False', 'version_check': 'True', 'log_path': '/opt/airflow/workspace/dbt_live/logs', 'warn_error': 'None', 'debug': 'False', 'use_colors': 'True', 'use_experimental_parser': 'False', 'empty': 'None', 'quiet': 'False', 'no_print': 'None', 'warn_error_options': 'WarnErrorOptions(include=[], exclude=[])', 'static_parser': 'True', 'invocation_command': 'dbt debug', 'introspect': 'True', 'target_path': 'None', 'log_format': 'default', 'send_anonymous_usage_stats': 'True'}
[0m09:49:52.951011 [info ] [MainThread]: dbt version: 1.9.6
[0m09:49:52.954366 [info ] [MainThread]: python version: 3.10.13
[0m09:49:52.955743 [info ] [MainThread]: python path: /usr/local/bin/python
[0m09:49:52.957082 [info ] [MainThread]: os info: Linux-6.6.87.1-microsoft-standard-WSL2-x86_64-with-glibc2.36
[0m09:49:53.747076 [info ] [MainThread]: Using profiles dir at /opt/airflow/workspace/dbt_live
[0m09:49:53.748819 [info ] [MainThread]: Using profiles.yml file at /opt/airflow/workspace/dbt_live/profiles.yml
[0m09:49:53.750601 [info ] [MainThread]: Using dbt_project.yml file at /opt/airflow/workspace/dbt_live/dbt_project.yml
[0m09:49:53.752183 [info ] [MainThread]: adapter type: snowflake
[0m09:49:53.754662 [info ] [MainThread]: adapter version: 1.9.4
[0m09:49:53.913278 [info ] [MainThread]: Configuration:
[0m09:49:53.914672 [info ] [MainThread]:   profiles.yml file [[32mOK found and valid[0m]
[0m09:49:53.915777 [info ] [MainThread]:   dbt_project.yml file [[32mOK found and valid[0m]
[0m09:49:53.916909 [info ] [MainThread]: Required dependencies:
[0m09:49:53.918157 [debug] [MainThread]: Executing "git --help"
[0m09:49:53.922956 [debug] [MainThread]: STDOUT: "b"usage: git [-v | --version] [-h | --help] [-C <path>] [-c <name>=<value>]\n           [--exec-path[=<path>]] [--html-path] [--man-path] [--info-path]\n           [-p | --paginate | -P | --no-pager] [--no-replace-objects] [--bare]\n           [--git-dir=<path>] [--work-tree=<path>] [--namespace=<name>]\n           [--super-prefix=<path>] [--config-env=<name>=<envvar>]\n           <command> [<args>]\n\nThese are common Git commands used in various situations:\n\nstart a working area (see also: git help tutorial)\n   clone     Clone a repository into a new directory\n   init      Create an empty Git repository or reinitialize an existing one\n\nwork on the current change (see also: git help everyday)\n   add       Add file contents to the index\n   mv        Move or rename a file, a directory, or a symlink\n   restore   Restore working tree files\n   rm        Remove files from the working tree and from the index\n\nexamine the history and state (see also: git help revisions)\n   bisect    Use binary search to find the commit that introduced a bug\n   diff      Show changes between commits, commit and working tree, etc\n   grep      Print lines matching a pattern\n   log       Show commit logs\n   show      Show various types of objects\n   status    Show the working tree status\n\ngrow, mark and tweak your common history\n   branch    List, create, or delete branches\n   commit    Record changes to the repository\n   merge     Join two or more development histories together\n   rebase    Reapply commits on top of another base tip\n   reset     Reset current HEAD to the specified state\n   switch    Switch branches\n   tag       Create, list, delete or verify a tag object signed with GPG\n\ncollaborate (see also: git help workflows)\n   fetch     Download objects and refs from another repository\n   pull      Fetch from and integrate with another repository or a local branch\n   push      Update remote refs along with associated objects\n\n'git help -a' and 'git help -g' list available subcommands and some\nconcept guides. See 'git help <command>' or 'git help <concept>'\nto read about a specific subcommand or concept.\nSee 'git help git' for an overview of the system.\n""
[0m09:49:53.924424 [debug] [MainThread]: STDERR: "b''"
[0m09:49:53.925655 [info ] [MainThread]:  - git [[32mOK found[0m]

[0m09:49:53.926874 [info ] [MainThread]: Connection:
[0m09:49:53.928024 [info ] [MainThread]:   account: SVLFKJI-IX89869
[0m09:49:53.929264 [info ] [MainThread]:   user: XINBINZHANG
[0m09:49:53.930551 [info ] [MainThread]:   database: MYDB
[0m09:49:53.931690 [info ] [MainThread]:   warehouse: COMPUTE_WH
[0m09:49:53.932755 [info ] [MainThread]:   role: ACCOUNTADMIN
[0m09:49:53.935089 [info ] [MainThread]:   schema: LIVE_DATA
[0m09:49:53.936332 [info ] [MainThread]:   authenticator: None
[0m09:49:53.939243 [info ] [MainThread]:   oauth_client_id: None
[0m09:49:53.940409 [info ] [MainThread]:   query_tag: None
[0m09:49:53.941706 [info ] [MainThread]:   client_session_keep_alive: False
[0m09:49:53.943040 [info ] [MainThread]:   host: None
[0m09:49:53.944358 [info ] [MainThread]:   port: None
[0m09:49:53.945659 [info ] [MainThread]:   proxy_host: None
[0m09:49:53.946711 [info ] [MainThread]:   proxy_port: None
[0m09:49:53.947775 [info ] [MainThread]:   protocol: None
[0m09:49:53.948830 [info ] [MainThread]:   connect_retries: 1
[0m09:49:53.949732 [info ] [MainThread]:   connect_timeout: None
[0m09:49:53.951226 [info ] [MainThread]:   retry_on_database_errors: False
[0m09:49:53.956725 [info ] [MainThread]:   retry_all: False
[0m09:49:53.958193 [info ] [MainThread]:   insecure_mode: False
[0m09:49:53.959669 [info ] [MainThread]:   reuse_connections: True
[0m09:49:53.961104 [info ] [MainThread]: Registered adapter: snowflake=1.9.4
[0m09:49:54.149003 [debug] [MainThread]: Acquiring new snowflake connection 'debug'
[0m09:49:54.263438 [debug] [MainThread]: Using snowflake connection "debug"
[0m09:49:54.264562 [debug] [MainThread]: On debug: select 1 as id
[0m09:49:54.265763 [debug] [MainThread]: Opening a new connection, currently in state init
[0m09:49:54.767281 [debug] [MainThread]: SQL status: SUCCESS 1 in 0.501 seconds
[0m09:49:54.769131 [info ] [MainThread]:   Connection test: [[32mOK connection ok[0m]

[0m09:49:54.770177 [info ] [MainThread]: [32mAll checks passed![0m
[0m09:49:54.773313 [debug] [MainThread]: Resource report: {"command_name": "debug", "command_success": true, "command_wall_clock_time": 2.018734, "process_in_blocks": "0", "process_kernel_time": 0.553639, "process_mem_max_rss": "190776", "process_out_blocks": "16", "process_user_time": 3.115253}
[0m09:49:54.774689 [debug] [MainThread]: Command `dbt debug` succeeded at 09:49:54.774491 after 2.02 seconds
[0m09:49:54.775691 [debug] [MainThread]: Connection 'debug' was left open.
[0m09:49:54.776583 [debug] [MainThread]: On debug: Close
[0m09:49:54.856230 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7c49057428c0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7c48ee0a3610>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7c48ed9db340>]}
[0m09:49:54.858302 [debug] [MainThread]: Flushing usage events
[0m09:49:55.824076 [debug] [MainThread]: An error was encountered while trying to flush usage events
[0m09:49:59.617810 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7116fbdce920>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7116facc1780>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7116facc3400>]}


============================== 09:49:59.626301 | 532c2039-8cf4-4867-88ec-9ad73661c036 ==============================
[0m09:49:59.626301 [info ] [MainThread]: Running with dbt=1.9.6
[0m09:49:59.628055 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'log_cache_events': 'False', 'write_json': 'True', 'partial_parse': 'True', 'cache_selected_only': 'False', 'warn_error': 'None', 'version_check': 'True', 'fail_fast': 'False', 'log_path': '/opt/airflow/workspace/dbt_live/logs', 'debug': 'False', 'profiles_dir': '/opt/airflow/workspace/dbt_live', 'use_colors': 'True', 'use_experimental_parser': 'False', 'empty': 'None', 'quiet': 'False', 'no_print': 'None', 'log_format': 'default', 'invocation_command': 'dbt deps', 'warn_error_options': 'WarnErrorOptions(include=[], exclude=[])', 'introspect': 'True', 'target_path': 'None', 'static_parser': 'True', 'send_anonymous_usage_stats': 'True'}
[0m09:49:59.866796 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'project_id', 'label': '532c2039-8cf4-4867-88ec-9ad73661c036', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7116fd182560>]}
[0m09:50:00.169151 [debug] [MainThread]: Set downloads directory='/tmp/dbt-downloads-32fo5jm2'
[0m09:50:00.170168 [debug] [MainThread]: Making package index registry request: GET https://hub.getdbt.com/api/v1/index.json
[0m09:50:00.287706 [debug] [MainThread]: Response from registry index: GET https://hub.getdbt.com/api/v1/index.json 200
[0m09:50:00.291016 [debug] [MainThread]: Making package registry request: GET https://hub.getdbt.com/api/v1/dbt-labs/dbt_utils.json
[0m09:50:00.388156 [debug] [MainThread]: Response from registry: GET https://hub.getdbt.com/api/v1/dbt-labs/dbt_utils.json 200
[0m09:50:00.396713 [info ] [MainThread]: Installing dbt-labs/dbt_utils
[0m09:50:03.646938 [info ] [MainThread]: Installed from version 1.1.1
[0m09:50:03.648059 [info ] [MainThread]: Updated version available: 1.3.0
[0m09:50:03.649181 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'package', 'label': '532c2039-8cf4-4867-88ec-9ad73661c036', 'property_': 'install', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7116fba416c0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7116fab339a0>]}
[0m09:50:03.650184 [info ] [MainThread]: 
[0m09:50:03.652332 [info ] [MainThread]: Updates available for packages: ['dbt-labs/dbt_utils']                 
Update your versions in packages.yml, then run dbt deps
[0m09:50:03.654704 [debug] [MainThread]: Resource report: {"command_name": "deps", "command_success": true, "command_wall_clock_time": 4.144397, "process_in_blocks": "0", "process_kernel_time": 0.388685, "process_mem_max_rss": "94512", "process_out_blocks": "200", "process_user_time": 2.109426}
[0m09:50:03.655931 [debug] [MainThread]: Command `dbt deps` succeeded at 09:50:03.655787 after 4.15 seconds
[0m09:50:03.656898 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7116fbdce920>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7116fbe62350>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7116fba4f040>]}
[0m09:50:03.657846 [debug] [MainThread]: Flushing usage events
[0m09:50:04.621875 [debug] [MainThread]: An error was encountered while trying to flush usage events
[0m09:50:07.593689 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x776e8e99a920>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x776e8d8a7ee0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x776e8d8a66b0>]}


============================== 09:50:07.601263 | e63288a7-5a11-4141-b7bf-4a16e0590575 ==============================
[0m09:50:07.601263 [info ] [MainThread]: Running with dbt=1.9.6
[0m09:50:07.602455 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'log_cache_events': 'False', 'write_json': 'True', 'partial_parse': 'True', 'cache_selected_only': 'False', 'warn_error': 'None', 'fail_fast': 'False', 'version_check': 'True', 'log_path': '/opt/airflow/workspace/dbt_live/logs', 'profiles_dir': '/opt/airflow/workspace/dbt_live', 'debug': 'False', 'use_colors': 'True', 'use_experimental_parser': 'False', 'empty': 'False', 'quiet': 'False', 'no_print': 'None', 'warn_error_options': 'WarnErrorOptions(include=[], exclude=[])', 'invocation_command': 'dbt run --models staging', 'introspect': 'True', 'static_parser': 'True', 'target_path': 'None', 'log_format': 'default', 'send_anonymous_usage_stats': 'True'}
[0m09:50:08.478733 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'project_id', 'label': 'e63288a7-5a11-4141-b7bf-4a16e0590575', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x776e8d8e76d0>]}
[0m09:50:08.553887 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'adapter_info', 'label': 'e63288a7-5a11-4141-b7bf-4a16e0590575', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x776e8df8bdc0>]}
[0m09:50:08.557030 [info ] [MainThread]: Registered adapter: snowflake=1.9.4
[0m09:50:08.739230 [debug] [MainThread]: checksum: dd6dc1e5178459e3de3bf2eeb7c86bed4be2266c311fce8c15d86eb4ff94e7ad, vars: {}, profile: , target: , version: 1.9.6
[0m09:50:10.069970 [debug] [MainThread]: Partial parsing enabled: 0 files deleted, 0 files added, 0 files changed.
[0m09:50:10.071583 [debug] [MainThread]: Partial parsing enabled, no changes found, skipping parsing
[0m09:50:10.083544 [warn ] [MainThread]: [[33mWARNING[0m]: Configuration paths exist in your dbt_project.yml file which do not apply to any resources.
There are 3 unused configuration paths:
- models.live_c360.incremental
- seeds.live_c360
- snapshots.live_c360
[0m09:50:10.144881 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'load_project', 'label': 'e63288a7-5a11-4141-b7bf-4a16e0590575', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x776e7668c130>]}
[0m09:50:10.325528 [debug] [MainThread]: Wrote artifact WritableManifest to /opt/airflow/workspace/dbt_live/target/manifest.json
[0m09:50:10.333991 [debug] [MainThread]: Wrote artifact SemanticManifest to /opt/airflow/workspace/dbt_live/target/semantic_manifest.json
[0m09:50:10.366372 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'resource_counts', 'label': 'e63288a7-5a11-4141-b7bf-4a16e0590575', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x776e766d43a0>]}
[0m09:50:10.367937 [info ] [MainThread]: Found 12 models, 21 data tests, 3 sources, 590 macros
[0m09:50:10.369236 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'runnable_timing', 'label': 'e63288a7-5a11-4141-b7bf-4a16e0590575', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x776e766d4490>]}
[0m09:50:10.371929 [info ] [MainThread]: 
[0m09:50:10.373298 [info ] [MainThread]: Concurrency: 4 threads (target='live')
[0m09:50:10.374694 [info ] [MainThread]: 
[0m09:50:10.376152 [debug] [MainThread]: Acquiring new snowflake connection 'master'
[0m09:50:10.383531 [debug] [ThreadPool]: Acquiring new snowflake connection 'list_MYDB'
[0m09:50:10.436147 [debug] [ThreadPool]: Using snowflake connection "list_MYDB"
[0m09:50:10.437701 [debug] [ThreadPool]: On list_MYDB: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB"} */
show terse schemas in database MYDB
    limit 10000
[0m09:50:10.438993 [debug] [ThreadPool]: Opening a new connection, currently in state init
[0m09:50:11.039891 [debug] [ThreadPool]: SQL status: SUCCESS 6 in 0.601 seconds
[0m09:50:11.047140 [debug] [ThreadPool]: Re-using an available connection from the pool (formerly list_MYDB, now list_MYDB_LIVE_DATA_dbt_test__audit)
[0m09:50:11.048553 [debug] [ThreadPool]: Acquiring new snowflake connection 'list_MYDB_LIVE_DATA'
[0m09:50:11.049863 [debug] [ThreadPool]: Acquiring new snowflake connection 'list_MYDB_LIVE_DATA_ANALYTICS'
[0m09:50:11.065475 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_dbt_test__audit"
[0m09:50:11.069285 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA"
[0m09:50:11.073910 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_ANALYTICS"
[0m09:50:11.075756 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_dbt_test__audit: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_dbt_test__audit"} */
alter session set quoted_identifiers_ignore_case = false;
[0m09:50:11.077499 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA"} */
alter session set quoted_identifiers_ignore_case = false;
[0m09:50:11.079096 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_ANALYTICS: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_ANALYTICS"} */
alter session set quoted_identifiers_ignore_case = false;
[0m09:50:11.083380 [debug] [ThreadPool]: Opening a new connection, currently in state init
[0m09:50:11.084721 [debug] [ThreadPool]: Opening a new connection, currently in state init
[0m09:50:11.185684 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.105 seconds
[0m09:50:11.191543 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_dbt_test__audit"
[0m09:50:11.193009 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_dbt_test__audit: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_dbt_test__audit"} */
show objects in MYDB.LIVE_DATA_dbt_test__audit
    limit 10000
    
;
[0m09:50:11.295396 [debug] [ThreadPool]: SQL status: SUCCESS 21 in 0.101 seconds
[0m09:50:11.299985 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_dbt_test__audit"
[0m09:50:11.301118 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_dbt_test__audit: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_dbt_test__audit"} */
alter session unset quoted_identifiers_ignore_case;
[0m09:50:11.394047 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.092 seconds
[0m09:50:11.609876 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.525 seconds
[0m09:50:11.613360 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_ANALYTICS"
[0m09:50:11.617837 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.534 seconds
[0m09:50:11.618602 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_ANALYTICS: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_ANALYTICS"} */
show objects in MYDB.LIVE_DATA_ANALYTICS
    limit 10000
    
;
[0m09:50:11.622765 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA"
[0m09:50:11.633489 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA"} */
show objects in MYDB.LIVE_DATA
    limit 10000
    
;
[0m09:50:11.751628 [debug] [ThreadPool]: SQL status: SUCCESS 14 in 0.117 seconds
[0m09:50:11.753069 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.124 seconds
[0m09:50:11.756883 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA"
[0m09:50:11.763671 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_ANALYTICS"
[0m09:50:11.769334 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA"} */
alter session unset quoted_identifiers_ignore_case;
[0m09:50:11.771562 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_ANALYTICS: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_ANALYTICS"} */
alter session unset quoted_identifiers_ignore_case;
[0m09:50:11.856317 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.079 seconds
[0m09:50:11.857666 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.084 seconds
[0m09:50:11.867257 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'runnable_timing', 'label': 'e63288a7-5a11-4141-b7bf-4a16e0590575', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x776e76d79f30>]}
[0m09:50:11.874428 [debug] [Thread-1 (]: Began running node model.live_c360.stg_events
[0m09:50:11.875199 [debug] [Thread-2 (]: Began running node model.live_c360.stg_orders
[0m09:50:11.875990 [debug] [Thread-3 (]: Began running node model.live_c360.stg_users
[0m09:50:11.877772 [info ] [Thread-1 (]: 1 of 3 START sql view model LIVE_DATA.stg_events ............................... [RUN]
[0m09:50:11.879510 [info ] [Thread-2 (]: 2 of 3 START sql view model LIVE_DATA.stg_orders ............................... [RUN]
[0m09:50:11.882611 [info ] [Thread-3 (]: 3 of 3 START sql view model LIVE_DATA.stg_users ................................ [RUN]
[0m09:50:11.884337 [debug] [Thread-1 (]: Re-using an available connection from the pool (formerly list_MYDB_LIVE_DATA, now model.live_c360.stg_events)
[0m09:50:11.886006 [debug] [Thread-2 (]: Re-using an available connection from the pool (formerly list_MYDB_LIVE_DATA_ANALYTICS, now model.live_c360.stg_orders)
[0m09:50:11.888254 [debug] [Thread-3 (]: Re-using an available connection from the pool (formerly list_MYDB_LIVE_DATA_dbt_test__audit, now model.live_c360.stg_users)
[0m09:50:11.890034 [debug] [Thread-1 (]: Began compiling node model.live_c360.stg_events
[0m09:50:11.891337 [debug] [Thread-2 (]: Began compiling node model.live_c360.stg_orders
[0m09:50:11.892631 [debug] [Thread-3 (]: Began compiling node model.live_c360.stg_users
[0m09:50:11.904132 [debug] [Thread-1 (]: Writing injected SQL for node "model.live_c360.stg_events"
[0m09:50:11.910233 [debug] [Thread-2 (]: Writing injected SQL for node "model.live_c360.stg_orders"
[0m09:50:11.915965 [debug] [Thread-3 (]: Writing injected SQL for node "model.live_c360.stg_users"
[0m09:50:11.922193 [debug] [Thread-1 (]: Began executing node model.live_c360.stg_events
[0m09:50:11.923679 [debug] [Thread-3 (]: Began executing node model.live_c360.stg_users
[0m09:50:11.946825 [debug] [Thread-2 (]: Began executing node model.live_c360.stg_orders
[0m09:50:11.977046 [debug] [Thread-1 (]: Writing runtime sql for node "model.live_c360.stg_events"
[0m09:50:11.977896 [debug] [Thread-3 (]: Writing runtime sql for node "model.live_c360.stg_users"
[0m09:50:11.982673 [debug] [Thread-2 (]: Writing runtime sql for node "model.live_c360.stg_orders"
[0m09:50:11.990556 [debug] [Thread-2 (]: Using snowflake connection "model.live_c360.stg_orders"
[0m09:50:11.993713 [debug] [Thread-1 (]: Using snowflake connection "model.live_c360.stg_events"
[0m09:50:11.994769 [debug] [Thread-2 (]: On model.live_c360.stg_orders: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "node_id": "model.live_c360.stg_orders"} */
create or replace   view MYDB.LIVE_DATA.stg_orders
  
   as (
    

-- Staging model for live orders data
-- Cleans and standardizes raw order data from S3 external table

with source_data as (
    select * from MYDB.LIVE_DATA.ext_live_orders
),

cleaned_data as (
    select
        -- Primary key
        id as order_id,
        user_id,
        
        -- Transaction details
        try_to_timestamp(transaction_date, 'MM-DD-YYYY HH24:MI:SS') as transaction_date,
        item_count,
        amount,
        
        -- Metadata
        batch_id,
        try_to_timestamp(generated_at) as generated_at,
        current_timestamp() as processed_at
        
    from source_data
    where id is not null  -- Filter out any null order IDs
      and user_id is not null  -- Filter out orders without user IDs
      and amount > 0  -- Filter out invalid amounts
),

final as (
    select
        *,
        -- Derived fields
        round(amount / item_count, 2) as avg_item_price,
        
        -- Date extractions
        date(transaction_date) as transaction_date_only,
        extract(year from transaction_date) as transaction_year,
        extract(month from transaction_date) as transaction_month,
        extract(day from transaction_date) as transaction_day,
        extract(hour from transaction_date) as transaction_hour,
        dayname(transaction_date) as transaction_day_name,
        
        -- Time-based segments
        case 
            when extract(hour from transaction_date) between 6 and 11 then 'Morning'
            when extract(hour from transaction_date) between 12 and 17 then 'Afternoon'
            when extract(hour from transaction_date) between 18 and 22 then 'Evening'
            else 'Night'
        end as time_of_day_segment,
        
        -- Order size segments
        case 
            when amount < 25 then 'Small'
            when amount < 75 then 'Medium'
            when amount < 150 then 'Large'
            else 'Extra Large'
        end as order_size_segment,
        
        -- Item quantity segments
        case 
            when item_count = 1 then 'Single Item'
            when item_count <= 3 then 'Few Items'
            when item_count <= 5 then 'Multiple Items'
            else 'Bulk Order'
        end as quantity_segment,
        
        -- Data quality flags
        case 
            when transaction_date > current_timestamp() then true
            else false
        end as has_future_transaction_date,
        
        case 
            when avg_item_price > 500 then true  -- Flag unusually expensive items
            else false
        end as has_high_item_price,
        
        -- Recency
        datediff('day', transaction_date, current_timestamp()) as days_since_transaction
        
    from cleaned_data
)

select * from final
  );
[0m09:50:11.997508 [debug] [Thread-3 (]: Using snowflake connection "model.live_c360.stg_users"
[0m09:50:11.999327 [debug] [Thread-1 (]: On model.live_c360.stg_events: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "node_id": "model.live_c360.stg_events"} */
create or replace   view MYDB.LIVE_DATA.stg_events
  
   as (
    

-- Staging model for live events data
-- Cleans and standardizes raw event data from S3 external table

with source_data as (
    select * from MYDB.LIVE_DATA.ext_live_events
),

cleaned_data as (
    select
        -- Primary key
        event_id,
        user_id,
        session_id,
        
        -- Event details
        try_to_timestamp(date, 'MM-DD-YYYY HH24:MI:SS') as event_timestamp,
        lower(trim(platform)) as platform,
        lower(trim(action)) as action,
        trim(url) as url,
        
        -- Metadata
        batch_id,
        try_to_timestamp(generated_at) as generated_at,
        current_timestamp() as processed_at
        
    from source_data
    where event_id is not null  -- Filter out any null event IDs
      and user_id is not null   -- Filter out events without user IDs
),

url_parsed as (
    select
        *,
        -- URL parsing
        case 
            when url like '%/product/%' then 'Product Page'
            when url like '%/cart%' then 'Cart'
            when url like '%/checkout%' then 'Checkout'
            when url like '%/search%' then 'Search'
            when url like '%/category/%' then 'Category'
            when url like '%/home%' or url = 'https://example.com/' then 'Home'
            else 'Other'
        end as page_type,
        
        -- Extract path from URL
        regexp_substr(url, 'https?://[^/]+(/.*)', 1, 1, 'e', 1) as url_path
        
    from cleaned_data
),

final as (
    select
        *,
        -- Date extractions
        date(event_timestamp) as event_date,
        extract(year from event_timestamp) as event_year,
        extract(month from event_timestamp) as event_month,
        extract(day from event_timestamp) as event_day,
        extract(hour from event_timestamp) as event_hour,
        dayname(event_timestamp) as event_day_name,
        
        -- Time-based segments
        case 
            when extract(hour from event_timestamp) between 6 and 11 then 'Morning'
            when extract(hour from event_timestamp) between 12 and 17 then 'Afternoon'
            when extract(hour from event_timestamp) between 18 and 22 then 'Evening'
            else 'Night'
        end as time_of_day_segment,
        
        -- Platform standardization
        case 
            when platform in ('ios', 'android') then 'Mobile'
            when platform = 'web' then 'Web'
            else 'Unknown'
        end as device_category,
        
        -- Action categorization
        case 
            when action in ('view', 'click') then 'Engagement'
            when action = 'log' then 'Authentication'
            when action = 'purchase' then 'Conversion'
            else 'Other'
        end as action_category,
        
        -- Funnel stage
        case 
            when action = 'view' and page_type = 'Home' then 'Awareness'
            when action = 'view' and page_type in ('Category', 'Search') then 'Interest'
            when action = 'view' and page_type = 'Product Page' then 'Consideration'
            when action = 'click' and page_type = 'Cart' then 'Intent'
            when action = 'purchase' then 'Purchase'
            else 'Other'
        end as funnel_stage,
        
        -- Data quality flags
        case 
            when event_timestamp > current_timestamp() then true
            else false
        end as has_future_event_timestamp,
        
        -- Recency
        datediff('minute', event_timestamp, current_timestamp()) as minutes_since_event,
        datediff('hour', event_timestamp, current_timestamp()) as hours_since_event,
        datediff('day', event_timestamp, current_timestamp()) as days_since_event
        
    from url_parsed
)

select * from final
  );
[0m09:50:12.002693 [debug] [Thread-3 (]: On model.live_c360.stg_users: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "node_id": "model.live_c360.stg_users"} */
create or replace   view MYDB.LIVE_DATA.stg_users
  
   as (
    

-- Staging model for live users data
-- Cleans and standardizes raw user data from S3 external table

with source_data as (
    select * from MYDB.LIVE_DATA.ext_live_users
),

cleaned_data as (
    select
        -- Primary key
        id as user_id,
        
        -- Personal information (anonymized)
        sha1(email) as email_hash,
        initcap(trim(firstname)) as firstname,
        initcap(trim(lastname)) as lastname,
        trim(address) as address,
        upper(trim(canal)) as acquisition_channel,
        upper(trim(country)) as country,
        
        -- Dates
        try_to_timestamp(creation_date, 'MM-DD-YYYY HH24:MI:SS') as creation_date,
        try_to_timestamp(last_activity_date, 'MM-DD-YYYY HH24:MI:SS') as last_activity_date,
        
        -- Demographics
        case 
            when gender = 0 then 'F'
            when gender = 1 then 'M'
            else 'Unknown'
        end as gender,
        age_group,
        
        -- Behavioral flags
        churn as is_churned,
        
        -- Metadata
        batch_id,
        try_to_timestamp(generated_at) as generated_at,
        current_timestamp() as processed_at
        
    from source_data
    where id is not null  -- Filter out any null user IDs
),

final as (
    select
        *,
        -- Derived fields
        datediff('day', creation_date, current_timestamp()) as days_since_creation,
        datediff('day', last_activity_date, current_timestamp()) as days_since_last_activity,
        
        -- Data quality flags
        case 
            when creation_date > current_timestamp() then true
            else false
        end as has_future_creation_date,
        
        case 
            when last_activity_date < creation_date then true
            else false
        end as has_invalid_activity_date,
        
        -- Segmentation
        case 
            when days_since_last_activity <= 7 then 'Active'
            when days_since_last_activity <= 30 then 'Recent'
            when days_since_last_activity <= 90 then 'Dormant'
            else 'Inactive'
        end as activity_segment
        
    from cleaned_data
)

select * from final
  );
[0m09:50:12.092255 [debug] [Thread-3 (]: Snowflake adapter: Snowflake query id: 01bccd2e-3204-802a-0002-4ad6000690a2
[0m09:50:12.094088 [debug] [Thread-3 (]: Snowflake adapter: Snowflake error: 091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
[0m09:50:12.098153 [debug] [Thread-3 (]: Database Error in model stg_users (models/staging/stg_users.sql)
  091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_users.sql
[0m09:50:12.106451 [debug] [Thread-3 (]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': 'e63288a7-5a11-4141-b7bf-4a16e0590575', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x776e758ea3b0>]}
[0m09:50:12.107553 [debug] [Thread-2 (]: Snowflake adapter: Snowflake query id: 01bccd2e-3204-802b-0002-4ad6000680ee
[0m09:50:12.109365 [error] [Thread-3 (]: 3 of 3 ERROR creating sql view model LIVE_DATA.stg_users ....................... [[31mERROR[0m in 0.22s]
[0m09:50:12.110991 [debug] [Thread-2 (]: Snowflake adapter: Snowflake error: 091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
[0m09:50:12.117392 [debug] [Thread-3 (]: Finished running node model.live_c360.stg_users
[0m09:50:12.120079 [debug] [Thread-2 (]: Database Error in model stg_orders (models/staging/stg_orders.sql)
  091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_orders.sql
[0m09:50:12.123327 [debug] [Thread-2 (]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': 'e63288a7-5a11-4141-b7bf-4a16e0590575', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x776e7465fe50>]}
[0m09:50:12.121531 [debug] [Thread-7 (]: Marking all children of 'model.live_c360.stg_users' to be skipped because of status 'error'.  Reason: Database Error in model stg_users (models/staging/stg_users.sql)
  091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_users.sql.
[0m09:50:12.125128 [error] [Thread-2 (]: 2 of 3 ERROR creating sql view model LIVE_DATA.stg_orders ...................... [[31mERROR[0m in 0.24s]
[0m09:50:12.130764 [debug] [Thread-2 (]: Finished running node model.live_c360.stg_orders
[0m09:50:12.134859 [debug] [Thread-7 (]: Marking all children of 'model.live_c360.stg_orders' to be skipped because of status 'error'.  Reason: Database Error in model stg_orders (models/staging/stg_orders.sql)
  091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_orders.sql.
[0m09:50:12.142558 [debug] [Thread-1 (]: Snowflake adapter: Snowflake query id: 01bccd2e-3204-7fc5-0002-4ad6000631ce
[0m09:50:12.148674 [debug] [Thread-1 (]: Snowflake adapter: Snowflake error: 091093 (55000): External table EXT_LIVE_EVENTS marked invalid. Stage S3_LIVE_EVENTS_STAGE dropped.
[0m09:50:12.151342 [debug] [Thread-1 (]: Database Error in model stg_events (models/staging/stg_events.sql)
  091093 (55000): External table EXT_LIVE_EVENTS marked invalid. Stage S3_LIVE_EVENTS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_events.sql
[0m09:50:12.153342 [debug] [Thread-1 (]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': 'e63288a7-5a11-4141-b7bf-4a16e0590575', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x776e7582ece0>]}
[0m09:50:12.156046 [error] [Thread-1 (]: 1 of 3 ERROR creating sql view model LIVE_DATA.stg_events ...................... [[31mERROR[0m in 0.27s]
[0m09:50:12.157700 [debug] [Thread-1 (]: Finished running node model.live_c360.stg_events
[0m09:50:12.159670 [debug] [Thread-7 (]: Marking all children of 'model.live_c360.stg_events' to be skipped because of status 'error'.  Reason: Database Error in model stg_events (models/staging/stg_events.sql)
  091093 (55000): External table EXT_LIVE_EVENTS marked invalid. Stage S3_LIVE_EVENTS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_events.sql.
[0m09:50:12.162529 [debug] [MainThread]: Connection 'master' was properly closed.
[0m09:50:12.163887 [debug] [MainThread]: Connection 'model.live_c360.stg_users' was left open.
[0m09:50:12.166014 [debug] [MainThread]: On model.live_c360.stg_users: Close
[0m09:50:12.260028 [debug] [MainThread]: Connection 'model.live_c360.stg_events' was left open.
[0m09:50:12.261979 [debug] [MainThread]: On model.live_c360.stg_events: Close
[0m09:50:12.341128 [debug] [MainThread]: Connection 'model.live_c360.stg_orders' was left open.
[0m09:50:12.343350 [debug] [MainThread]: On model.live_c360.stg_orders: Close
[0m09:50:12.411772 [info ] [MainThread]: 
[0m09:50:12.413890 [info ] [MainThread]: Finished running 3 view models in 0 hours 0 minutes and 2.04 seconds (2.04s).
[0m09:50:12.417320 [debug] [MainThread]: Command end result
[0m09:50:12.507097 [debug] [MainThread]: Wrote artifact WritableManifest to /opt/airflow/workspace/dbt_live/target/manifest.json
[0m09:50:12.513185 [debug] [MainThread]: Wrote artifact SemanticManifest to /opt/airflow/workspace/dbt_live/target/semantic_manifest.json
[0m09:50:12.527155 [debug] [MainThread]: Wrote artifact RunExecutionResult to /opt/airflow/workspace/dbt_live/target/run_results.json
[0m09:50:12.528395 [info ] [MainThread]: 
[0m09:50:12.529568 [info ] [MainThread]: [31mCompleted with 3 errors, 0 partial successes, and 0 warnings:[0m
[0m09:50:12.531782 [info ] [MainThread]: 
[0m09:50:12.533125 [error] [MainThread]:   Database Error in model stg_users (models/staging/stg_users.sql)
  091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_users.sql
[0m09:50:12.534269 [info ] [MainThread]: 
[0m09:50:12.535339 [error] [MainThread]:   Database Error in model stg_orders (models/staging/stg_orders.sql)
  091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_orders.sql
[0m09:50:12.536469 [info ] [MainThread]: 
[0m09:50:12.537585 [error] [MainThread]:   Database Error in model stg_events (models/staging/stg_events.sql)
  091093 (55000): External table EXT_LIVE_EVENTS marked invalid. Stage S3_LIVE_EVENTS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_events.sql
[0m09:50:12.538669 [info ] [MainThread]: 
[0m09:50:12.539703 [info ] [MainThread]: Done. PASS=0 WARN=0 ERROR=3 SKIP=0 TOTAL=3
[0m09:50:12.541446 [debug] [MainThread]: Resource report: {"command_name": "run", "command_success": false, "command_wall_clock_time": 5.0344563, "process_in_blocks": "0", "process_kernel_time": 0.60259, "process_mem_max_rss": "204212", "process_out_blocks": "16", "process_user_time": 3.763117}
[0m09:50:12.542710 [debug] [MainThread]: Command `dbt run` failed at 09:50:12.542578 after 5.04 seconds
[0m09:50:12.543829 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x776e8e99a920>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x776e746717b0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x776e766b2980>]}
[0m09:50:12.544907 [debug] [MainThread]: Flushing usage events
[0m09:50:13.522100 [debug] [MainThread]: An error was encountered while trying to flush usage events
[0m10:07:52.413008 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7ae2314f6980>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7ae23116ed70>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7ae2303e3850>]}


============================== 10:07:52.420145 | 944ed657-30af-467c-bf78-bc149dbe0c3e ==============================
[0m10:07:52.420145 [info ] [MainThread]: Running with dbt=1.9.6
[0m10:07:52.421528 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'log_cache_events': 'False', 'write_json': 'True', 'partial_parse': 'True', 'cache_selected_only': 'False', 'warn_error': 'None', 'fail_fast': 'False', 'version_check': 'True', 'log_path': '/opt/airflow/workspace/dbt_live/logs', 'profiles_dir': '/opt/airflow/workspace/dbt_live', 'debug': 'False', 'use_colors': 'True', 'use_experimental_parser': 'False', 'no_print': 'None', 'quiet': 'False', 'empty': 'False', 'warn_error_options': 'WarnErrorOptions(include=[], exclude=[])', 'invocation_command': 'dbt run --models marts', 'static_parser': 'True', 'introspect': 'True', 'target_path': 'None', 'log_format': 'default', 'send_anonymous_usage_stats': 'True'}
[0m10:07:53.768818 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'project_id', 'label': '944ed657-30af-467c-bf78-bc149dbe0c3e', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7ae2304211e0>]}
[0m10:07:53.856633 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'adapter_info', 'label': '944ed657-30af-467c-bf78-bc149dbe0c3e', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7ae219904e50>]}
[0m10:07:53.858279 [info ] [MainThread]: Registered adapter: snowflake=1.9.4
[0m10:07:54.113956 [debug] [MainThread]: checksum: dd6dc1e5178459e3de3bf2eeb7c86bed4be2266c311fce8c15d86eb4ff94e7ad, vars: {}, profile: , target: , version: 1.9.6
[0m10:07:55.491013 [debug] [MainThread]: Partial parsing enabled: 0 files deleted, 0 files added, 0 files changed.
[0m10:07:55.492662 [debug] [MainThread]: Partial parsing enabled, no changes found, skipping parsing
[0m10:07:55.502770 [warn ] [MainThread]: [[33mWARNING[0m]: Configuration paths exist in your dbt_project.yml file which do not apply to any resources.
There are 3 unused configuration paths:
- models.live_c360.incremental
- seeds.live_c360
- snapshots.live_c360
[0m10:07:55.566406 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'load_project', 'label': '944ed657-30af-467c-bf78-bc149dbe0c3e', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7ae2191ac130>]}
[0m10:07:55.747915 [debug] [MainThread]: Wrote artifact WritableManifest to /opt/airflow/workspace/dbt_live/target/manifest.json
[0m10:07:55.754581 [debug] [MainThread]: Wrote artifact SemanticManifest to /opt/airflow/workspace/dbt_live/target/semantic_manifest.json
[0m10:07:55.786699 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'resource_counts', 'label': '944ed657-30af-467c-bf78-bc149dbe0c3e', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7ae2191f8400>]}
[0m10:07:55.787997 [info ] [MainThread]: Found 12 models, 21 data tests, 3 sources, 590 macros
[0m10:07:55.789185 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'runnable_timing', 'label': '944ed657-30af-467c-bf78-bc149dbe0c3e', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7ae2191f84f0>]}
[0m10:07:55.791704 [info ] [MainThread]: 
[0m10:07:55.792936 [info ] [MainThread]: Concurrency: 4 threads (target='live')
[0m10:07:55.793988 [info ] [MainThread]: 
[0m10:07:55.797331 [debug] [MainThread]: Acquiring new snowflake connection 'master'
[0m10:07:55.804114 [debug] [ThreadPool]: Acquiring new snowflake connection 'list_MYDB'
[0m10:07:55.805144 [debug] [ThreadPool]: Acquiring new snowflake connection 'list_MYDB'
[0m10:07:55.865785 [debug] [ThreadPool]: Using snowflake connection "list_MYDB"
[0m10:07:55.866527 [debug] [ThreadPool]: Using snowflake connection "list_MYDB"
[0m10:07:55.867647 [debug] [ThreadPool]: On list_MYDB: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB"} */
show terse schemas in database MYDB
    limit 10000
[0m10:07:55.869429 [debug] [ThreadPool]: On list_MYDB: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB"} */
show terse schemas in database MYDB
    limit 10000
[0m10:07:55.870892 [debug] [ThreadPool]: Opening a new connection, currently in state init
[0m10:07:55.871989 [debug] [ThreadPool]: Opening a new connection, currently in state init
[0m10:07:57.942250 [debug] [ThreadPool]: SQL status: SUCCESS 6 in 2.071 seconds
[0m10:07:57.944533 [debug] [ThreadPool]: SQL status: SUCCESS 6 in 2.072 seconds
[0m10:07:57.954481 [debug] [ThreadPool]: Re-using an available connection from the pool (formerly list_MYDB, now list_MYDB_LIVE_DATA)
[0m10:07:57.955379 [debug] [ThreadPool]: Re-using an available connection from the pool (formerly list_MYDB, now list_MYDB_LIVE_DATA_dbt_test__audit)
[0m10:07:57.956456 [debug] [ThreadPool]: Acquiring new snowflake connection 'list_MYDB_LIVE_DATA_ANALYTICS'
[0m10:07:57.971512 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA"
[0m10:07:57.975214 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_dbt_test__audit"
[0m10:07:57.979277 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_ANALYTICS"
[0m10:07:57.982452 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA"} */
alter session set quoted_identifiers_ignore_case = false;
[0m10:07:57.984000 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_dbt_test__audit: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_dbt_test__audit"} */
alter session set quoted_identifiers_ignore_case = false;
[0m10:07:57.985541 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_ANALYTICS: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_ANALYTICS"} */
alter session set quoted_identifiers_ignore_case = false;
[0m10:07:57.992523 [debug] [ThreadPool]: Opening a new connection, currently in state init
[0m10:07:58.137271 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.150 seconds
[0m10:07:58.143194 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA"
[0m10:07:58.144666 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA"} */
show objects in MYDB.LIVE_DATA
    limit 10000
    
;
[0m10:07:58.295749 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.303 seconds
[0m10:07:58.301478 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_ANALYTICS"
[0m10:07:58.303086 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_ANALYTICS: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_ANALYTICS"} */
show objects in MYDB.LIVE_DATA_ANALYTICS
    limit 10000
    
;
[0m10:07:58.382544 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.078 seconds
[0m10:07:58.386542 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_ANALYTICS"
[0m10:07:58.388366 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_ANALYTICS: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_ANALYTICS"} */
alter session unset quoted_identifiers_ignore_case;
[0m10:07:58.401260 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.411 seconds
[0m10:07:58.405341 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_dbt_test__audit"
[0m10:07:58.406707 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_dbt_test__audit: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_dbt_test__audit"} */
show objects in MYDB.LIVE_DATA_dbt_test__audit
    limit 10000
    
;
[0m10:07:58.472210 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.082 seconds
[0m10:07:58.474424 [debug] [ThreadPool]: SQL status: SUCCESS 14 in 0.328 seconds
[0m10:07:58.480488 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA"
[0m10:07:58.484162 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA"} */
alter session unset quoted_identifiers_ignore_case;
[0m10:07:58.579002 [debug] [ThreadPool]: SQL status: SUCCESS 21 in 0.171 seconds
[0m10:07:58.585479 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_dbt_test__audit"
[0m10:07:58.587338 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_dbt_test__audit: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_dbt_test__audit"} */
alter session unset quoted_identifiers_ignore_case;
[0m10:07:58.589499 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.104 seconds
[0m10:07:58.657738 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.067 seconds
[0m10:07:58.667463 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'runnable_timing', 'label': '944ed657-30af-467c-bf78-bc149dbe0c3e', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7ae232872dd0>]}
[0m10:07:58.674248 [debug] [Thread-1 (]: Began running node model.live_c360.dim_users
[0m10:07:58.674960 [debug] [Thread-2 (]: Began running node model.live_c360.dim_users_scd2
[0m10:07:58.675514 [debug] [Thread-3 (]: Began running node model.live_c360.fact_orders
[0m10:07:58.676141 [debug] [Thread-4 (]: Began running node model.live_c360.mv_fact_orders
[0m10:07:58.677532 [info ] [Thread-1 (]: 1 of 4 START sql table model LIVE_DATA.dim_users ............................... [RUN]
[0m10:07:58.679232 [info ] [Thread-2 (]: 2 of 4 START sql incremental model LIVE_DATA.dim_users_scd2 .................... [RUN]
[0m10:07:58.680952 [info ] [Thread-3 (]: 3 of 4 START sql table model LIVE_DATA.fact_orders ............................. [RUN]
[0m10:07:58.684325 [info ] [Thread-4 (]: 4 of 4 START sql view model LIVE_DATA_ANALYTICS.mv_fact_orders ................. [RUN]
[0m10:07:58.686012 [debug] [Thread-1 (]: Re-using an available connection from the pool (formerly list_MYDB_LIVE_DATA_dbt_test__audit, now model.live_c360.dim_users)
[0m10:07:58.687593 [debug] [Thread-2 (]: Re-using an available connection from the pool (formerly list_MYDB_LIVE_DATA, now model.live_c360.dim_users_scd2)
[0m10:07:58.689074 [debug] [Thread-3 (]: Re-using an available connection from the pool (formerly list_MYDB_LIVE_DATA_ANALYTICS, now model.live_c360.fact_orders)
[0m10:07:58.690391 [debug] [Thread-4 (]: Acquiring new snowflake connection 'model.live_c360.mv_fact_orders'
[0m10:07:58.691460 [debug] [Thread-1 (]: Began compiling node model.live_c360.dim_users
[0m10:07:58.692694 [debug] [Thread-2 (]: Began compiling node model.live_c360.dim_users_scd2
[0m10:07:58.694340 [debug] [Thread-3 (]: Began compiling node model.live_c360.fact_orders
[0m10:07:58.695533 [debug] [Thread-4 (]: Began compiling node model.live_c360.mv_fact_orders
[0m10:07:58.710178 [debug] [Thread-1 (]: Writing injected SQL for node "model.live_c360.dim_users"
[0m10:07:58.738462 [debug] [Thread-3 (]: Writing injected SQL for node "model.live_c360.fact_orders"
[0m10:07:58.745401 [debug] [Thread-2 (]: Writing injected SQL for node "model.live_c360.dim_users_scd2"
[0m10:07:58.752196 [debug] [Thread-4 (]: Writing injected SQL for node "model.live_c360.mv_fact_orders"
[0m10:07:58.762225 [debug] [Thread-1 (]: Began executing node model.live_c360.dim_users
[0m10:07:58.769142 [debug] [Thread-3 (]: Began executing node model.live_c360.fact_orders
[0m10:07:58.782062 [debug] [Thread-2 (]: Began executing node model.live_c360.dim_users_scd2
[0m10:07:58.799186 [debug] [Thread-4 (]: Began executing node model.live_c360.mv_fact_orders
[0m10:07:58.835799 [debug] [Thread-3 (]: Writing runtime sql for node "model.live_c360.fact_orders"
[0m10:07:58.848529 [debug] [Thread-1 (]: Writing runtime sql for node "model.live_c360.dim_users"
[0m10:07:58.910478 [debug] [Thread-4 (]: Writing runtime sql for node "model.live_c360.mv_fact_orders"
[0m10:07:58.916522 [debug] [Thread-2 (]: Using snowflake connection "model.live_c360.dim_users_scd2"
[0m10:07:58.920963 [debug] [Thread-2 (]: On model.live_c360.dim_users_scd2: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "node_id": "model.live_c360.dim_users_scd2"} */
create or replace  temporary view MYDB.LIVE_DATA.dim_users_scd2__dbt_tmp
  
   as (
    

with 

-- Get the latest user data from staging
source_data as (
    select 
        user_id,
        firstname,
        lastname,
        email_hash,
        address,
        acquisition_channel as canal,
        country,
        gender,
        age_group,
        creation_date,
        last_activity_date,
        is_churned as churn,
        batch_id,
        current_timestamp() as dbt_updated_at,
        -- Track when this record was processed
        current_timestamp() as processed_at
    from MYDB.LIVE_DATA.stg_users
    where user_id is not null
    qualify row_number() over (partition by user_id order by generated_at desc) = 1
    
    
    -- Only process users that are new or have changed since the last run
    and user_id in (
        select user_id
        from MYDB.LIVE_DATA.stg_users
        where generated_at > (
            select coalesce(max(dbt_valid_from), '1900-01-01'::timestamp)
            from MYDB.LIVE_DATA.dim_users_scd2
            where is_current_version = true
        )
    )
    
),

-- Get the current version of each user from the dimension
existing_dimension as (
    
    select
        user_sk,
        user_id,
        firstname,
        lastname,
        email,
        address,
        acquisition_channel,
        country,
        gender,
        age_group,
        creation_date,
        last_activity_date,
        churn,
        batch_id,
        dbt_valid_from,
        dbt_valid_to,
        is_current_version,
        dbt_change_type,
        current_timestamp() as processed_at  -- Add processed_at for consistency
    from MYDB.LIVE_DATA.dim_users_scd2
    where is_current_version = true
    
),

-- Identify new and changed records
changes_to_apply as (
    select
        s.*,
        case
            when e.user_sk is null then 'insert'
            when e.firstname != s.firstname 
              or e.lastname != s.lastname
              or e.email != s.email_hash
              or e.address != s.address
              or e.acquisition_channel != s.canal
              or e.country != s.country
              or e.gender != s.gender
              or e.age_group != s.age_group
              or e.churn != s.churn then 'update'
            else 'no_change'
        end as change_type
    from source_data s
    left join existing_dimension e 
        on s.user_id = e.user_id
)

-- For initial load, just insert all records


-- For incremental loads, handle inserts and updates
select * from (
    -- New or updated records
    select
        md5(cast(coalesce(cast(user_id as TEXT), '_dbt_utils_surrogate_key_null_') || '-' || coalesce(cast(dbt_updated_at as TEXT), '_dbt_utils_surrogate_key_null_') as TEXT)) as user_sk,
        user_id,
        firstname,
        lastname,
        email_hash as email,
        address,
        canal as acquisition_channel,
        country,
        gender,
        age_group,
        creation_date,
        last_activity_date,
        churn,
        batch_id,
        dbt_updated_at as dbt_valid_from,
        cast(null as timestamp_ntz) as dbt_valid_to,
        true as is_current_version,
        change_type as dbt_change_type,
        processed_at
    from changes_to_apply
    where change_type in ('insert', 'update')
    
    union all
    
    -- Expire old versions of updated records
    select
        e.user_sk,
        e.user_id,
        e.firstname,
        e.lastname,
        e.email,
        e.address,
        e.acquisition_channel,
        e.country,
        e.gender,
        e.age_group,
        e.creation_date,
        e.last_activity_date,
        e.churn,
        e.batch_id,
        e.dbt_valid_from,
        current_timestamp() as dbt_valid_to,
        false as is_current_version,
        'expire' as dbt_change_type,
        e.processed_at
    from existing_dimension e
    inner join changes_to_apply c 
        on e.user_id = c.user_id
        and c.change_type = 'update'
)


  );
[0m10:07:58.930847 [debug] [Thread-4 (]: Using snowflake connection "model.live_c360.mv_fact_orders"
[0m10:07:58.934243 [debug] [Thread-4 (]: On model.live_c360.mv_fact_orders: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "node_id": "model.live_c360.mv_fact_orders"} */
create or replace   view MYDB.LIVE_DATA_ANALYTICS.mv_fact_orders
  
   as (
    

with 
-- Get the 90th percentile order amount for high-value threshold
order_thresholds as (
    select 
        percentile_cont(0.9) within group (order by amount) as high_value_threshold
    from MYDB.LIVE_DATA.stg_orders
    where amount is not null
),

-- Get basic order metrics
order_metrics as (
    select
        -- Primary key and foreign key
        order_id,
        user_id,
        
        -- Core order details
        transaction_date,
        item_count,
        amount as order_amount,
        
        -- Basic metrics
        row_number() over (partition by user_id order by transaction_date) as user_order_sequence,
        
        -- Time since last order
        datediff(
            'day',
            lag(transaction_date) over (partition by user_id order by transaction_date),
            transaction_date
        ) as days_since_previous_order,
        
        -- Running totals
        sum(amount) over (order by transaction_date) as running_total_amount,
        count(*) over (order by date_trunc('day', transaction_date)) as running_order_count,
        
        -- Include the generated_at for incremental logic if needed
        generated_at
        
    from MYDB.LIVE_DATA.stg_orders
    where transaction_date is not null
    qualify row_number() over (partition by order_id order by generated_at desc) = 1
)

-- Final select with basic segmentation
select
    om.*,
    
    -- Simple customer type
    case 
        when user_order_sequence = 1 then 'New Customer'
        when days_since_previous_order is null then 'New Customer'
        when days_since_previous_order <= 30 then 'Repeat Customer (30d)'
        else 'Reactivated Customer'
    end as customer_type,
    
    -- High value flag (using pre-calculated threshold)
    case 
        when order_amount > (select high_value_threshold from order_thresholds) then true
        else false
    end as is_high_value_order,
    
    -- Large order flag
    case 
        when item_count > 10 then true
        else false
    end as is_large_order,
    
    -- Time of day
    case 
        when extract(hour from transaction_date) between 6 and 11 then 'Morning'
        when extract(hour from transaction_date) between 12 and 17 then 'Afternoon'
        when extract(hour from transaction_date) between 18 and 21 then 'Evening'
        else 'Night'
    end as time_of_day,
    
    -- Add a timestamp for when this record was created
    current_timestamp() as dbt_updated_at
    
from order_metrics om
  );
[0m10:07:59.022276 [debug] [Thread-1 (]: Using snowflake connection "model.live_c360.dim_users"
[0m10:07:59.022921 [debug] [Thread-4 (]: Opening a new connection, currently in state init
[0m10:07:59.025183 [debug] [Thread-3 (]: Using snowflake connection "model.live_c360.fact_orders"
[0m10:07:59.027062 [debug] [Thread-1 (]: On model.live_c360.dim_users: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "node_id": "model.live_c360.dim_users"} */
create or replace transient table MYDB.LIVE_DATA.dim_users
    

    
    as (

-- User dimension table for live data pipeline
-- Provides a clean, analytics-ready view of user data

with users_base as (
    select * from MYDB.LIVE_DATA.stg_users
),

user_metrics as (
    select
        user_id,
        count(*) as total_orders,
        sum(amount) as total_spent,
        avg(amount) as avg_order_value,
        min(transaction_date) as first_order_date,
        max(transaction_date) as last_order_date,
        sum(item_count) as total_items_purchased
    from MYDB.LIVE_DATA.stg_orders
    group by user_id
),

user_events as (
    select
        user_id,
        count(*) as total_events,
        count(distinct session_id) as total_sessions,
        count(distinct event_date) as active_days,
        min(event_timestamp) as first_event_date,
        max(event_timestamp) as last_event_date,
        
        -- Event type counts
        sum(case when action = 'view' then 1 else 0 end) as view_events,
        sum(case when action = 'click' then 1 else 0 end) as click_events,
        sum(case when action = 'log' then 1 else 0 end) as login_events,
        sum(case when action = 'purchase' then 1 else 0 end) as purchase_events,
        
        -- Platform usage
        sum(case when device_category = 'Mobile' then 1 else 0 end) as mobile_events,
        sum(case when device_category = 'Web' then 1 else 0 end) as web_events
        
    from MYDB.LIVE_DATA.stg_events
    group by user_id
),

final as (
    select
        -- User identifiers
        u.user_id,
        u.email_hash,
        
        -- Personal information
        u.firstname,
        u.lastname,
        u.address,
        u.acquisition_channel,
        u.country,
        u.gender,
        u.age_group,
        
        -- Dates
        u.creation_date,
        u.last_activity_date,
        
        -- Behavioral flags
        u.is_churned,
        u.activity_segment,
        
        -- Derived user metrics
        u.days_since_creation,
        u.days_since_last_activity,
        
        -- Order metrics
        coalesce(om.total_orders, 0) as total_orders,
        coalesce(om.total_spent, 0) as total_spent,
        coalesce(om.avg_order_value, 0) as avg_order_value,
        om.first_order_date,
        om.last_order_date,
        coalesce(om.total_items_purchased, 0) as total_items_purchased,
        
        -- Event metrics
        coalesce(ue.total_events, 0) as total_events,
        coalesce(ue.total_sessions, 0) as total_sessions,
        coalesce(ue.active_days, 0) as active_days,
        ue.first_event_date,
        ue.last_event_date,
        
        -- Event type metrics
        coalesce(ue.view_events, 0) as view_events,
        coalesce(ue.click_events, 0) as click_events,
        coalesce(ue.login_events, 0) as login_events,
        coalesce(ue.purchase_events, 0) as purchase_events,
        
        -- Platform metrics
        coalesce(ue.mobile_events, 0) as mobile_events,
        coalesce(ue.web_events, 0) as web_events,
        
        -- Calculated metrics
        case 
            when om.total_orders > 0 then round(ue.total_events::float / om.total_orders, 2)
            else 0
        end as events_per_order,
        
        case 
            when ue.total_sessions > 0 then round(ue.total_events::float / ue.total_sessions, 2)
            else 0
        end as events_per_session,
        
        case 
            when om.total_orders > 0 then 
                datediff('day', om.first_order_date, om.last_order_date)::float / om.total_orders
            else 0
        end as avg_days_between_orders,
        
        -- Customer segments
        case 
            when om.total_orders = 0 then 'No Orders'
            when om.total_orders = 1 then 'One-time Buyer'
            when om.total_orders <= 3 then 'Occasional Buyer'
            when om.total_orders <= 10 then 'Regular Buyer'
            else 'Frequent Buyer'
        end as purchase_segment,
        
        case 
            when om.total_spent = 0 then 'No Spend'
            when om.total_spent < 50 then 'Low Value'
            when om.total_spent < 200 then 'Medium Value'
            when om.total_spent < 500 then 'High Value'
            else 'Premium Value'
        end as value_segment,
        
        case 
            when ue.mobile_events > ue.web_events then 'Mobile Preferred'
            when ue.web_events > ue.mobile_events then 'Web Preferred'
            when ue.mobile_events = ue.web_events and ue.mobile_events > 0 then 'Multi-Platform'
            else 'Unknown'
        end as platform_preference,
        
        -- Data quality flags
        u.has_future_creation_date,
        u.has_invalid_activity_date,
        
        -- Metadata
        u.batch_id,
        u.generated_at,
        u.processed_at,
        current_timestamp() as mart_created_at
        
    from users_base u
    left join user_metrics om on u.user_id = om.user_id
    left join user_events ue on u.user_id = ue.user_id
)

select * from final
    )
;
[0m10:07:59.034613 [debug] [Thread-3 (]: On model.live_c360.fact_orders: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "node_id": "model.live_c360.fact_orders"} */
create or replace transient table MYDB.LIVE_DATA.fact_orders
    

    
    as (

with 
-- Get the 90th percentile order amount for high-value threshold
order_thresholds as (
    select 
        percentile_cont(0.9) within group (order by amount) as high_value_threshold
    from MYDB.LIVE_DATA.stg_orders
    where amount is not null
),

-- Get basic order metrics
order_metrics as (
    select
        -- Primary key and foreign key
        order_id,
        user_id,
        
        -- Core order details
        transaction_date,
        item_count,
        amount as order_amount,
        
        -- Basic metrics
        row_number() over (partition by user_id order by transaction_date) as user_order_sequence,
        
        -- Time since last order
        datediff(
            'day',
            lag(transaction_date) over (partition by user_id order by transaction_date),
            transaction_date
        ) as days_since_previous_order,
        
        -- Running totals
        sum(amount) over (order by transaction_date) as running_total_amount,
        count(*) over (order by date_trunc('day', transaction_date)) as running_order_count
        
    from MYDB.LIVE_DATA.stg_orders
    where transaction_date is not null
    qualify row_number() over (partition by order_id order by generated_at desc) = 1
)

-- Final select with basic segmentation
select
    om.*,
    
    -- Simple customer type
    case 
        when user_order_sequence = 1 then 'New Customer'
        when days_since_previous_order is null then 'New Customer'
        when days_since_previous_order <= 30 then 'Repeat Customer (30d)'
        else 'Reactivated Customer'
    end as customer_type,
    
    -- High value flag (using pre-calculated threshold)
    case 
        when order_amount > (select high_value_threshold from order_thresholds) then true
        else false
    end as is_high_value_order,
    
    -- Large order flag
    case 
        when item_count > 10 then true
        else false
    end as is_large_order,
    
    -- Time of day
    case 
        when extract(hour from transaction_date) between 6 and 11 then 'Morning'
        when extract(hour from transaction_date) between 12 and 17 then 'Afternoon'
        when extract(hour from transaction_date) between 18 and 21 then 'Evening'
        else 'Night'
    end as time_of_day
    
from order_metrics om
    )
;
[0m10:07:59.169672 [debug] [Thread-2 (]: Snowflake adapter: Snowflake query id: 01bccd3f-3204-7f80-0002-4ad60006713a
[0m10:07:59.171439 [debug] [Thread-2 (]: Snowflake adapter: Snowflake error: 091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
[0m10:07:59.174082 [debug] [Thread-2 (]: Database Error in model dim_users_scd2 (models/marts/dim_users_scd2.sql)
  091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
[0m10:07:59.177188 [debug] [Thread-2 (]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '944ed657-30af-467c-bf78-bc149dbe0c3e', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7ae2315930d0>]}
[0m10:07:59.179196 [error] [Thread-2 (]: 2 of 4 ERROR creating sql incremental model LIVE_DATA.dim_users_scd2 ........... [[31mERROR[0m in 0.49s]
[0m10:07:59.183776 [debug] [Thread-2 (]: Finished running node model.live_c360.dim_users_scd2
[0m10:07:59.185800 [debug] [Thread-7 (]: Marking all children of 'model.live_c360.dim_users_scd2' to be skipped because of status 'error'.  Reason: Database Error in model dim_users_scd2 (models/marts/dim_users_scd2.sql)
  091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped..
[0m10:07:59.211749 [debug] [Thread-3 (]: Snowflake adapter: Snowflake query id: 01bccd3f-3204-7f80-0002-4ad60006713e
[0m10:07:59.213214 [debug] [Thread-3 (]: Snowflake adapter: Snowflake error: 091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
[0m10:07:59.215568 [debug] [Thread-3 (]: Database Error in model fact_orders (models/marts/fact_orders.sql)
  091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/marts/fact_orders.sql
[0m10:07:59.217794 [debug] [Thread-3 (]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '944ed657-30af-467c-bf78-bc149dbe0c3e', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7ae23282e7a0>]}
[0m10:07:59.219324 [error] [Thread-3 (]: 3 of 4 ERROR creating sql table model LIVE_DATA.fact_orders .................... [[31mERROR[0m in 0.53s]
[0m10:07:59.220553 [debug] [Thread-3 (]: Finished running node model.live_c360.fact_orders
[0m10:07:59.221878 [debug] [Thread-7 (]: Marking all children of 'model.live_c360.fact_orders' to be skipped because of status 'error'.  Reason: Database Error in model fact_orders (models/marts/fact_orders.sql)
  091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/marts/fact_orders.sql.
[0m10:07:59.416264 [debug] [Thread-4 (]: Snowflake adapter: Snowflake query id: 01bccd3f-3204-7fc5-0002-4ad6000631ea
[0m10:07:59.418899 [debug] [Thread-4 (]: Snowflake adapter: Snowflake error: 091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
[0m10:07:59.422558 [debug] [Thread-4 (]: Database Error in model mv_fact_orders (models/marts/mv_fact_orders.sql)
  091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/marts/mv_fact_orders.sql
[0m10:07:59.424439 [debug] [Thread-4 (]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '944ed657-30af-467c-bf78-bc149dbe0c3e', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7ae21937b940>]}
[0m10:07:59.426191 [error] [Thread-4 (]: 4 of 4 ERROR creating sql view model LIVE_DATA_ANALYTICS.mv_fact_orders ........ [[31mERROR[0m in 0.73s]
[0m10:07:59.428021 [debug] [Thread-4 (]: Finished running node model.live_c360.mv_fact_orders
[0m10:07:59.429664 [debug] [Thread-7 (]: Marking all children of 'model.live_c360.mv_fact_orders' to be skipped because of status 'error'.  Reason: Database Error in model mv_fact_orders (models/marts/mv_fact_orders.sql)
  091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/marts/mv_fact_orders.sql.
[0m10:07:59.754757 [debug] [Thread-1 (]: Snowflake adapter: Snowflake query id: 01bccd3f-3204-7eab-0002-4ad600061236
[0m10:07:59.756247 [debug] [Thread-1 (]: Snowflake adapter: Snowflake error: 091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
[0m10:07:59.758294 [debug] [Thread-1 (]: Database Error in model dim_users (models/marts/dim_users.sql)
  091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
  compiled code at target/run/live_c360/models/marts/dim_users.sql
[0m10:07:59.760148 [debug] [Thread-1 (]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '944ed657-30af-467c-bf78-bc149dbe0c3e', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7ae2111461d0>]}
[0m10:07:59.762104 [error] [Thread-1 (]: 1 of 4 ERROR creating sql table model LIVE_DATA.dim_users ...................... [[31mERROR[0m in 1.07s]
[0m10:07:59.763817 [debug] [Thread-1 (]: Finished running node model.live_c360.dim_users
[0m10:07:59.766935 [debug] [Thread-7 (]: Marking all children of 'model.live_c360.dim_users' to be skipped because of status 'error'.  Reason: Database Error in model dim_users (models/marts/dim_users.sql)
  091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
  compiled code at target/run/live_c360/models/marts/dim_users.sql.
[0m10:07:59.770334 [debug] [MainThread]: Connection 'master' was properly closed.
[0m10:07:59.771679 [debug] [MainThread]: Connection 'model.live_c360.dim_users' was left open.
[0m10:07:59.772868 [debug] [MainThread]: On model.live_c360.dim_users: Close
[0m10:07:59.887478 [debug] [MainThread]: Connection 'model.live_c360.dim_users_scd2' was left open.
[0m10:07:59.889462 [debug] [MainThread]: On model.live_c360.dim_users_scd2: Close
[0m10:07:59.973490 [debug] [MainThread]: Connection 'model.live_c360.fact_orders' was left open.
[0m10:07:59.976054 [debug] [MainThread]: On model.live_c360.fact_orders: Close
[0m10:08:00.071861 [debug] [MainThread]: Connection 'model.live_c360.mv_fact_orders' was left open.
[0m10:08:00.093447 [debug] [MainThread]: On model.live_c360.mv_fact_orders: Close
[0m10:08:00.198402 [info ] [MainThread]: 
[0m10:08:00.201668 [info ] [MainThread]: Finished running 1 incremental model, 2 table models, 1 view model in 0 hours 0 minutes and 4.40 seconds (4.40s).
[0m10:08:00.203844 [debug] [MainThread]: Command end result
[0m10:08:00.285768 [debug] [MainThread]: Wrote artifact WritableManifest to /opt/airflow/workspace/dbt_live/target/manifest.json
[0m10:08:00.292479 [debug] [MainThread]: Wrote artifact SemanticManifest to /opt/airflow/workspace/dbt_live/target/semantic_manifest.json
[0m10:08:00.305828 [debug] [MainThread]: Wrote artifact RunExecutionResult to /opt/airflow/workspace/dbt_live/target/run_results.json
[0m10:08:00.307532 [info ] [MainThread]: 
[0m10:08:00.309695 [info ] [MainThread]: [31mCompleted with 4 errors, 0 partial successes, and 0 warnings:[0m
[0m10:08:00.311406 [info ] [MainThread]: 
[0m10:08:00.313301 [error] [MainThread]:   Database Error in model dim_users_scd2 (models/marts/dim_users_scd2.sql)
  091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
[0m10:08:00.315215 [info ] [MainThread]: 
[0m10:08:00.318163 [error] [MainThread]:   Database Error in model fact_orders (models/marts/fact_orders.sql)
  091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/marts/fact_orders.sql
[0m10:08:00.319437 [info ] [MainThread]: 
[0m10:08:00.320673 [error] [MainThread]:   Database Error in model mv_fact_orders (models/marts/mv_fact_orders.sql)
  091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/marts/mv_fact_orders.sql
[0m10:08:00.321897 [info ] [MainThread]: 
[0m10:08:00.323410 [error] [MainThread]:   Database Error in model dim_users (models/marts/dim_users.sql)
  091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
  compiled code at target/run/live_c360/models/marts/dim_users.sql
[0m10:08:00.325020 [info ] [MainThread]: 
[0m10:08:00.326107 [info ] [MainThread]: Done. PASS=0 WARN=0 ERROR=4 SKIP=0 TOTAL=4
[0m10:08:00.327944 [debug] [MainThread]: Resource report: {"command_name": "run", "command_success": false, "command_wall_clock_time": 8.0369, "process_in_blocks": "0", "process_kernel_time": 1.182882, "process_mem_max_rss": "210332", "process_out_blocks": "5128", "process_user_time": 5.705424}
[0m10:08:00.329680 [debug] [MainThread]: Command `dbt run` failed at 10:08:00.329524 after 8.04 seconds
[0m10:08:00.330903 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7ae2314f6980>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7ae213e57d00>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7ae213e57ca0>]}
[0m10:08:00.332116 [debug] [MainThread]: Flushing usage events
[0m10:08:01.315644 [debug] [MainThread]: An error was encountered while trying to flush usage events
[0m10:10:05.523492 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x75715a322950>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x75715b395660>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x75715b3978b0>]}


============================== 10:10:05.531720 | 32e30ba0-f6dd-4728-b187-9660d639d2da ==============================
[0m10:10:05.531720 [info ] [MainThread]: Running with dbt=1.9.6
[0m10:10:05.533275 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'write_json': 'True', 'log_cache_events': 'False', 'partial_parse': 'True', 'cache_selected_only': 'False', 'profiles_dir': '/opt/airflow/workspace/dbt_live', 'fail_fast': 'False', 'version_check': 'True', 'log_path': '/opt/airflow/workspace/dbt_live/logs', 'debug': 'False', 'warn_error': 'None', 'use_colors': 'True', 'use_experimental_parser': 'False', 'no_print': 'None', 'quiet': 'False', 'empty': 'False', 'log_format': 'default', 'invocation_command': 'dbt run --models marts', 'warn_error_options': 'WarnErrorOptions(include=[], exclude=[])', 'static_parser': 'True', 'target_path': 'None', 'introspect': 'True', 'send_anonymous_usage_stats': 'True'}
[0m10:10:06.667728 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'project_id', 'label': '32e30ba0-f6dd-4728-b187-9660d639d2da', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x75715926c4c0>]}
[0m10:10:06.746336 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'adapter_info', 'label': '32e30ba0-f6dd-4728-b187-9660d639d2da', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x75715b397cd0>]}
[0m10:10:06.748231 [info ] [MainThread]: Registered adapter: snowflake=1.9.4
[0m10:10:06.937880 [debug] [MainThread]: checksum: dd6dc1e5178459e3de3bf2eeb7c86bed4be2266c311fce8c15d86eb4ff94e7ad, vars: {}, profile: , target: , version: 1.9.6
[0m10:10:08.174539 [debug] [MainThread]: Partial parsing enabled: 0 files deleted, 0 files added, 0 files changed.
[0m10:10:08.178180 [debug] [MainThread]: Partial parsing enabled, no changes found, skipping parsing
[0m10:10:08.187520 [warn ] [MainThread]: [[33mWARNING[0m]: Configuration paths exist in your dbt_project.yml file which do not apply to any resources.
There are 3 unused configuration paths:
- models.live_c360.incremental
- seeds.live_c360
- snapshots.live_c360
[0m10:10:08.254677 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'load_project', 'label': '32e30ba0-f6dd-4728-b187-9660d639d2da', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x757142004130>]}
[0m10:10:08.431838 [debug] [MainThread]: Wrote artifact WritableManifest to /opt/airflow/workspace/dbt_live/target/manifest.json
[0m10:10:08.437611 [debug] [MainThread]: Wrote artifact SemanticManifest to /opt/airflow/workspace/dbt_live/target/semantic_manifest.json
[0m10:10:08.468756 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'resource_counts', 'label': '32e30ba0-f6dd-4728-b187-9660d639d2da', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7571420503d0>]}
[0m10:10:08.470152 [info ] [MainThread]: Found 12 models, 21 data tests, 3 sources, 590 macros
[0m10:10:08.471253 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'runnable_timing', 'label': '32e30ba0-f6dd-4728-b187-9660d639d2da', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7571420504c0>]}
[0m10:10:08.473948 [info ] [MainThread]: 
[0m10:10:08.475331 [info ] [MainThread]: Concurrency: 4 threads (target='live')
[0m10:10:08.478115 [info ] [MainThread]: 
[0m10:10:08.479515 [debug] [MainThread]: Acquiring new snowflake connection 'master'
[0m10:10:08.486377 [debug] [ThreadPool]: Acquiring new snowflake connection 'list_MYDB'
[0m10:10:08.487637 [debug] [ThreadPool]: Acquiring new snowflake connection 'list_MYDB'
[0m10:10:08.542446 [debug] [ThreadPool]: Using snowflake connection "list_MYDB"
[0m10:10:08.543383 [debug] [ThreadPool]: Using snowflake connection "list_MYDB"
[0m10:10:08.545167 [debug] [ThreadPool]: On list_MYDB: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB"} */
show terse schemas in database MYDB
    limit 10000
[0m10:10:08.546555 [debug] [ThreadPool]: On list_MYDB: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB"} */
show terse schemas in database MYDB
    limit 10000
[0m10:10:08.547784 [debug] [ThreadPool]: Opening a new connection, currently in state init
[0m10:10:08.549235 [debug] [ThreadPool]: Opening a new connection, currently in state init
[0m10:10:09.137814 [debug] [ThreadPool]: SQL status: SUCCESS 6 in 0.590 seconds
[0m10:10:09.159579 [debug] [ThreadPool]: SQL status: SUCCESS 6 in 0.610 seconds
[0m10:10:09.167420 [debug] [ThreadPool]: Re-using an available connection from the pool (formerly list_MYDB, now list_MYDB_LIVE_DATA_ANALYTICS)
[0m10:10:09.168390 [debug] [ThreadPool]: Re-using an available connection from the pool (formerly list_MYDB, now list_MYDB_LIVE_DATA_dbt_test__audit)
[0m10:10:09.169693 [debug] [ThreadPool]: Acquiring new snowflake connection 'list_MYDB_LIVE_DATA'
[0m10:10:09.183881 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_ANALYTICS"
[0m10:10:09.187470 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_dbt_test__audit"
[0m10:10:09.192662 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA"
[0m10:10:09.194946 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_ANALYTICS: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_ANALYTICS"} */
alter session set quoted_identifiers_ignore_case = false;
[0m10:10:09.196487 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_dbt_test__audit: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_dbt_test__audit"} */
alter session set quoted_identifiers_ignore_case = false;
[0m10:10:09.197713 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA"} */
alter session set quoted_identifiers_ignore_case = false;
[0m10:10:09.203912 [debug] [ThreadPool]: Opening a new connection, currently in state init
[0m10:10:09.289405 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.090 seconds
[0m10:10:09.292151 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.091 seconds
[0m10:10:09.299749 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_ANALYTICS"
[0m10:10:09.303263 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_dbt_test__audit"
[0m10:10:09.304840 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_ANALYTICS: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_ANALYTICS"} */
show objects in MYDB.LIVE_DATA_ANALYTICS
    limit 10000
    
;
[0m10:10:09.306193 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_dbt_test__audit: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_dbt_test__audit"} */
show objects in MYDB.LIVE_DATA_dbt_test__audit
    limit 10000
    
;
[0m10:10:09.406095 [debug] [ThreadPool]: SQL status: SUCCESS 21 in 0.094 seconds
[0m10:10:09.408511 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.101 seconds
[0m10:10:09.413387 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_dbt_test__audit"
[0m10:10:09.416597 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_ANALYTICS"
[0m10:10:09.418196 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_dbt_test__audit: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_dbt_test__audit"} */
alter session unset quoted_identifiers_ignore_case;
[0m10:10:09.419540 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_ANALYTICS: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_ANALYTICS"} */
alter session unset quoted_identifiers_ignore_case;
[0m10:10:09.496943 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.076 seconds
[0m10:10:09.506996 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.083 seconds
[0m10:10:09.691565 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.488 seconds
[0m10:10:09.696993 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA"
[0m10:10:09.699023 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA"} */
show objects in MYDB.LIVE_DATA
    limit 10000
    
;
[0m10:10:09.819473 [debug] [ThreadPool]: SQL status: SUCCESS 14 in 0.118 seconds
[0m10:10:09.823117 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA"
[0m10:10:09.824444 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA"} */
alter session unset quoted_identifiers_ignore_case;
[0m10:10:09.905788 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.080 seconds
[0m10:10:09.913719 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'runnable_timing', 'label': '32e30ba0-f6dd-4728-b187-9660d639d2da', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x75715b6d6e00>]}
[0m10:10:09.919696 [debug] [Thread-1 (]: Began running node model.live_c360.dim_users
[0m10:10:09.920340 [debug] [Thread-2 (]: Began running node model.live_c360.dim_users_scd2
[0m10:10:09.921354 [debug] [Thread-3 (]: Began running node model.live_c360.fact_orders
[0m10:10:09.921989 [debug] [Thread-4 (]: Began running node model.live_c360.mv_fact_orders
[0m10:10:09.923092 [info ] [Thread-1 (]: 1 of 4 START sql table model LIVE_DATA.dim_users ............................... [RUN]
[0m10:10:09.924705 [info ] [Thread-2 (]: 2 of 4 START sql incremental model LIVE_DATA.dim_users_scd2 .................... [RUN]
[0m10:10:09.929339 [info ] [Thread-3 (]: 3 of 4 START sql table model LIVE_DATA.fact_orders ............................. [RUN]
[0m10:10:09.931008 [info ] [Thread-4 (]: 4 of 4 START sql view model LIVE_DATA_ANALYTICS.mv_fact_orders ................. [RUN]
[0m10:10:09.932632 [debug] [Thread-1 (]: Re-using an available connection from the pool (formerly list_MYDB_LIVE_DATA, now model.live_c360.dim_users)
[0m10:10:09.934134 [debug] [Thread-2 (]: Re-using an available connection from the pool (formerly list_MYDB_LIVE_DATA_ANALYTICS, now model.live_c360.dim_users_scd2)
[0m10:10:09.935604 [debug] [Thread-3 (]: Re-using an available connection from the pool (formerly list_MYDB_LIVE_DATA_dbt_test__audit, now model.live_c360.fact_orders)
[0m10:10:09.937071 [debug] [Thread-4 (]: Acquiring new snowflake connection 'model.live_c360.mv_fact_orders'
[0m10:10:09.938420 [debug] [Thread-1 (]: Began compiling node model.live_c360.dim_users
[0m10:10:09.939737 [debug] [Thread-2 (]: Began compiling node model.live_c360.dim_users_scd2
[0m10:10:09.940989 [debug] [Thread-3 (]: Began compiling node model.live_c360.fact_orders
[0m10:10:09.942381 [debug] [Thread-4 (]: Began compiling node model.live_c360.mv_fact_orders
[0m10:10:09.955131 [debug] [Thread-1 (]: Writing injected SQL for node "model.live_c360.dim_users"
[0m10:10:09.982547 [debug] [Thread-3 (]: Writing injected SQL for node "model.live_c360.fact_orders"
[0m10:10:09.990422 [debug] [Thread-2 (]: Writing injected SQL for node "model.live_c360.dim_users_scd2"
[0m10:10:09.997256 [debug] [Thread-4 (]: Writing injected SQL for node "model.live_c360.mv_fact_orders"
[0m10:10:10.005651 [debug] [Thread-3 (]: Began executing node model.live_c360.fact_orders
[0m10:10:10.006687 [debug] [Thread-2 (]: Began executing node model.live_c360.dim_users_scd2
[0m10:10:10.007988 [debug] [Thread-1 (]: Began executing node model.live_c360.dim_users
[0m10:10:10.019853 [debug] [Thread-4 (]: Began executing node model.live_c360.mv_fact_orders
[0m10:10:10.079979 [debug] [Thread-3 (]: Writing runtime sql for node "model.live_c360.fact_orders"
[0m10:10:10.084895 [debug] [Thread-1 (]: Writing runtime sql for node "model.live_c360.dim_users"
[0m10:10:10.128084 [debug] [Thread-2 (]: Using snowflake connection "model.live_c360.dim_users_scd2"
[0m10:10:10.132538 [debug] [Thread-4 (]: Writing runtime sql for node "model.live_c360.mv_fact_orders"
[0m10:10:10.136059 [debug] [Thread-2 (]: On model.live_c360.dim_users_scd2: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "node_id": "model.live_c360.dim_users_scd2"} */
create or replace  temporary view MYDB.LIVE_DATA.dim_users_scd2__dbt_tmp
  
   as (
    

with 

-- Get the latest user data from staging
source_data as (
    select 
        user_id,
        firstname,
        lastname,
        email_hash,
        address,
        acquisition_channel as canal,
        country,
        gender,
        age_group,
        creation_date,
        last_activity_date,
        is_churned as churn,
        batch_id,
        current_timestamp() as dbt_updated_at,
        -- Track when this record was processed
        current_timestamp() as processed_at
    from MYDB.LIVE_DATA.stg_users
    where user_id is not null
    qualify row_number() over (partition by user_id order by generated_at desc) = 1
    
    
    -- Only process users that are new or have changed since the last run
    and user_id in (
        select user_id
        from MYDB.LIVE_DATA.stg_users
        where generated_at > (
            select coalesce(max(dbt_valid_from), '1900-01-01'::timestamp)
            from MYDB.LIVE_DATA.dim_users_scd2
            where is_current_version = true
        )
    )
    
),

-- Get the current version of each user from the dimension
existing_dimension as (
    
    select
        user_sk,
        user_id,
        firstname,
        lastname,
        email,
        address,
        acquisition_channel,
        country,
        gender,
        age_group,
        creation_date,
        last_activity_date,
        churn,
        batch_id,
        dbt_valid_from,
        dbt_valid_to,
        is_current_version,
        dbt_change_type,
        current_timestamp() as processed_at  -- Add processed_at for consistency
    from MYDB.LIVE_DATA.dim_users_scd2
    where is_current_version = true
    
),

-- Identify new and changed records
changes_to_apply as (
    select
        s.*,
        case
            when e.user_sk is null then 'insert'
            when e.firstname != s.firstname 
              or e.lastname != s.lastname
              or e.email != s.email_hash
              or e.address != s.address
              or e.acquisition_channel != s.canal
              or e.country != s.country
              or e.gender != s.gender
              or e.age_group != s.age_group
              or e.churn != s.churn then 'update'
            else 'no_change'
        end as change_type
    from source_data s
    left join existing_dimension e 
        on s.user_id = e.user_id
)

-- For initial load, just insert all records


-- For incremental loads, handle inserts and updates
select * from (
    -- New or updated records
    select
        md5(cast(coalesce(cast(user_id as TEXT), '_dbt_utils_surrogate_key_null_') || '-' || coalesce(cast(dbt_updated_at as TEXT), '_dbt_utils_surrogate_key_null_') as TEXT)) as user_sk,
        user_id,
        firstname,
        lastname,
        email_hash as email,
        address,
        canal as acquisition_channel,
        country,
        gender,
        age_group,
        creation_date,
        last_activity_date,
        churn,
        batch_id,
        dbt_updated_at as dbt_valid_from,
        cast(null as timestamp_ntz) as dbt_valid_to,
        true as is_current_version,
        change_type as dbt_change_type,
        processed_at
    from changes_to_apply
    where change_type in ('insert', 'update')
    
    union all
    
    -- Expire old versions of updated records
    select
        e.user_sk,
        e.user_id,
        e.firstname,
        e.lastname,
        e.email,
        e.address,
        e.acquisition_channel,
        e.country,
        e.gender,
        e.age_group,
        e.creation_date,
        e.last_activity_date,
        e.churn,
        e.batch_id,
        e.dbt_valid_from,
        current_timestamp() as dbt_valid_to,
        false as is_current_version,
        'expire' as dbt_change_type,
        e.processed_at
    from existing_dimension e
    inner join changes_to_apply c 
        on e.user_id = c.user_id
        and c.change_type = 'update'
)


  );
[0m10:10:10.147783 [debug] [Thread-3 (]: Using snowflake connection "model.live_c360.fact_orders"
[0m10:10:10.153079 [debug] [Thread-1 (]: Using snowflake connection "model.live_c360.dim_users"
[0m10:10:10.154873 [debug] [Thread-3 (]: On model.live_c360.fact_orders: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "node_id": "model.live_c360.fact_orders"} */
create or replace transient table MYDB.LIVE_DATA.fact_orders
    

    
    as (

with 
-- Get the 90th percentile order amount for high-value threshold
order_thresholds as (
    select 
        percentile_cont(0.9) within group (order by amount) as high_value_threshold
    from MYDB.LIVE_DATA.stg_orders
    where amount is not null
),

-- Get basic order metrics
order_metrics as (
    select
        -- Primary key and foreign key
        order_id,
        user_id,
        
        -- Core order details
        transaction_date,
        item_count,
        amount as order_amount,
        
        -- Basic metrics
        row_number() over (partition by user_id order by transaction_date) as user_order_sequence,
        
        -- Time since last order
        datediff(
            'day',
            lag(transaction_date) over (partition by user_id order by transaction_date),
            transaction_date
        ) as days_since_previous_order,
        
        -- Running totals
        sum(amount) over (order by transaction_date) as running_total_amount,
        count(*) over (order by date_trunc('day', transaction_date)) as running_order_count
        
    from MYDB.LIVE_DATA.stg_orders
    where transaction_date is not null
    qualify row_number() over (partition by order_id order by generated_at desc) = 1
)

-- Final select with basic segmentation
select
    om.*,
    
    -- Simple customer type
    case 
        when user_order_sequence = 1 then 'New Customer'
        when days_since_previous_order is null then 'New Customer'
        when days_since_previous_order <= 30 then 'Repeat Customer (30d)'
        else 'Reactivated Customer'
    end as customer_type,
    
    -- High value flag (using pre-calculated threshold)
    case 
        when order_amount > (select high_value_threshold from order_thresholds) then true
        else false
    end as is_high_value_order,
    
    -- Large order flag
    case 
        when item_count > 10 then true
        else false
    end as is_large_order,
    
    -- Time of day
    case 
        when extract(hour from transaction_date) between 6 and 11 then 'Morning'
        when extract(hour from transaction_date) between 12 and 17 then 'Afternoon'
        when extract(hour from transaction_date) between 18 and 21 then 'Evening'
        else 'Night'
    end as time_of_day
    
from order_metrics om
    )
;
[0m10:10:10.156867 [debug] [Thread-1 (]: On model.live_c360.dim_users: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "node_id": "model.live_c360.dim_users"} */
create or replace transient table MYDB.LIVE_DATA.dim_users
    

    
    as (

-- User dimension table for live data pipeline
-- Provides a clean, analytics-ready view of user data

with users_base as (
    select * from MYDB.LIVE_DATA.stg_users
),

user_metrics as (
    select
        user_id,
        count(*) as total_orders,
        sum(amount) as total_spent,
        avg(amount) as avg_order_value,
        min(transaction_date) as first_order_date,
        max(transaction_date) as last_order_date,
        sum(item_count) as total_items_purchased
    from MYDB.LIVE_DATA.stg_orders
    group by user_id
),

user_events as (
    select
        user_id,
        count(*) as total_events,
        count(distinct session_id) as total_sessions,
        count(distinct event_date) as active_days,
        min(event_timestamp) as first_event_date,
        max(event_timestamp) as last_event_date,
        
        -- Event type counts
        sum(case when action = 'view' then 1 else 0 end) as view_events,
        sum(case when action = 'click' then 1 else 0 end) as click_events,
        sum(case when action = 'log' then 1 else 0 end) as login_events,
        sum(case when action = 'purchase' then 1 else 0 end) as purchase_events,
        
        -- Platform usage
        sum(case when device_category = 'Mobile' then 1 else 0 end) as mobile_events,
        sum(case when device_category = 'Web' then 1 else 0 end) as web_events
        
    from MYDB.LIVE_DATA.stg_events
    group by user_id
),

final as (
    select
        -- User identifiers
        u.user_id,
        u.email_hash,
        
        -- Personal information
        u.firstname,
        u.lastname,
        u.address,
        u.acquisition_channel,
        u.country,
        u.gender,
        u.age_group,
        
        -- Dates
        u.creation_date,
        u.last_activity_date,
        
        -- Behavioral flags
        u.is_churned,
        u.activity_segment,
        
        -- Derived user metrics
        u.days_since_creation,
        u.days_since_last_activity,
        
        -- Order metrics
        coalesce(om.total_orders, 0) as total_orders,
        coalesce(om.total_spent, 0) as total_spent,
        coalesce(om.avg_order_value, 0) as avg_order_value,
        om.first_order_date,
        om.last_order_date,
        coalesce(om.total_items_purchased, 0) as total_items_purchased,
        
        -- Event metrics
        coalesce(ue.total_events, 0) as total_events,
        coalesce(ue.total_sessions, 0) as total_sessions,
        coalesce(ue.active_days, 0) as active_days,
        ue.first_event_date,
        ue.last_event_date,
        
        -- Event type metrics
        coalesce(ue.view_events, 0) as view_events,
        coalesce(ue.click_events, 0) as click_events,
        coalesce(ue.login_events, 0) as login_events,
        coalesce(ue.purchase_events, 0) as purchase_events,
        
        -- Platform metrics
        coalesce(ue.mobile_events, 0) as mobile_events,
        coalesce(ue.web_events, 0) as web_events,
        
        -- Calculated metrics
        case 
            when om.total_orders > 0 then round(ue.total_events::float / om.total_orders, 2)
            else 0
        end as events_per_order,
        
        case 
            when ue.total_sessions > 0 then round(ue.total_events::float / ue.total_sessions, 2)
            else 0
        end as events_per_session,
        
        case 
            when om.total_orders > 0 then 
                datediff('day', om.first_order_date, om.last_order_date)::float / om.total_orders
            else 0
        end as avg_days_between_orders,
        
        -- Customer segments
        case 
            when om.total_orders = 0 then 'No Orders'
            when om.total_orders = 1 then 'One-time Buyer'
            when om.total_orders <= 3 then 'Occasional Buyer'
            when om.total_orders <= 10 then 'Regular Buyer'
            else 'Frequent Buyer'
        end as purchase_segment,
        
        case 
            when om.total_spent = 0 then 'No Spend'
            when om.total_spent < 50 then 'Low Value'
            when om.total_spent < 200 then 'Medium Value'
            when om.total_spent < 500 then 'High Value'
            else 'Premium Value'
        end as value_segment,
        
        case 
            when ue.mobile_events > ue.web_events then 'Mobile Preferred'
            when ue.web_events > ue.mobile_events then 'Web Preferred'
            when ue.mobile_events = ue.web_events and ue.mobile_events > 0 then 'Multi-Platform'
            else 'Unknown'
        end as platform_preference,
        
        -- Data quality flags
        u.has_future_creation_date,
        u.has_invalid_activity_date,
        
        -- Metadata
        u.batch_id,
        u.generated_at,
        u.processed_at,
        current_timestamp() as mart_created_at
        
    from users_base u
    left join user_metrics om on u.user_id = om.user_id
    left join user_events ue on u.user_id = ue.user_id
)

select * from final
    )
;
[0m10:10:10.159871 [debug] [Thread-4 (]: Using snowflake connection "model.live_c360.mv_fact_orders"
[0m10:10:10.167194 [debug] [Thread-4 (]: On model.live_c360.mv_fact_orders: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "node_id": "model.live_c360.mv_fact_orders"} */
create or replace   view MYDB.LIVE_DATA_ANALYTICS.mv_fact_orders
  
   as (
    

with 
-- Get the 90th percentile order amount for high-value threshold
order_thresholds as (
    select 
        percentile_cont(0.9) within group (order by amount) as high_value_threshold
    from MYDB.LIVE_DATA.stg_orders
    where amount is not null
),

-- Get basic order metrics
order_metrics as (
    select
        -- Primary key and foreign key
        order_id,
        user_id,
        
        -- Core order details
        transaction_date,
        item_count,
        amount as order_amount,
        
        -- Basic metrics
        row_number() over (partition by user_id order by transaction_date) as user_order_sequence,
        
        -- Time since last order
        datediff(
            'day',
            lag(transaction_date) over (partition by user_id order by transaction_date),
            transaction_date
        ) as days_since_previous_order,
        
        -- Running totals
        sum(amount) over (order by transaction_date) as running_total_amount,
        count(*) over (order by date_trunc('day', transaction_date)) as running_order_count,
        
        -- Include the generated_at for incremental logic if needed
        generated_at
        
    from MYDB.LIVE_DATA.stg_orders
    where transaction_date is not null
    qualify row_number() over (partition by order_id order by generated_at desc) = 1
)

-- Final select with basic segmentation
select
    om.*,
    
    -- Simple customer type
    case 
        when user_order_sequence = 1 then 'New Customer'
        when days_since_previous_order is null then 'New Customer'
        when days_since_previous_order <= 30 then 'Repeat Customer (30d)'
        else 'Reactivated Customer'
    end as customer_type,
    
    -- High value flag (using pre-calculated threshold)
    case 
        when order_amount > (select high_value_threshold from order_thresholds) then true
        else false
    end as is_high_value_order,
    
    -- Large order flag
    case 
        when item_count > 10 then true
        else false
    end as is_large_order,
    
    -- Time of day
    case 
        when extract(hour from transaction_date) between 6 and 11 then 'Morning'
        when extract(hour from transaction_date) between 12 and 17 then 'Afternoon'
        when extract(hour from transaction_date) between 18 and 21 then 'Evening'
        else 'Night'
    end as time_of_day,
    
    -- Add a timestamp for when this record was created
    current_timestamp() as dbt_updated_at
    
from order_metrics om
  );
[0m10:10:10.168802 [debug] [Thread-4 (]: Opening a new connection, currently in state init
[0m10:10:10.349926 [debug] [Thread-3 (]: Snowflake adapter: Snowflake query id: 01bccd42-3204-802b-0002-4ad600068126
[0m10:10:10.351676 [debug] [Thread-3 (]: Snowflake adapter: Snowflake error: 091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
[0m10:10:10.354433 [debug] [Thread-3 (]: Database Error in model fact_orders (models/marts/fact_orders.sql)
  091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/marts/fact_orders.sql
[0m10:10:10.357377 [debug] [Thread-3 (]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '32e30ba0-f6dd-4728-b187-9660d639d2da', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x75715b6d6e00>]}
[0m10:10:10.360703 [error] [Thread-3 (]: 3 of 4 ERROR creating sql table model LIVE_DATA.fact_orders .................... [[31mERROR[0m in 0.42s]
[0m10:10:10.362766 [debug] [Thread-3 (]: Finished running node model.live_c360.fact_orders
[0m10:10:10.364559 [debug] [Thread-7 (]: Marking all children of 'model.live_c360.fact_orders' to be skipped because of status 'error'.  Reason: Database Error in model fact_orders (models/marts/fact_orders.sql)
  091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/marts/fact_orders.sql.
[0m10:10:10.380702 [debug] [Thread-2 (]: Snowflake adapter: Snowflake query id: 01bccd42-3204-800f-0002-4ad6000650e6
[0m10:10:10.382327 [debug] [Thread-2 (]: Snowflake adapter: Snowflake error: 091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
[0m10:10:10.384435 [debug] [Thread-2 (]: Database Error in model dim_users_scd2 (models/marts/dim_users_scd2.sql)
  091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
[0m10:10:10.385942 [debug] [Thread-2 (]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '32e30ba0-f6dd-4728-b187-9660d639d2da', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7571401cc160>]}
[0m10:10:10.387570 [error] [Thread-2 (]: 2 of 4 ERROR creating sql incremental model LIVE_DATA.dim_users_scd2 ........... [[31mERROR[0m in 0.45s]
[0m10:10:10.389133 [debug] [Thread-2 (]: Finished running node model.live_c360.dim_users_scd2
[0m10:10:10.391207 [debug] [Thread-7 (]: Marking all children of 'model.live_c360.dim_users_scd2' to be skipped because of status 'error'.  Reason: Database Error in model dim_users_scd2 (models/marts/dim_users_scd2.sql)
  091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped..
[0m10:10:10.397565 [debug] [Thread-1 (]: Snowflake adapter: Snowflake query id: 01bccd42-3204-7fc5-0002-4ad6000631f2
[0m10:10:10.399355 [debug] [Thread-1 (]: Snowflake adapter: Snowflake error: 091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
[0m10:10:10.401674 [debug] [Thread-1 (]: Database Error in model dim_users (models/marts/dim_users.sql)
  091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
  compiled code at target/run/live_c360/models/marts/dim_users.sql
[0m10:10:10.403249 [debug] [Thread-1 (]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '32e30ba0-f6dd-4728-b187-9660d639d2da', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7571401183a0>]}
[0m10:10:10.404773 [error] [Thread-1 (]: 1 of 4 ERROR creating sql table model LIVE_DATA.dim_users ...................... [[31mERROR[0m in 0.47s]
[0m10:10:10.406525 [debug] [Thread-1 (]: Finished running node model.live_c360.dim_users
[0m10:10:10.408210 [debug] [Thread-7 (]: Marking all children of 'model.live_c360.dim_users' to be skipped because of status 'error'.  Reason: Database Error in model dim_users (models/marts/dim_users.sql)
  091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
  compiled code at target/run/live_c360/models/marts/dim_users.sql.
[0m10:10:10.719916 [debug] [Thread-4 (]: Snowflake adapter: Snowflake query id: 01bccd42-3204-7eab-0002-4ad60006123e
[0m10:10:10.721485 [debug] [Thread-4 (]: Snowflake adapter: Snowflake error: 091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
[0m10:10:10.723854 [debug] [Thread-4 (]: Database Error in model mv_fact_orders (models/marts/mv_fact_orders.sql)
  091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/marts/mv_fact_orders.sql
[0m10:10:10.725339 [debug] [Thread-4 (]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '32e30ba0-f6dd-4728-b187-9660d639d2da', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7571421eb460>]}
[0m10:10:10.728759 [error] [Thread-4 (]: 4 of 4 ERROR creating sql view model LIVE_DATA_ANALYTICS.mv_fact_orders ........ [[31mERROR[0m in 0.79s]
[0m10:10:10.730546 [debug] [Thread-4 (]: Finished running node model.live_c360.mv_fact_orders
[0m10:10:10.732187 [debug] [Thread-7 (]: Marking all children of 'model.live_c360.mv_fact_orders' to be skipped because of status 'error'.  Reason: Database Error in model mv_fact_orders (models/marts/mv_fact_orders.sql)
  091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/marts/mv_fact_orders.sql.
[0m10:10:10.734989 [debug] [MainThread]: Connection 'master' was properly closed.
[0m10:10:10.736417 [debug] [MainThread]: Connection 'model.live_c360.fact_orders' was left open.
[0m10:10:10.737588 [debug] [MainThread]: On model.live_c360.fact_orders: Close
[0m10:10:10.814649 [debug] [MainThread]: Connection 'model.live_c360.dim_users_scd2' was left open.
[0m10:10:10.816691 [debug] [MainThread]: On model.live_c360.dim_users_scd2: Close
[0m10:10:10.888793 [debug] [MainThread]: Connection 'model.live_c360.dim_users' was left open.
[0m10:10:10.890413 [debug] [MainThread]: On model.live_c360.dim_users: Close
[0m10:10:10.966314 [debug] [MainThread]: Connection 'model.live_c360.mv_fact_orders' was left open.
[0m10:10:10.968124 [debug] [MainThread]: On model.live_c360.mv_fact_orders: Close
[0m10:10:11.042435 [info ] [MainThread]: 
[0m10:10:11.045612 [info ] [MainThread]: Finished running 1 incremental model, 2 table models, 1 view model in 0 hours 0 minutes and 2.56 seconds (2.56s).
[0m10:10:11.048186 [debug] [MainThread]: Command end result
[0m10:10:11.206143 [debug] [MainThread]: Wrote artifact WritableManifest to /opt/airflow/workspace/dbt_live/target/manifest.json
[0m10:10:11.214892 [debug] [MainThread]: Wrote artifact SemanticManifest to /opt/airflow/workspace/dbt_live/target/semantic_manifest.json
[0m10:10:11.230607 [debug] [MainThread]: Wrote artifact RunExecutionResult to /opt/airflow/workspace/dbt_live/target/run_results.json
[0m10:10:11.233223 [info ] [MainThread]: 
[0m10:10:11.234946 [info ] [MainThread]: [31mCompleted with 4 errors, 0 partial successes, and 0 warnings:[0m
[0m10:10:11.236291 [info ] [MainThread]: 
[0m10:10:11.237594 [error] [MainThread]:   Database Error in model fact_orders (models/marts/fact_orders.sql)
  091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/marts/fact_orders.sql
[0m10:10:11.238921 [info ] [MainThread]: 
[0m10:10:11.240207 [error] [MainThread]:   Database Error in model dim_users_scd2 (models/marts/dim_users_scd2.sql)
  091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
[0m10:10:11.241424 [info ] [MainThread]: 
[0m10:10:11.244538 [error] [MainThread]:   Database Error in model dim_users (models/marts/dim_users.sql)
  091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
  compiled code at target/run/live_c360/models/marts/dim_users.sql
[0m10:10:11.246285 [info ] [MainThread]: 
[0m10:10:11.247511 [error] [MainThread]:   Database Error in model mv_fact_orders (models/marts/mv_fact_orders.sql)
  091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/marts/mv_fact_orders.sql
[0m10:10:11.248605 [info ] [MainThread]: 
[0m10:10:11.249717 [info ] [MainThread]: Done. PASS=0 WARN=0 ERROR=4 SKIP=0 TOTAL=4
[0m10:10:11.251458 [debug] [MainThread]: Resource report: {"command_name": "run", "command_success": false, "command_wall_clock_time": 5.8502784, "process_in_blocks": "0", "process_kernel_time": 0.727454, "process_mem_max_rss": "206384", "process_out_blocks": "16", "process_user_time": 4.574331}
[0m10:10:11.252745 [debug] [MainThread]: Command `dbt run` failed at 10:10:11.252619 after 5.85 seconds
[0m10:10:11.253855 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x75715a322950>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x75715a1a0f70>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7571422d2800>]}
[0m10:10:11.255450 [debug] [MainThread]: Flushing usage events
[0m10:10:12.238087 [debug] [MainThread]: An error was encountered while trying to flush usage events
[0m10:10:19.647902 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x76a8952ca890>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x76a8941f0c10>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x76a8941f0f70>]}


============================== 10:10:19.655632 | 020f1e74-4456-48f5-9cb4-1ee6f4667fb3 ==============================
[0m10:10:19.655632 [info ] [MainThread]: Running with dbt=1.9.6
[0m10:10:19.656746 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'write_json': 'True', 'log_cache_events': 'False', 'partial_parse': 'True', 'cache_selected_only': 'False', 'warn_error': 'None', 'fail_fast': 'False', 'debug': 'False', 'log_path': '/opt/airflow/workspace/dbt_live/logs', 'version_check': 'True', 'profiles_dir': '/opt/airflow/workspace/dbt_live', 'use_colors': 'True', 'use_experimental_parser': 'False', 'no_print': 'None', 'quiet': 'False', 'empty': 'False', 'log_format': 'default', 'invocation_command': 'dbt run --models marts', 'warn_error_options': 'WarnErrorOptions(include=[], exclude=[])', 'introspect': 'True', 'target_path': 'None', 'static_parser': 'True', 'send_anonymous_usage_stats': 'True'}
[0m10:10:20.589236 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'project_id', 'label': '020f1e74-4456-48f5-9cb4-1ee6f4667fb3', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x76a894f38760>]}
[0m10:10:20.678912 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'adapter_info', 'label': '020f1e74-4456-48f5-9cb4-1ee6f4667fb3', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x76a895003a60>]}
[0m10:10:20.683261 [info ] [MainThread]: Registered adapter: snowflake=1.9.4
[0m10:10:20.879522 [debug] [MainThread]: checksum: dd6dc1e5178459e3de3bf2eeb7c86bed4be2266c311fce8c15d86eb4ff94e7ad, vars: {}, profile: , target: , version: 1.9.6
[0m10:10:22.243200 [debug] [MainThread]: Partial parsing enabled: 0 files deleted, 0 files added, 0 files changed.
[0m10:10:22.244753 [debug] [MainThread]: Partial parsing enabled, no changes found, skipping parsing
[0m10:10:22.256331 [warn ] [MainThread]: [[33mWARNING[0m]: Configuration paths exist in your dbt_project.yml file which do not apply to any resources.
There are 3 unused configuration paths:
- models.live_c360.incremental
- seeds.live_c360
- snapshots.live_c360
[0m10:10:22.323695 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'load_project', 'label': '020f1e74-4456-48f5-9cb4-1ee6f4667fb3', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x76a881168130>]}
[0m10:10:22.505935 [debug] [MainThread]: Wrote artifact WritableManifest to /opt/airflow/workspace/dbt_live/target/manifest.json
[0m10:10:22.511643 [debug] [MainThread]: Wrote artifact SemanticManifest to /opt/airflow/workspace/dbt_live/target/semantic_manifest.json
[0m10:10:22.543554 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'resource_counts', 'label': '020f1e74-4456-48f5-9cb4-1ee6f4667fb3', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x76a880fb8310>]}
[0m10:10:22.544885 [info ] [MainThread]: Found 12 models, 21 data tests, 3 sources, 590 macros
[0m10:10:22.546117 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'runnable_timing', 'label': '020f1e74-4456-48f5-9cb4-1ee6f4667fb3', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x76a880fb8400>]}
[0m10:10:22.548722 [info ] [MainThread]: 
[0m10:10:22.551907 [info ] [MainThread]: Concurrency: 4 threads (target='live')
[0m10:10:22.553070 [info ] [MainThread]: 
[0m10:10:22.554346 [debug] [MainThread]: Acquiring new snowflake connection 'master'
[0m10:10:22.561450 [debug] [ThreadPool]: Acquiring new snowflake connection 'list_MYDB'
[0m10:10:22.562586 [debug] [ThreadPool]: Acquiring new snowflake connection 'list_MYDB'
[0m10:10:22.619447 [debug] [ThreadPool]: Using snowflake connection "list_MYDB"
[0m10:10:22.620505 [debug] [ThreadPool]: Using snowflake connection "list_MYDB"
[0m10:10:22.624012 [debug] [ThreadPool]: On list_MYDB: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB"} */
show terse schemas in database MYDB
    limit 10000
[0m10:10:22.625619 [debug] [ThreadPool]: On list_MYDB: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB"} */
show terse schemas in database MYDB
    limit 10000
[0m10:10:22.627474 [debug] [ThreadPool]: Opening a new connection, currently in state init
[0m10:10:22.628958 [debug] [ThreadPool]: Opening a new connection, currently in state init
[0m10:10:23.208103 [debug] [ThreadPool]: SQL status: SUCCESS 6 in 0.581 seconds
[0m10:10:23.254168 [debug] [ThreadPool]: SQL status: SUCCESS 6 in 0.625 seconds
[0m10:10:23.261070 [debug] [ThreadPool]: Re-using an available connection from the pool (formerly list_MYDB, now list_MYDB_LIVE_DATA_dbt_test__audit)
[0m10:10:23.262113 [debug] [ThreadPool]: Re-using an available connection from the pool (formerly list_MYDB, now list_MYDB_LIVE_DATA_ANALYTICS)
[0m10:10:23.263624 [debug] [ThreadPool]: Acquiring new snowflake connection 'list_MYDB_LIVE_DATA'
[0m10:10:23.278480 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_dbt_test__audit"
[0m10:10:23.281960 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_ANALYTICS"
[0m10:10:23.288662 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA"
[0m10:10:23.290239 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_dbt_test__audit: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_dbt_test__audit"} */
alter session set quoted_identifiers_ignore_case = false;
[0m10:10:23.291642 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_ANALYTICS: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_ANALYTICS"} */
alter session set quoted_identifiers_ignore_case = false;
[0m10:10:23.292677 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA"} */
alter session set quoted_identifiers_ignore_case = false;
[0m10:10:23.299161 [debug] [ThreadPool]: Opening a new connection, currently in state init
[0m10:10:23.366443 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.072 seconds
[0m10:10:23.374840 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_dbt_test__audit"
[0m10:10:23.376140 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.080 seconds
[0m10:10:23.377511 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_dbt_test__audit: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_dbt_test__audit"} */
show objects in MYDB.LIVE_DATA_dbt_test__audit
    limit 10000
    
;
[0m10:10:23.380701 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_ANALYTICS"
[0m10:10:23.387034 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_ANALYTICS: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_ANALYTICS"} */
show objects in MYDB.LIVE_DATA_ANALYTICS
    limit 10000
    
;
[0m10:10:23.509071 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.120 seconds
[0m10:10:23.512209 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_ANALYTICS"
[0m10:10:23.514463 [debug] [ThreadPool]: SQL status: SUCCESS 21 in 0.132 seconds
[0m10:10:23.515731 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_ANALYTICS: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_ANALYTICS"} */
alter session unset quoted_identifiers_ignore_case;
[0m10:10:23.520627 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_dbt_test__audit"
[0m10:10:23.525145 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_dbt_test__audit: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_dbt_test__audit"} */
alter session unset quoted_identifiers_ignore_case;
[0m10:10:23.598199 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.076 seconds
[0m10:10:23.599329 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.072 seconds
[0m10:10:23.675811 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.377 seconds
[0m10:10:23.679065 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA"
[0m10:10:23.680399 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA"} */
show objects in MYDB.LIVE_DATA
    limit 10000
    
;
[0m10:10:23.778770 [debug] [ThreadPool]: SQL status: SUCCESS 14 in 0.097 seconds
[0m10:10:23.781986 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA"
[0m10:10:23.783165 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA"} */
alter session unset quoted_identifiers_ignore_case;
[0m10:10:23.891511 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.105 seconds
[0m10:10:23.898846 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'runnable_timing', 'label': '020f1e74-4456-48f5-9cb4-1ee6f4667fb3', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x76a8963e78e0>]}
[0m10:10:23.906907 [debug] [Thread-1 (]: Began running node model.live_c360.dim_users
[0m10:10:23.907578 [debug] [Thread-2 (]: Began running node model.live_c360.dim_users_scd2
[0m10:10:23.909471 [debug] [Thread-3 (]: Began running node model.live_c360.fact_orders
[0m10:10:23.910245 [debug] [Thread-4 (]: Began running node model.live_c360.mv_fact_orders
[0m10:10:23.915032 [info ] [Thread-1 (]: 1 of 4 START sql table model LIVE_DATA.dim_users ............................... [RUN]
[0m10:10:23.921613 [info ] [Thread-2 (]: 2 of 4 START sql incremental model LIVE_DATA.dim_users_scd2 .................... [RUN]
[0m10:10:23.928387 [info ] [Thread-3 (]: 3 of 4 START sql table model LIVE_DATA.fact_orders ............................. [RUN]
[0m10:10:23.937621 [info ] [Thread-4 (]: 4 of 4 START sql view model LIVE_DATA_ANALYTICS.mv_fact_orders ................. [RUN]
[0m10:10:23.939927 [debug] [Thread-1 (]: Re-using an available connection from the pool (formerly list_MYDB_LIVE_DATA, now model.live_c360.dim_users)
[0m10:10:23.941817 [debug] [Thread-2 (]: Re-using an available connection from the pool (formerly list_MYDB_LIVE_DATA_dbt_test__audit, now model.live_c360.dim_users_scd2)
[0m10:10:23.943387 [debug] [Thread-3 (]: Re-using an available connection from the pool (formerly list_MYDB_LIVE_DATA_ANALYTICS, now model.live_c360.fact_orders)
[0m10:10:23.945205 [debug] [Thread-4 (]: Acquiring new snowflake connection 'model.live_c360.mv_fact_orders'
[0m10:10:23.946797 [debug] [Thread-1 (]: Began compiling node model.live_c360.dim_users
[0m10:10:23.948309 [debug] [Thread-2 (]: Began compiling node model.live_c360.dim_users_scd2
[0m10:10:23.949839 [debug] [Thread-3 (]: Began compiling node model.live_c360.fact_orders
[0m10:10:23.952801 [debug] [Thread-4 (]: Began compiling node model.live_c360.mv_fact_orders
[0m10:10:23.964445 [debug] [Thread-1 (]: Writing injected SQL for node "model.live_c360.dim_users"
[0m10:10:23.991665 [debug] [Thread-3 (]: Writing injected SQL for node "model.live_c360.fact_orders"
[0m10:10:23.998708 [debug] [Thread-2 (]: Writing injected SQL for node "model.live_c360.dim_users_scd2"
[0m10:10:24.006418 [debug] [Thread-4 (]: Writing injected SQL for node "model.live_c360.mv_fact_orders"
[0m10:10:24.014994 [debug] [Thread-1 (]: Began executing node model.live_c360.dim_users
[0m10:10:24.016046 [debug] [Thread-3 (]: Began executing node model.live_c360.fact_orders
[0m10:10:24.016984 [debug] [Thread-2 (]: Began executing node model.live_c360.dim_users_scd2
[0m10:10:24.030462 [debug] [Thread-4 (]: Began executing node model.live_c360.mv_fact_orders
[0m10:10:24.081754 [debug] [Thread-1 (]: Writing runtime sql for node "model.live_c360.dim_users"
[0m10:10:24.083513 [debug] [Thread-3 (]: Writing runtime sql for node "model.live_c360.fact_orders"
[0m10:10:24.157136 [debug] [Thread-4 (]: Writing runtime sql for node "model.live_c360.mv_fact_orders"
[0m10:10:24.167533 [debug] [Thread-2 (]: Using snowflake connection "model.live_c360.dim_users_scd2"
[0m10:10:24.172585 [debug] [Thread-2 (]: On model.live_c360.dim_users_scd2: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "node_id": "model.live_c360.dim_users_scd2"} */
create or replace  temporary view MYDB.LIVE_DATA.dim_users_scd2__dbt_tmp
  
   as (
    

with 

-- Get the latest user data from staging
source_data as (
    select 
        user_id,
        firstname,
        lastname,
        email_hash,
        address,
        acquisition_channel as canal,
        country,
        gender,
        age_group,
        creation_date,
        last_activity_date,
        is_churned as churn,
        batch_id,
        current_timestamp() as dbt_updated_at,
        -- Track when this record was processed
        current_timestamp() as processed_at
    from MYDB.LIVE_DATA.stg_users
    where user_id is not null
    qualify row_number() over (partition by user_id order by generated_at desc) = 1
    
    
    -- Only process users that are new or have changed since the last run
    and user_id in (
        select user_id
        from MYDB.LIVE_DATA.stg_users
        where generated_at > (
            select coalesce(max(dbt_valid_from), '1900-01-01'::timestamp)
            from MYDB.LIVE_DATA.dim_users_scd2
            where is_current_version = true
        )
    )
    
),

-- Get the current version of each user from the dimension
existing_dimension as (
    
    select
        user_sk,
        user_id,
        firstname,
        lastname,
        email,
        address,
        acquisition_channel,
        country,
        gender,
        age_group,
        creation_date,
        last_activity_date,
        churn,
        batch_id,
        dbt_valid_from,
        dbt_valid_to,
        is_current_version,
        dbt_change_type,
        current_timestamp() as processed_at  -- Add processed_at for consistency
    from MYDB.LIVE_DATA.dim_users_scd2
    where is_current_version = true
    
),

-- Identify new and changed records
changes_to_apply as (
    select
        s.*,
        case
            when e.user_sk is null then 'insert'
            when e.firstname != s.firstname 
              or e.lastname != s.lastname
              or e.email != s.email_hash
              or e.address != s.address
              or e.acquisition_channel != s.canal
              or e.country != s.country
              or e.gender != s.gender
              or e.age_group != s.age_group
              or e.churn != s.churn then 'update'
            else 'no_change'
        end as change_type
    from source_data s
    left join existing_dimension e 
        on s.user_id = e.user_id
)

-- For initial load, just insert all records


-- For incremental loads, handle inserts and updates
select * from (
    -- New or updated records
    select
        md5(cast(coalesce(cast(user_id as TEXT), '_dbt_utils_surrogate_key_null_') || '-' || coalesce(cast(dbt_updated_at as TEXT), '_dbt_utils_surrogate_key_null_') as TEXT)) as user_sk,
        user_id,
        firstname,
        lastname,
        email_hash as email,
        address,
        canal as acquisition_channel,
        country,
        gender,
        age_group,
        creation_date,
        last_activity_date,
        churn,
        batch_id,
        dbt_updated_at as dbt_valid_from,
        cast(null as timestamp_ntz) as dbt_valid_to,
        true as is_current_version,
        change_type as dbt_change_type,
        processed_at
    from changes_to_apply
    where change_type in ('insert', 'update')
    
    union all
    
    -- Expire old versions of updated records
    select
        e.user_sk,
        e.user_id,
        e.firstname,
        e.lastname,
        e.email,
        e.address,
        e.acquisition_channel,
        e.country,
        e.gender,
        e.age_group,
        e.creation_date,
        e.last_activity_date,
        e.churn,
        e.batch_id,
        e.dbt_valid_from,
        current_timestamp() as dbt_valid_to,
        false as is_current_version,
        'expire' as dbt_change_type,
        e.processed_at
    from existing_dimension e
    inner join changes_to_apply c 
        on e.user_id = c.user_id
        and c.change_type = 'update'
)


  );
[0m10:10:24.177422 [debug] [Thread-4 (]: Using snowflake connection "model.live_c360.mv_fact_orders"
[0m10:10:24.182306 [debug] [Thread-1 (]: Using snowflake connection "model.live_c360.dim_users"
[0m10:10:24.186460 [debug] [Thread-4 (]: On model.live_c360.mv_fact_orders: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "node_id": "model.live_c360.mv_fact_orders"} */
create or replace   view MYDB.LIVE_DATA_ANALYTICS.mv_fact_orders
  
   as (
    

with 
-- Get the 90th percentile order amount for high-value threshold
order_thresholds as (
    select 
        percentile_cont(0.9) within group (order by amount) as high_value_threshold
    from MYDB.LIVE_DATA.stg_orders
    where amount is not null
),

-- Get basic order metrics
order_metrics as (
    select
        -- Primary key and foreign key
        order_id,
        user_id,
        
        -- Core order details
        transaction_date,
        item_count,
        amount as order_amount,
        
        -- Basic metrics
        row_number() over (partition by user_id order by transaction_date) as user_order_sequence,
        
        -- Time since last order
        datediff(
            'day',
            lag(transaction_date) over (partition by user_id order by transaction_date),
            transaction_date
        ) as days_since_previous_order,
        
        -- Running totals
        sum(amount) over (order by transaction_date) as running_total_amount,
        count(*) over (order by date_trunc('day', transaction_date)) as running_order_count,
        
        -- Include the generated_at for incremental logic if needed
        generated_at
        
    from MYDB.LIVE_DATA.stg_orders
    where transaction_date is not null
    qualify row_number() over (partition by order_id order by generated_at desc) = 1
)

-- Final select with basic segmentation
select
    om.*,
    
    -- Simple customer type
    case 
        when user_order_sequence = 1 then 'New Customer'
        when days_since_previous_order is null then 'New Customer'
        when days_since_previous_order <= 30 then 'Repeat Customer (30d)'
        else 'Reactivated Customer'
    end as customer_type,
    
    -- High value flag (using pre-calculated threshold)
    case 
        when order_amount > (select high_value_threshold from order_thresholds) then true
        else false
    end as is_high_value_order,
    
    -- Large order flag
    case 
        when item_count > 10 then true
        else false
    end as is_large_order,
    
    -- Time of day
    case 
        when extract(hour from transaction_date) between 6 and 11 then 'Morning'
        when extract(hour from transaction_date) between 12 and 17 then 'Afternoon'
        when extract(hour from transaction_date) between 18 and 21 then 'Evening'
        else 'Night'
    end as time_of_day,
    
    -- Add a timestamp for when this record was created
    current_timestamp() as dbt_updated_at
    
from order_metrics om
  );
[0m10:10:24.188959 [debug] [Thread-1 (]: On model.live_c360.dim_users: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "node_id": "model.live_c360.dim_users"} */
create or replace transient table MYDB.LIVE_DATA.dim_users
    

    
    as (

-- User dimension table for live data pipeline
-- Provides a clean, analytics-ready view of user data

with users_base as (
    select * from MYDB.LIVE_DATA.stg_users
),

user_metrics as (
    select
        user_id,
        count(*) as total_orders,
        sum(amount) as total_spent,
        avg(amount) as avg_order_value,
        min(transaction_date) as first_order_date,
        max(transaction_date) as last_order_date,
        sum(item_count) as total_items_purchased
    from MYDB.LIVE_DATA.stg_orders
    group by user_id
),

user_events as (
    select
        user_id,
        count(*) as total_events,
        count(distinct session_id) as total_sessions,
        count(distinct event_date) as active_days,
        min(event_timestamp) as first_event_date,
        max(event_timestamp) as last_event_date,
        
        -- Event type counts
        sum(case when action = 'view' then 1 else 0 end) as view_events,
        sum(case when action = 'click' then 1 else 0 end) as click_events,
        sum(case when action = 'log' then 1 else 0 end) as login_events,
        sum(case when action = 'purchase' then 1 else 0 end) as purchase_events,
        
        -- Platform usage
        sum(case when device_category = 'Mobile' then 1 else 0 end) as mobile_events,
        sum(case when device_category = 'Web' then 1 else 0 end) as web_events
        
    from MYDB.LIVE_DATA.stg_events
    group by user_id
),

final as (
    select
        -- User identifiers
        u.user_id,
        u.email_hash,
        
        -- Personal information
        u.firstname,
        u.lastname,
        u.address,
        u.acquisition_channel,
        u.country,
        u.gender,
        u.age_group,
        
        -- Dates
        u.creation_date,
        u.last_activity_date,
        
        -- Behavioral flags
        u.is_churned,
        u.activity_segment,
        
        -- Derived user metrics
        u.days_since_creation,
        u.days_since_last_activity,
        
        -- Order metrics
        coalesce(om.total_orders, 0) as total_orders,
        coalesce(om.total_spent, 0) as total_spent,
        coalesce(om.avg_order_value, 0) as avg_order_value,
        om.first_order_date,
        om.last_order_date,
        coalesce(om.total_items_purchased, 0) as total_items_purchased,
        
        -- Event metrics
        coalesce(ue.total_events, 0) as total_events,
        coalesce(ue.total_sessions, 0) as total_sessions,
        coalesce(ue.active_days, 0) as active_days,
        ue.first_event_date,
        ue.last_event_date,
        
        -- Event type metrics
        coalesce(ue.view_events, 0) as view_events,
        coalesce(ue.click_events, 0) as click_events,
        coalesce(ue.login_events, 0) as login_events,
        coalesce(ue.purchase_events, 0) as purchase_events,
        
        -- Platform metrics
        coalesce(ue.mobile_events, 0) as mobile_events,
        coalesce(ue.web_events, 0) as web_events,
        
        -- Calculated metrics
        case 
            when om.total_orders > 0 then round(ue.total_events::float / om.total_orders, 2)
            else 0
        end as events_per_order,
        
        case 
            when ue.total_sessions > 0 then round(ue.total_events::float / ue.total_sessions, 2)
            else 0
        end as events_per_session,
        
        case 
            when om.total_orders > 0 then 
                datediff('day', om.first_order_date, om.last_order_date)::float / om.total_orders
            else 0
        end as avg_days_between_orders,
        
        -- Customer segments
        case 
            when om.total_orders = 0 then 'No Orders'
            when om.total_orders = 1 then 'One-time Buyer'
            when om.total_orders <= 3 then 'Occasional Buyer'
            when om.total_orders <= 10 then 'Regular Buyer'
            else 'Frequent Buyer'
        end as purchase_segment,
        
        case 
            when om.total_spent = 0 then 'No Spend'
            when om.total_spent < 50 then 'Low Value'
            when om.total_spent < 200 then 'Medium Value'
            when om.total_spent < 500 then 'High Value'
            else 'Premium Value'
        end as value_segment,
        
        case 
            when ue.mobile_events > ue.web_events then 'Mobile Preferred'
            when ue.web_events > ue.mobile_events then 'Web Preferred'
            when ue.mobile_events = ue.web_events and ue.mobile_events > 0 then 'Multi-Platform'
            else 'Unknown'
        end as platform_preference,
        
        -- Data quality flags
        u.has_future_creation_date,
        u.has_invalid_activity_date,
        
        -- Metadata
        u.batch_id,
        u.generated_at,
        u.processed_at,
        current_timestamp() as mart_created_at
        
    from users_base u
    left join user_metrics om on u.user_id = om.user_id
    left join user_events ue on u.user_id = ue.user_id
)

select * from final
    )
;
[0m10:10:24.191753 [debug] [Thread-3 (]: Using snowflake connection "model.live_c360.fact_orders"
[0m10:10:24.192996 [debug] [Thread-4 (]: Opening a new connection, currently in state init
[0m10:10:24.197170 [debug] [Thread-3 (]: On model.live_c360.fact_orders: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "node_id": "model.live_c360.fact_orders"} */
create or replace transient table MYDB.LIVE_DATA.fact_orders
    

    
    as (

with 
-- Get the 90th percentile order amount for high-value threshold
order_thresholds as (
    select 
        percentile_cont(0.9) within group (order by amount) as high_value_threshold
    from MYDB.LIVE_DATA.stg_orders
    where amount is not null
),

-- Get basic order metrics
order_metrics as (
    select
        -- Primary key and foreign key
        order_id,
        user_id,
        
        -- Core order details
        transaction_date,
        item_count,
        amount as order_amount,
        
        -- Basic metrics
        row_number() over (partition by user_id order by transaction_date) as user_order_sequence,
        
        -- Time since last order
        datediff(
            'day',
            lag(transaction_date) over (partition by user_id order by transaction_date),
            transaction_date
        ) as days_since_previous_order,
        
        -- Running totals
        sum(amount) over (order by transaction_date) as running_total_amount,
        count(*) over (order by date_trunc('day', transaction_date)) as running_order_count
        
    from MYDB.LIVE_DATA.stg_orders
    where transaction_date is not null
    qualify row_number() over (partition by order_id order by generated_at desc) = 1
)

-- Final select with basic segmentation
select
    om.*,
    
    -- Simple customer type
    case 
        when user_order_sequence = 1 then 'New Customer'
        when days_since_previous_order is null then 'New Customer'
        when days_since_previous_order <= 30 then 'Repeat Customer (30d)'
        else 'Reactivated Customer'
    end as customer_type,
    
    -- High value flag (using pre-calculated threshold)
    case 
        when order_amount > (select high_value_threshold from order_thresholds) then true
        else false
    end as is_high_value_order,
    
    -- Large order flag
    case 
        when item_count > 10 then true
        else false
    end as is_large_order,
    
    -- Time of day
    case 
        when extract(hour from transaction_date) between 6 and 11 then 'Morning'
        when extract(hour from transaction_date) between 12 and 17 then 'Afternoon'
        when extract(hour from transaction_date) between 18 and 21 then 'Evening'
        else 'Night'
    end as time_of_day
    
from order_metrics om
    )
;
[0m10:10:24.370581 [debug] [Thread-3 (]: Snowflake adapter: Snowflake query id: 01bccd42-3204-7fc5-0002-4ad6000631fe
[0m10:10:24.372577 [debug] [Thread-3 (]: Snowflake adapter: Snowflake error: 091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
[0m10:10:24.375009 [debug] [Thread-3 (]: Database Error in model fact_orders (models/marts/fact_orders.sql)
  091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/marts/fact_orders.sql
[0m10:10:24.377878 [debug] [Thread-3 (]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '020f1e74-4456-48f5-9cb4-1ee6f4667fb3', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x76a8800c1510>]}
[0m10:10:24.379531 [error] [Thread-3 (]: 3 of 4 ERROR creating sql table model LIVE_DATA.fact_orders .................... [[31mERROR[0m in 0.43s]
[0m10:10:24.381176 [debug] [Thread-3 (]: Finished running node model.live_c360.fact_orders
[0m10:10:24.382697 [debug] [Thread-7 (]: Marking all children of 'model.live_c360.fact_orders' to be skipped because of status 'error'.  Reason: Database Error in model fact_orders (models/marts/fact_orders.sql)
  091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/marts/fact_orders.sql.
[0m10:10:24.388138 [debug] [Thread-1 (]: Snowflake adapter: Snowflake query id: 01bccd42-3204-7fc5-0002-4ad6000631fa
[0m10:10:24.389502 [debug] [Thread-1 (]: Snowflake adapter: Snowflake error: 091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
[0m10:10:24.391639 [debug] [Thread-1 (]: Database Error in model dim_users (models/marts/dim_users.sql)
  091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
  compiled code at target/run/live_c360/models/marts/dim_users.sql
[0m10:10:24.393009 [debug] [Thread-1 (]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '020f1e74-4456-48f5-9cb4-1ee6f4667fb3', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x76a880152e00>]}
[0m10:10:24.394474 [error] [Thread-1 (]: 1 of 4 ERROR creating sql table model LIVE_DATA.dim_users ...................... [[31mERROR[0m in 0.45s]
[0m10:10:24.395946 [debug] [Thread-1 (]: Finished running node model.live_c360.dim_users
[0m10:10:24.397450 [debug] [Thread-7 (]: Marking all children of 'model.live_c360.dim_users' to be skipped because of status 'error'.  Reason: Database Error in model dim_users (models/marts/dim_users.sql)
  091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
  compiled code at target/run/live_c360/models/marts/dim_users.sql.
[0m10:10:24.823396 [debug] [Thread-2 (]: Snowflake adapter: Snowflake query id: 01bccd42-3204-7eab-0002-4ad600061246
[0m10:10:24.825071 [debug] [Thread-2 (]: Snowflake adapter: Snowflake error: 091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
[0m10:10:24.827592 [debug] [Thread-2 (]: Database Error in model dim_users_scd2 (models/marts/dim_users_scd2.sql)
  091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
[0m10:10:24.829358 [debug] [Thread-2 (]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '020f1e74-4456-48f5-9cb4-1ee6f4667fb3', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x76a878f0bf70>]}
[0m10:10:24.830962 [error] [Thread-2 (]: 2 of 4 ERROR creating sql incremental model LIVE_DATA.dim_users_scd2 ........... [[31mERROR[0m in 0.89s]
[0m10:10:24.832570 [debug] [Thread-2 (]: Finished running node model.live_c360.dim_users_scd2
[0m10:10:24.834087 [debug] [Thread-7 (]: Marking all children of 'model.live_c360.dim_users_scd2' to be skipped because of status 'error'.  Reason: Database Error in model dim_users_scd2 (models/marts/dim_users_scd2.sql)
  091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped..
[0m10:10:24.838882 [debug] [Thread-4 (]: Snowflake adapter: Snowflake query id: 01bccd42-3204-800f-0002-4ad6000650f2
[0m10:10:24.840299 [debug] [Thread-4 (]: Snowflake adapter: Snowflake error: 091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
[0m10:10:24.842585 [debug] [Thread-4 (]: Database Error in model mv_fact_orders (models/marts/mv_fact_orders.sql)
  091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/marts/mv_fact_orders.sql
[0m10:10:24.844470 [debug] [Thread-4 (]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '020f1e74-4456-48f5-9cb4-1ee6f4667fb3', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x76a880fb83a0>]}
[0m10:10:24.846320 [error] [Thread-4 (]: 4 of 4 ERROR creating sql view model LIVE_DATA_ANALYTICS.mv_fact_orders ........ [[31mERROR[0m in 0.90s]
[0m10:10:24.847846 [debug] [Thread-4 (]: Finished running node model.live_c360.mv_fact_orders
[0m10:10:24.849315 [debug] [Thread-7 (]: Marking all children of 'model.live_c360.mv_fact_orders' to be skipped because of status 'error'.  Reason: Database Error in model mv_fact_orders (models/marts/mv_fact_orders.sql)
  091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/marts/mv_fact_orders.sql.
[0m10:10:24.852524 [debug] [MainThread]: Connection 'master' was properly closed.
[0m10:10:24.854444 [debug] [MainThread]: Connection 'model.live_c360.fact_orders' was left open.
[0m10:10:24.855900 [debug] [MainThread]: On model.live_c360.fact_orders: Close
[0m10:10:24.946834 [debug] [MainThread]: Connection 'model.live_c360.dim_users_scd2' was left open.
[0m10:10:24.948533 [debug] [MainThread]: On model.live_c360.dim_users_scd2: Close
[0m10:10:25.023220 [debug] [MainThread]: Connection 'model.live_c360.dim_users' was left open.
[0m10:10:25.025063 [debug] [MainThread]: On model.live_c360.dim_users: Close
[0m10:10:25.097067 [debug] [MainThread]: Connection 'model.live_c360.mv_fact_orders' was left open.
[0m10:10:25.098675 [debug] [MainThread]: On model.live_c360.mv_fact_orders: Close
[0m10:10:25.167025 [info ] [MainThread]: 
[0m10:10:25.170726 [info ] [MainThread]: Finished running 1 incremental model, 2 table models, 1 view model in 0 hours 0 minutes and 2.61 seconds (2.61s).
[0m10:10:25.173278 [debug] [MainThread]: Command end result
[0m10:10:25.330744 [debug] [MainThread]: Wrote artifact WritableManifest to /opt/airflow/workspace/dbt_live/target/manifest.json
[0m10:10:25.338947 [debug] [MainThread]: Wrote artifact SemanticManifest to /opt/airflow/workspace/dbt_live/target/semantic_manifest.json
[0m10:10:25.354189 [debug] [MainThread]: Wrote artifact RunExecutionResult to /opt/airflow/workspace/dbt_live/target/run_results.json
[0m10:10:25.355733 [info ] [MainThread]: 
[0m10:10:25.357151 [info ] [MainThread]: [31mCompleted with 4 errors, 0 partial successes, and 0 warnings:[0m
[0m10:10:25.358456 [info ] [MainThread]: 
[0m10:10:25.359657 [error] [MainThread]:   Database Error in model fact_orders (models/marts/fact_orders.sql)
  091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/marts/fact_orders.sql
[0m10:10:25.360866 [info ] [MainThread]: 
[0m10:10:25.362050 [error] [MainThread]:   Database Error in model dim_users (models/marts/dim_users.sql)
  091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
  compiled code at target/run/live_c360/models/marts/dim_users.sql
[0m10:10:25.363209 [info ] [MainThread]: 
[0m10:10:25.364350 [error] [MainThread]:   Database Error in model dim_users_scd2 (models/marts/dim_users_scd2.sql)
  091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
[0m10:10:25.365498 [info ] [MainThread]: 
[0m10:10:25.366713 [error] [MainThread]:   Database Error in model mv_fact_orders (models/marts/mv_fact_orders.sql)
  091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/marts/mv_fact_orders.sql
[0m10:10:25.367873 [info ] [MainThread]: 
[0m10:10:25.371048 [info ] [MainThread]: Done. PASS=0 WARN=0 ERROR=4 SKIP=0 TOTAL=4
[0m10:10:25.373171 [debug] [MainThread]: Resource report: {"command_name": "run", "command_success": false, "command_wall_clock_time": 5.81497, "process_in_blocks": "0", "process_kernel_time": 0.697448, "process_mem_max_rss": "206892", "process_out_blocks": "16", "process_user_time": 4.067776}
[0m10:10:25.374534 [debug] [MainThread]: Command `dbt run` failed at 10:10:25.374400 after 5.82 seconds
[0m10:10:25.375699 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x76a8952ca890>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x76a896433af0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x76a89612cb80>]}
[0m10:10:25.376848 [debug] [MainThread]: Flushing usage events
[0m10:10:26.349741 [debug] [MainThread]: An error was encountered while trying to flush usage events
[0m10:12:30.103131 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x709249b16980>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x70924978ed70>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x709248a66260>]}


============================== 10:12:30.110295 | 0d5b25e0-2dcb-40bb-bf39-1acec9f0fbc5 ==============================
[0m10:12:30.110295 [info ] [MainThread]: Running with dbt=1.9.6
[0m10:12:30.113332 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'write_json': 'True', 'log_cache_events': 'False', 'partial_parse': 'True', 'cache_selected_only': 'False', 'profiles_dir': '/opt/airflow/workspace/dbt_live', 'version_check': 'True', 'warn_error': 'None', 'log_path': '/opt/airflow/workspace/dbt_live/logs', 'debug': 'False', 'fail_fast': 'False', 'use_colors': 'True', 'use_experimental_parser': 'False', 'empty': 'False', 'quiet': 'False', 'no_print': 'None', 'log_format': 'default', 'static_parser': 'True', 'warn_error_options': 'WarnErrorOptions(include=[], exclude=[])', 'introspect': 'True', 'target_path': 'None', 'invocation_command': 'dbt run --models marts', 'send_anonymous_usage_stats': 'True'}
[0m10:12:31.064728 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'project_id', 'label': '0d5b25e0-2dcb-40bb-bf39-1acec9f0fbc5', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7092490d8d30>]}
[0m10:12:31.140334 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'adapter_info', 'label': '0d5b25e0-2dcb-40bb-bf39-1acec9f0fbc5', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x709231ed95a0>]}
[0m10:12:31.142221 [info ] [MainThread]: Registered adapter: snowflake=1.9.4
[0m10:12:31.348914 [debug] [MainThread]: checksum: dd6dc1e5178459e3de3bf2eeb7c86bed4be2266c311fce8c15d86eb4ff94e7ad, vars: {}, profile: , target: , version: 1.9.6
[0m10:12:32.694318 [debug] [MainThread]: Partial parsing enabled: 0 files deleted, 0 files added, 0 files changed.
[0m10:12:32.695987 [debug] [MainThread]: Partial parsing enabled, no changes found, skipping parsing
[0m10:12:32.706834 [warn ] [MainThread]: [[33mWARNING[0m]: Configuration paths exist in your dbt_project.yml file which do not apply to any resources.
There are 3 unused configuration paths:
- models.live_c360.incremental
- seeds.live_c360
- snapshots.live_c360
[0m10:12:32.776637 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'load_project', 'label': '0d5b25e0-2dcb-40bb-bf39-1acec9f0fbc5', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7092317ec130>]}
[0m10:12:32.956676 [debug] [MainThread]: Wrote artifact WritableManifest to /opt/airflow/workspace/dbt_live/target/manifest.json
[0m10:12:32.963021 [debug] [MainThread]: Wrote artifact SemanticManifest to /opt/airflow/workspace/dbt_live/target/semantic_manifest.json
[0m10:12:32.994761 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'resource_counts', 'label': '0d5b25e0-2dcb-40bb-bf39-1acec9f0fbc5', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7092318383d0>]}
[0m10:12:32.996219 [info ] [MainThread]: Found 12 models, 21 data tests, 3 sources, 590 macros
[0m10:12:32.997305 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'runnable_timing', 'label': '0d5b25e0-2dcb-40bb-bf39-1acec9f0fbc5', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7092318384c0>]}
[0m10:12:33.002502 [info ] [MainThread]: 
[0m10:12:33.004085 [info ] [MainThread]: Concurrency: 4 threads (target='live')
[0m10:12:33.005354 [info ] [MainThread]: 
[0m10:12:33.006821 [debug] [MainThread]: Acquiring new snowflake connection 'master'
[0m10:12:33.021716 [debug] [ThreadPool]: Acquiring new snowflake connection 'list_MYDB'
[0m10:12:33.023283 [debug] [ThreadPool]: Acquiring new snowflake connection 'list_MYDB'
[0m10:12:33.078657 [debug] [ThreadPool]: Using snowflake connection "list_MYDB"
[0m10:12:33.079335 [debug] [ThreadPool]: Using snowflake connection "list_MYDB"
[0m10:12:33.080627 [debug] [ThreadPool]: On list_MYDB: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB"} */
show terse schemas in database MYDB
    limit 10000
[0m10:12:33.083908 [debug] [ThreadPool]: On list_MYDB: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB"} */
show terse schemas in database MYDB
    limit 10000
[0m10:12:33.085545 [debug] [ThreadPool]: Opening a new connection, currently in state init
[0m10:12:33.086995 [debug] [ThreadPool]: Opening a new connection, currently in state init
[0m10:12:33.666070 [debug] [ThreadPool]: SQL status: SUCCESS 6 in 0.580 seconds
[0m10:12:33.667855 [debug] [ThreadPool]: SQL status: SUCCESS 6 in 0.581 seconds
[0m10:12:33.676687 [debug] [ThreadPool]: Re-using an available connection from the pool (formerly list_MYDB, now list_MYDB_LIVE_DATA)
[0m10:12:33.677583 [debug] [ThreadPool]: Re-using an available connection from the pool (formerly list_MYDB, now list_MYDB_LIVE_DATA_dbt_test__audit)
[0m10:12:33.678540 [debug] [ThreadPool]: Acquiring new snowflake connection 'list_MYDB_LIVE_DATA_ANALYTICS'
[0m10:12:33.692991 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA"
[0m10:12:33.696712 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_dbt_test__audit"
[0m10:12:33.703733 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_ANALYTICS"
[0m10:12:33.705383 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA"} */
alter session set quoted_identifiers_ignore_case = false;
[0m10:12:33.706674 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_dbt_test__audit: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_dbt_test__audit"} */
alter session set quoted_identifiers_ignore_case = false;
[0m10:12:33.708298 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_ANALYTICS: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_ANALYTICS"} */
alter session set quoted_identifiers_ignore_case = false;
[0m10:12:33.717283 [debug] [ThreadPool]: Opening a new connection, currently in state init
[0m10:12:33.812752 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.100 seconds
[0m10:12:33.821277 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_dbt_test__audit"
[0m10:12:33.824071 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.114 seconds
[0m10:12:33.824951 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_dbt_test__audit: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_dbt_test__audit"} */
show objects in MYDB.LIVE_DATA_dbt_test__audit
    limit 10000
    
;
[0m10:12:33.828250 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA"
[0m10:12:33.834121 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA"} */
show objects in MYDB.LIVE_DATA
    limit 10000
    
;
[0m10:12:33.924766 [debug] [ThreadPool]: SQL status: SUCCESS 14 in 0.089 seconds
[0m10:12:33.929035 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA"
[0m10:12:33.932074 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA"} */
alter session unset quoted_identifiers_ignore_case;
[0m10:12:33.960296 [debug] [ThreadPool]: SQL status: SUCCESS 21 in 0.130 seconds
[0m10:12:33.965065 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_dbt_test__audit"
[0m10:12:33.966995 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_dbt_test__audit: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_dbt_test__audit"} */
alter session unset quoted_identifiers_ignore_case;
[0m10:12:34.027919 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.094 seconds
[0m10:12:34.053774 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.085 seconds
[0m10:12:34.207028 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.490 seconds
[0m10:12:34.210122 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_ANALYTICS"
[0m10:12:34.211666 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_ANALYTICS: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_ANALYTICS"} */
show objects in MYDB.LIVE_DATA_ANALYTICS
    limit 10000
    
;
[0m10:12:34.633379 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.420 seconds
[0m10:12:34.636583 [debug] [ThreadPool]: Using snowflake connection "list_MYDB_LIVE_DATA_ANALYTICS"
[0m10:12:34.637947 [debug] [ThreadPool]: On list_MYDB_LIVE_DATA_ANALYTICS: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "connection_name": "list_MYDB_LIVE_DATA_ANALYTICS"} */
alter session unset quoted_identifiers_ignore_case;
[0m10:12:34.722459 [debug] [ThreadPool]: SQL status: SUCCESS 1 in 0.083 seconds
[0m10:12:34.726403 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'runnable_timing', 'label': '0d5b25e0-2dcb-40bb-bf39-1acec9f0fbc5', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x70924ae92dd0>]}
[0m10:12:34.734130 [debug] [Thread-1 (]: Began running node model.live_c360.dim_users
[0m10:12:34.734817 [debug] [Thread-2 (]: Began running node model.live_c360.dim_users_scd2
[0m10:12:34.735584 [debug] [Thread-3 (]: Began running node model.live_c360.fact_orders
[0m10:12:34.736278 [debug] [Thread-4 (]: Began running node model.live_c360.mv_fact_orders
[0m10:12:34.737499 [info ] [Thread-1 (]: 1 of 4 START sql table model LIVE_DATA.dim_users ............................... [RUN]
[0m10:12:34.739289 [info ] [Thread-2 (]: 2 of 4 START sql incremental model LIVE_DATA.dim_users_scd2 .................... [RUN]
[0m10:12:34.741162 [info ] [Thread-3 (]: 3 of 4 START sql table model LIVE_DATA.fact_orders ............................. [RUN]
[0m10:12:34.742613 [info ] [Thread-4 (]: 4 of 4 START sql view model LIVE_DATA_ANALYTICS.mv_fact_orders ................. [RUN]
[0m10:12:34.744648 [debug] [Thread-1 (]: Re-using an available connection from the pool (formerly list_MYDB_LIVE_DATA_ANALYTICS, now model.live_c360.dim_users)
[0m10:12:34.746206 [debug] [Thread-2 (]: Re-using an available connection from the pool (formerly list_MYDB_LIVE_DATA_dbt_test__audit, now model.live_c360.dim_users_scd2)
[0m10:12:34.748945 [debug] [Thread-3 (]: Re-using an available connection from the pool (formerly list_MYDB_LIVE_DATA, now model.live_c360.fact_orders)
[0m10:12:34.750801 [debug] [Thread-4 (]: Acquiring new snowflake connection 'model.live_c360.mv_fact_orders'
[0m10:12:34.752267 [debug] [Thread-1 (]: Began compiling node model.live_c360.dim_users
[0m10:12:34.753643 [debug] [Thread-2 (]: Began compiling node model.live_c360.dim_users_scd2
[0m10:12:34.755164 [debug] [Thread-3 (]: Began compiling node model.live_c360.fact_orders
[0m10:12:34.756526 [debug] [Thread-4 (]: Began compiling node model.live_c360.mv_fact_orders
[0m10:12:34.785100 [debug] [Thread-1 (]: Writing injected SQL for node "model.live_c360.dim_users"
[0m10:12:34.814406 [debug] [Thread-2 (]: Writing injected SQL for node "model.live_c360.dim_users_scd2"
[0m10:12:34.819562 [debug] [Thread-3 (]: Writing injected SQL for node "model.live_c360.fact_orders"
[0m10:12:34.826068 [debug] [Thread-4 (]: Writing injected SQL for node "model.live_c360.mv_fact_orders"
[0m10:12:34.833388 [debug] [Thread-1 (]: Began executing node model.live_c360.dim_users
[0m10:12:34.834198 [debug] [Thread-3 (]: Began executing node model.live_c360.fact_orders
[0m10:12:34.840594 [debug] [Thread-2 (]: Began executing node model.live_c360.dim_users_scd2
[0m10:12:34.850968 [debug] [Thread-4 (]: Began executing node model.live_c360.mv_fact_orders
[0m10:12:34.900991 [debug] [Thread-3 (]: Writing runtime sql for node "model.live_c360.fact_orders"
[0m10:12:34.905917 [debug] [Thread-1 (]: Writing runtime sql for node "model.live_c360.dim_users"
[0m10:12:34.974467 [debug] [Thread-4 (]: Writing runtime sql for node "model.live_c360.mv_fact_orders"
[0m10:12:34.978398 [debug] [Thread-2 (]: Using snowflake connection "model.live_c360.dim_users_scd2"
[0m10:12:34.984310 [debug] [Thread-2 (]: On model.live_c360.dim_users_scd2: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "node_id": "model.live_c360.dim_users_scd2"} */
create or replace  temporary view MYDB.LIVE_DATA.dim_users_scd2__dbt_tmp
  
   as (
    

with 

-- Get the latest user data from staging
source_data as (
    select 
        user_id,
        firstname,
        lastname,
        email_hash,
        address,
        acquisition_channel as canal,
        country,
        gender,
        age_group,
        creation_date,
        last_activity_date,
        is_churned as churn,
        batch_id,
        current_timestamp() as dbt_updated_at,
        -- Track when this record was processed
        current_timestamp() as processed_at
    from MYDB.LIVE_DATA.stg_users
    where user_id is not null
    qualify row_number() over (partition by user_id order by generated_at desc) = 1
    
    
    -- Only process users that are new or have changed since the last run
    and user_id in (
        select user_id
        from MYDB.LIVE_DATA.stg_users
        where generated_at > (
            select coalesce(max(dbt_valid_from), '1900-01-01'::timestamp)
            from MYDB.LIVE_DATA.dim_users_scd2
            where is_current_version = true
        )
    )
    
),

-- Get the current version of each user from the dimension
existing_dimension as (
    
    select
        user_sk,
        user_id,
        firstname,
        lastname,
        email,
        address,
        acquisition_channel,
        country,
        gender,
        age_group,
        creation_date,
        last_activity_date,
        churn,
        batch_id,
        dbt_valid_from,
        dbt_valid_to,
        is_current_version,
        dbt_change_type,
        current_timestamp() as processed_at  -- Add processed_at for consistency
    from MYDB.LIVE_DATA.dim_users_scd2
    where is_current_version = true
    
),

-- Identify new and changed records
changes_to_apply as (
    select
        s.*,
        case
            when e.user_sk is null then 'insert'
            when e.firstname != s.firstname 
              or e.lastname != s.lastname
              or e.email != s.email_hash
              or e.address != s.address
              or e.acquisition_channel != s.canal
              or e.country != s.country
              or e.gender != s.gender
              or e.age_group != s.age_group
              or e.churn != s.churn then 'update'
            else 'no_change'
        end as change_type
    from source_data s
    left join existing_dimension e 
        on s.user_id = e.user_id
)

-- For initial load, just insert all records


-- For incremental loads, handle inserts and updates
select * from (
    -- New or updated records
    select
        md5(cast(coalesce(cast(user_id as TEXT), '_dbt_utils_surrogate_key_null_') || '-' || coalesce(cast(dbt_updated_at as TEXT), '_dbt_utils_surrogate_key_null_') as TEXT)) as user_sk,
        user_id,
        firstname,
        lastname,
        email_hash as email,
        address,
        canal as acquisition_channel,
        country,
        gender,
        age_group,
        creation_date,
        last_activity_date,
        churn,
        batch_id,
        dbt_updated_at as dbt_valid_from,
        cast(null as timestamp_ntz) as dbt_valid_to,
        true as is_current_version,
        change_type as dbt_change_type,
        processed_at
    from changes_to_apply
    where change_type in ('insert', 'update')
    
    union all
    
    -- Expire old versions of updated records
    select
        e.user_sk,
        e.user_id,
        e.firstname,
        e.lastname,
        e.email,
        e.address,
        e.acquisition_channel,
        e.country,
        e.gender,
        e.age_group,
        e.creation_date,
        e.last_activity_date,
        e.churn,
        e.batch_id,
        e.dbt_valid_from,
        current_timestamp() as dbt_valid_to,
        false as is_current_version,
        'expire' as dbt_change_type,
        e.processed_at
    from existing_dimension e
    inner join changes_to_apply c 
        on e.user_id = c.user_id
        and c.change_type = 'update'
)


  );
[0m10:12:34.989011 [debug] [Thread-3 (]: Using snowflake connection "model.live_c360.fact_orders"
[0m10:12:34.993135 [debug] [Thread-4 (]: Using snowflake connection "model.live_c360.mv_fact_orders"
[0m10:12:34.994730 [debug] [Thread-3 (]: On model.live_c360.fact_orders: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "node_id": "model.live_c360.fact_orders"} */
create or replace transient table MYDB.LIVE_DATA.fact_orders
    

    
    as (

with 
-- Get the 90th percentile order amount for high-value threshold
order_thresholds as (
    select 
        percentile_cont(0.9) within group (order by amount) as high_value_threshold
    from MYDB.LIVE_DATA.stg_orders
    where amount is not null
),

-- Get basic order metrics
order_metrics as (
    select
        -- Primary key and foreign key
        order_id,
        user_id,
        
        -- Core order details
        transaction_date,
        item_count,
        amount as order_amount,
        
        -- Basic metrics
        row_number() over (partition by user_id order by transaction_date) as user_order_sequence,
        
        -- Time since last order
        datediff(
            'day',
            lag(transaction_date) over (partition by user_id order by transaction_date),
            transaction_date
        ) as days_since_previous_order,
        
        -- Running totals
        sum(amount) over (order by transaction_date) as running_total_amount,
        count(*) over (order by date_trunc('day', transaction_date)) as running_order_count
        
    from MYDB.LIVE_DATA.stg_orders
    where transaction_date is not null
    qualify row_number() over (partition by order_id order by generated_at desc) = 1
)

-- Final select with basic segmentation
select
    om.*,
    
    -- Simple customer type
    case 
        when user_order_sequence = 1 then 'New Customer'
        when days_since_previous_order is null then 'New Customer'
        when days_since_previous_order <= 30 then 'Repeat Customer (30d)'
        else 'Reactivated Customer'
    end as customer_type,
    
    -- High value flag (using pre-calculated threshold)
    case 
        when order_amount > (select high_value_threshold from order_thresholds) then true
        else false
    end as is_high_value_order,
    
    -- Large order flag
    case 
        when item_count > 10 then true
        else false
    end as is_large_order,
    
    -- Time of day
    case 
        when extract(hour from transaction_date) between 6 and 11 then 'Morning'
        when extract(hour from transaction_date) between 12 and 17 then 'Afternoon'
        when extract(hour from transaction_date) between 18 and 21 then 'Evening'
        else 'Night'
    end as time_of_day
    
from order_metrics om
    )
;
[0m10:12:35.001877 [debug] [Thread-1 (]: Using snowflake connection "model.live_c360.dim_users"
[0m10:12:35.003184 [debug] [Thread-4 (]: On model.live_c360.mv_fact_orders: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "node_id": "model.live_c360.mv_fact_orders"} */
create or replace   view MYDB.LIVE_DATA_ANALYTICS.mv_fact_orders
  
   as (
    

with 
-- Get the 90th percentile order amount for high-value threshold
order_thresholds as (
    select 
        percentile_cont(0.9) within group (order by amount) as high_value_threshold
    from MYDB.LIVE_DATA.stg_orders
    where amount is not null
),

-- Get basic order metrics
order_metrics as (
    select
        -- Primary key and foreign key
        order_id,
        user_id,
        
        -- Core order details
        transaction_date,
        item_count,
        amount as order_amount,
        
        -- Basic metrics
        row_number() over (partition by user_id order by transaction_date) as user_order_sequence,
        
        -- Time since last order
        datediff(
            'day',
            lag(transaction_date) over (partition by user_id order by transaction_date),
            transaction_date
        ) as days_since_previous_order,
        
        -- Running totals
        sum(amount) over (order by transaction_date) as running_total_amount,
        count(*) over (order by date_trunc('day', transaction_date)) as running_order_count,
        
        -- Include the generated_at for incremental logic if needed
        generated_at
        
    from MYDB.LIVE_DATA.stg_orders
    where transaction_date is not null
    qualify row_number() over (partition by order_id order by generated_at desc) = 1
)

-- Final select with basic segmentation
select
    om.*,
    
    -- Simple customer type
    case 
        when user_order_sequence = 1 then 'New Customer'
        when days_since_previous_order is null then 'New Customer'
        when days_since_previous_order <= 30 then 'Repeat Customer (30d)'
        else 'Reactivated Customer'
    end as customer_type,
    
    -- High value flag (using pre-calculated threshold)
    case 
        when order_amount > (select high_value_threshold from order_thresholds) then true
        else false
    end as is_high_value_order,
    
    -- Large order flag
    case 
        when item_count > 10 then true
        else false
    end as is_large_order,
    
    -- Time of day
    case 
        when extract(hour from transaction_date) between 6 and 11 then 'Morning'
        when extract(hour from transaction_date) between 12 and 17 then 'Afternoon'
        when extract(hour from transaction_date) between 18 and 21 then 'Evening'
        else 'Night'
    end as time_of_day,
    
    -- Add a timestamp for when this record was created
    current_timestamp() as dbt_updated_at
    
from order_metrics om
  );
[0m10:12:35.007541 [debug] [Thread-1 (]: On model.live_c360.dim_users: /* {"app": "dbt", "dbt_version": "1.9.6", "profile_name": "live_c360", "target_name": "live", "node_id": "model.live_c360.dim_users"} */
create or replace transient table MYDB.LIVE_DATA.dim_users
    

    
    as (

-- User dimension table for live data pipeline
-- Provides a clean, analytics-ready view of user data

with users_base as (
    select * from MYDB.LIVE_DATA.stg_users
),

user_metrics as (
    select
        user_id,
        count(*) as total_orders,
        sum(amount) as total_spent,
        avg(amount) as avg_order_value,
        min(transaction_date) as first_order_date,
        max(transaction_date) as last_order_date,
        sum(item_count) as total_items_purchased
    from MYDB.LIVE_DATA.stg_orders
    group by user_id
),

user_events as (
    select
        user_id,
        count(*) as total_events,
        count(distinct session_id) as total_sessions,
        count(distinct event_date) as active_days,
        min(event_timestamp) as first_event_date,
        max(event_timestamp) as last_event_date,
        
        -- Event type counts
        sum(case when action = 'view' then 1 else 0 end) as view_events,
        sum(case when action = 'click' then 1 else 0 end) as click_events,
        sum(case when action = 'log' then 1 else 0 end) as login_events,
        sum(case when action = 'purchase' then 1 else 0 end) as purchase_events,
        
        -- Platform usage
        sum(case when device_category = 'Mobile' then 1 else 0 end) as mobile_events,
        sum(case when device_category = 'Web' then 1 else 0 end) as web_events
        
    from MYDB.LIVE_DATA.stg_events
    group by user_id
),

final as (
    select
        -- User identifiers
        u.user_id,
        u.email_hash,
        
        -- Personal information
        u.firstname,
        u.lastname,
        u.address,
        u.acquisition_channel,
        u.country,
        u.gender,
        u.age_group,
        
        -- Dates
        u.creation_date,
        u.last_activity_date,
        
        -- Behavioral flags
        u.is_churned,
        u.activity_segment,
        
        -- Derived user metrics
        u.days_since_creation,
        u.days_since_last_activity,
        
        -- Order metrics
        coalesce(om.total_orders, 0) as total_orders,
        coalesce(om.total_spent, 0) as total_spent,
        coalesce(om.avg_order_value, 0) as avg_order_value,
        om.first_order_date,
        om.last_order_date,
        coalesce(om.total_items_purchased, 0) as total_items_purchased,
        
        -- Event metrics
        coalesce(ue.total_events, 0) as total_events,
        coalesce(ue.total_sessions, 0) as total_sessions,
        coalesce(ue.active_days, 0) as active_days,
        ue.first_event_date,
        ue.last_event_date,
        
        -- Event type metrics
        coalesce(ue.view_events, 0) as view_events,
        coalesce(ue.click_events, 0) as click_events,
        coalesce(ue.login_events, 0) as login_events,
        coalesce(ue.purchase_events, 0) as purchase_events,
        
        -- Platform metrics
        coalesce(ue.mobile_events, 0) as mobile_events,
        coalesce(ue.web_events, 0) as web_events,
        
        -- Calculated metrics
        case 
            when om.total_orders > 0 then round(ue.total_events::float / om.total_orders, 2)
            else 0
        end as events_per_order,
        
        case 
            when ue.total_sessions > 0 then round(ue.total_events::float / ue.total_sessions, 2)
            else 0
        end as events_per_session,
        
        case 
            when om.total_orders > 0 then 
                datediff('day', om.first_order_date, om.last_order_date)::float / om.total_orders
            else 0
        end as avg_days_between_orders,
        
        -- Customer segments
        case 
            when om.total_orders = 0 then 'No Orders'
            when om.total_orders = 1 then 'One-time Buyer'
            when om.total_orders <= 3 then 'Occasional Buyer'
            when om.total_orders <= 10 then 'Regular Buyer'
            else 'Frequent Buyer'
        end as purchase_segment,
        
        case 
            when om.total_spent = 0 then 'No Spend'
            when om.total_spent < 50 then 'Low Value'
            when om.total_spent < 200 then 'Medium Value'
            when om.total_spent < 500 then 'High Value'
            else 'Premium Value'
        end as value_segment,
        
        case 
            when ue.mobile_events > ue.web_events then 'Mobile Preferred'
            when ue.web_events > ue.mobile_events then 'Web Preferred'
            when ue.mobile_events = ue.web_events and ue.mobile_events > 0 then 'Multi-Platform'
            else 'Unknown'
        end as platform_preference,
        
        -- Data quality flags
        u.has_future_creation_date,
        u.has_invalid_activity_date,
        
        -- Metadata
        u.batch_id,
        u.generated_at,
        u.processed_at,
        current_timestamp() as mart_created_at
        
    from users_base u
    left join user_metrics om on u.user_id = om.user_id
    left join user_events ue on u.user_id = ue.user_id
)

select * from final
    )
;
[0m10:12:35.009206 [debug] [Thread-4 (]: Opening a new connection, currently in state init
[0m10:12:35.260273 [debug] [Thread-2 (]: Snowflake adapter: Snowflake query id: 01bccd44-3204-802b-0002-4ad600068132
[0m10:12:35.261235 [debug] [Thread-1 (]: Snowflake adapter: Snowflake query id: 01bccd44-3204-800f-0002-4ad6000650f6
[0m10:12:35.262843 [debug] [Thread-2 (]: Snowflake adapter: Snowflake error: 091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
[0m10:12:35.265433 [debug] [Thread-1 (]: Snowflake adapter: Snowflake error: 091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
[0m10:12:35.268506 [debug] [Thread-2 (]: Database Error in model dim_users_scd2 (models/marts/dim_users_scd2.sql)
  091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
[0m10:12:35.269937 [debug] [Thread-1 (]: Database Error in model dim_users (models/marts/dim_users.sql)
  091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
  compiled code at target/run/live_c360/models/marts/dim_users.sql
[0m10:12:35.273055 [debug] [Thread-2 (]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '0d5b25e0-2dcb-40bb-bf39-1acec9f0fbc5', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7092297854b0>]}
[0m10:12:35.273924 [debug] [Thread-1 (]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '0d5b25e0-2dcb-40bb-bf39-1acec9f0fbc5', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x70923013fa60>]}
[0m10:12:35.275725 [error] [Thread-2 (]: 2 of 4 ERROR creating sql incremental model LIVE_DATA.dim_users_scd2 ........... [[31mERROR[0m in 0.53s]
[0m10:12:35.277362 [error] [Thread-1 (]: 1 of 4 ERROR creating sql table model LIVE_DATA.dim_users ...................... [[31mERROR[0m in 0.53s]
[0m10:12:35.279331 [debug] [Thread-2 (]: Finished running node model.live_c360.dim_users_scd2
[0m10:12:35.282263 [debug] [Thread-1 (]: Finished running node model.live_c360.dim_users
[0m10:12:35.283868 [debug] [Thread-7 (]: Marking all children of 'model.live_c360.dim_users_scd2' to be skipped because of status 'error'.  Reason: Database Error in model dim_users_scd2 (models/marts/dim_users_scd2.sql)
  091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped..
[0m10:12:35.286765 [debug] [Thread-7 (]: Marking all children of 'model.live_c360.dim_users' to be skipped because of status 'error'.  Reason: Database Error in model dim_users (models/marts/dim_users.sql)
  091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
  compiled code at target/run/live_c360/models/marts/dim_users.sql.
[0m10:12:35.352930 [debug] [Thread-3 (]: Snowflake adapter: Snowflake query id: 01bccd44-3204-7ffc-0002-4ad60006614e
[0m10:12:35.354293 [debug] [Thread-3 (]: Snowflake adapter: Snowflake error: 091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
[0m10:12:35.356240 [debug] [Thread-3 (]: Database Error in model fact_orders (models/marts/fact_orders.sql)
  091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/marts/fact_orders.sql
[0m10:12:35.357824 [debug] [Thread-3 (]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '0d5b25e0-2dcb-40bb-bf39-1acec9f0fbc5', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7092297b09a0>]}
[0m10:12:35.359422 [error] [Thread-3 (]: 3 of 4 ERROR creating sql table model LIVE_DATA.fact_orders .................... [[31mERROR[0m in 0.61s]
[0m10:12:35.360938 [debug] [Thread-3 (]: Finished running node model.live_c360.fact_orders
[0m10:12:35.362489 [debug] [Thread-7 (]: Marking all children of 'model.live_c360.fact_orders' to be skipped because of status 'error'.  Reason: Database Error in model fact_orders (models/marts/fact_orders.sql)
  091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/marts/fact_orders.sql.
[0m10:12:35.622556 [debug] [Thread-4 (]: Snowflake adapter: Snowflake query id: 01bccd44-3204-7f69-0002-4ad60005d206
[0m10:12:35.623916 [debug] [Thread-4 (]: Snowflake adapter: Snowflake error: 091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
[0m10:12:35.625968 [debug] [Thread-4 (]: Database Error in model mv_fact_orders (models/marts/mv_fact_orders.sql)
  091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/marts/mv_fact_orders.sql
[0m10:12:35.627377 [debug] [Thread-4 (]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '0d5b25e0-2dcb-40bb-bf39-1acec9f0fbc5', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x709229794eb0>]}
[0m10:12:35.628833 [error] [Thread-4 (]: 4 of 4 ERROR creating sql view model LIVE_DATA_ANALYTICS.mv_fact_orders ........ [[31mERROR[0m in 0.88s]
[0m10:12:35.631896 [debug] [Thread-4 (]: Finished running node model.live_c360.mv_fact_orders
[0m10:12:35.633666 [debug] [Thread-7 (]: Marking all children of 'model.live_c360.mv_fact_orders' to be skipped because of status 'error'.  Reason: Database Error in model mv_fact_orders (models/marts/mv_fact_orders.sql)
  091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/marts/mv_fact_orders.sql.
[0m10:12:35.637034 [debug] [MainThread]: Connection 'master' was properly closed.
[0m10:12:35.638183 [debug] [MainThread]: Connection 'model.live_c360.dim_users_scd2' was left open.
[0m10:12:35.639353 [debug] [MainThread]: On model.live_c360.dim_users_scd2: Close
[0m10:12:35.712687 [debug] [MainThread]: Connection 'model.live_c360.fact_orders' was left open.
[0m10:12:35.715723 [debug] [MainThread]: On model.live_c360.fact_orders: Close
[0m10:12:35.802260 [debug] [MainThread]: Connection 'model.live_c360.dim_users' was left open.
[0m10:12:35.804244 [debug] [MainThread]: On model.live_c360.dim_users: Close
[0m10:12:35.870748 [debug] [MainThread]: Connection 'model.live_c360.mv_fact_orders' was left open.
[0m10:12:35.872572 [debug] [MainThread]: On model.live_c360.mv_fact_orders: Close
[0m10:12:35.983597 [info ] [MainThread]: 
[0m10:12:35.985494 [info ] [MainThread]: Finished running 1 incremental model, 2 table models, 1 view model in 0 hours 0 minutes and 2.98 seconds (2.98s).
[0m10:12:35.987918 [debug] [MainThread]: Command end result
[0m10:12:36.159312 [debug] [MainThread]: Wrote artifact WritableManifest to /opt/airflow/workspace/dbt_live/target/manifest.json
[0m10:12:36.172479 [debug] [MainThread]: Wrote artifact SemanticManifest to /opt/airflow/workspace/dbt_live/target/semantic_manifest.json
[0m10:12:36.190109 [debug] [MainThread]: Wrote artifact RunExecutionResult to /opt/airflow/workspace/dbt_live/target/run_results.json
[0m10:12:36.191529 [info ] [MainThread]: 
[0m10:12:36.192941 [info ] [MainThread]: [31mCompleted with 4 errors, 0 partial successes, and 0 warnings:[0m
[0m10:12:36.194190 [info ] [MainThread]: 
[0m10:12:36.195514 [error] [MainThread]:   Database Error in model dim_users_scd2 (models/marts/dim_users_scd2.sql)
  091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
[0m10:12:36.198341 [info ] [MainThread]: 
[0m10:12:36.199737 [error] [MainThread]:   Database Error in model dim_users (models/marts/dim_users.sql)
  091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
  compiled code at target/run/live_c360/models/marts/dim_users.sql
[0m10:12:36.201026 [info ] [MainThread]: 
[0m10:12:36.202325 [error] [MainThread]:   Database Error in model fact_orders (models/marts/fact_orders.sql)
  091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/marts/fact_orders.sql
[0m10:12:36.203487 [info ] [MainThread]: 
[0m10:12:36.204712 [error] [MainThread]:   Database Error in model mv_fact_orders (models/marts/mv_fact_orders.sql)
  091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/marts/mv_fact_orders.sql
[0m10:12:36.206046 [info ] [MainThread]: 
[0m10:12:36.207252 [info ] [MainThread]: Done. PASS=0 WARN=0 ERROR=4 SKIP=0 TOTAL=4
[0m10:12:36.209112 [debug] [MainThread]: Resource report: {"command_name": "run", "command_success": false, "command_wall_clock_time": 6.207173, "process_in_blocks": "0", "process_kernel_time": 0.629279, "process_mem_max_rss": "206384", "process_out_blocks": "16", "process_user_time": 4.116705}
[0m10:12:36.210523 [debug] [MainThread]: Command `dbt run` failed at 10:12:36.210394 after 6.21 seconds
[0m10:12:36.211773 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x709249b16980>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x70924b04f3d0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7092297e4520>]}
[0m10:12:36.214623 [debug] [MainThread]: Flushing usage events
[0m10:12:37.189649 [debug] [MainThread]: An error was encountered while trying to flush usage events
[0m20:21:54.147536 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000002514DB2F050>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000002514DB2F110>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000002514DB2F010>]}
[0m20:21:54.298957 [debug] [MainThread]: An error was encountered while trying to send an event


============================== 20:21:54.298957 | 42844381-28c6-47a4-ac6b-597e95d8cc13 ==============================
[0m20:21:54.298957 [info ] [MainThread]: Running with dbt=1.9.6
[0m20:21:54.298957 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'write_json': 'True', 'log_cache_events': 'False', 'partial_parse': 'True', 'cache_selected_only': 'False', 'profiles_dir': 'G:\\github\\S3 dbt-snowflake c360\\experiment2\\dbt_live', 'version_check': 'True', 'warn_error': 'None', 'log_path': 'G:\\github\\S3 dbt-snowflake c360\\experiment2\\dbt_live\\logs', 'debug': 'False', 'fail_fast': 'False', 'use_colors': 'True', 'use_experimental_parser': 'False', 'empty': 'False', 'quiet': 'False', 'no_print': 'None', 'log_format': 'default', 'static_parser': 'True', 'invocation_command': 'dbt run', 'introspect': 'True', 'target_path': 'None', 'warn_error_options': 'WarnErrorOptions(include=[], exclude=[])', 'send_anonymous_usage_stats': 'True'}
[0m20:21:55.916113 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'project_id', 'label': '42844381-28c6-47a4-ac6b-597e95d8cc13', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000025150EEFB50>]}
[0m20:21:55.916113 [debug] [MainThread]: An error was encountered while trying to send an event
[0m20:21:55.996061 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'adapter_info', 'label': '42844381-28c6-47a4-ac6b-597e95d8cc13', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000025150DEEFD0>]}
[0m20:21:55.996061 [debug] [MainThread]: An error was encountered while trying to send an event
[0m20:21:55.996061 [info ] [MainThread]: Registered adapter: snowflake=1.9.4
[0m20:21:56.618840 [debug] [MainThread]: checksum: dd6dc1e5178459e3de3bf2eeb7c86bed4be2266c311fce8c15d86eb4ff94e7ad, vars: {}, profile: , target: , version: 1.9.6
[0m20:21:57.175353 [debug] [MainThread]: Partial parsing enabled: 208 files deleted, 208 files added, 0 files changed.
[0m20:21:57.175353 [debug] [MainThread]: Partial parsing: added file: dbt://macros\relations\create.sql
[0m20:21:57.175353 [debug] [MainThread]: Partial parsing: added file: dbt://macros\relations\rename.sql
[0m20:21:57.175353 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros\generic_tests\not_null_proportion.sql
[0m20:21:57.175353 [debug] [MainThread]: Partial parsing: added file: dbt://macros\utils\position.sql
[0m20:21:57.175353 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros\materializations\dynamic_table.sql
[0m20:21:57.186953 [debug] [MainThread]: Partial parsing: added file: dbt://macros\utils\equals.sql
[0m20:21:57.187461 [debug] [MainThread]: Partial parsing: added file: dbt://macros\materializations\tests\where_subquery.sql
[0m20:21:57.187461 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros\adapters.sql
[0m20:21:57.187461 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros\sql\groupby.sql
[0m20:21:57.187461 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros\relations\drop.sql
[0m20:21:57.187461 [debug] [MainThread]: Partial parsing: added file: dbt://macros\utils\data_types.sql
[0m20:21:57.187461 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros\generic_tests\fewer_rows_than.sql
[0m20:21:57.187461 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros\relations\create.sql
[0m20:21:57.187461 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros\relations\view\rename.sql
[0m20:21:57.187461 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros\sql\get_tables_by_prefix_sql.sql
[0m20:21:57.187461 [debug] [MainThread]: Partial parsing: added file: dbt://macros\relations\drop.sql
[0m20:21:57.187461 [debug] [MainThread]: Partial parsing: added file: dbt://macros\adapters\validate_sql.sql
[0m20:21:57.187461 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros\jinja_helpers\slugify.sql
[0m20:21:57.187461 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros\jinja_helpers\pretty_time.sql
[0m20:21:57.187461 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros\generic_tests\relationships_where.sql
[0m20:21:57.197500 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros\web\get_url_host.sql
[0m20:21:57.197500 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros\utils\optional.sql
[0m20:21:57.197500 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros\utils\right.sql
[0m20:21:57.197500 [debug] [MainThread]: Partial parsing: added file: dbt://macros\utils\array_construct.sql
[0m20:21:57.197500 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros\apply_grants.sql
[0m20:21:57.197500 [debug] [MainThread]: Partial parsing: added file: dbt://macros\adapters\metadata.sql
[0m20:21:57.197500 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros\relations\view\replace.sql
[0m20:21:57.197500 [debug] [MainThread]: Partial parsing: added file: dbt://macros\materializations\models\table.sql
[0m20:21:57.197500 [debug] [MainThread]: Partial parsing: added file: dbt://macros\relations\table\replace.sql
[0m20:21:57.197500 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros\relations\table\drop.sql
[0m20:21:57.197500 [debug] [MainThread]: Partial parsing: added file: dbt://macros\materializations\models\clone\create_or_replace_clone.sql
[0m20:21:57.197500 [debug] [MainThread]: Partial parsing: added file: dbt://macros\utils\length.sql
[0m20:21:57.197500 [debug] [MainThread]: Partial parsing: added file: live_c360://models\monitoring\pipeline_runtime_health.sql
[0m20:21:57.207499 [debug] [MainThread]: Partial parsing: added file: dbt://macros\utils\concat.sql
[0m20:21:57.207499 [debug] [MainThread]: Partial parsing: added file: dbt://macros\utils\bool_or.sql
[0m20:21:57.207499 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros\generic_tests\not_empty_string.sql
[0m20:21:57.207499 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros\generic_tests\recency.sql
[0m20:21:57.207499 [debug] [MainThread]: Partial parsing: added file: dbt://macros\utils\except.sql
[0m20:21:57.207499 [debug] [MainThread]: Partial parsing: added file: dbt://macros\get_custom_name\get_custom_database.sql
[0m20:21:57.207499 [debug] [MainThread]: Partial parsing: added file: dbt://macros\get_custom_name\get_custom_schema.sql
[0m20:21:57.207499 [debug] [MainThread]: Partial parsing: added file: dbt://macros\materializations\models\incremental\column_helpers.sql
[0m20:21:57.207499 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros\sql\safe_add.sql
[0m20:21:57.207499 [debug] [MainThread]: Partial parsing: added file: dbt://macros\utils\date_spine.sql
[0m20:21:57.207499 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros\generic_tests\at_least_one.sql
[0m20:21:57.207499 [debug] [MainThread]: Partial parsing: added file: dbt://macros\utils\cast_bool_to_text.sql
[0m20:21:57.207499 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros\generic_tests\not_accepted_values.sql
[0m20:21:57.207499 [debug] [MainThread]: Partial parsing: added file: dbt://macros\relations\materialized_view\alter.sql
[0m20:21:57.207499 [debug] [MainThread]: Partial parsing: added file: live_c360://models\marts\dim_users.sql
[0m20:21:57.207499 [debug] [MainThread]: Partial parsing: added file: dbt://macros\relations\table\rename.sql
[0m20:21:57.217469 [debug] [MainThread]: Partial parsing: added file: dbt://macros\etc\statement.sql
[0m20:21:57.217469 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros\relations\rename.sql
[0m20:21:57.217469 [debug] [MainThread]: Partial parsing: added file: dbt://macros\relations\create_backup.sql
[0m20:21:57.217469 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros\web\get_url_path.sql
[0m20:21:57.217469 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros\sql\union.sql
[0m20:21:57.217469 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros\jinja_helpers\_is_relation.sql
[0m20:21:57.217469 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros\generic_tests\equal_rowcount.sql
[0m20:21:57.217469 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros\sql\nullcheck_table.sql
[0m20:21:57.217469 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros\generic_tests\equality.sql
[0m20:21:57.217469 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros\materializations\incremental.sql
[0m20:21:57.217469 [debug] [MainThread]: Partial parsing: added file: dbt://macros\relations\view\drop.sql
[0m20:21:57.225505 [debug] [MainThread]: Partial parsing: added file: live_c360://models\staging\stg_events.sql
[0m20:21:57.225505 [debug] [MainThread]: Partial parsing: added file: dbt://macros\relations\materialized_view\create.sql
[0m20:21:57.225505 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros\sql\generate_surrogate_key.sql
[0m20:21:57.225505 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros\relations\replace.sql
[0m20:21:57.225505 [debug] [MainThread]: Partial parsing: added file: live_c360://models\staging\stg_users.sql
[0m20:21:57.225505 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros\relations\create_backup.sql
[0m20:21:57.225505 [debug] [MainThread]: Partial parsing: added file: dbt://macros\utils\dateadd.sql
[0m20:21:57.225505 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros\generic_tests\mutually_exclusive_ranges.sql
[0m20:21:57.225505 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros\relations\dynamic_table\drop.sql
[0m20:21:57.225505 [debug] [MainThread]: Partial parsing: added file: dbt://macros\materializations\hooks.sql
[0m20:21:57.225505 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros\utils\array_construct.sql
[0m20:21:57.225505 [debug] [MainThread]: Partial parsing: added file: dbt://macros\relations\view\rename.sql
[0m20:21:57.225505 [debug] [MainThread]: Partial parsing: added file: dbt://macros\adapters\columns.sql
[0m20:21:57.225505 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros\relations\dynamic_table\replace.sql
[0m20:21:57.235647 [debug] [MainThread]: Partial parsing: added file: dbt://macros\utils\datediff.sql
[0m20:21:57.236160 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros\sql\get_relations_by_pattern.sql
[0m20:21:57.236160 [debug] [MainThread]: Partial parsing: added file: dbt://macros\etc\datetime.sql
[0m20:21:57.236160 [debug] [MainThread]: Partial parsing: added file: dbt://macros\materializations\models\incremental\on_schema_change.sql
[0m20:21:57.236160 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros\materializations\snapshot.sql
[0m20:21:57.236160 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros\utils\safe_cast.sql
[0m20:21:57.236160 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros\sql\get_query_results_as_dict.sql
[0m20:21:57.236160 [debug] [MainThread]: Partial parsing: added file: dbt://macros\utils\array_concat.sql
[0m20:21:57.236160 [debug] [MainThread]: Partial parsing: added file: live_c360://models\sources.yml
[0m20:21:57.236160 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros\relations\table\rename.sql
[0m20:21:57.236160 [debug] [MainThread]: Partial parsing: added file: dbt://macros\utils\generate_series.sql
[0m20:21:57.236160 [debug] [MainThread]: Partial parsing: added file: live_c360://models\staging\stg_orders.sql
[0m20:21:57.236160 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros\relations\dynamic_table\refresh.sql
[0m20:21:57.236160 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros\relations\table\replace.sql
[0m20:21:57.236160 [debug] [MainThread]: Partial parsing: added file: dbt://macros\generic_test_sql\not_null.sql
[0m20:21:57.236160 [debug] [MainThread]: Partial parsing: added file: live_c360://models\marts\dim_users_scd2.sql
[0m20:21:57.246162 [debug] [MainThread]: Partial parsing: added file: live_c360://models\monitoring\dbt_test_health.sql
[0m20:21:57.246162 [debug] [MainThread]: Partial parsing: added file: live_c360://models\marts\mv_fact_orders.sql
[0m20:21:57.247249 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros\sql\pivot.sql
[0m20:21:57.247249 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros\materializations\incremental\merge.sql
[0m20:21:57.247249 [debug] [MainThread]: Partial parsing: added file: dbt://macros\relations\replace.sql
[0m20:21:57.247249 [debug] [MainThread]: Partial parsing: added file: dbt://macros\get_custom_name\get_custom_alias.sql
[0m20:21:57.247249 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros\utils\timestamps.sql
[0m20:21:57.247249 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros\generic_tests\expression_is_true.sql
[0m20:21:57.247249 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros\sql\get_tables_by_pattern_sql.sql
[0m20:21:57.247249 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros\metadata.sql
[0m20:21:57.247249 [debug] [MainThread]: Partial parsing: added file: dbt://macros\adapters\relation.sql
[0m20:21:57.247249 [debug] [MainThread]: Partial parsing: added file: dbt://macros\utils\split_part.sql
[0m20:21:57.247249 [debug] [MainThread]: Partial parsing: added file: dbt://macros\materializations\tests\unit.sql
[0m20:21:57.247249 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros\sql\safe_divide.sql
[0m20:21:57.247249 [debug] [MainThread]: Partial parsing: added file: dbt://macros\utils\cast.sql
[0m20:21:57.247249 [debug] [MainThread]: Partial parsing: added file: dbt://macros\materializations\configs.sql
[0m20:21:57.247249 [debug] [MainThread]: Partial parsing: added file: dbt://macros\utils\hash.sql
[0m20:21:57.257958 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros\sql\haversine_distance.sql
[0m20:21:57.257958 [debug] [MainThread]: Partial parsing: added file: dbt://macros\materializations\models\view.sql
[0m20:21:57.257958 [debug] [MainThread]: Partial parsing: added file: dbt://macros\generic_test_sql\relationships.sql
[0m20:21:57.257958 [debug] [MainThread]: Partial parsing: added file: dbt://macros\relations\table\drop.sql
[0m20:21:57.257958 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros\jinja_helpers\_is_ephemeral.sql
[0m20:21:57.257958 [debug] [MainThread]: Partial parsing: added file: dbt://tests\generic\builtin.sql
[0m20:21:57.257958 [debug] [MainThread]: Partial parsing: added file: dbt://macros\adapters\show.sql
[0m20:21:57.257958 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros\jinja_helpers\pretty_log_format.sql
[0m20:21:57.257958 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros\jinja_helpers\log_info.sql
[0m20:21:57.257958 [debug] [MainThread]: Partial parsing: added file: dbt://macros\materializations\tests\helpers.sql
[0m20:21:57.257958 [debug] [MainThread]: Partial parsing: added file: dbt://macros\generic_test_sql\unique.sql
[0m20:21:57.257958 [debug] [MainThread]: Partial parsing: added file: dbt://macros\utils\escape_single_quotes.sql
[0m20:21:57.257958 [debug] [MainThread]: Partial parsing: added file: dbt://macros\materializations\seeds\seed.sql
[0m20:21:57.257958 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros\materializations\clone.sql
[0m20:21:57.267967 [debug] [MainThread]: Partial parsing: added file: dbt://macros\adapters\indexes.sql
[0m20:21:57.267967 [debug] [MainThread]: Partial parsing: added file: dbt://macros\materializations\models\incremental\incremental.sql
[0m20:21:57.267967 [debug] [MainThread]: Partial parsing: added file: dbt://macros\relations\drop_backup.sql
[0m20:21:57.267967 [debug] [MainThread]: Partial parsing: added file: dbt://macros\materializations\models\incremental\strategies.sql
[0m20:21:57.267967 [debug] [MainThread]: Partial parsing: added file: dbt://macros\utils\listagg.sql
[0m20:21:57.267967 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros\generic_tests\accepted_range.sql
[0m20:21:57.267967 [debug] [MainThread]: Partial parsing: added file: dbt://macros\relations\view\create.sql
[0m20:21:57.267967 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros\sql\width_bucket.sql
[0m20:21:57.267967 [debug] [MainThread]: Partial parsing: added file: dbt://macros\utils\intersect.sql
[0m20:21:57.267967 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros\metadata\list_relations_without_caching.sql
[0m20:21:57.267967 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros\sql\nullcheck.sql
[0m20:21:57.267967 [debug] [MainThread]: Partial parsing: added file: dbt://macros\utils\date_trunc.sql
[0m20:21:57.267967 [debug] [MainThread]: Partial parsing: added file: dbt://macros\adapters\apply_grants.sql
[0m20:21:57.267967 [debug] [MainThread]: Partial parsing: added file: dbt://macros\relations\materialized_view\refresh.sql
[0m20:21:57.267967 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros\sql\get_single_value.sql
[0m20:21:57.278000 [debug] [MainThread]: Partial parsing: added file: live_c360://models\monitoring\query_history_health.sql
[0m20:21:57.278000 [debug] [MainThread]: Partial parsing: added file: dbt://macros\materializations\models\clone\can_clone_table.sql
[0m20:21:57.278000 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros\generic_tests\not_constant.sql
[0m20:21:57.278000 [debug] [MainThread]: Partial parsing: added file: dbt://macros\adapters\timestamps.sql
[0m20:21:57.278000 [debug] [MainThread]: Partial parsing: added file: dbt://macros\utils\array_append.sql
[0m20:21:57.278000 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros\sql\generate_series.sql
[0m20:21:57.278000 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros\sql\get_filtered_columns_in_relation.sql
[0m20:21:57.278000 [debug] [MainThread]: Partial parsing: added file: dbt://macros\adapters\freshness.sql
[0m20:21:57.278000 [debug] [MainThread]: Partial parsing: added file: dbt://macros\materializations\snapshots\helpers.sql
[0m20:21:57.278000 [debug] [MainThread]: Partial parsing: added file: dbt://macros\materializations\snapshots\snapshot.sql
[0m20:21:57.278000 [debug] [MainThread]: Partial parsing: added file: dbt://macros\relations\column\columns_spec_ddl.sql
[0m20:21:57.278000 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros\relations\dynamic_table\alter.sql
[0m20:21:57.278000 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros\sql\star.sql
[0m20:21:57.278000 [debug] [MainThread]: Partial parsing: added file: live_c360://models\monitoring\etl_health_dashboard.sql
[0m20:21:57.288013 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros\sql\unpivot.sql
[0m20:21:57.288013 [debug] [MainThread]: Partial parsing: added file: dbt://macros\relations\materialized_view\rename.sql
[0m20:21:57.288013 [debug] [MainThread]: Partial parsing: added file: dbt://macros\utils\last_day.sql
[0m20:21:57.288013 [debug] [MainThread]: Partial parsing: added file: dbt://macros\materializations\snapshots\strategies.sql
[0m20:21:57.288013 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros\relations\view\drop.sql
[0m20:21:57.288013 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros\sql\deduplicate.sql
[0m20:21:57.288013 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros\sql\get_relations_by_prefix.sql
[0m20:21:57.288013 [debug] [MainThread]: Partial parsing: added file: dbt://macros\materializations\models\incremental\is_incremental.sql
[0m20:21:57.288013 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros\catalog.sql
[0m20:21:57.288013 [debug] [MainThread]: Partial parsing: added file: dbt://macros\materializations\models\materialized_view.sql
[0m20:21:57.288013 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros\generic_tests\unique_combination_of_columns.sql
[0m20:21:57.288013 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros\materializations\test.sql
[0m20:21:57.288013 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros\utils\escape_single_quotes.sql
[0m20:21:57.288013 [debug] [MainThread]: Partial parsing: added file: dbt://macros\materializations\tests\test.sql
[0m20:21:57.288013 [debug] [MainThread]: Partial parsing: added file: dbt://macros\adapters\schema.sql
[0m20:21:57.297970 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros\materializations\incremental\insert_overwrite.sql
[0m20:21:57.297970 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros\generic_tests\cardinality_equality.sql
[0m20:21:57.297970 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros\utils\bool_or.sql
[0m20:21:57.297970 [debug] [MainThread]: Partial parsing: added file: dbt://macros\utils\safe_cast.sql
[0m20:21:57.297970 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros\relations\dynamic_table\describe.sql
[0m20:21:57.297970 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros\sql\surrogate_key.sql
[0m20:21:57.297970 [debug] [MainThread]: Partial parsing: added file: dbt://macros\python_model\python.sql
[0m20:21:57.297970 [debug] [MainThread]: Partial parsing: added file: dbt://macros\relations\schema.sql
[0m20:21:57.297970 [debug] [MainThread]: Partial parsing: added file: dbt://macros\adapters\persist_docs.sql
[0m20:21:57.297970 [debug] [MainThread]: Partial parsing: added file: dbt://macros\relations\view\replace.sql
[0m20:21:57.297970 [debug] [MainThread]: Partial parsing: added file: live_c360://models\monitoring\data_quality_health.sql
[0m20:21:57.297970 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros\materializations\table.sql
[0m20:21:57.308289 [debug] [MainThread]: Partial parsing: added file: live_c360://models\marts\fact_orders.sql
[0m20:21:57.309398 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros\relations\view\create.sql
[0m20:21:57.309929 [debug] [MainThread]: Partial parsing: added file: dbt://macros\materializations\seeds\helpers.sql
[0m20:21:57.311018 [debug] [MainThread]: Partial parsing: added file: dbt://macros\utils\replace.sql
[0m20:21:57.312086 [debug] [MainThread]: Partial parsing: added file: dbt://macros\generic_test_sql\accepted_values.sql
[0m20:21:57.312605 [debug] [MainThread]: Partial parsing: added file: dbt://macros\materializations\models\incremental\merge.sql
[0m20:21:57.313719 [debug] [MainThread]: Partial parsing: added file: dbt://macros\materializations\snapshots\snapshot_merge.sql
[0m20:21:57.314263 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros\materializations\seed.sql
[0m20:21:57.315453 [debug] [MainThread]: Partial parsing: added file: dbt://macros\materializations\models\clone\clone.sql
[0m20:21:57.315977 [debug] [MainThread]: Partial parsing: added file: dbt://macros\utils\right.sql
[0m20:21:57.316745 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros\sql\date_spine.sql
[0m20:21:57.318701 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros\sql\get_table_types_sql.sql
[0m20:21:57.319239 [debug] [MainThread]: Partial parsing: added file: dbt://macros\relations\materialized_view\drop.sql
[0m20:21:57.319239 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros\web\get_url_parameter.sql
[0m20:21:57.319239 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros\materializations\view.sql
[0m20:21:57.319239 [debug] [MainThread]: Partial parsing: added file: dbt://macros\unit_test_sql\get_fixture_sql.sql
[0m20:21:57.319239 [debug] [MainThread]: Partial parsing: added file: dbt://macros\utils\literal.sql
[0m20:21:57.319239 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros\generic_tests\sequential_values.sql
[0m20:21:57.319239 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros\sql\get_column_values.sql
[0m20:21:57.319239 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros\relations\dynamic_table\create.sql
[0m20:21:57.319239 [debug] [MainThread]: Partial parsing: added file: dbt://docs\overview.md
[0m20:21:57.319239 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros\relations\table\create.sql
[0m20:21:57.319239 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros\relations\rename_intermediate.sql
[0m20:21:57.319239 [debug] [MainThread]: Partial parsing: added file: dbt://macros\relations\create_intermediate.sql
[0m20:21:57.319239 [debug] [MainThread]: Partial parsing: added file: dbt_snowflake://macros\utils\cast.sql
[0m20:21:57.319239 [debug] [MainThread]: Partial parsing: added file: dbt://macros\relations\table\create.sql
[0m20:21:57.319239 [debug] [MainThread]: Partial parsing: added file: dbt://macros\relations\rename_intermediate.sql
[0m20:21:57.319239 [debug] [MainThread]: Partial parsing: added file: dbt://macros\relations\materialized_view\replace.sql
[0m20:21:57.329280 [debug] [MainThread]: Partial parsing: added file: dbt://macros\utils\any_value.sql
[0m20:21:57.329280 [debug] [MainThread]: Partial parsing: added file: dbt_utils://macros\sql\safe_subtract.sql
[0m20:21:57.329280 [debug] [MainThread]: Partial parsing: added file: dbt://macros\utils\date.sql
[0m20:21:57.329280 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/utils/split_part.sql
[0m20:21:57.329280 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros/relations/view/replace.sql
[0m20:21:57.329280 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/utils/right.sql
[0m20:21:57.329280 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros/generic_tests/not_accepted_values.sql
[0m20:21:57.329280 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros/generic_tests/fewer_rows_than.sql
[0m20:21:57.329280 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros/sql/groupby.sql
[0m20:21:57.329280 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros/materializations/view.sql
[0m20:21:57.329280 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models/staging/stg_events.sql
[0m20:21:57.329280 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/materializations/models/incremental/merge.sql
[0m20:21:57.329280 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros/materializations/snapshot.sql
[0m20:21:57.329280 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/relations/schema.sql
[0m20:21:57.339248 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/etc/datetime.sql
[0m20:21:57.339248 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros/utils/bool_or.sql
[0m20:21:57.339248 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/materializations/hooks.sql
[0m20:21:57.339248 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/utils/length.sql
[0m20:21:57.339248 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros/sql/nullcheck_table.sql
[0m20:21:57.339248 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/relations/materialized_view/alter.sql
[0m20:21:57.339248 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/utils/array_construct.sql
[0m20:21:57.339248 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros/metadata/list_relations_without_caching.sql
[0m20:21:57.339248 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros/materializations/table.sql
[0m20:21:57.339248 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/relations/rename_intermediate.sql
[0m20:21:57.339248 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/adapters/apply_grants.sql
[0m20:21:57.339248 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/materializations/snapshots/snapshot.sql
[0m20:21:57.339248 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros/adapters.sql
[0m20:21:57.339248 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros/generic_tests/unique_combination_of_columns.sql
[0m20:21:57.339248 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models/monitoring/etl_health_dashboard.sql
[0m20:21:57.339248 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros/sql/star.sql
[0m20:21:57.349277 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/utils/equals.sql
[0m20:21:57.349277 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/relations/table/replace.sql
[0m20:21:57.349277 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros/relations/drop.sql
[0m20:21:57.349277 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/utils/replace.sql
[0m20:21:57.349277 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros/relations/dynamic_table/replace.sql
[0m20:21:57.349277 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros/sql/get_single_value.sql
[0m20:21:57.349277 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros/utils/safe_cast.sql
[0m20:21:57.349277 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/utils/array_append.sql
[0m20:21:57.349277 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/relations/drop.sql
[0m20:21:57.349277 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros/sql/generate_surrogate_key.sql
[0m20:21:57.349277 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/materializations/seeds/seed.sql
[0m20:21:57.349277 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/utils/listagg.sql
[0m20:21:57.359248 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/adapters/timestamps.sql
[0m20:21:57.359248 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/materializations/tests/test.sql
[0m20:21:57.359248 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros/jinja_helpers/log_info.sql
[0m20:21:57.359248 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros/relations/view/rename.sql
[0m20:21:57.359248 [debug] [MainThread]: Partial parsing: deleted file: dbt://docs/overview.md
[0m20:21:57.359248 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/unit_test_sql/get_fixture_sql.sql
[0m20:21:57.359248 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros/materializations/incremental.sql
[0m20:21:57.359248 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros/generic_tests/expression_is_true.sql
[0m20:21:57.359248 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros/jinja_helpers/pretty_log_format.sql
[0m20:21:57.359248 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/utils/dateadd.sql
[0m20:21:57.359248 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models/monitoring/query_history_health.sql
[0m20:21:57.359248 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros/relations/view/create.sql
[0m20:21:57.359248 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros/materializations/incremental/insert_overwrite.sql
[0m20:21:57.359248 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/utils/intersect.sql
[0m20:21:57.359248 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/materializations/models/materialized_view.sql
[0m20:21:57.369277 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/python_model/python.sql
[0m20:21:57.369277 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/adapters/validate_sql.sql
[0m20:21:57.369277 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros/sql/get_tables_by_prefix_sql.sql
[0m20:21:57.369277 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros/sql/generate_series.sql
[0m20:21:57.369277 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/adapters/freshness.sql
[0m20:21:57.369277 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/utils/date.sql
[0m20:21:57.369277 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/utils/array_concat.sql
[0m20:21:57.369277 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/materializations/snapshots/snapshot_merge.sql
[0m20:21:57.369277 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/adapters/metadata.sql
[0m20:21:57.369277 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros/relations/table/drop.sql
[0m20:21:57.369277 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros/apply_grants.sql
[0m20:21:57.369277 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/materializations/models/table.sql
[0m20:21:57.369277 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/generic_test_sql/not_null.sql
[0m20:21:57.369277 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros/jinja_helpers/pretty_time.sql
[0m20:21:57.369277 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros/relations/rename_intermediate.sql
[0m20:21:57.369277 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/get_custom_name/get_custom_alias.sql
[0m20:21:57.379279 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros/sql/safe_subtract.sql
[0m20:21:57.379279 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models/marts/fact_orders.sql
[0m20:21:57.379279 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros/relations/table/create.sql
[0m20:21:57.379279 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/materializations/tests/helpers.sql
[0m20:21:57.379279 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros/jinja_helpers/slugify.sql
[0m20:21:57.379279 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models/marts/dim_users.sql
[0m20:21:57.379279 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros/sql/union.sql
[0m20:21:57.379279 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros/relations/dynamic_table/describe.sql
[0m20:21:57.379279 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/utils/position.sql
[0m20:21:57.379279 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros/sql/unpivot.sql
[0m20:21:57.379279 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/materializations/models/incremental/strategies.sql
[0m20:21:57.379279 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/materializations/models/incremental/on_schema_change.sql
[0m20:21:57.379279 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/relations/drop_backup.sql
[0m20:21:57.379279 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros/generic_tests/relationships_where.sql
[0m20:21:57.389276 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros/jinja_helpers/_is_ephemeral.sql
[0m20:21:57.389276 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros/utils/cast.sql
[0m20:21:57.389276 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/relations/materialized_view/replace.sql
[0m20:21:57.389276 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/utils/escape_single_quotes.sql
[0m20:21:57.389276 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros/relations/dynamic_table/drop.sql
[0m20:21:57.389276 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/relations/create_backup.sql
[0m20:21:57.389276 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models/marts/mv_fact_orders.sql
[0m20:21:57.389276 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros/catalog.sql
[0m20:21:57.389276 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models/monitoring/data_quality_health.sql
[0m20:21:57.389276 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/relations/replace.sql
[0m20:21:57.389276 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/utils/any_value.sql
[0m20:21:57.389276 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros/sql/safe_add.sql
[0m20:21:57.389276 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros/relations/replace.sql
[0m20:21:57.389276 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/utils/data_types.sql
[0m20:21:57.389276 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros/relations/create.sql
[0m20:21:57.389276 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/materializations/models/clone/create_or_replace_clone.sql
[0m20:21:57.399548 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/materializations/tests/unit.sql
[0m20:21:57.402850 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/materializations/models/view.sql
[0m20:21:57.402850 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/utils/except.sql
[0m20:21:57.403848 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros/relations/table/rename.sql
[0m20:21:57.404855 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/utils/date_spine.sql
[0m20:21:57.404855 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros/sql/get_relations_by_prefix.sql
[0m20:21:57.405873 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros/generic_tests/recency.sql
[0m20:21:57.405873 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/materializations/tests/where_subquery.sql
[0m20:21:57.406878 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/adapters/show.sql
[0m20:21:57.407860 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros/utils/optional.sql
[0m20:21:57.407860 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/adapters/schema.sql
[0m20:21:57.408878 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros/utils/timestamps.sql
[0m20:21:57.408878 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros/relations/dynamic_table/alter.sql
[0m20:21:57.409848 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros/sql/surrogate_key.sql
[0m20:21:57.410859 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/materializations/models/clone/clone.sql
[0m20:21:57.410859 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros/web/get_url_path.sql
[0m20:21:57.411863 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/relations/column/columns_spec_ddl.sql
[0m20:21:57.412877 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros/generic_tests/equality.sql
[0m20:21:57.412877 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros/generic_tests/not_empty_string.sql
[0m20:21:57.413850 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros/materializations/clone.sql
[0m20:21:57.414847 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros/web/get_url_host.sql
[0m20:21:57.415854 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/utils/date_trunc.sql
[0m20:21:57.415854 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/utils/concat.sql
[0m20:21:57.416847 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/generic_test_sql/unique.sql
[0m20:21:57.416847 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros/sql/deduplicate.sql
[0m20:21:57.417778 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros/generic_tests/not_constant.sql
[0m20:21:57.417778 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/relations/create.sql
[0m20:21:57.417778 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/adapters/indexes.sql
[0m20:21:57.417778 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/materializations/models/incremental/is_incremental.sql
[0m20:21:57.417778 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/utils/datediff.sql
[0m20:21:57.417778 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros/generic_tests/cardinality_equality.sql
[0m20:21:57.417778 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/utils/cast_bool_to_text.sql
[0m20:21:57.417778 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros/sql/date_spine.sql
[0m20:21:57.417778 [debug] [MainThread]: Partial parsing: deleted file: dbt://tests/generic/builtin.sql
[0m20:21:57.417778 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros/relations/rename.sql
[0m20:21:57.417778 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros/relations/dynamic_table/refresh.sql
[0m20:21:57.426027 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/generic_test_sql/accepted_values.sql
[0m20:21:57.426533 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/relations/rename.sql
[0m20:21:57.426533 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros/generic_tests/equal_rowcount.sql
[0m20:21:57.426533 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models/staging/stg_orders.sql
[0m20:21:57.426533 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros/materializations/test.sql
[0m20:21:57.426533 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros/generic_tests/sequential_values.sql
[0m20:21:57.426533 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros/sql/width_bucket.sql
[0m20:21:57.426533 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/materializations/snapshots/helpers.sql
[0m20:21:57.426533 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/relations/materialized_view/rename.sql
[0m20:21:57.426533 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/relations/table/rename.sql
[0m20:21:57.426533 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/adapters/persist_docs.sql
[0m20:21:57.426533 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/relations/view/replace.sql
[0m20:21:57.426533 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/materializations/seeds/helpers.sql
[0m20:21:57.426533 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros/sql/get_query_results_as_dict.sql
[0m20:21:57.426533 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros/utils/escape_single_quotes.sql
[0m20:21:57.426533 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/materializations/configs.sql
[0m20:21:57.436587 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros/relations/view/drop.sql
[0m20:21:57.436587 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/materializations/snapshots/strategies.sql
[0m20:21:57.436587 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros/web/get_url_parameter.sql
[0m20:21:57.436587 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models/staging/stg_users.sql
[0m20:21:57.436587 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/materializations/models/incremental/incremental.sql
[0m20:21:57.436587 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros/materializations/seed.sql
[0m20:21:57.436587 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/relations/materialized_view/refresh.sql
[0m20:21:57.436587 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/get_custom_name/get_custom_schema.sql
[0m20:21:57.436587 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/relations/view/drop.sql
[0m20:21:57.436587 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros/sql/pivot.sql
[0m20:21:57.436587 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros/generic_tests/at_least_one.sql
[0m20:21:57.436587 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros/metadata.sql
[0m20:21:57.436587 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/utils/cast.sql
[0m20:21:57.436587 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/relations/materialized_view/create.sql
[0m20:21:57.436587 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/generic_test_sql/relationships.sql
[0m20:21:57.436587 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/relations/table/drop.sql
[0m20:21:57.436587 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/materializations/models/incremental/column_helpers.sql
[0m20:21:57.446565 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros/sql/haversine_distance.sql
[0m20:21:57.446565 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/materializations/models/clone/can_clone_table.sql
[0m20:21:57.446565 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models/marts/dim_users_scd2.sql
[0m20:21:57.446565 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/relations/materialized_view/drop.sql
[0m20:21:57.446565 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models/monitoring/dbt_test_health.sql
[0m20:21:57.446565 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros/jinja_helpers/_is_relation.sql
[0m20:21:57.446565 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/utils/safe_cast.sql
[0m20:21:57.446565 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros/sql/get_filtered_columns_in_relation.sql
[0m20:21:57.446565 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/relations/view/create.sql
[0m20:21:57.446565 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros/generic_tests/not_null_proportion.sql
[0m20:21:57.446565 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/relations/view/rename.sql
[0m20:21:57.446565 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros/sql/get_table_types_sql.sql
[0m20:21:57.446565 [debug] [MainThread]: Partial parsing: deleted file: live_c360://models/monitoring/pipeline_runtime_health.sql
[0m20:21:57.446565 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/utils/last_day.sql
[0m20:21:57.446565 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/relations/create_intermediate.sql
[0m20:21:57.456540 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros/sql/get_tables_by_pattern_sql.sql
[0m20:21:57.456540 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros/sql/safe_divide.sql
[0m20:21:57.456540 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros/relations/table/replace.sql
[0m20:21:57.458348 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros/materializations/dynamic_table.sql
[0m20:21:57.458348 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/utils/bool_or.sql
[0m20:21:57.458348 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/relations/table/create.sql
[0m20:21:57.458348 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros/relations/dynamic_table/create.sql
[0m20:21:57.458348 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/adapters/columns.sql
[0m20:21:57.458348 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros/generic_tests/accepted_range.sql
[0m20:21:57.458348 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/etc/statement.sql
[0m20:21:57.458348 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros/sql/get_column_values.sql
[0m20:21:57.458348 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros/sql/nullcheck.sql
[0m20:21:57.458348 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/utils/generate_series.sql
[0m20:21:57.458348 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros/utils/right.sql
[0m20:21:57.458348 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros/sql/get_relations_by_pattern.sql
[0m20:21:57.458348 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/utils/literal.sql
[0m20:21:57.458348 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros/relations/create_backup.sql
[0m20:21:57.458348 [debug] [MainThread]: Partial parsing: deleted file: dbt_utils://macros/generic_tests/mutually_exclusive_ranges.sql
[0m20:21:57.468360 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/utils/hash.sql
[0m20:21:57.468360 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/adapters/relation.sql
[0m20:21:57.468360 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros/materializations/incremental/merge.sql
[0m20:21:57.468360 [debug] [MainThread]: Partial parsing: deleted file: dbt://macros/get_custom_name/get_custom_database.sql
[0m20:21:57.468360 [debug] [MainThread]: Partial parsing: deleted file: dbt_snowflake://macros/utils/array_construct.sql
[0m20:21:57.478391 [error] [MainThread]: Encountered an error:
'dbt_snowflake://macros\\adapters.sql'
[0m20:21:57.478391 [error] [MainThread]: Traceback (most recent call last):
  File "E:\Users\Administrator\anaconda3\envs\pyspark\Lib\site-packages\dbt\cli\requires.py", line 153, in wrapper
    result, success = func(*args, **kwargs)
                      ^^^^^^^^^^^^^^^^^^^^^
  File "E:\Users\Administrator\anaconda3\envs\pyspark\Lib\site-packages\dbt\cli\requires.py", line 103, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "E:\Users\Administrator\anaconda3\envs\pyspark\Lib\site-packages\dbt\cli\requires.py", line 235, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "E:\Users\Administrator\anaconda3\envs\pyspark\Lib\site-packages\dbt\cli\requires.py", line 264, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "E:\Users\Administrator\anaconda3\envs\pyspark\Lib\site-packages\dbt\cli\requires.py", line 311, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "E:\Users\Administrator\anaconda3\envs\pyspark\Lib\site-packages\dbt\cli\requires.py", line 327, in wrapper
    setup_manifest(ctx, write=write, write_perf_info=write_perf_info)
  File "E:\Users\Administrator\anaconda3\envs\pyspark\Lib\site-packages\dbt\cli\requires.py", line 351, in setup_manifest
    ctx.obj["manifest"] = parse_manifest(
                          ^^^^^^^^^^^^^^^
  File "E:\Users\Administrator\anaconda3\envs\pyspark\Lib\site-packages\dbt\parser\manifest.py", line 2069, in parse_manifest
    manifest = ManifestLoader.get_full_manifest(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Users\Administrator\anaconda3\envs\pyspark\Lib\site-packages\dbt\parser\manifest.py", line 312, in get_full_manifest
    manifest = loader.load()
               ^^^^^^^^^^^^^
  File "E:\Users\Administrator\anaconda3\envs\pyspark\Lib\site-packages\dbt\parser\manifest.py", line 381, in load
    self.load_and_parse_macros(project_parser_files)
  File "E:\Users\Administrator\anaconda3\envs\pyspark\Lib\site-packages\dbt\parser\manifest.py", line 683, in load_and_parse_macros
    block = FileBlock(self.manifest.files[file_id])
                      ~~~~~~~~~~~~~~~~~~~^^^^^^^^^
KeyError: 'dbt_snowflake://macros\\adapters.sql'

[0m20:21:57.478391 [debug] [MainThread]: Command `dbt run` failed at 20:21:57.478391 after 3.44 seconds
[0m20:21:57.478391 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000002514DB8FB50>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x00000251472793D0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000002514DB2FF90>]}
[0m20:21:57.478391 [debug] [MainThread]: An error was encountered while trying to send an event
[0m20:21:57.478391 [debug] [MainThread]: Flushing usage events
