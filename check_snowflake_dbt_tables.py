#!/usr/bin/env python3
"""
Check Snowflake dbt Tables
Shows what dbt models exist in Snowflake and where to find them.
"""

import snowflake.connector
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_dbt_tables():
    """Check what dbt tables exist in Snowflake."""
    
    connection_params = {
        'account': 'SVLFKJI-IX89869',
        'user': 'XINBINZHANG',
        'password': '****************',
        'warehouse': 'COMPUTE_WH',
        'database': 'MYDB',
        'schema': 'PUBLIC',
        'role': 'ACCOUNTADMIN'
    }
    
    try:
        logger.info("🔍 Connecting to Snowflake to check dbt tables...")
        conn = snowflake.connector.connect(**connection_params)
        cursor = conn.cursor()
        
        # Set context
        cursor.execute("USE DATABASE MYDB")
        cursor.execute("USE SCHEMA PUBLIC")
        
        logger.info("✅ Connected to Snowflake successfully!")
        
        # Check what schemas exist
        logger.info("\n📋 Available schemas in MYDB:")
        cursor.execute("SHOW SCHEMAS IN DATABASE MYDB")
        schemas = cursor.fetchall()
        for schema in schemas:
            logger.info(f"   📁 {schema[1]}")  # Schema name is in column 1
        
        # Check tables in PUBLIC schema
        logger.info("\n📊 Tables in MYDB.PUBLIC schema:")
        cursor.execute("SHOW TABLES IN SCHEMA PUBLIC")
        tables = cursor.fetchall()
        
        if tables:
            for table in tables:
                table_name = table[1]  # Table name is in column 1
                table_type = table[3]  # Table type is in column 3
                
                # Get row count
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    count = cursor.fetchone()[0]
                    logger.info(f"   📋 {table_name} ({table_type}): {count:,} rows")
                except Exception as e:
                    logger.info(f"   📋 {table_name} ({table_type}): Error getting count - {e}")
        else:
            logger.info("   ❌ No tables found in PUBLIC schema")
        
        # Check for dbt-specific tables (they usually start with dbt_c360)
        logger.info("\n🔧 Looking for dbt models (tables starting with 'dbt_c360' or common dbt patterns):")
        dbt_patterns = ['dbt_c360%', 'stg_%', 'dim_%', 'fact_%', 'mart_%']
        
        found_dbt_tables = False
        for pattern in dbt_patterns:
            cursor.execute(f"SHOW TABLES LIKE '{pattern}' IN SCHEMA PUBLIC")
            dbt_tables = cursor.fetchall()
            
            if dbt_tables:
                found_dbt_tables = True
                logger.info(f"\n   📊 Tables matching '{pattern}':")
                for table in dbt_tables:
                    table_name = table[1]
                    try:
                        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                        count = cursor.fetchone()[0]
                        logger.info(f"      ✅ {table_name}: {count:,} rows")
                    except Exception as e:
                        logger.info(f"      ⚠️ {table_name}: Error - {e}")
        
        if not found_dbt_tables:
            logger.info("   ❌ No dbt models found. You may need to run 'dbt run' first.")
        
        # Check for seed tables (raw data)
        logger.info("\n🌱 Looking for seed tables (raw data):")
        seed_patterns = ['raw_%']
        
        for pattern in seed_patterns:
            cursor.execute(f"SHOW TABLES LIKE '{pattern}' IN SCHEMA PUBLIC")
            seed_tables = cursor.fetchall()
            
            if seed_tables:
                logger.info(f"\n   📊 Seed tables matching '{pattern}':")
                for table in seed_tables:
                    table_name = table[1]
                    try:
                        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                        count = cursor.fetchone()[0]
                        logger.info(f"      ✅ {table_name}: {count:,} rows")
                        
                        # Show sample data
                        cursor.execute(f"SELECT * FROM {table_name} LIMIT 3")
                        sample_data = cursor.fetchall()
                        logger.info(f"         Sample: {len(sample_data)} rows")
                        
                    except Exception as e:
                        logger.info(f"      ⚠️ {table_name}: Error - {e}")
        
        # Check LIVE_DATA schema if it exists
        logger.info("\n❄️ Checking LIVE_DATA schema (for Experiment 2):")
        try:
            cursor.execute("USE SCHEMA LIVE_DATA")
            cursor.execute("SHOW TABLES IN SCHEMA LIVE_DATA")
            live_tables = cursor.fetchall()
            
            if live_tables:
                logger.info("   📊 Tables in LIVE_DATA schema:")
                for table in live_tables:
                    table_name = table[1]
                    table_type = table[3]
                    try:
                        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                        count = cursor.fetchone()[0]
                        logger.info(f"      ✅ {table_name} ({table_type}): {count:,} rows")
                    except Exception as e:
                        logger.info(f"      ⚠️ {table_name} ({table_type}): Error - {e}")
            else:
                logger.info("   ❌ No tables found in LIVE_DATA schema")
                
        except Exception as e:
            logger.info(f"   ⚠️ LIVE_DATA schema not accessible or doesn't exist: {e}")
        
        # Show where to find these in Snowflake UI
        logger.info("\n🎯 WHERE TO FIND DBT MODELS IN SNOWFLAKE UI:")
        logger.info("=" * 60)
        logger.info("1. 📱 Snowflake Web UI: https://app.snowflake.com/")
        logger.info("2. 🏠 Navigate: Data → Databases → MYDB")
        logger.info("3. 📁 Click on: PUBLIC schema (or LIVE_DATA if exists)")
        logger.info("4. 📊 Look for tables with these patterns:")
        logger.info("   • dbt_c360_bronze_* (Bronze layer - raw data cleaning)")
        logger.info("   • dbt_c360_silver_* (Silver layer - business logic)")
        logger.info("   • dbt_c360_gold_* (Gold layer - analytics ready)")
        logger.info("   • stg_* (Staging models)")
        logger.info("   • dim_* (Dimension tables)")
        logger.info("   • fact_* (Fact tables)")
        logger.info("5. 🔍 Click on any table to:")
        logger.info("   • See table structure (columns, data types)")
        logger.info("   • Preview data (first 100 rows)")
        logger.info("   • See table details (row count, size)")
        logger.info("6. 📝 Use Worksheets to query:")
        logger.info("   • SELECT * FROM dbt_c360_gold_churn_features LIMIT 10;")
        logger.info("   • SELECT COUNT(*) FROM dbt_c360_silver_users;")
        
        cursor.close()
        conn.close()
        
        logger.info("\n🎉 Snowflake dbt table check completed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to check Snowflake tables: {e}")
        return False

def main():
    """Main function."""
    check_dbt_tables()

if __name__ == "__main__":
    main()
