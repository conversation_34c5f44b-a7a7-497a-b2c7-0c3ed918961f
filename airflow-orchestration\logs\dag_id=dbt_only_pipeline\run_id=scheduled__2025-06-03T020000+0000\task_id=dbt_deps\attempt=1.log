[2025-06-04T09:43:01.159+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=non-requeueable deps ti=<TaskInstance: dbt_only_pipeline.dbt_deps scheduled__2025-06-03T02:00:00+00:00 [queued]>
[2025-06-04T09:43:01.170+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=requeueable deps ti=<TaskInstance: dbt_only_pipeline.dbt_deps scheduled__2025-06-03T02:00:00+00:00 [queued]>
[2025-06-04T09:43:01.171+0000] {taskinstance.py:2170} INFO - Starting attempt 1 of 3
[2025-06-04T09:43:01.183+0000] {taskinstance.py:2191} INFO - Executing <Task(PythonOperator): dbt_deps> on 2025-06-03 02:00:00+00:00
[2025-06-04T09:43:01.190+0000] {standard_task_runner.py:60} INFO - Started process 4181 to run task
[2025-06-04T09:43:01.194+0000] {standard_task_runner.py:87} INFO - Running: ['***', 'tasks', 'run', 'dbt_only_pipeline', 'dbt_deps', 'scheduled__2025-06-03T02:00:00+00:00', '--job-id', '106', '--raw', '--subdir', 'DAGS_FOLDER/dbt_only_dag.py', '--cfg-path', '/tmp/tmpys_mlg10']
[2025-06-04T09:43:01.195+0000] {standard_task_runner.py:88} INFO - Job 106: Subtask dbt_deps
[2025-06-04T09:43:01.265+0000] {task_command.py:423} INFO - Running <TaskInstance: dbt_only_pipeline.dbt_deps scheduled__2025-06-03T02:00:00+00:00 [running]> on host 8fb7c8dc5097
[2025-06-04T09:43:01.349+0000] {taskinstance.py:2480} INFO - Exporting env vars: AIRFLOW_CTX_DAG_OWNER='data-team' AIRFLOW_CTX_DAG_ID='dbt_only_pipeline' AIRFLOW_CTX_TASK_ID='dbt_deps' AIRFLOW_CTX_EXECUTION_DATE='2025-06-03T02:00:00+00:00' AIRFLOW_CTX_TRY_NUMBER='1' AIRFLOW_CTX_DAG_RUN_ID='scheduled__2025-06-03T02:00:00+00:00'
[2025-06-04T09:43:01.352+0000] {dbt_only_dag.py:140} INFO - 📦 Installing dbt dependencies...
[2025-06-04T09:43:01.353+0000] {dbt_only_dag.py:68} INFO - 🚀 Running dbt deps
[2025-06-04T09:43:01.356+0000] {dbt_only_dag.py:88} INFO - 📋 Executing: dbt deps
[2025-06-04T09:43:10.891+0000] {dbt_only_dag.py:101} INFO - 📄 stdout: [0m09:43:03  Running with dbt=1.9.6
[0m09:43:04  Installing dbt-labs/dbt_utils
[0m09:43:09  Installed from version 1.1.1
[0m09:43:09  Updated version available: 1.3.0
[0m09:43:09  
[0m09:43:09  Updates available for packages: ['dbt-labs/dbt_utils']                 
Update your versions in packages.yml, then run dbt deps

[2025-06-04T09:43:10.892+0000] {dbt_only_dag.py:107} INFO - ✅ dbt deps completed successfully
[2025-06-04T09:43:10.893+0000] {python.py:201} INFO - Done. Returned value was: {'success': True, 'command': 'dbt deps', 'stdout': "\x1b[0m09:43:03  Running with dbt=1.9.6\n\x1b[0m09:43:04  Installing dbt-labs/dbt_utils\n\x1b[0m09:43:09  Installed from version 1.1.1\n\x1b[0m09:43:09  Updated version available: 1.3.0\n\x1b[0m09:43:09  \n\x1b[0m09:43:09  Updates available for packages: ['dbt-labs/dbt_utils']                 \nUpdate your versions in packages.yml, then run dbt deps\n", 'stderr': ''}
[2025-06-04T09:43:10.917+0000] {taskinstance.py:1138} INFO - Marking task as SUCCESS. dag_id=dbt_only_pipeline, task_id=dbt_deps, execution_date=20250603T020000, start_date=20250604T094301, end_date=20250604T094310
[2025-06-04T09:43:10.968+0000] {local_task_job_runner.py:234} INFO - Task exited with return code 0
[2025-06-04T09:43:11.011+0000] {taskinstance.py:3280} INFO - 0 downstream tasks scheduled from follow-on schedule check
