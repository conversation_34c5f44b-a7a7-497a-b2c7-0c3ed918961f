[2025-06-04T05:19:35.489+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=non-requeueable deps ti=<TaskInstance: simple_etl_pipeline.run_dbt_models manual__2025-06-04T05:19:27.959189+00:00 [queued]>
[2025-06-04T05:19:35.509+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=requeueable deps ti=<TaskInstance: simple_etl_pipeline.run_dbt_models manual__2025-06-04T05:19:27.959189+00:00 [queued]>
[2025-06-04T05:19:35.511+0000] {taskinstance.py:2170} INFO - Starting attempt 1 of 2
[2025-06-04T05:19:35.531+0000] {taskinstance.py:2191} INFO - Executing <Task(PythonOperator): run_dbt_models> on 2025-06-04 05:19:27.959189+00:00
[2025-06-04T05:19:35.542+0000] {standard_task_runner.py:60} INFO - Started process 751 to run task
[2025-06-04T05:19:35.556+0000] {standard_task_runner.py:87} INFO - Running: ['***', 'tasks', 'run', 'simple_etl_pipeline', 'run_dbt_models', 'manual__2025-06-04T05:19:27.959189+00:00', '--job-id', '93', '--raw', '--subdir', 'DAGS_FOLDER/simple_etl_dag.py', '--cfg-path', '/tmp/tmpd71mbjwp']
[2025-06-04T05:19:35.559+0000] {standard_task_runner.py:88} INFO - Job 93: Subtask run_dbt_models
[2025-06-04T05:19:35.629+0000] {task_command.py:423} INFO - Running <TaskInstance: simple_etl_pipeline.run_dbt_models manual__2025-06-04T05:19:27.959189+00:00 [running]> on host 8fb7c8dc5097
[2025-06-04T05:19:35.757+0000] {taskinstance.py:2480} INFO - Exporting env vars: AIRFLOW_CTX_DAG_OWNER='data-team' AIRFLOW_CTX_DAG_ID='simple_etl_pipeline' AIRFLOW_CTX_TASK_ID='run_dbt_models' AIRFLOW_CTX_EXECUTION_DATE='2025-06-04T05:19:27.959189+00:00' AIRFLOW_CTX_TRY_NUMBER='1' AIRFLOW_CTX_DAG_RUN_ID='manual__2025-06-04T05:19:27.959189+00:00'
[2025-06-04T05:19:35.761+0000] {simple_etl_dag.py:78} INFO - Running dbt run using Python API
[2025-06-04T05:19:37.849+0000] {simple_etl_dag.py:109} INFO - Executing dbt run
[2025-06-04T05:19:38.010+0000] {logging_mixin.py:188} INFO - 05:19:38  Running with dbt=1.9.6
[2025-06-04T05:19:41.431+0000] {logging_mixin.py:188} INFO - 05:19:41  Registered adapter: snowflake=1.9.4
[2025-06-04T05:19:41.859+0000] {logging_mixin.py:188} INFO - 05:19:41  Encountered an error:
Compilation Error
  dbt found more than one package with the name "dbt_utils" included in this project. Package names must be unique in a project. Please rename one of these packages.
[2025-06-04T05:19:42.793+0000] {simple_etl_dag.py:116} ERROR - dbt run failed
[2025-06-04T05:19:42.794+0000] {taskinstance.py:2698} ERROR - Task failed with exception
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/models/taskinstance.py", line 433, in _execute_task
    result = execute_callable(context=context, **execute_callable_kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/operators/python.py", line 199, in execute
    return_value = self.execute_callable()
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/operators/python.py", line 216, in execute_callable
    return self.python_callable(*self.op_args, **self.op_kwargs)
  File "/opt/airflow/dags/simple_etl_dag.py", line 219, in run_dbt_models
    return run_dbt_command_python('run', **context)
  File "/opt/airflow/dags/simple_etl_dag.py", line 117, in run_dbt_command_python
    raise Exception(f"dbt {command_type} failed")
Exception: dbt run failed
[2025-06-04T05:19:42.806+0000] {taskinstance.py:1138} INFO - Marking task as UP_FOR_RETRY. dag_id=simple_etl_pipeline, task_id=run_dbt_models, execution_date=20250604T051927, start_date=20250604T051935, end_date=20250604T051942
[2025-06-04T05:19:42.819+0000] {standard_task_runner.py:107} ERROR - Failed to execute job 93 for task run_dbt_models (dbt run failed; 751)
[2025-06-04T05:19:42.871+0000] {local_task_job_runner.py:234} INFO - Task exited with return code 1
[2025-06-04T05:19:42.905+0000] {taskinstance.py:3280} INFO - 0 downstream tasks scheduled from follow-on schedule check
