# Troubleshooting Guide: A<PERSON> & Snowflake for Azure Experts

## 🚨 Common Issues and Solutions

### 1. AWS Credential Issues

#### Problem: "Unable to locate credentials"
```bash
❌ Error: NoCredentialsError: Unable to locate credentials
```

#### Azure Equivalent:
```bash
# In Azure, you'd run:
az login
az account set --subscription "subscription-id"
```

#### AWS Solution:
```bash
# Check current credentials
aws sts get-caller-identity

# If not configured, run:
aws configure
# Enter: Access Key ID, Secret Access Key, Region (ap-southeast-2), Output format (json)

# Test access
aws s3 ls
```

#### Where to Check:
- **AWS Console** → **IAM** → **Users** → Your user → **Security credentials**
- **Local file**: `~/.aws/credentials` (Windows: `C:\Users\<USER>\.aws\credentials`)

---

### 2. S3 Access Denied Issues

#### Problem: "Access Denied" when accessing S3
```bash
❌ Error: An error occurred (AccessDenied) when calling the ListObjects operation
```

#### Azure Equivalent:
```bash
# In Azure, you'd check:
az role assignment list --assignee <user-id> --scope <storage-account-scope>
```

#### AWS Solution:
```bash
# Check bucket policy
aws s3api get-bucket-policy --bucket lake-loader-input-************-********-001439

# Check your user permissions
aws iam list-attached-user-policies --user-name YOUR_USERNAME

# Test specific bucket access
aws s3 ls s3://lake-loader-input-************-********-001439/
```

#### Where to Check:
- **AWS Console** → **S3** → Your bucket → **Permissions** → **Bucket policy**
- **AWS Console** → **IAM** → **Users** → Your user → **Permissions**

---

### 3. Snowflake Connection Issues

#### Problem: "Failed to connect to Snowflake"
```bash
❌ Error: 250001: Could not connect to Snowflake backend after 0 attempt(s)
```

#### Azure Equivalent:
```python
# In Azure, you'd check Databricks connection:
spark.conf.get("spark.databricks.service.address")
```

#### Snowflake Solution:
```python
# Test connection parameters
import snowflake.connector

conn = snowflake.connector.connect(
    account='SVLFKJI-IX89869',  # Check this format!
    user='XINBINZHANG',
    password='****************',
    warehouse='COMPUTE_WH',
    database='MYDB',
    schema='LIVE_DATA'
)

# Test query
cursor = conn.cursor()
cursor.execute("SELECT CURRENT_TIMESTAMP()")
print(cursor.fetchone())
```

#### Common Account Format Issues:
```bash
❌ Wrong: IX89869
❌ Wrong: IX89869.ap-southeast-2
❌ Wrong: IX89869.ap-southeast-2.aws
✅ Correct: SVLFKJI-IX89869
```

#### Where to Check:
- **Snowflake Web UI** → **Admin** → **Accounts** → See account identifier
- **URL**: `https://SVLFKJI-IX89869.snowflakecomputing.com/`

---

### 4. External Table Issues

#### Problem: "External table shows no data"
```sql
❌ SELECT COUNT(*) FROM EXT_LIVE_USERS; -- Returns 0
```

#### Azure Equivalent:
```python
# In Databricks, you'd check mount:
dbutils.fs.ls("/mnt/external-data/")
```

#### Snowflake Solution:
```sql
-- Check if stage can see files
LIST @S3_LIVE_USERS_STAGE;

-- Check storage integration
DESC STORAGE INTEGRATION S3_LIVE_INTEGRATION;

-- Manually refresh external table
ALTER EXTERNAL TABLE EXT_LIVE_USERS REFRESH;

-- Check file format
SELECT $1, $2, $3 FROM @S3_LIVE_USERS_STAGE/daily_users_20250125.csv LIMIT 5;
```

#### Common Issues:
1. **IAM Role not trusted by Snowflake**
2. **S3 files in wrong location**
3. **File format mismatch**
4. **Auto-refresh not working**

#### Where to Check:
- **Snowflake** → **Data** → **Databases** → `MYDB` → **Stages**
- **AWS Console** → **S3** → Verify files exist in expected location
- **AWS Console** → **IAM** → **Roles** → Check trust relationship

---

### 5. dbt Connection Issues

#### Problem: "dbt debug fails"
```bash
❌ Error: Database Error in test connection (database='MYDB', schema='LIVE_DATA')
```

#### Azure Equivalent:
```python
# In Databricks, you'd check cluster connection:
spark.sql("SHOW DATABASES").show()
```

#### dbt Solution:
```bash
cd dbt_live

# Check profiles.yml
cat profiles.yml

# Test connection step by step
dbt debug --log-level debug

# Test specific model
dbt run --models stg_users --log-level debug
```

#### Common profiles.yml Issues:
```yaml
# ❌ Wrong schema reference
schema: "{{ var('schema', 'PUBLIC') }}"

# ✅ Correct schema reference  
schema: LIVE_DATA

# ❌ Wrong account format
account: IX89869

# ✅ Correct account format
account: SVLFKJI-IX89869
```

#### Where to Check:
- **File**: `dbt_live/profiles.yml`
- **Snowflake** → **Admin** → **Warehouses** → Check if `COMPUTE_WH` is running
- **Snowflake** → **Data** → **Databases** → Check if `MYDB.LIVE_DATA` exists

---

### 6. Performance Issues

#### Problem: "Pipeline runs very slowly"
```bash
⏰ Pipeline taking > 30 minutes to complete
```

#### Azure Equivalent:
```python
# In Databricks, you'd scale cluster:
cluster_config["num_workers"] = 8
```

#### AWS/Snowflake Solution:
```sql
-- Scale up Snowflake warehouse
ALTER WAREHOUSE COMPUTE_WH SET WAREHOUSE_SIZE = 'LARGE';

-- Check warehouse usage
SELECT * FROM SNOWFLAKE.ACCOUNT_USAGE.WAREHOUSE_METERING_HISTORY 
WHERE WAREHOUSE_NAME = 'COMPUTE_WH'
ORDER BY START_TIME DESC LIMIT 10;
```

```bash
# Optimize S3 uploads (parallel)
# In daily_data_generator.py, use ThreadPoolExecutor for concurrent uploads
```

#### Performance Tuning:
1. **Snowflake Warehouse Size**: X-Small → Small → Medium → Large
2. **S3 Upload Parallelization**: Upload multiple files concurrently
3. **dbt Model Optimization**: Use incremental models for large tables
4. **External Table Partitioning**: Organize S3 files by date

---

### 7. Cost Issues

#### Problem: "Unexpected high costs"
```bash
💰 AWS/Snowflake bills higher than expected
```

#### Azure Equivalent:
```bash
# In Azure, you'd check:
az consumption usage list --billing-period-name 202501
```

#### AWS/Snowflake Solution:
```bash
# Check AWS costs
aws ce get-cost-and-usage \
  --time-period Start=2025-01-01,End=2025-01-31 \
  --granularity MONTHLY \
  --metrics BlendedCost

# Check S3 storage costs
aws s3api list-objects-v2 --bucket your-bucket --query 'sum(Contents[].Size)'
```

```sql
-- Check Snowflake credit usage
SELECT * FROM SNOWFLAKE.ACCOUNT_USAGE.WAREHOUSE_METERING_HISTORY 
WHERE START_TIME >= CURRENT_DATE - 7
ORDER BY CREDITS_USED DESC;

-- Check storage costs
SELECT * FROM SNOWFLAKE.ACCOUNT_USAGE.STORAGE_USAGE 
WHERE USAGE_DATE >= CURRENT_DATE - 7;
```

#### Cost Optimization:
1. **Auto-suspend warehouses**: Set to 1-5 minutes
2. **Right-size warehouses**: Start small, scale up only when needed
3. **S3 lifecycle policies**: Move old data to cheaper storage classes
4. **Clean up test data**: Delete unnecessary files and tables

---

### 8. Data Quality Issues

#### Problem: "Data looks incorrect or missing"
```sql
❌ SELECT COUNT(*) FROM DIM_USERS WHERE total_orders IS NULL; -- Too many nulls
```

#### Azure Equivalent:
```python
# In Databricks, you'd use data quality checks:
df.filter(col("total_orders").isNull()).count()
```

#### Solution:
```bash
# Run our data quality tests
python test_daily_pipeline.py

# Run dbt tests
cd dbt_live
dbt test --models staging
dbt test --models marts
```

```sql
-- Check data freshness
SELECT MAX(generated_at) FROM EXT_LIVE_USERS;

-- Check for duplicates
SELECT id, COUNT(*) FROM EXT_LIVE_USERS GROUP BY id HAVING COUNT(*) > 1;

-- Check referential integrity
SELECT COUNT(*) FROM EXT_LIVE_ORDERS o 
LEFT JOIN EXT_LIVE_USERS u ON o.user_id = u.id 
WHERE u.id IS NULL;
```

---

## 🔧 Diagnostic Commands Cheat Sheet

### AWS Diagnostics:
```bash
# Check identity
aws sts get-caller-identity

# Check S3 access
aws s3 ls s3://bucket-name/

# Check IAM role
aws iam get-role --role-name SnowflakeS3AccessRole

# Check bucket policy
aws s3api get-bucket-policy --bucket bucket-name
```

### Snowflake Diagnostics:
```sql
-- Check connection
SELECT CURRENT_TIMESTAMP();

-- Check warehouses
SHOW WAREHOUSES;

-- Check databases
SHOW DATABASES;

-- Check external tables
SHOW EXTERNAL TABLES IN SCHEMA LIVE_DATA;

-- Check stages
SHOW STAGES IN SCHEMA LIVE_DATA;
```

### dbt Diagnostics:
```bash
# Check connection
dbt debug

# Check models
dbt list

# Compile without running
dbt compile

# Run with verbose logging
dbt run --log-level debug
```

---

## 📞 Getting Help

### AWS Support:
- **Documentation**: https://docs.aws.amazon.com/
- **Forums**: https://forums.aws.amazon.com/
- **Support Center**: AWS Console → Support

### Snowflake Support:
- **Documentation**: https://docs.snowflake.com/
- **Community**: https://community.snowflake.com/
- **Support Portal**: Snowflake UI → Help → Support

### dbt Support:
- **Documentation**: https://docs.getdbt.com/
- **Community**: https://discourse.getdbt.com/
- **Slack**: dbt Community Slack

Remember: Most issues are permissions-related (IAM roles, trust policies) or configuration-related (account identifiers, connection strings). Start with the diagnostic commands above! 🔍
