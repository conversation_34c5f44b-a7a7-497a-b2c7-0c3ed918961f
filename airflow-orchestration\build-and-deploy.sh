#!/bin/bash
# Build and Deploy Airflow with dbt-core
# Bash script to build custom Docker image and deploy Airflow

echo "🚀 Building and Deploying Airflow with dbt-core..."

# Check if Docker is running
echo "🔍 Checking Docker status..."
if ! docker version > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker."
    exit 1
fi
echo "✅ Docker is running"

# Navigate to script directory
cd "$(dirname "$0")"
echo "📁 Working directory: $(pwd)"

# Stop existing containers
echo "🛑 Stopping existing containers..."
docker compose down

# Build the custom image
echo "🔨 Building custom Airflow image with dbt-core..."
docker compose build

if [ $? -ne 0 ]; then
    echo "❌ Docker build failed!"
    exit 1
fi

echo "✅ Docker build completed successfully!"

# Initialize Airflow
echo "🔧 Initializing Airflow..."
docker compose up airflow-init

if [ $? -ne 0 ]; then
    echo "❌ Airflow initialization failed!"
    exit 1
fi

echo "✅ Airflow initialization completed!"

# Start all services
echo "🚀 Starting Airflow services..."
docker compose up -d

if [ $? -ne 0 ]; then
    echo "❌ Failed to start Airflow services!"
    exit 1
fi

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
sleep 30

# Check service status
echo "📊 Checking service status..."
docker compose ps

# Test dbt installation
echo "🧪 Testing dbt installation..."
if docker compose exec -T airflow-webserver dbt --version > /dev/null 2>&1; then
    echo "✅ dbt is installed and working!"
    echo "dbt version: $(docker compose exec -T airflow-webserver dbt --version)"
else
    echo "⚠️ dbt test failed, but services are running"
fi

echo ""
echo "🎉 Deployment completed!"
echo "📱 Airflow UI: http://localhost:8080"
echo "👤 Username: airflow"
echo "🔑 Password: airflow"

echo ""
echo "📋 Next steps:"
echo "1. Open http://localhost:8080 in your browser"
echo "2. Login with airflow/airflow"
echo "3. Enable the simple_etl_pipeline DAG"
echo "4. Trigger a manual run to test dbt integration"

echo ""
echo "🔧 Useful commands:"
echo "- View logs: docker compose logs -f"
echo "- Stop services: docker compose down"
echo "- Restart services: docker compose restart"
echo "- Test dbt: docker compose exec airflow-webserver dbt --version"
