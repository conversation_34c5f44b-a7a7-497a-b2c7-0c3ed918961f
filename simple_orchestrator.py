#!/usr/bin/env python3
"""
Simple ETL Pipeline Orchestrator
Alternative to Airflow - runs your ETL pipeline with scheduling and monitoring
"""

import os
import sys
import subprocess
import time
import logging
import schedule
from datetime import datetime, timedelta
import json
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('pipeline_orchestrator.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ETLOrchestrator:
    def __init__(self, workspace_path=None):
        self.workspace_path = workspace_path or os.getcwd()
        self.dbt_path = os.path.join(self.workspace_path, "dbt_live")
        self.status_file = "pipeline_status.json"
        
    def run_script(self, script_name, description):
        """Run a Python script and return success status"""
        logger.info(f"🔄 Starting: {description}")
        script_path = os.path.join(self.workspace_path, script_name)
        
        if not os.path.exists(script_path):
            logger.error(f"❌ Script not found: {script_path}")
            return False
        
        try:
            # Change to workspace directory
            original_cwd = os.getcwd()
            os.chdir(self.workspace_path)
            
            result = subprocess.run([
                sys.executable, script_name
            ], capture_output=True, text=True, timeout=600)  # 10 minute timeout
            
            if result.returncode == 0:
                logger.info(f"✅ Completed: {description}")
                logger.debug(f"Output: {result.stdout}")
                return True
            else:
                logger.error(f"❌ Failed: {description}")
                logger.error(f"Error: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            logger.error(f"⏰ Timeout: {description}")
            return False
        except Exception as e:
            logger.error(f"💥 Exception in {description}: {str(e)}")
            return False
        finally:
            os.chdir(original_cwd)
    
    def run_dbt_command(self, command, description):
        """Run a dbt command and return success status"""
        logger.info(f"🔄 Starting: {description}")
        
        if not os.path.exists(self.dbt_path):
            logger.error(f"❌ dbt project not found: {self.dbt_path}")
            return False
        
        try:
            # Change to dbt directory
            original_cwd = os.getcwd()
            os.chdir(self.dbt_path)
            
            result = subprocess.run(
                command.split(), 
                capture_output=True, 
                text=True, 
                timeout=600
            )
            
            if result.returncode == 0:
                logger.info(f"✅ Completed: {description}")
                logger.debug(f"Output: {result.stdout}")
                return True
            else:
                logger.error(f"❌ Failed: {description}")
                logger.error(f"Error: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            logger.error(f"⏰ Timeout: {description}")
            return False
        except Exception as e:
            logger.error(f"💥 Exception in {description}: {str(e)}")
            return False
        finally:
            os.chdir(original_cwd)
    
    def save_status(self, status):
        """Save pipeline status to file"""
        status_data = {
            'timestamp': datetime.now().isoformat(),
            'status': status,
            'workspace_path': self.workspace_path
        }
        
        with open(self.status_file, 'w') as f:
            json.dump(status_data, f, indent=2)
    
    def run_full_pipeline(self):
        """Run the complete ETL pipeline"""
        start_time = datetime.now()
        logger.info("🚀 Starting ETL Pipeline")
        
        pipeline_steps = [
            ("daily_data_generator.py", "Generate Daily Data"),
            ("create_snowflake_stages.py", "Refresh Snowflake Stages"),
        ]
        
        dbt_steps = [
            ("dbt deps", "Install dbt Dependencies"),
            ("dbt run", "Run dbt Models"),
            ("dbt test", "Run dbt Tests"),
            ("dbt run --models monitoring", "Run Monitoring Models"),
        ]
        
        monitoring_steps = [
            ("show_etl_health_dashboard.py", "Generate Health Dashboard"),
            ("monitor_pipeline_alerts.py", "Check for Alerts"),
            ("check_snowflake_dbt_tables.py", "Validate Results"),
        ]
        
        # Track results
        results = {
            'start_time': start_time.isoformat(),
            'steps': [],
            'overall_success': True
        }
        
        # Run Python scripts
        for script, description in pipeline_steps:
            success = self.run_script(script, description)
            results['steps'].append({
                'step': description,
                'success': success,
                'timestamp': datetime.now().isoformat()
            })
            if not success:
                results['overall_success'] = False
                logger.error(f"🛑 Pipeline stopped due to failure in: {description}")
                break
        
        # Run dbt commands if previous steps succeeded
        if results['overall_success']:
            for command, description in dbt_steps:
                success = self.run_dbt_command(command, description)
                results['steps'].append({
                    'step': description,
                    'success': success,
                    'timestamp': datetime.now().isoformat()
                })
                if not success and "test" not in command.lower():  # Allow test failures
                    results['overall_success'] = False
                    logger.error(f"🛑 Pipeline stopped due to failure in: {description}")
                    break
        
        # Run monitoring steps
        if results['overall_success']:
            for script, description in monitoring_steps:
                success = self.run_script(script, description)
                results['steps'].append({
                    'step': description,
                    'success': success,
                    'timestamp': datetime.now().isoformat()
                })
                # Don't stop pipeline for monitoring failures
        
        # Calculate duration
        end_time = datetime.now()
        duration = end_time - start_time
        results['end_time'] = end_time.isoformat()
        results['duration_minutes'] = duration.total_seconds() / 60
        
        # Save status
        self.save_status(results)
        
        # Log summary
        if results['overall_success']:
            logger.info(f"🎉 Pipeline completed successfully in {duration.total_seconds():.1f} seconds")
        else:
            logger.error(f"💥 Pipeline failed after {duration.total_seconds():.1f} seconds")
        
        return results['overall_success']
    
    def run_monitoring_only(self):
        """Run only monitoring and health checks"""
        logger.info("🏥 Running Health Check")
        
        monitoring_steps = [
            ("dbt run --models monitoring", "Update Monitoring Models"),
            ("show_etl_health_dashboard.py", "Generate Health Dashboard"),
            ("monitor_pipeline_alerts.py", "Check for Alerts"),
        ]
        
        success_count = 0
        for step in monitoring_steps:
            if step[0].startswith("dbt"):
                success = self.run_dbt_command(step[0], step[1])
            else:
                success = self.run_script(step[0], step[1])
            
            if success:
                success_count += 1
        
        logger.info(f"🏥 Health check completed: {success_count}/{len(monitoring_steps)} steps successful")
        return success_count == len(monitoring_steps)

def main():
    """Main orchestrator function"""
    print("🚁 ETL Pipeline Orchestrator")
    print("=" * 50)
    
    orchestrator = ETLOrchestrator()
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "run":
            # Run pipeline once
            orchestrator.run_full_pipeline()
            
        elif command == "monitor":
            # Run monitoring only
            orchestrator.run_monitoring_only()
            
        elif command == "schedule":
            # Run with scheduling
            print("📅 Setting up scheduled runs...")
            
            # Schedule full pipeline daily at 6 AM
            schedule.every().day.at("06:00").do(orchestrator.run_full_pipeline)
            
            # Schedule monitoring every 4 hours
            schedule.every(4).hours.do(orchestrator.run_monitoring_only)
            
            print("⏰ Scheduled jobs:")
            print("  - Full pipeline: Daily at 6:00 AM")
            print("  - Health monitoring: Every 4 hours")
            print("  - Press Ctrl+C to stop")
            
            try:
                while True:
                    schedule.run_pending()
                    time.sleep(60)  # Check every minute
            except KeyboardInterrupt:
                print("\n👋 Scheduler stopped")
                
        else:
            print("❌ Unknown command. Use: run, monitor, or schedule")
    else:
        print("📋 Available commands:")
        print("  python simple_orchestrator.py run      - Run pipeline once")
        print("  python simple_orchestrator.py monitor  - Run health check")
        print("  python simple_orchestrator.py schedule - Run with scheduling")

if __name__ == "__main__":
    main()
