duration_minutes: 1.26
duration_seconds: 75.545388
end_time: '2025-06-04T20:33:10.361331'
start_time: '2025-06-04T20:31:54.815943'
steps:
  step1_generate_data:
    date: '2025-06-04'
    events_count: 75000
    files:
      events: live-data/events/daily_events_20250604.csv
      orders: live-data/orders/daily_orders_20250604.csv
      users: live-data/users/daily_users_20250604.csv
    orders_count: 15000
    success: true
    total_records: 95000
    users_count: 5000
  step2_verify_s3:
    events:
      exists: true
      last_modified: '2025-06-04T10:32:19+00:00'
      size_bytes: 16321737
      size_mb: 15.57
    orders:
      exists: true
      last_modified: '2025-06-04T10:32:04+00:00'
      size_bytes: 2183968
      size_mb: 2.08
    users:
      exists: true
      last_modified: '2025-06-04T10:32:02+00:00'
      size_bytes: 1119390
      size_mb: 1.07
  step3_refresh_stages: null
  step4_dbt_pipeline:
    run:
      returncode: 0
      stderr: ''
      stdout: "10:32:33  Running with dbt=1.9.6\n10:32:35  Registered adapter: snowflake=1.9.4\n\
        10:32:36  Found 12 models, 21 data tests, 3 sources, 592 macros\n10:32:36\
        \  \n10:32:36  Concurrency: 4 threads (target='live')\n10:32:36  \n10:32:38\
        \  1 of 12 START sql table model LIVE_DATA.dbt_test_health ........................\
        \ [RUN]\n10:32:38  2 of 12 START sql table model LIVE_DATA.pipeline_runtime_health\
        \ ................ [RUN]\n10:32:38  3 of 12 START sql table model LIVE_DATA.query_history_health\
        \ ................... [RUN]\n10:32:38  4 of 12 START sql view model LIVE_DATA.stg_events\
        \ .............................. [RUN]\n10:32:40  4 of 12 OK created sql view\
        \ model LIVE_DATA.stg_events ......................... [SUCCESS 1 in 1.56s]\n\
        10:32:40  5 of 12 START sql view model LIVE_DATA.stg_orders ..............................\
        \ [RUN]\n10:32:40  1 of 12 OK created sql table model LIVE_DATA.dbt_test_health\
        \ ................... [SUCCESS 1 in 1.60s]\n10:32:40  6 of 12 START sql view\
        \ model LIVE_DATA.stg_users ............................... [RUN]\n10:32:40\
        \  5 of 12 OK created sql view model LIVE_DATA.stg_orders .........................\
        \ [SUCCESS 1 in 0.47s]\n10:32:40  7 of 12 START sql table model LIVE_DATA.fact_orders\
        \ ............................ [RUN]\n10:32:40  6 of 12 OK created sql view\
        \ model LIVE_DATA.stg_users .......................... [SUCCESS 1 in 0.48s]\n\
        10:32:40  8 of 12 START sql view model LIVE_DATA_ANALYTICS.mv_fact_orders\
        \ ................ [RUN]\n10:32:41  8 of 12 OK created sql view model LIVE_DATA_ANALYTICS.mv_fact_orders\
        \ ........... [SUCCESS 1 in 0.39s]\n10:32:41  9 of 12 START sql table model\
        \ LIVE_DATA.dim_users .............................. [RUN]\n10:32:41  3 of\
        \ 12 OK created sql table model LIVE_DATA.query_history_health ..............\
        \ [SUCCESS 1 in 3.06s]\n10:32:41  10 of 12 START sql incremental model LIVE_DATA.dim_users_scd2\
        \ .................. [RUN]\n10:32:42  2 of 12 OK created sql table model LIVE_DATA.pipeline_runtime_health\
        \ ........... [SUCCESS 1 in 3.34s]\n10:32:42  7 of 12 OK created sql table\
        \ model LIVE_DATA.fact_orders ....................... [SUCCESS 1 in 1.93s]\n\
        10:32:44  9 of 12 OK created sql table model LIVE_DATA.dim_users .........................\
        \ [SUCCESS 1 in 2.84s]\n10:32:44  11 of 12 START sql table model LIVE_DATA.data_quality_health\
        \ ................... [RUN]\n10:32:46  10 of 12 OK created sql incremental\
        \ model LIVE_DATA.dim_users_scd2 ............. [SUCCESS 5000 in 4.18s]\n10:32:46\
        \  11 of 12 OK created sql table model LIVE_DATA.data_quality_health ..............\
        \ [SUCCESS 1 in 2.54s]\n10:32:46  12 of 12 START sql table model LIVE_DATA.etl_health_dashboard\
        \ .................. [RUN]\n10:32:47  12 of 12 OK created sql table model\
        \ LIVE_DATA.etl_health_dashboard ............. [SUCCESS 1 in 0.89s]\n10:32:48\
        \  \n10:32:48  Finished running 1 incremental model, 7 table models, 4 view\
        \ models in 0 hours 0 minutes and 11.95 seconds (11.95s).\n10:32:48  \n10:32:48\
        \  Completed successfully\n10:32:48  \n10:32:48  Done. PASS=12 WARN=0 ERROR=0\
        \ SKIP=0 TOTAL=12\n"
    test:
      returncode: 0
      stderr: ''
      stdout: "10:32:52  Running with dbt=1.9.6\n10:32:54  Registered adapter: snowflake=1.9.4\n\
        10:32:55  Found 12 models, 21 data tests, 3 sources, 592 macros\n10:32:55\
        \  \n10:32:55  Concurrency: 4 threads (target='live')\n10:32:55  \n10:32:57\
        \  1 of 21 START test dbt_utils_source_accepted_range_live_external_ext_live_orders_amount__False__0\
        \  [RUN]\n10:32:57  2 of 21 START test dbt_utils_source_accepted_range_live_external_ext_live_orders_item_count__100__1\
        \  [RUN]\n10:32:57  3 of 21 START test source_accepted_values_live_external_ext_live_events_action__view__click__log__purchase\
        \  [RUN]\n10:32:57  4 of 21 START test source_accepted_values_live_external_ext_live_events_platform__ios__android__web__None\
        \  [RUN]\n10:32:59  1 of 21 PASS dbt_utils_source_accepted_range_live_external_ext_live_orders_amount__False__0\
        \  [PASS in 1.73s]\n10:32:59  5 of 21 START test source_not_null_live_external_ext_live_events_event_id\
        \ ...... [RUN]\n10:33:00  3 of 21 PASS source_accepted_values_live_external_ext_live_events_action__view__click__log__purchase\
        \  [PASS in 2.37s]\n10:33:00  6 of 21 START test source_not_null_live_external_ext_live_events_generated_at\
        \ .. [RUN]\n10:33:00  2 of 21 PASS dbt_utils_source_accepted_range_live_external_ext_live_orders_item_count__100__1\
        \  [PASS in 2.43s]\n10:33:00  7 of 21 START test source_not_null_live_external_ext_live_events_session_id\
        \ .... [RUN]\n10:33:00  4 of 21 PASS source_accepted_values_live_external_ext_live_events_platform__ios__android__web__None\
        \  [PASS in 2.77s]\n10:33:00  8 of 21 START test source_not_null_live_external_ext_live_events_user_id\
        \ ....... [RUN]\n10:33:00  5 of 21 PASS source_not_null_live_external_ext_live_events_event_id\
        \ ............ [PASS in 1.20s]\n10:33:00  9 of 21 START test source_not_null_live_external_ext_live_orders_amount\
        \ ........ [RUN]\n10:33:01  6 of 21 PASS source_not_null_live_external_ext_live_events_generated_at\
        \ ........ [PASS in 1.35s]\n10:33:01  10 of 21 START test source_not_null_live_external_ext_live_orders_generated_at\
        \ . [RUN]\n10:33:01  9 of 21 PASS source_not_null_live_external_ext_live_orders_amount\
        \ .............. [PASS in 0.99s]\n10:33:01  11 of 21 START test source_not_null_live_external_ext_live_orders_id\
        \ ........... [RUN]\n10:33:01  7 of 21 PASS source_not_null_live_external_ext_live_events_session_id\
        \ .......... [PASS in 1.52s]\n10:33:01  12 of 21 START test source_not_null_live_external_ext_live_orders_item_count\
        \ ... [RUN]\n10:33:02  8 of 21 PASS source_not_null_live_external_ext_live_events_user_id\
        \ ............. [PASS in 1.47s]\n10:33:02  13 of 21 START test source_not_null_live_external_ext_live_orders_user_id\
        \ ...... [RUN]\n10:33:02  10 of 21 PASS source_not_null_live_external_ext_live_orders_generated_at\
        \ ....... [PASS in 1.07s]\n10:33:02  14 of 21 START test source_not_null_live_external_ext_live_users_email\
        \ ......... [RUN]\n10:33:02  11 of 21 PASS source_not_null_live_external_ext_live_orders_id\
        \ ................. [PASS in 0.98s]\n10:33:02  12 of 21 PASS source_not_null_live_external_ext_live_orders_item_count\
        \ ......... [PASS in 0.95s]\n10:33:02  15 of 21 START test source_not_null_live_external_ext_live_users_generated_at\
        \ .. [RUN]\n10:33:02  16 of 21 START test source_not_null_live_external_ext_live_users_id\
        \ ............ [RUN]\n10:33:03  13 of 21 PASS source_not_null_live_external_ext_live_orders_user_id\
        \ ............ [PASS in 1.09s]\n10:33:03  17 of 21 START test source_relationships_live_external_ext_live_events_user_id__id__source_live_external_ext_live_users_\
        \  [RUN]\n10:33:03  14 of 21 PASS source_not_null_live_external_ext_live_users_email\
        \ ............... [PASS in 1.15s]\n10:33:03  18 of 21 START test source_relationships_live_external_ext_live_orders_user_id__id__source_live_external_ext_live_users_\
        \  [RUN]\n10:33:03  15 of 21 PASS source_not_null_live_external_ext_live_users_generated_at\
        \ ........ [PASS in 1.01s]\n10:33:03  19 of 21 START test source_unique_live_external_ext_live_events_event_id\
        \ ....... [RUN]\n10:33:04  16 of 21 PASS source_not_null_live_external_ext_live_users_id\
        \ .................. [PASS in 1.13s]\n10:33:04  20 of 21 START test source_unique_live_external_ext_live_orders_id\
        \ ............. [RUN]\n10:33:04  17 of 21 WARN 396000 source_relationships_live_external_ext_live_events_user_id__id__source_live_external_ext_live_users_\
        \  [WARN 396000 in 1.70s]\n10:33:04  21 of 21 START test source_unique_live_external_ext_live_users_id\
        \ .............. [RUN]\n10:33:05  20 of 21 PASS source_unique_live_external_ext_live_orders_id\
        \ ................... [PASS in 1.16s]\n10:33:05  19 of 21 PASS source_unique_live_external_ext_live_events_event_id\
        \ ............. [PASS in 1.43s]\n10:33:05  18 of 21 WARN 72000 source_relationships_live_external_ext_live_orders_user_id__id__source_live_external_ext_live_users_\
        \  [WARN 72000 in 1.70s]\n10:33:05  21 of 21 PASS source_unique_live_external_ext_live_users_id\
        \ .................... [PASS in 1.04s]\n10:33:06  \n10:33:06  Finished running\
        \ 21 data tests in 0 hours 0 minutes and 11.11 seconds (11.11s).\n10:33:06\
        \  \n10:33:06  Completed with 2 warnings:\n10:33:06  \n10:33:06  Warning in\
        \ test source_relationships_live_external_ext_live_events_user_id__id__source_live_external_ext_live_users_\
        \ (models\\sources.yml)\n10:33:06  Got 396000 results, configured to warn\
        \ if != 0\n10:33:06  \n10:33:06    compiled code at target\\compiled\\live_c360\\\
        models\\sources.yml\\source_relationships_live_exte_f8dcab25f08d3d0cecc66acc19c0f320.sql\n\
        10:33:06  \n10:33:06    See test failures:\n  ------------------------------------------------------------------------------------------------------------\n\
        \  select * from MYDB.LIVE_DATA_dbt_test__audit.source_relationships_live_exte_f8dcab25f08d3d0cecc66acc19c0f320\n\
        \  ------------------------------------------------------------------------------------------------------------\n\
        10:33:06  \n10:33:06  Warning in test source_relationships_live_external_ext_live_orders_user_id__id__source_live_external_ext_live_users_\
        \ (models\\sources.yml)\n10:33:06  Got 72000 results, configured to warn if\
        \ != 0\n10:33:06  \n10:33:06    compiled code at target\\compiled\\live_c360\\\
        models\\sources.yml\\source_relationships_live_exte_3905f58a8f4f802f3d4269e076a3f201.sql\n\
        10:33:06  \n10:33:06    See test failures:\n  ------------------------------------------------------------------------------------------------------------\n\
        \  select * from MYDB.LIVE_DATA_dbt_test__audit.source_relationships_live_exte_3905f58a8f4f802f3d4269e076a3f201\n\
        \  ------------------------------------------------------------------------------------------------------------\n\
        10:33:06  \n10:33:06  Done. PASS=19 WARN=2 ERROR=0 SKIP=0 TOTAL=21\n"
  step5_validate_results:
    data_freshness_hours: 'Error: unsupported operand type(s) for -: ''datetime.datetime''
      and ''str'''
    dbt_dim_users: 30000
    dbt_stg_events: 450000
    dbt_stg_orders: 90000
    dbt_stg_users: 30000
    external_ext_live_events: 450000
    external_ext_live_orders: 90000
    external_ext_live_users: 30000
success: true
target_date: '2025-06-04'
