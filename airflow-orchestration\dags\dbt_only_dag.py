"""
dbt-Only Pipeline DAG
Dedicated DAG for running dbt transformations only
Focuses on dbt model execution, testing, and documentation
"""

from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.operators.bash import BashOperator
from airflow.operators.dummy import DummyOperator
from airflow.utils.dates import days_ago
import os
import subprocess
import logging
import time

# Default arguments for the DAG
default_args = {
    'owner': 'data-team',
    'depends_on_past': False,
    'start_date': days_ago(1),
    'email_on_failure': False,
    'email_on_retry': False,
    'retries': 2,
    'retry_delay': timedelta(minutes=3),
    'catchup': False,
}

# DAG definition
dag = DAG(
    'dbt_only_pipeline',
    default_args=default_args,
    description='dbt-Only Pipeline for Data Transformations',
    schedule_interval='0 2 * * *',  # Daily at 2 AM
    max_active_runs=1,
    tags=['dbt', 'transformations', 'data-quality'],
    params={
        'run_marts': True,
        'run_monitoring': True,
        'run_tests': True,
        'generate_docs': False,
        'docs_port': 8088,
        'staging_removed': True,  # Note: staging tasks removed per user request
    }
)

def get_workspace_path():
    """Get the workspace path for dbt project"""
    return "/opt/airflow/workspace"

def get_dbt_project_path():
    """Get the dbt project path"""
    workspace_path = get_workspace_path()
    return os.path.join(workspace_path, "dbt_live")

def run_dbt_command(command_type, models=None, extra_args=None, **context):
    """
    Run a dbt command with comprehensive error handling
    
    Args:
        command_type (str): The dbt command to run (run, test, deps, etc.)
        models (str): Optional model selection
        extra_args (list): Additional command line arguments
    """
    dbt_path = get_dbt_project_path()
    
    logging.info(f"🚀 Running dbt {command_type}")
    
    if not os.path.exists(dbt_path):
        logging.error(f"❌ dbt project not found: {dbt_path}")
        raise FileNotFoundError(f"dbt project not found: {dbt_path}")
    
    # Change to dbt directory
    original_cwd = os.getcwd()
    os.chdir(dbt_path)
    
    try:
        # Build command
        command_parts = ["dbt", command_type]
        
        if models:
            command_parts.extend(["--models", models])
        
        if extra_args:
            command_parts.extend(extra_args)
        
        logging.info(f"📋 Executing: {' '.join(command_parts)}")
        
        # Run command with timeout
        result = subprocess.run(
            command_parts,
            capture_output=True,
            text=True,
            cwd=dbt_path,
            timeout=600  # 10 minute timeout
        )
        
        # Log output
        if result.stdout:
            logging.info(f"📄 stdout: {result.stdout}")
        
        if result.stderr:
            logging.warning(f"⚠️ stderr: {result.stderr}")
        
        if result.returncode == 0:
            logging.info(f"✅ dbt {command_type} completed successfully")
            return {
                'success': True,
                'command': ' '.join(command_parts),
                'stdout': result.stdout,
                'stderr': result.stderr
            }
        else:
            error_msg = f"dbt {command_type} failed with return code {result.returncode}"
            logging.error(f"❌ {error_msg}")
            logging.error(f"❌ Error output: {result.stderr}")
            raise Exception(f"{error_msg}: {result.stderr}")
    
    except subprocess.TimeoutExpired:
        error_msg = f"dbt {command_type} timed out after 10 minutes"
        logging.error(f"❌ {error_msg}")
        raise Exception(error_msg)
    
    except Exception as e:
        logging.error(f"❌ dbt {command_type} failed: {e}")
        raise Exception(f"dbt {command_type} failed: {e}")
    
    finally:
        os.chdir(original_cwd)

# Task functions
def dbt_debug(**context):
    """Run dbt debug to check connection and configuration"""
    logging.info("🔍 Running dbt debug to validate configuration...")
    return run_dbt_command('debug', **context)

def dbt_deps(**context):
    """Install dbt dependencies"""
    logging.info("📦 Installing dbt dependencies...")
    return run_dbt_command('deps', **context)

def refresh_snowflake_stages(**context):
    """Refresh Snowflake external stages before running staging models"""
    logging.info("🔄 Refreshing Snowflake external stages...")

    try:
        import snowflake.connector
        import yaml

        workspace_path = get_workspace_path()
        config_file = os.path.join(workspace_path, 'config/live_pipeline_config.yml')

        # Load configuration
        with open(config_file, 'r') as f:
            config = yaml.safe_load(f)

        # Connection parameters
        connection_params = {
            'account': config['snowflake']['account'],
            'user': config['snowflake']['user'],
            'password': 'Asdfjkll1234!@#$',  # Use the same password as other scripts
            'warehouse': config['snowflake']['warehouse'],
            'database': config['snowflake']['database'],
            'schema': config['snowflake']['schema'],
            'role': config['snowflake']['role']
        }

        logging.info(f"🔗 Connecting to Snowflake: {config['snowflake']['account']}")
        conn = snowflake.connector.connect(**connection_params)
        cursor = conn.cursor()

        # Set context
        cursor.execute(f"USE DATABASE {config['snowflake']['database']}")
        cursor.execute(f"USE SCHEMA {config['snowflake']['schema']}")

        # External tables that need refreshing
        external_tables = ['EXT_LIVE_USERS', 'EXT_LIVE_ORDERS', 'EXT_LIVE_EVENTS']

        refresh_results = {}

        for table in external_tables:
            try:
                logging.info(f"🔄 Refreshing external table: {table}")

                # Check if table exists and get current count
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count_before = cursor.fetchone()[0]

                # Refresh external table
                cursor.execute(f"ALTER EXTERNAL TABLE {table} REFRESH")

                # Check new count
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count_after = cursor.fetchone()[0]

                refresh_results[table] = {
                    'count_before': count_before,
                    'count_after': count_after,
                    'new_records': count_after - count_before
                }

                logging.info(f"✅ {table}: {count_before:,} → {count_after:,} (+{count_after - count_before:,})")

            except Exception as e:
                refresh_results[table] = {'error': str(e)}
                logging.error(f"❌ Failed to refresh {table}: {e}")
                # Continue with other tables even if one fails

        cursor.close()
        conn.close()

        # Check if any tables were successfully refreshed
        successful_refreshes = [table for table, result in refresh_results.items() if 'error' not in result]

        if successful_refreshes:
            logging.info(f"✅ Successfully refreshed {len(successful_refreshes)} external tables")
            return f"Stage refresh completed: {refresh_results}"
        else:
            logging.error("❌ No external tables were successfully refreshed")
            raise Exception(f"Stage refresh failed for all tables: {refresh_results}")

    except Exception as e:
        logging.error(f"❌ Stage refresh failed: {e}")
        raise Exception(f"Stage refresh failed: {e}")

# Staging tasks removed per user request
# def dbt_run_staging(**context):
#     """Run staging models"""
#     logging.info("🏗️ Running staging models...")
#     return run_dbt_command('run', models='staging', **context)

def dbt_run_marts(**context):
    """Run marts models"""
    logging.info("🏪 Running marts models...")
    return run_dbt_command('run', models='marts', **context)

def dbt_run_monitoring(**context):
    """Run monitoring models"""
    logging.info("📊 Running monitoring models...")
    return run_dbt_command('run', models='monitoring', **context)

def dbt_run_all(**context):
    """Run all dbt models"""
    logging.info("🚀 Running all dbt models...")
    return run_dbt_command('run', **context)

def dbt_test(**context):
    """Run dbt tests"""
    logging.info("🧪 Running dbt tests...")
    return run_dbt_command('test', **context)

# def dbt_test_staging(**context):
#     """Run tests for staging models only"""
#     logging.info("🧪 Running staging model tests...")
#     return run_dbt_command('test', models='staging', **context)

def dbt_test_marts(**context):
    """Run tests for marts models only"""
    logging.info("🧪 Running marts model tests...")
    return run_dbt_command('test', models='marts', **context)

def dbt_generate_docs(**context):
    """Generate dbt documentation"""
    logging.info("📚 Generating dbt documentation...")
    return run_dbt_command('docs', extra_args=['generate'], **context)

def validate_dbt_results(**context):
    """Validate dbt pipeline results"""
    logging.info("✅ Validating dbt pipeline results...")
    
    dbt_path = get_dbt_project_path()
    target_path = os.path.join(dbt_path, "target")
    
    if not os.path.exists(target_path):
        raise Exception("dbt target directory not found - models may not have run successfully")
    
    # Check for run_results.json
    run_results_path = os.path.join(target_path, "run_results.json")
    if os.path.exists(run_results_path):
        logging.info("✅ Found run_results.json - dbt run completed")
    else:
        logging.warning("⚠️ run_results.json not found")
    
    # Check for manifest.json
    manifest_path = os.path.join(target_path, "manifest.json")
    if os.path.exists(manifest_path):
        logging.info("✅ Found manifest.json - dbt compilation successful")
    else:
        logging.warning("⚠️ manifest.json not found")
    
    logging.info("✅ dbt pipeline validation completed")
    return "dbt pipeline validation successful"

def cleanup_dbt_artifacts(**context):
    """Clean up old dbt artifacts"""
    logging.info("🧹 Cleaning up old dbt artifacts...")
    
    dbt_path = get_dbt_project_path()
    
    # Clean target directory (but keep it)
    target_path = os.path.join(dbt_path, "target")
    if os.path.exists(target_path):
        import shutil
        for item in os.listdir(target_path):
            item_path = os.path.join(target_path, item)
            if os.path.isfile(item_path) and item.endswith('.json'):
                # Keep only the most recent files
                continue
    
    logging.info("✅ Cleanup completed")
    return "Cleanup successful"

# =============================================================================
# TASK DEFINITIONS
# =============================================================================

# Start task
start_task = DummyOperator(
    task_id='start_dbt_pipeline',
    dag=dag,
)

# Health check and setup tasks
debug_task = PythonOperator(
    task_id='dbt_debug',
    python_callable=dbt_debug,
    dag=dag,
)

deps_task = PythonOperator(
    task_id='dbt_deps',
    python_callable=dbt_deps,
    dag=dag,
)

# Stage refresh task (critical for external tables)
refresh_stages_task = PythonOperator(
    task_id='refresh_snowflake_stages',
    python_callable=refresh_snowflake_stages,
    dag=dag,
)

# Model execution tasks (staging tasks removed per user request)
# run_staging_task = PythonOperator(
#     task_id='dbt_run_staging',
#     python_callable=dbt_run_staging,
#     dag=dag,
# )

run_marts_task = PythonOperator(
    task_id='dbt_run_marts',
    python_callable=dbt_run_marts,
    dag=dag,
)

run_monitoring_task = PythonOperator(
    task_id='dbt_run_monitoring',
    python_callable=dbt_run_monitoring,
    dag=dag,
)

# Testing tasks (staging test removed per user request)
# test_staging_task = PythonOperator(
#     task_id='dbt_test_staging',
#     python_callable=dbt_test_staging,
#     dag=dag,
# )

test_marts_task = PythonOperator(
    task_id='dbt_test_marts',
    python_callable=dbt_test_marts,
    dag=dag,
)

test_all_task = PythonOperator(
    task_id='dbt_test_all',
    python_callable=dbt_test,
    dag=dag,
)

# Documentation task (optional)
docs_task = PythonOperator(
    task_id='dbt_generate_docs',
    python_callable=dbt_generate_docs,
    dag=dag,
)

# Validation and cleanup tasks
validate_task = PythonOperator(
    task_id='validate_dbt_results',
    python_callable=validate_dbt_results,
    dag=dag,
)

cleanup_task = PythonOperator(
    task_id='cleanup_dbt_artifacts',
    python_callable=cleanup_dbt_artifacts,
    dag=dag,
)

# End task
end_task = DummyOperator(
    task_id='end_dbt_pipeline',
    dag=dag,
)

# =============================================================================
# TASK DEPENDENCIES
# =============================================================================

# Main pipeline flow
start_task >> debug_task >> deps_task >> refresh_stages_task

# Model execution flow (marts -> monitoring)
# IMPORTANT: Refresh stages before running models (staging tasks removed)
refresh_stages_task >> run_marts_task >> test_marts_task
test_marts_task >> run_monitoring_task

# Final testing and validation
run_monitoring_task >> test_all_task >> validate_task

# Optional documentation generation (parallel to validation)
run_monitoring_task >> docs_task

# Cleanup and end
[validate_task, docs_task] >> cleanup_task >> end_task
