[2025-06-04T03:52:13.738+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=non-requeueable deps ti=<TaskInstance: simple_etl_pipeline.run_dbt_models manual__2025-06-04T03:52:05.226263+00:00 [queued]>
[2025-06-04T03:52:13.748+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=requeueable deps ti=<TaskInstance: simple_etl_pipeline.run_dbt_models manual__2025-06-04T03:52:05.226263+00:00 [queued]>
[2025-06-04T03:52:13.749+0000] {taskinstance.py:2170} INFO - Starting attempt 1 of 2
[2025-06-04T03:52:13.762+0000] {taskinstance.py:2191} INFO - Executing <Task(PythonOperator): run_dbt_models> on 2025-06-04 03:52:05.226263+00:00
[2025-06-04T03:52:13.767+0000] {standard_task_runner.py:60} INFO - Started process 1126 to run task
[2025-06-04T03:52:13.771+0000] {standard_task_runner.py:87} INFO - Running: ['***', 'tasks', 'run', 'simple_etl_pipeline', 'run_dbt_models', 'manual__2025-06-04T03:52:05.226263+00:00', '--job-id', '66', '--raw', '--subdir', 'DAGS_FOLDER/simple_etl_dag.py', '--cfg-path', '/tmp/tmpy3se30gr']
[2025-06-04T03:52:13.775+0000] {standard_task_runner.py:88} INFO - Job 66: Subtask run_dbt_models
[2025-06-04T03:52:13.832+0000] {task_command.py:423} INFO - Running <TaskInstance: simple_etl_pipeline.run_dbt_models manual__2025-06-04T03:52:05.226263+00:00 [running]> on host 9fdd53a423c8
[2025-06-04T03:52:13.915+0000] {taskinstance.py:2480} INFO - Exporting env vars: AIRFLOW_CTX_DAG_OWNER='data-team' AIRFLOW_CTX_DAG_ID='simple_etl_pipeline' AIRFLOW_CTX_TASK_ID='run_dbt_models' AIRFLOW_CTX_EXECUTION_DATE='2025-06-04T03:52:05.226263+00:00' AIRFLOW_CTX_TRY_NUMBER='1' AIRFLOW_CTX_DAG_RUN_ID='manual__2025-06-04T03:52:05.226263+00:00'
[2025-06-04T03:52:13.917+0000] {simple_etl_dag.py:78} INFO - Running dbt run using Python API
[2025-06-04T03:52:13.920+0000] {simple_etl_dag.py:121} WARNING - dbt Python API not available, falling back to subprocess
[2025-06-04T03:52:13.966+0000] {simple_etl_dag.py:141} ERROR - dbt run failed: /usr/local/bin/python: No module named dbt

[2025-06-04T03:52:13.967+0000] {taskinstance.py:2698} ERROR - Task failed with exception
Traceback (most recent call last):
  File "/opt/airflow/dags/simple_etl_dag.py", line 91, in run_dbt_command_python
    from dbt.cli.main import dbtRunner, dbtRunnerResult
ModuleNotFoundError: No module named 'dbt'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/models/taskinstance.py", line 433, in _execute_task
    result = execute_callable(context=context, **execute_callable_kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/operators/python.py", line 199, in execute
    return_value = self.execute_callable()
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/operators/python.py", line 216, in execute_callable
    return self.python_callable(*self.op_args, **self.op_kwargs)
  File "/opt/airflow/dags/simple_etl_dag.py", line 183, in run_dbt_models
    return run_dbt_command_python('run', **context)
  File "/opt/airflow/dags/simple_etl_dag.py", line 122, in run_dbt_command_python
    return run_dbt_command_subprocess(command_type, models, dbt_path)
  File "/opt/airflow/dags/simple_etl_dag.py", line 142, in run_dbt_command_subprocess
    raise Exception(f"dbt {command_type} failed: {result.stderr}")
Exception: dbt run failed: /usr/local/bin/python: No module named dbt

[2025-06-04T03:52:13.983+0000] {taskinstance.py:1138} INFO - Marking task as UP_FOR_RETRY. dag_id=simple_etl_pipeline, task_id=run_dbt_models, execution_date=20250604T035205, start_date=20250604T035213, end_date=20250604T035213
[2025-06-04T03:52:13.998+0000] {standard_task_runner.py:107} ERROR - Failed to execute job 66 for task run_dbt_models (dbt run failed: /usr/local/bin/python: No module named dbt
; 1126)
[2025-06-04T03:52:14.025+0000] {local_task_job_runner.py:234} INFO - Task exited with return code 1
[2025-06-04T03:52:14.049+0000] {taskinstance.py:3280} INFO - 0 downstream tasks scheduled from follow-on schedule check
