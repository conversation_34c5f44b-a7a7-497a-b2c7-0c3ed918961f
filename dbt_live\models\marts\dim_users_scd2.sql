{{
    config(
        materialized='incremental',
        unique_key='user_sk',
        tags=['marts', 'dimension', 'scd2'],
        description='Slowly Changing Dimension Type 2 for users'
    )
}}

with 

-- Get the latest user data from staging
source_data as (
    select 
        user_id,
        firstname,
        lastname,
        email_hash,
        address,
        acquisition_channel as canal,
        country,
        gender,
        age_group,
        creation_date,
        last_activity_date,
        is_churned as churn,
        batch_id,
        current_timestamp() as dbt_updated_at,
        -- Track when this record was processed
        current_timestamp() as processed_at
    from {{ ref('stg_users') }}
    where user_id is not null
    qualify row_number() over (partition by user_id order by generated_at desc) = 1
    
    {% if is_incremental() %}
    -- Only process users that are new or have changed since the last run
    and user_id in (
        select user_id
        from {{ ref('stg_users') }}
        where generated_at > (
            select coalesce(max(dbt_valid_from), '1900-01-01'::timestamp)
            from {{ this }}
            where is_current_version = true
        )
    )
    {% endif %}
),

-- Get the current version of each user from the dimension
existing_dimension as (
    {% if is_incremental() %}
    select
        user_sk,
        user_id,
        firstname,
        lastname,
        email,
        address,
        acquisition_channel,
        country,
        gender,
        age_group,
        creation_date,
        last_activity_date,
        churn,
        batch_id,
        dbt_valid_from,
        dbt_valid_to,
        is_current_version,
        dbt_change_type,
        current_timestamp() as processed_at  -- Add processed_at for consistency
    from {{ this }}
    where is_current_version = true
    {% else %}
    -- For initial load, return empty result set with correct schema
    select
        cast(null as varchar) as user_sk,
        cast(null as varchar) as user_id,
        cast(null as varchar) as firstname,
        cast(null as varchar) as lastname,
        cast(null as varchar) as email,
        cast(null as varchar) as address,
        cast(null as varchar) as acquisition_channel,
        cast(null as varchar) as country,
        cast(null as varchar) as gender,
        cast(null as varchar) as age_group,
        cast(null as timestamp_ntz) as creation_date,
        cast(null as timestamp_ntz) as last_activity_date,
        cast(null as boolean) as churn,
        cast(null as varchar) as batch_id,
        cast(null as timestamp_ntz) as dbt_valid_from,
        cast(null as timestamp_ntz) as dbt_valid_to,
        cast(null as boolean) as is_current_version,
        cast(null as varchar) as dbt_change_type,
        cast(null as timestamp_ntz) as processed_at
    where 1=0  -- Return no rows for initial load
    {% endif %}
),

-- Identify new and changed records
changes_to_apply as (
    select
        s.*,
        case
            when e.user_sk is null then 'insert'
            when e.firstname != s.firstname 
              or e.lastname != s.lastname
              or e.email != s.email_hash
              or e.address != s.address
              or e.acquisition_channel != s.canal
              or e.country != s.country
              or e.gender != s.gender
              or e.age_group != s.age_group
              or e.churn != s.churn then 'update'
            else 'no_change'
        end as change_type
    from source_data s
    left join existing_dimension e 
        on s.user_id = e.user_id
)

-- For initial load, just insert all records
{% if not is_incremental() %}

select
    -- Business key
    {{ dbt_utils.generate_surrogate_key(['user_id', 'dbt_updated_at']) }} as user_sk,
    
    -- Natural key
    user_id,
    
    -- User attributes
    firstname,
    lastname,
    email_hash as email,
    address,
    canal as acquisition_channel,
    country,
    gender,
    age_group,
    
    -- Metadata
    creation_date,
    last_activity_date,
    churn,
    batch_id,
    
    -- SCD2 tracking columns
    dbt_updated_at as dbt_valid_from,
    cast(null as timestamp_ntz) as dbt_valid_to,
    true as is_current_version,
    
    -- Track the change
    'initial_load' as dbt_change_type,
    
    -- Track when this record was processed
    processed_at
    
from source_data

{% else %}

-- For incremental loads, handle inserts and updates
select * from (
    -- New or updated records
    select
        {{ dbt_utils.generate_surrogate_key(['user_id', 'dbt_updated_at']) }} as user_sk,
        user_id,
        firstname,
        lastname,
        email_hash as email,
        address,
        canal as acquisition_channel,
        country,
        gender,
        age_group,
        creation_date,
        last_activity_date,
        churn,
        batch_id,
        dbt_updated_at as dbt_valid_from,
        cast(null as timestamp_ntz) as dbt_valid_to,
        true as is_current_version,
        change_type as dbt_change_type,
        processed_at
    from changes_to_apply
    where change_type in ('insert', 'update')
    
    union all
    
    -- Expire old versions of updated records
    select
        e.user_sk,
        e.user_id,
        e.firstname,
        e.lastname,
        e.email,
        e.address,
        e.acquisition_channel,
        e.country,
        e.gender,
        e.age_group,
        e.creation_date,
        e.last_activity_date,
        e.churn,
        e.batch_id,
        e.dbt_valid_from,
        current_timestamp() as dbt_valid_to,
        false as is_current_version,
        'expire' as dbt_change_type,
        e.processed_at
    from existing_dimension e
    inner join changes_to_apply c 
        on e.user_id = c.user_id
        and c.change_type = 'update'
)

{% endif %}
