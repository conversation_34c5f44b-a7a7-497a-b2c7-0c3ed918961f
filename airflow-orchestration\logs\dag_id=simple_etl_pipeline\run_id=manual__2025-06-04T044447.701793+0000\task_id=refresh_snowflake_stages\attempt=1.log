[2025-06-04T04:44:50.719+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=non-requeueable deps ti=<TaskInstance: simple_etl_pipeline.refresh_snowflake_stages manual__2025-06-04T04:44:47.701793+00:00 [queued]>
[2025-06-04T04:44:50.728+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=requeueable deps ti=<TaskInstance: simple_etl_pipeline.refresh_snowflake_stages manual__2025-06-04T04:44:47.701793+00:00 [queued]>
[2025-06-04T04:44:50.729+0000] {taskinstance.py:2170} INFO - Starting attempt 1 of 2
[2025-06-04T04:44:50.740+0000] {taskinstance.py:2191} INFO - Executing <Task(PythonOperator): refresh_snowflake_stages> on 2025-06-04 04:44:47.701793+00:00
[2025-06-04T04:44:50.748+0000] {standard_task_runner.py:60} INFO - Started process 238 to run task
[2025-06-04T04:44:50.751+0000] {standard_task_runner.py:87} INFO - Running: ['***', 'tasks', 'run', 'simple_etl_pipeline', 'refresh_snowflake_stages', 'manual__2025-06-04T04:44:47.701793+00:00', '--job-id', '78', '--raw', '--subdir', 'DAGS_FOLDER/simple_etl_dag.py', '--cfg-path', '/tmp/tmpt09m2t4f']
[2025-06-04T04:44:50.753+0000] {standard_task_runner.py:88} INFO - Job 78: Subtask refresh_snowflake_stages
[2025-06-04T04:44:50.803+0000] {task_command.py:423} INFO - Running <TaskInstance: simple_etl_pipeline.refresh_snowflake_stages manual__2025-06-04T04:44:47.701793+00:00 [running]> on host 8fb7c8dc5097
[2025-06-04T04:44:50.879+0000] {taskinstance.py:2480} INFO - Exporting env vars: AIRFLOW_CTX_DAG_OWNER='data-team' AIRFLOW_CTX_DAG_ID='simple_etl_pipeline' AIRFLOW_CTX_TASK_ID='refresh_snowflake_stages' AIRFLOW_CTX_EXECUTION_DATE='2025-06-04T04:44:47.701793+00:00' AIRFLOW_CTX_TRY_NUMBER='1' AIRFLOW_CTX_DAG_RUN_ID='manual__2025-06-04T04:44:47.701793+00:00'
[2025-06-04T04:44:50.881+0000] {simple_etl_dag.py:192} INFO - Refreshing Snowflake external stages...
[2025-06-04T04:44:51.629+0000] {create_snowflake_stages.py:36} INFO - ✅ Snowflake Stages Setup initialized
[2025-06-04T04:44:51.634+0000] {create_snowflake_stages.py:408} INFO - ✅ Test queries saved to config/test_queries.sql
[2025-06-04T04:44:51.635+0000] {create_snowflake_stages.py:311} INFO - 🚀 Starting Snowflake external stages setup...
[2025-06-04T04:44:51.635+0000] {create_snowflake_stages.py:315} INFO - 🔍 Role ARN not provided, attempting to get/create it...
[2025-06-04T04:44:51.884+0000] {create_snowflake_stages.py:304} ERROR - ❌ Failed to get/create role ARN: Unable to locate credentials
[2025-06-04T04:44:51.885+0000] {create_snowflake_stages.py:306} WARNING - ⚠️ Using development placeholder role ARN
[2025-06-04T04:44:51.885+0000] {create_snowflake_stages.py:50} INFO - 🔄 Connecting to Snowflake...
[2025-06-04T04:44:51.886+0000] {connection.py:486} INFO - Snowflake Connector for Python Version: 3.15.0, Python Version: 3.10.13, Platform: Linux-6.6.87.1-microsoft-standard-WSL2-x86_64-with-glibc2.36
[2025-06-04T04:44:51.887+0000] {connection.py:1391} INFO - Connecting to GLOBAL Snowflake domain
[2025-06-04T04:44:52.764+0000] {create_snowflake_stages.py:58} INFO - ✅ Connected to Snowflake successfully
[2025-06-04T04:44:52.765+0000] {create_snowflake_stages.py:69} INFO - 🔄 Creating schema LIVE_DATA...
[2025-06-04T04:44:52.981+0000] {create_snowflake_stages.py:74} INFO - ✅ Schema LIVE_DATA ready
[2025-06-04T04:44:52.982+0000] {create_snowflake_stages.py:83} INFO - 🔄 Creating storage integration...
[2025-06-04T04:44:53.132+0000] {create_snowflake_stages.py:100} INFO - ✅ Storage integration created successfully
[2025-06-04T04:44:53.279+0000] {create_snowflake_stages.py:107} INFO -    STORAGE_AWS_IAM_USER_ARN: String
[2025-06-04T04:44:53.280+0000] {create_snowflake_stages.py:107} INFO -    STORAGE_AWS_EXTERNAL_ID: String
[2025-06-04T04:44:53.280+0000] {create_snowflake_stages.py:116} INFO - 🔄 Creating file format...
[2025-06-04T04:44:53.382+0000] {create_snowflake_stages.py:136} INFO - ✅ File format created successfully
[2025-06-04T04:44:53.382+0000] {create_snowflake_stages.py:152} INFO - 🔄 Creating stage S3_LIVE_USERS_STAGE...
[2025-06-04T04:44:53.556+0000] {create_snowflake_stages.py:163} INFO - ✅ Stage S3_LIVE_USERS_STAGE created successfully
[2025-06-04T04:44:53.557+0000] {create_snowflake_stages.py:152} INFO - 🔄 Creating stage S3_LIVE_ORDERS_STAGE...
[2025-06-04T04:44:53.762+0000] {create_snowflake_stages.py:163} INFO - ✅ Stage S3_LIVE_ORDERS_STAGE created successfully
[2025-06-04T04:44:53.763+0000] {create_snowflake_stages.py:152} INFO - 🔄 Creating stage S3_LIVE_EVENTS_STAGE...
[2025-06-04T04:44:53.902+0000] {create_snowflake_stages.py:163} INFO - ✅ Stage S3_LIVE_EVENTS_STAGE created successfully
[2025-06-04T04:44:53.903+0000] {create_snowflake_stages.py:172} INFO - 🔄 Creating external tables...
[2025-06-04T04:44:54.545+0000] {create_snowflake_stages.py:241} ERROR - ❌ Failed to create external tables: 003167 (42601): 01bccbfc-3204-7ffc-0002-4ad60006605e: Error assuming AWS_ROLE:
User: arn:aws:iam::184862803517:user/400z0000-s is not authorized to perform: sts:AssumeRole on resource: arn:aws:iam::365542662955:role/SnowflakeS3AccessRole
[2025-06-04T04:44:54.546+0000] {create_snowflake_stages.py:339} WARNING - ⚠️ External tables creation failed (IAM role issue): 003167 (42601): 01bccbfc-3204-7ffc-0002-4ad60006605e: Error assuming AWS_ROLE:
User: arn:aws:iam::184862803517:user/400z0000-s is not authorized to perform: sts:AssumeRole on resource: arn:aws:iam::365542662955:role/SnowflakeS3AccessRole
[2025-06-04T04:44:54.547+0000] {create_snowflake_stages.py:340} INFO - 📝 Continuing without external tables - stages are ready for dbt
[2025-06-04T04:44:54.548+0000] {create_snowflake_stages.py:347} INFO - ⏭️ Skipping stage tests (external tables not created)
[2025-06-04T04:44:54.608+0000] {create_snowflake_stages.py:353} INFO - 🎉 Snowflake external stages setup completed successfully!
[2025-06-04T04:44:54.610+0000] {create_snowflake_stages.py:422} INFO - 
📋 AIRFLOW SETUP SUMMARY:
[2025-06-04T04:44:54.611+0000] {create_snowflake_stages.py:423} INFO - ✅ Storage Integration: S3_LIVE_INTEGRATION
[2025-06-04T04:44:54.611+0000] {create_snowflake_stages.py:424} INFO - ✅ File Format: CSV_LIVE_FORMAT
[2025-06-04T04:44:54.612+0000] {create_snowflake_stages.py:425} INFO - ✅ External Stages: S3_LIVE_USERS_STAGE, S3_LIVE_ORDERS_STAGE, S3_LIVE_EVENTS_STAGE
[2025-06-04T04:44:54.612+0000] {create_snowflake_stages.py:429} INFO - ⚠️ External Tables: Not created (IAM role configuration needed)
[2025-06-04T04:44:54.613+0000] {create_snowflake_stages.py:430} INFO - ✅ IAM Role ARN: arn:aws:iam::365542662955:role/SnowflakeS3AccessRole
[2025-06-04T04:44:54.614+0000] {simple_etl_dag.py:207} INFO - Stages refreshed successfully: {'storage_integration': 'S3_LIVE_INTEGRATION', 'file_format': 'CSV_LIVE_FORMAT', 'stages': ['S3_LIVE_USERS_STAGE', 'S3_LIVE_ORDERS_STAGE', 'S3_LIVE_EVENTS_STAGE'], 'role_arn': 'arn:aws:iam::365542662955:role/SnowflakeS3AccessRole', 'external_tables_created': False}
[2025-06-04T04:44:54.614+0000] {python.py:201} INFO - Done. Returned value was: Stage refresh successful: {'storage_integration': 'S3_LIVE_INTEGRATION', 'file_format': 'CSV_LIVE_FORMAT', 'stages': ['S3_LIVE_USERS_STAGE', 'S3_LIVE_ORDERS_STAGE', 'S3_LIVE_EVENTS_STAGE'], 'role_arn': 'arn:aws:iam::365542662955:role/SnowflakeS3AccessRole', 'external_tables_created': False}
[2025-06-04T04:44:54.640+0000] {taskinstance.py:1138} INFO - Marking task as SUCCESS. dag_id=simple_etl_pipeline, task_id=refresh_snowflake_stages, execution_date=20250604T044447, start_date=20250604T044450, end_date=20250604T044454
[2025-06-04T04:44:54.695+0000] {local_task_job_runner.py:234} INFO - Task exited with return code 0
[2025-06-04T04:44:54.724+0000] {taskinstance.py:3280} INFO - 1 downstream tasks scheduled from follow-on schedule check
