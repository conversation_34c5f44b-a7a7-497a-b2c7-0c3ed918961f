[2025-06-04T05:13:11.583+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=non-requeueable deps ti=<TaskInstance: simple_etl_pipeline.fix_dbt_packages manual__2025-06-04T05:13:10.079396+00:00 [queued]>
[2025-06-04T05:13:11.597+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=requeueable deps ti=<TaskInstance: simple_etl_pipeline.fix_dbt_packages manual__2025-06-04T05:13:10.079396+00:00 [queued]>
[2025-06-04T05:13:11.598+0000] {taskinstance.py:2170} INFO - Starting attempt 1 of 2
[2025-06-04T05:13:11.615+0000] {taskinstance.py:2191} INFO - Executing <Task(PythonOperator): fix_dbt_packages> on 2025-06-04 05:13:10.079396+00:00
[2025-06-04T05:13:11.621+0000] {standard_task_runner.py:60} INFO - Started process 604 to run task
[2025-06-04T05:13:11.626+0000] {standard_task_runner.py:87} INFO - Running: ['***', 'tasks', 'run', 'simple_etl_pipeline', 'fix_dbt_packages', 'manual__2025-06-04T05:13:10.079396+00:00', '--job-id', '85', '--raw', '--subdir', 'DAGS_FOLDER/simple_etl_dag.py', '--cfg-path', '/tmp/tmpz1zd6ccu']
[2025-06-04T05:13:11.629+0000] {standard_task_runner.py:88} INFO - Job 85: Subtask fix_dbt_packages
[2025-06-04T05:13:12.560+0000] {task_command.py:423} INFO - Running <TaskInstance: simple_etl_pipeline.fix_dbt_packages manual__2025-06-04T05:13:10.079396+00:00 [running]> on host 8fb7c8dc5097
[2025-06-04T05:13:12.640+0000] {taskinstance.py:2480} INFO - Exporting env vars: AIRFLOW_CTX_DAG_OWNER='data-team' AIRFLOW_CTX_DAG_ID='simple_etl_pipeline' AIRFLOW_CTX_TASK_ID='fix_dbt_packages' AIRFLOW_CTX_EXECUTION_DATE='2025-06-04T05:13:10.079396+00:00' AIRFLOW_CTX_TRY_NUMBER='1' AIRFLOW_CTX_DAG_RUN_ID='manual__2025-06-04T05:13:10.079396+00:00'
[2025-06-04T05:13:12.642+0000] {simple_etl_dag.py:48} INFO - Running script: /opt/***/workspace/fix_docker_dbt_packages.py
[2025-06-04T05:13:29.720+0000] {simple_etl_dag.py:67} INFO - Script completed successfully: 
[2025-06-04T05:13:29.721+0000] {python.py:201} INFO - Done. Returned value was: 
[2025-06-04T05:13:29.741+0000] {taskinstance.py:1138} INFO - Marking task as SUCCESS. dag_id=simple_etl_pipeline, task_id=fix_dbt_packages, execution_date=20250604T051310, start_date=20250604T051311, end_date=20250604T051329
[2025-06-04T05:13:29.780+0000] {local_task_job_runner.py:234} INFO - Task exited with return code 0
[2025-06-04T05:13:29.807+0000] {taskinstance.py:3280} INFO - 0 downstream tasks scheduled from follow-on schedule check
