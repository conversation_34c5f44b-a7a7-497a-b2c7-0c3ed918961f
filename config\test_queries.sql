-- Test queries for Snowflake external tables

-- Check users data
SELECT COUNT(*) as user_count FROM EXT_LIVE_USERS;
SELECT * FROM EXT_LIVE_USERS LIMIT 5;

-- Check orders data
SELECT COUNT(*) as order_count FROM EXT_LIVE_ORDERS;
SELECT * FROM EXT_LIVE_ORDERS LIMIT 5;

-- Check events data
SELECT COUNT(*) as event_count FROM EXT_LIVE_EVENTS;
SELECT * FROM EXT_LIVE_EVENTS LIMIT 5;

-- Data freshness check
SELECT 
  MAX(generated_at) as latest_users_data
FROM EXT_LIVE_USERS;

SELECT 
  MAX(generated_at) as latest_orders_data
FROM EXT_LIVE_ORDERS;

SELECT 
  MAX(generated_at) as latest_events_data
FROM EXT_LIVE_EVENTS;