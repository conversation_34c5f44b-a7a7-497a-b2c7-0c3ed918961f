[2025-06-04T05:26:35.380+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=non-requeueable deps ti=<TaskInstance: simple_etl_pipeline.fix_dbt_packages manual__2025-06-04T05:26:33.910798+00:00 [queued]>
[2025-06-04T05:26:35.398+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=requeueable deps ti=<TaskInstance: simple_etl_pipeline.fix_dbt_packages manual__2025-06-04T05:26:33.910798+00:00 [queued]>
[2025-06-04T05:26:35.399+0000] {taskinstance.py:2170} INFO - Starting attempt 1 of 2
[2025-06-04T05:26:35.416+0000] {taskinstance.py:2191} INFO - Executing <Task(PythonOperator): fix_dbt_packages> on 2025-06-04 05:26:33.910798+00:00
[2025-06-04T05:26:35.423+0000] {standard_task_runner.py:60} INFO - Started process 867 to run task
[2025-06-04T05:26:35.434+0000] {standard_task_runner.py:87} INFO - Running: ['***', 'tasks', 'run', 'simple_etl_pipeline', 'fix_dbt_packages', 'manual__2025-06-04T05:26:33.910798+00:00', '--job-id', '95', '--raw', '--subdir', 'DAGS_FOLDER/simple_etl_dag.py', '--cfg-path', '/tmp/tmps91943up']
[2025-06-04T05:26:35.443+0000] {standard_task_runner.py:88} INFO - Job 95: Subtask fix_dbt_packages
[2025-06-04T05:26:35.550+0000] {task_command.py:423} INFO - Running <TaskInstance: simple_etl_pipeline.fix_dbt_packages manual__2025-06-04T05:26:33.910798+00:00 [running]> on host 8fb7c8dc5097
[2025-06-04T05:26:35.663+0000] {taskinstance.py:2480} INFO - Exporting env vars: AIRFLOW_CTX_DAG_OWNER='data-team' AIRFLOW_CTX_DAG_ID='simple_etl_pipeline' AIRFLOW_CTX_TASK_ID='fix_dbt_packages' AIRFLOW_CTX_EXECUTION_DATE='2025-06-04T05:26:33.910798+00:00' AIRFLOW_CTX_TRY_NUMBER='1' AIRFLOW_CTX_DAG_RUN_ID='manual__2025-06-04T05:26:33.910798+00:00'
[2025-06-04T05:26:35.666+0000] {simple_etl_dag.py:48} INFO - Running script: /opt/***/workspace/fix_docker_dbt_packages.py
[2025-06-04T05:26:56.144+0000] {simple_etl_dag.py:67} INFO - Script completed successfully: 
[2025-06-04T05:26:56.144+0000] {python.py:201} INFO - Done. Returned value was: 
[2025-06-04T05:26:56.209+0000] {taskinstance.py:1138} INFO - Marking task as SUCCESS. dag_id=simple_etl_pipeline, task_id=fix_dbt_packages, execution_date=20250604T052633, start_date=20250604T052635, end_date=20250604T052656
[2025-06-04T05:26:56.270+0000] {local_task_job_runner.py:234} INFO - Task exited with return code 0
[2025-06-04T05:26:56.296+0000] {taskinstance.py:3280} INFO - 0 downstream tasks scheduled from follow-on schedule check
