# Hands-On Tutorial: <PERSON><PERSON> & Snowflake for Azure/Databricks Experts

## 🎯 Learning Objectives
By the end of this tutorial, you'll understand:
- How S3 compares to Azure Data Lake Storage
- How Snowflake Virtual Warehouses compare to Databricks clusters
- How External Tables work vs Databricks mount points
- How IAM roles work vs Azure Service Principals

---

## 📚 Exercise 1: Understanding S3 vs Azure Data Lake

### What You Know (Azure):
```python
# Azure Data Lake Storage Gen2
from azure.storage.filedatalake import DataLakeServiceClient

# Mount in Databricks
dbutils.fs.mount(
  source = "abfss://<EMAIL>/",
  mount_point = "/mnt/datalake",
  extra_configs = configs
)

# Read data
df = spark.read.csv("/mnt/datalake/data/users.csv")
```

### AWS Equivalent:
```python
# Amazon S3
import boto3

# No mounting needed - direct access
s3_client = boto3.client('s3')

# List objects
response = s3_client.list_objects_v2(
    Bucket='lake-loader-input-365542662955-20250525-001439',
    Prefix='live-data/users/'
)

# Upload file
s3_client.upload_file('local_file.csv', 'bucket-name', 'path/file.csv')
```

### 🔬 Hands-On Exercise:
1. **Run our data generator**:
   ```bash
   python daily_data_generator.py
   ```

2. **Explore in AWS Console**:
   - Go to **AWS Console** → **S3**
   - Find bucket: `lake-loader-input-365542662955-20250525-001439`
   - Navigate to: `live-data/users/`
   - Click on a CSV file → **Download** → Open in Excel

3. **Compare with Azure**:
   - **Azure**: Container → Virtual Directory → Blob
   - **AWS**: Bucket → Prefix (folder-like) → Object

4. **Try AWS CLI**:
   ```bash
   # List files (like az storage blob list)
   aws s3 ls s3://lake-loader-input-365542662955-20250525-001439/live-data/users/
   
   # Download file (like az storage blob download)
   aws s3 cp s3://lake-loader-input-365542662955-20250525-001439/live-data/users/daily_users_20250125.csv ./downloaded_users.csv
   ```

### 💡 Key Insights:
- **S3 has no real folders** - just key names with "/" (like Azure flat namespace)
- **No mounting required** - applications access S3 directly via APIs
- **Lifecycle policies** work like Azure storage tiers (Hot/Cool/Archive)

---

## 📚 Exercise 2: IAM Roles vs Azure Service Principals

### What You Know (Azure):
```bash
# Create Service Principal
az ad sp create-for-rbac --name "databricks-sp"

# Assign permissions
az role assignment create \
  --assignee <service-principal-id> \
  --role "Storage Blob Data Contributor" \
  --scope "/subscriptions/<sub>/resourceGroups/<rg>/providers/Microsoft.Storage/storageAccounts/<account>"
```

### AWS Equivalent:
```bash
# Create IAM role
aws iam create-role \
  --role-name SnowflakeS3AccessRole \
  --assume-role-policy-document file://trust-policy.json

# Attach policy
aws iam attach-role-policy \
  --role-name SnowflakeS3AccessRole \
  --policy-arn arn:aws:iam::account:policy/SnowflakeS3AccessPolicy
```

### 🔬 Hands-On Exercise:
1. **Run our AWS setup**:
   ```bash
   python setup_aws_credentials.py
   ```

2. **Explore in AWS Console**:
   - Go to **IAM** → **Roles** → `SnowflakeS3AccessRole`
   - Click **Trust relationships** tab
   - See who can "assume" this role (Snowflake's AWS account)
   - Click **Permissions** tab
   - See what this role can do (read S3 buckets)

3. **Compare the JSON**:
   ```json
   // Trust Policy (who can use this role)
   {
     "Principal": {
       "AWS": "arn:aws:iam::************:user/k7m2-s-v2st2732"  // Snowflake's identity
     }
   }
   
   // Permissions Policy (what this role can do)
   {
     "Action": ["s3:GetObject", "s3:ListBucket"],
     "Resource": ["arn:aws:s3:::bucket-name/*"]
   }
   ```

### 💡 Key Insights:
- **Azure SP**: Identity + Direct permissions
- **AWS Role**: Identity + Assumable permissions + Trust policy
- **Azure**: Resource Groups for scope
- **AWS**: ARNs (Amazon Resource Names) for scope

---

## 📚 Exercise 3: Snowflake Virtual Warehouses vs Databricks Clusters

### What You Know (Databricks):
```python
# Cluster configuration
cluster_config = {
    "cluster_name": "my-cluster",
    "spark_version": "11.3.x-scala2.12",
    "node_type_id": "Standard_DS3_v2",
    "num_workers": 2,
    "autoscale": {
        "min_workers": 1,
        "max_workers": 8
    }
}
```

### Snowflake Equivalent:
```sql
-- Create/Configure warehouse
CREATE WAREHOUSE COMPUTE_WH WITH
  WAREHOUSE_SIZE = 'X-SMALL'
  AUTO_SUSPEND = 600  -- 10 minutes
  AUTO_RESUME = TRUE
  MAX_CLUSTER_COUNT = 1;

-- Use warehouse
USE WAREHOUSE COMPUTE_WH;

-- Scale up for heavy workload
ALTER WAREHOUSE COMPUTE_WH SET WAREHOUSE_SIZE = 'LARGE';
```

### 🔬 Hands-On Exercise:
1. **Connect to Snowflake**:
   ```bash
   python test_daily_pipeline.py --test snowflake
   ```

2. **Explore in Snowflake UI**:
   - Go to **Snowflake Web UI** → **Admin** → **Warehouses**
   - Click on `COMPUTE_WH`
   - See **Size**, **Auto-suspend**, **Auto-resume** settings
   - Click **Configure** → Try changing size to **SMALL**

3. **Monitor Usage**:
   - Go to **Account** → **Usage**
   - See **Warehouse** usage in credits
   - Compare different warehouse sizes

4. **Test Performance**:
   ```sql
   -- In Snowflake worksheet
   USE WAREHOUSE COMPUTE_WH;
   
   -- Time a query
   SELECT COUNT(*) FROM EXT_LIVE_USERS;
   
   -- Scale up and test again
   ALTER WAREHOUSE COMPUTE_WH SET WAREHOUSE_SIZE = 'MEDIUM';
   SELECT COUNT(*) FROM EXT_LIVE_USERS;
   ```

### 💡 Key Insights:
- **Databricks**: Choose node types, manage driver/workers
- **Snowflake**: Choose warehouse size, automatic management
- **Databricks**: Cluster startup time (2-5 minutes)
- **Snowflake**: Instant resume from suspended state
- **Databricks**: Pay for nodes even when idle
- **Snowflake**: Pay only when warehouse is running

---

## 📚 Exercise 4: External Tables vs Databricks Mount Points

### What You Know (Databricks):
```python
# Mount external storage
dbutils.fs.mount(
  source = "abfss://<EMAIL>/data",
  mount_point = "/mnt/external-data"
)

# Create external table
spark.sql("""
  CREATE TABLE external_users
  USING CSV
  OPTIONS (path "/mnt/external-data/users.csv", header "true")
""")

# Query external data
df = spark.sql("SELECT * FROM external_users")
```

### Snowflake Equivalent:
```sql
-- Create storage integration (like mount point)
CREATE STORAGE INTEGRATION S3_INTEGRATION
  TYPE = EXTERNAL_STAGE
  STORAGE_PROVIDER = 'S3'
  STORAGE_AWS_ROLE_ARN = 'arn:aws:iam::account:role/SnowflakeRole'
  STORAGE_ALLOWED_LOCATIONS = ('s3://bucket/path/');

-- Create external stage (like external location)
CREATE STAGE S3_STAGE
  STORAGE_INTEGRATION = S3_INTEGRATION
  URL = 's3://bucket/users/'
  FILE_FORMAT = (TYPE = 'CSV' SKIP_HEADER = 1);

-- Create external table
CREATE EXTERNAL TABLE EXT_USERS (
  id STRING AS (value:c1::STRING),
  name STRING AS (value:c2::STRING)
)
WITH LOCATION = @S3_STAGE
AUTO_REFRESH = TRUE;
```

### 🔬 Hands-On Exercise:
1. **Set up external tables**:
   ```bash
   # Get the role ARN from AWS setup
   aws iam get-role --role-name SnowflakeS3AccessRole --query 'Role.Arn'
   
   # Create Snowflake stages
   python create_snowflake_stages.py --role-arn "YOUR_ROLE_ARN"
   ```

2. **Explore in Snowflake**:
   - Go to **Data** → **Databases** → `MYDB` → `LIVE_DATA`
   - See **Stages**: `S3_LIVE_USERS_STAGE`, `S3_LIVE_ORDERS_STAGE`
   - See **Tables**: `EXT_LIVE_USERS`, `EXT_LIVE_ORDERS`

3. **Test external table queries**:
   ```sql
   -- In Snowflake worksheet
   USE DATABASE MYDB;
   USE SCHEMA LIVE_DATA;
   
   -- Query external data (like Databricks external table)
   SELECT COUNT(*) FROM EXT_LIVE_USERS;
   SELECT * FROM EXT_LIVE_USERS LIMIT 5;
   
   -- Check file metadata
   SELECT metadata$filename, metadata$file_row_number 
   FROM EXT_LIVE_USERS LIMIT 10;
   ```

4. **Browse stage files**:
   ```sql
   -- List files in stage (like dbutils.fs.ls)
   LIST @S3_LIVE_USERS_STAGE;
   
   -- Query files directly
   SELECT $1, $2, $3 FROM @S3_LIVE_USERS_STAGE/daily_users_20250125.csv;
   ```

### 💡 Key Insights:
- **Databricks**: Mount → External table → Query
- **Snowflake**: Storage integration → Stage → External table → Query
- **Databricks**: File-based partitioning
- **Snowflake**: Auto-refresh external tables
- **Both**: Query external data without loading

---

## 📚 Exercise 5: End-to-End ETL Pipeline

### What You Know (Databricks):
```python
# Databricks workflow
# 1. Read from mounted storage
df = spark.read.csv("/mnt/data/users.csv")

# 2. Transform data
df_clean = df.filter(df.age > 0).withColumn("age_group", 
  when(df.age < 25, "young").otherwise("adult"))

# 3. Write to Delta Lake
df_clean.write.format("delta").mode("overwrite").save("/mnt/output/users_clean")

# 4. Create table
spark.sql("CREATE TABLE users_clean USING DELTA LOCATION '/mnt/output/users_clean'")
```

### Our AWS/Snowflake Pipeline:
```bash
# 1. Generate data to S3
python daily_data_generator.py

# 2. External tables auto-refresh from S3
# 3. dbt transforms data in Snowflake
cd dbt_live && dbt run

# 4. Query final analytics tables
```

### 🔬 Hands-On Exercise:
1. **Run complete pipeline**:
   ```bash
   python run_end2end_pipeline.py --save-results
   ```

2. **Monitor each step**:
   - **Step 1**: Check S3 for new files
   - **Step 2**: Verify files in AWS Console
   - **Step 3**: See external table refresh in Snowflake
   - **Step 4**: Watch dbt models execute
   - **Step 5**: Query final results

3. **Compare transformations**:
   ```sql
   -- Raw data (like Databricks bronze)
   SELECT * FROM EXT_LIVE_USERS LIMIT 5;
   
   -- Cleaned data (like Databricks silver)
   SELECT * FROM STG_USERS LIMIT 5;
   
   -- Analytics data (like Databricks gold)
   SELECT * FROM DIM_USERS LIMIT 5;
   ```

4. **Explore dbt models**:
   ```bash
   cd dbt_live

   # See model lineage (like Databricks notebook dependencies)
   dbt docs generate
   dbt docs serve --port 8088  # Using port 8088 to avoid conflict with Airflow (8080)

   # Or use the convenient script:
   # .\serve_docs.ps1  (PowerShell)
   # serve_docs.bat    (Command Prompt)
   ```

### 💡 Key Insights:
- **Databricks**: Notebook-based transformations
- **Snowflake + dbt**: SQL-based transformations
- **Databricks**: Delta Lake for ACID transactions
- **Snowflake**: Built-in ACID compliance
- **Both**: Bronze/Silver/Gold medallion architecture

---

## 🎓 Summary: Key Concept Mappings

| Azure/Databricks | AWS/Snowflake | Key Difference |
|------------------|---------------|----------------|
| **ADLS Gen2** | **S3** | No mounting needed in S3 |
| **Service Principal** | **IAM Role** | Roles are assumable identities |
| **Databricks Cluster** | **Snowflake Warehouse** | Warehouses auto-suspend/resume |
| **Mount Point** | **External Stage** | Stages are more flexible |
| **Delta Lake** | **Snowflake Tables** | Built-in time travel and ACID |
| **Databricks Notebooks** | **dbt Models** | SQL-first approach |
| **Azure Monitor** | **CloudWatch** | Different metrics and dashboards |

## 🚀 Next Steps

1. **Practice**: Run our pipeline multiple times
2. **Experiment**: Modify warehouse sizes, add new dbt models
3. **Scale**: Increase data volumes, try different file formats
4. **Monitor**: Watch costs in AWS/Snowflake consoles
5. **Optimize**: Tune warehouse sizes, external table refresh rates

You now have hands-on experience with AWS S3, IAM, Snowflake warehouses, external tables, and dbt - the core components of modern cloud data platforms! 🎉
