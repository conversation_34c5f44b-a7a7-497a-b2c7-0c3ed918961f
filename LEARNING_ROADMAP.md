# Learning Roadmap: Azure/Databricks Expert → AWS/Snowflake Proficiency

## 🎯 Learning Path Overview

This roadmap takes you from Azure/Databricks expertise to AWS/Snowflake proficiency using our hands-on pipeline.

---

## 📅 Week 1: AWS Fundamentals

### Day 1-2: S3 Deep Dive
**Goal**: Understand S3 vs Azure Data Lake Storage

#### Morning (Theory):
- Read: [AWS S3 User Guide](https://docs.aws.amazon.com/s3/)
- Compare: S3 vs ADLS Gen2 architecture
- Understand: Buckets, objects, prefixes, lifecycle policies

#### Afternoon (Hands-On):
```bash
# Run our data generator
python daily_data_generator.py --days 3

# Explore AWS Console
# Navigate: S3 → Your bucket → Browse files
# Try: Download, upload, delete operations

# Practice AWS CLI
aws s3 ls s3://your-bucket/ --recursive
aws s3 cp local-file.csv s3://your-bucket/test/
aws s3 sync ./local-folder s3://your-bucket/sync-test/
```

#### Evening (Reflection):
- **Journal**: How does S3 prefix-based organization compare to ADLS hierarchical namespace?
- **Practice**: Create your own S3 bucket and upload test files

### Day 3-4: IAM Deep Dive
**Goal**: Master IAM roles vs Azure Service Principals

#### Morning (Theory):
- Read: [AWS IAM User Guide](https://docs.aws.amazon.com/iam/)
- Compare: IAM roles vs Azure Service Principals
- Understand: Trust policies, permission policies, role assumption

#### Afternoon (Hands-On):
```bash
# Run our AWS setup
python setup_aws_credentials.py

# Explore AWS Console
# Navigate: IAM → Roles → SnowflakeS3AccessRole
# Examine: Trust relationships, permissions, policies

# Practice AWS CLI
aws iam get-role --role-name SnowflakeS3AccessRole
aws iam list-attached-role-policies --role-name SnowflakeS3AccessRole
aws sts assume-role --role-arn "arn:aws:iam::account:role/SnowflakeS3AccessRole" --role-session-name test
```

#### Evening (Reflection):
- **Journal**: How does IAM role assumption differ from Azure Service Principal authentication?
- **Practice**: Create a test IAM role with S3 read-only permissions

### Day 5: AWS Integration Testing
**Goal**: Validate AWS knowledge with real scenarios

#### All Day (Hands-On):
```bash
# Test complete AWS setup
python test_daily_pipeline.py --test s3
python test_daily_pipeline.py --test aws

# Troubleshoot common issues
# Practice: Fix intentional permission errors
# Practice: Recreate IAM roles from scratch
```

#### Evening (Assessment):
- **Quiz**: Can you explain the difference between IAM users, roles, and policies?
- **Challenge**: Set up S3 cross-region replication (bonus)

---

## 📅 Week 2: Snowflake Fundamentals

### Day 1-2: Snowflake Architecture
**Goal**: Understand Snowflake vs Databricks architecture

#### Morning (Theory):
- Read: [Snowflake Architecture Overview](https://docs.snowflake.com/en/user-guide/intro-key-concepts)
- Compare: Snowflake virtual warehouses vs Databricks clusters
- Understand: Separation of storage and compute

#### Afternoon (Hands-On):
```bash
# Test Snowflake connection
python test_daily_pipeline.py --test snowflake

# Explore Snowflake UI
# Navigate: Admin → Warehouses → COMPUTE_WH
# Try: Resize warehouse, suspend/resume, monitor usage
```

```sql
-- Practice in Snowflake worksheet
USE WAREHOUSE COMPUTE_WH;
SHOW WAREHOUSES;

-- Test different warehouse sizes
ALTER WAREHOUSE COMPUTE_WH SET WAREHOUSE_SIZE = 'SMALL';
SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES; -- Time this query

ALTER WAREHOUSE COMPUTE_WH SET WAREHOUSE_SIZE = 'MEDIUM';
SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES; -- Compare timing
```

#### Evening (Reflection):
- **Journal**: How does Snowflake's instant resume compare to Databricks cluster startup?
- **Practice**: Calculate cost differences between warehouse sizes

### Day 3-4: External Tables & Stages
**Goal**: Master external data access in Snowflake

#### Morning (Theory):
- Read: [Snowflake External Tables](https://docs.snowflake.com/en/user-guide/tables-external)
- Compare: External tables vs Databricks mount points
- Understand: Storage integrations, stages, file formats

#### Afternoon (Hands-On):
```bash
# Set up external tables
python create_snowflake_stages.py --role-arn "YOUR_ROLE_ARN"

# Explore in Snowflake
# Navigate: Data → Databases → MYDB → LIVE_DATA → Stages
# Navigate: Data → Databases → MYDB → LIVE_DATA → Tables
```

```sql
-- Practice external table queries
USE DATABASE MYDB;
USE SCHEMA LIVE_DATA;

-- List files in stage
LIST @S3_LIVE_USERS_STAGE;

-- Query external table
SELECT COUNT(*) FROM EXT_LIVE_USERS;
SELECT * FROM EXT_LIVE_USERS LIMIT 10;

-- Check metadata
SELECT metadata$filename, metadata$file_row_number, * 
FROM EXT_LIVE_USERS LIMIT 5;

-- Refresh external table
ALTER EXTERNAL TABLE EXT_LIVE_USERS REFRESH;
```

#### Evening (Reflection):
- **Journal**: How do Snowflake stages compare to Databricks external locations?
- **Practice**: Create your own external table pointing to a different S3 path

### Day 5: Snowflake SQL Features
**Goal**: Learn Snowflake-specific SQL extensions

#### Morning (Theory):
- Read: [Snowflake SQL Reference](https://docs.snowflake.com/en/sql-reference)
- Learn: Semi-structured data, time travel, cloning
- Compare: Snowflake SQL vs Spark SQL

#### Afternoon (Hands-On):
```sql
-- Practice Snowflake SQL features
-- Semi-structured data (JSON-like access)
SELECT value:c1::STRING as user_id FROM @S3_LIVE_USERS_STAGE;

-- Time travel (if you have historical data)
SELECT COUNT(*) FROM EXT_LIVE_USERS AT (TIMESTAMP => CURRENT_TIMESTAMP - INTERVAL '1 HOUR');

-- Cloning (instant table copy)
CREATE TABLE EXT_LIVE_USERS_BACKUP CLONE EXT_LIVE_USERS;

-- Information schema
SELECT * FROM INFORMATION_SCHEMA.EXTERNAL_TABLES 
WHERE TABLE_SCHEMA = 'LIVE_DATA';
```

#### Evening (Assessment):
- **Quiz**: Can you explain the difference between stages and external tables?
- **Challenge**: Set up a new external table for a different file format (JSON, Parquet)

---

## 📅 Week 3: ETL Pipeline Mastery

### Day 1-2: dbt Integration
**Goal**: Understand dbt vs Databricks notebooks

#### Morning (Theory):
- Read: [dbt Documentation](https://docs.getdbt.com/)
- Compare: dbt models vs Databricks notebooks
- Understand: SQL-first transformations, model dependencies

#### Afternoon (Hands-On):
```bash
cd dbt_live

# Explore dbt project structure
ls -la models/
cat dbt_project.yml
cat profiles.yml

# Test dbt connection
dbt debug

# Run specific models
dbt run --models staging
dbt run --models marts

# Generate documentation
dbt docs generate
dbt docs serve --port 8088  # Using port 8088 to avoid conflict with Airflow (8080)
```

#### Evening (Reflection):
- **Journal**: How does dbt's SQL-first approach compare to Databricks' notebook-based transformations?
- **Practice**: Create a new dbt model that aggregates user behavior

### Day 3-4: End-to-End Pipeline
**Goal**: Master the complete data pipeline

#### Morning (Theory):
- Review: Data pipeline architecture patterns
- Compare: Batch vs streaming processing
- Understand: Data quality and monitoring

#### Afternoon (Hands-On):
```bash
# Run complete pipeline multiple times
python run_end2end_pipeline.py
python run_end2end_pipeline.py --date 2025-01-20
python run_end2end_pipeline.py --save-results

# Monitor each step
# Watch: S3 uploads, Snowflake refreshes, dbt transformations

# Analyze results
cat pipeline_results_*.yaml
```

#### Evening (Reflection):
- **Journal**: How does this pipeline compare to Azure Data Factory + Databricks workflows?
- **Practice**: Modify the pipeline to add data validation steps

### Day 5: Performance Optimization
**Goal**: Optimize pipeline performance and costs

#### All Day (Hands-On):
```bash
# Test different configurations
# Try: Different warehouse sizes
# Try: Different batch sizes in data generation
# Try: Parallel processing

# Monitor costs
# Check: AWS billing dashboard
# Check: Snowflake credit usage
```

```sql
-- Optimize Snowflake performance
-- Test warehouse scaling
ALTER WAREHOUSE COMPUTE_WH SET WAREHOUSE_SIZE = 'LARGE';

-- Monitor query performance
SELECT * FROM SNOWFLAKE.ACCOUNT_USAGE.QUERY_HISTORY 
WHERE START_TIME >= CURRENT_TIMESTAMP - INTERVAL '1 DAY'
ORDER BY TOTAL_ELAPSED_TIME DESC;

-- Check storage usage
SELECT * FROM SNOWFLAKE.ACCOUNT_USAGE.STORAGE_USAGE 
WHERE USAGE_DATE >= CURRENT_DATE - 7;
```

#### Evening (Assessment):
- **Challenge**: Optimize the pipeline to run in under 10 minutes
- **Challenge**: Reduce costs by 50% while maintaining performance

---

## 📅 Week 4: Advanced Topics & Real-World Application

### Day 1-2: Production Deployment
**Goal**: Deploy pipeline for production use

#### Tasks:
- Set up automated scheduling (cron/Airflow)
- Implement monitoring and alerting
- Add error handling and retry logic
- Set up data quality checks

### Day 3-4: Advanced Features
**Goal**: Explore advanced AWS/Snowflake features

#### AWS Advanced:
- S3 event notifications
- Lambda functions for automation
- CloudWatch monitoring
- Cost optimization strategies

#### Snowflake Advanced:
- Snowpipe for streaming ingestion
- Streams and tasks for CDC
- Secure data sharing
- Multi-cluster warehouses

### Day 5: Certification Preparation
**Goal**: Prepare for AWS/Snowflake certifications

#### Study Materials:
- AWS Certified Solutions Architect Associate
- Snowflake SnowPro Core Certification
- Practice exams and hands-on labs

---

## 🎯 Learning Milestones

### Week 1 Milestone:
✅ Can navigate AWS Console confidently  
✅ Understands IAM roles and S3 permissions  
✅ Can troubleshoot basic AWS connectivity issues

### Week 2 Milestone:
✅ Can create and manage Snowflake warehouses  
✅ Understands external tables and stages  
✅ Can write efficient Snowflake SQL queries

### Week 3 Milestone:
✅ Can build and modify dbt models  
✅ Understands end-to-end data pipeline architecture  
✅ Can optimize pipeline performance

### Week 4 Milestone:
✅ Can deploy production-ready data pipelines  
✅ Understands advanced AWS/Snowflake features  
✅ Ready for certification exams

---

## 📚 Recommended Resources

### Books:
- "Learning Amazon Web Services" by Mark Wilkins
- "Snowflake: The Definitive Guide" by Joyce Kay Avila

### Online Courses:
- AWS Training and Certification
- Snowflake University
- dbt Learn

### Practice Platforms:
- AWS Free Tier
- Snowflake Trial Account
- dbt Cloud Developer Plan

### Communities:
- AWS User Groups
- Snowflake Community
- dbt Community Slack

---

## 🏆 Success Metrics

By the end of this roadmap, you should be able to:

1. **Design** scalable data architectures using AWS and Snowflake
2. **Implement** production-ready ETL pipelines
3. **Optimize** performance and costs
4. **Troubleshoot** common issues independently
5. **Mentor** others transitioning from Azure/Databricks

**Estimated Time Investment**: 40-60 hours over 4 weeks  
**Outcome**: AWS/Snowflake proficiency equivalent to your current Azure/Databricks expertise

Good luck on your learning journey! 🚀
