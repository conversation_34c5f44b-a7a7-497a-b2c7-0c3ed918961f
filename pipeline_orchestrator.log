2025-06-04 07:48:02,771 - ERROR - Error: Traceback (most recent call last):
  File "G:\github\S3 dbt-snowflake c360\experiment2\show_etl_health_dashboard.py", line 199, in <module>
    display_dashboard()
  File "G:\github\S3 dbt-snowflake c360\experiment2\show_etl_health_dashboard.py", line 129, in display_dashboard
    print("\U0001f3e5 ETL HEALTH DASHBOARD")
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f3e5' in position 0: illegal multibyte sequence

2025-06-04 07:48:04,628 - ERROR - Error: Traceback (most recent call last):
  File "G:\github\S3 dbt-snowflake c360\experiment2\monitor_pipeline_alerts.py", line 254, in <module>
    main()
  File "G:\github\S3 dbt-snowflake c360\experiment2\monitor_pipeline_alerts.py", line 232, in main
    print("\U0001f50d Checking for ETL pipeline alerts...")
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f50d' in position 0: illegal multibyte sequence

