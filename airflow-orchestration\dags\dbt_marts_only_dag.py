"""
dbt Marts Only DAG
Minimal DAG focused solely on running dbt marts models
Simple, fast execution of business logic layer
"""

from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.operators.dummy import DummyOperator
from airflow.utils.dates import days_ago
import os
import subprocess
import logging

# Default arguments for the DAG
default_args = {
    'owner': 'data-team',
    'depends_on_past': False,
    'start_date': days_ago(1),
    'email_on_failure': False,
    'email_on_retry': False,
    'retries': 1,
    'retry_delay': timedelta(minutes=2),
    'catchup': False,
}

# DAG definition
dag = DAG(
    'dbt_marts_only',
    default_args=default_args,
    description='dbt Marts Only - Run Business Logic Models',
    schedule_interval='0 3 * * *',  # Daily at 3 AM (after main pipeline)
    max_active_runs=1,
    tags=['dbt', 'marts', 'business-logic'],
    params={
        'run_tests': True,
        'validate_results': True,
    }
)

def get_workspace_path():
    """Get the workspace path for dbt project"""
    return "/opt/airflow/workspace"

def get_dbt_project_path():
    """Get the dbt project path"""
    workspace_path = get_workspace_path()
    return os.path.join(workspace_path, "dbt_live")

def run_dbt_command(command_type, models=None, extra_args=None, **context):
    """
    Run a dbt command with error handling
    
    Args:
        command_type (str): The dbt command to run (run, test, etc.)
        models (str): Optional model selection
        extra_args (list): Additional command line arguments
    """
    dbt_path = get_dbt_project_path()
    
    logging.info(f"🚀 Running dbt {command_type}")
    
    if not os.path.exists(dbt_path):
        logging.error(f"❌ dbt project not found: {dbt_path}")
        raise FileNotFoundError(f"dbt project not found: {dbt_path}")
    
    # Change to dbt directory
    original_cwd = os.getcwd()
    os.chdir(dbt_path)
    
    try:
        # Build command
        command_parts = ["dbt", command_type]
        
        if models:
            command_parts.extend(["--models", models])
        
        if extra_args:
            command_parts.extend(extra_args)
        
        logging.info(f"📋 Executing: {' '.join(command_parts)}")
        
        # Run command with timeout
        result = subprocess.run(
            command_parts,
            capture_output=True,
            text=True,
            cwd=dbt_path,
            timeout=300  # 5 minute timeout for marts
        )
        
        # Log output
        if result.stdout:
            logging.info(f"📄 stdout: {result.stdout}")
        
        if result.stderr:
            logging.warning(f"⚠️ stderr: {result.stderr}")
        
        if result.returncode == 0:
            logging.info(f"✅ dbt {command_type} completed successfully")
            return {
                'success': True,
                'command': ' '.join(command_parts),
                'stdout': result.stdout,
                'stderr': result.stderr
            }
        else:
            error_msg = f"dbt {command_type} failed with return code {result.returncode}"
            logging.error(f"❌ {error_msg}")
            logging.error(f"❌ Error output: {result.stderr}")
            raise Exception(f"{error_msg}: {result.stderr}")
    
    except subprocess.TimeoutExpired:
        error_msg = f"dbt {command_type} timed out after 5 minutes"
        logging.error(f"❌ {error_msg}")
        raise Exception(error_msg)
    
    except Exception as e:
        logging.error(f"❌ dbt {command_type} failed: {e}")
        raise Exception(f"dbt {command_type} failed: {e}")
    
    finally:
        os.chdir(original_cwd)

# Task functions
def dbt_run_marts(**context):
    """Run dbt marts models only"""
    logging.info("🏪 Running dbt marts models...")
    return run_dbt_command('run', models='marts', **context)

def dbt_test_marts(**context):
    """Run tests for marts models only"""
    logging.info("🧪 Running marts model tests...")
    return run_dbt_command('test', models='marts', **context)

def validate_marts_results(**context):
    """Validate marts execution results"""
    logging.info("✅ Validating marts results...")
    
    dbt_path = get_dbt_project_path()
    target_path = os.path.join(dbt_path, "target")
    
    if not os.path.exists(target_path):
        raise Exception("dbt target directory not found - marts may not have run successfully")
    
    # Check for run_results.json
    run_results_path = os.path.join(target_path, "run_results.json")
    if os.path.exists(run_results_path):
        logging.info("✅ Found run_results.json - dbt run completed")
        
        # Try to read and validate results
        try:
            import json
            with open(run_results_path, 'r') as f:
                results = json.load(f)
            
            # Count successful models
            successful_models = [r for r in results.get('results', []) if r.get('status') == 'success']
            failed_models = [r for r in results.get('results', []) if r.get('status') == 'error']
            
            logging.info(f"📊 Marts execution summary:")
            logging.info(f"   ✅ Successful models: {len(successful_models)}")
            logging.info(f"   ❌ Failed models: {len(failed_models)}")
            
            if failed_models:
                failed_names = [r.get('unique_id', 'unknown') for r in failed_models]
                logging.error(f"❌ Failed models: {failed_names}")
                raise Exception(f"Some marts models failed: {failed_names}")
            
        except Exception as e:
            logging.warning(f"⚠️ Could not parse run results: {e}")
    else:
        logging.warning("⚠️ run_results.json not found")
    
    logging.info("✅ Marts validation completed")
    return "Marts validation successful"

# =============================================================================
# TASK DEFINITIONS
# =============================================================================

# Start task
start_task = DummyOperator(
    task_id='start_marts_pipeline',
    dag=dag,
)

# Main marts execution task
run_marts_task = PythonOperator(
    task_id='dbt_run_marts',
    python_callable=dbt_run_marts,
    dag=dag,
)

# Test marts task
test_marts_task = PythonOperator(
    task_id='dbt_test_marts',
    python_callable=dbt_test_marts,
    dag=dag,
)

# Validation task
validate_task = PythonOperator(
    task_id='validate_marts_results',
    python_callable=validate_marts_results,
    dag=dag,
)

# End task
end_task = DummyOperator(
    task_id='end_marts_pipeline',
    dag=dag,
)

# =============================================================================
# TASK DEPENDENCIES
# =============================================================================

# Simple linear flow
start_task >> run_marts_task >> test_marts_task >> validate_task >> end_task
