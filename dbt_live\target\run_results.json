{"metadata": {"dbt_schema_version": "https://schemas.getdbt.com/dbt/run-results/v6.json", "dbt_version": "1.9.6", "generated_at": "2025-06-04T10:12:35.986847Z", "invocation_id": "0d5b25e0-2dcb-40bb-bf39-1acec9f0fbc5", "env": {}}, "results": [{"status": "error", "timing": [{"name": "compile", "started_at": "2025-06-04T10:12:34.785935Z", "completed_at": "2025-06-04T10:12:34.840336Z"}, {"name": "execute", "started_at": "2025-06-04T10:12:34.893860Z", "completed_at": "2025-06-04T10:12:35.267008Z"}], "thread_id": "Thread-2 (worker)", "execution_time": 0.5250465869903564, "adapter_response": {}, "message": "Database Error in model dim_users_scd2 (models/marts/dim_users_scd2.sql)\n  091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.", "failures": null, "unique_id": "model.live_c360.dim_users_scd2", "compiled": true, "compiled_code": "\n\nwith \n\n-- Get the latest user data from staging\nsource_data as (\n    select \n        user_id,\n        firstname,\n        lastname,\n        email_hash,\n        address,\n        acquisition_channel as canal,\n        country,\n        gender,\n        age_group,\n        creation_date,\n        last_activity_date,\n        is_churned as churn,\n        batch_id,\n        current_timestamp() as dbt_updated_at,\n        -- Track when this record was processed\n        current_timestamp() as processed_at\n    from MYDB.LIVE_DATA.stg_users\n    where user_id is not null\n    qualify row_number() over (partition by user_id order by generated_at desc) = 1\n    \n    \n    -- Only process users that are new or have changed since the last run\n    and user_id in (\n        select user_id\n        from MYDB.LIVE_DATA.stg_users\n        where generated_at > (\n            select coalesce(max(dbt_valid_from), '1900-01-01'::timestamp)\n            from MYDB.LIVE_DATA.dim_users_scd2\n            where is_current_version = true\n        )\n    )\n    \n),\n\n-- Get the current version of each user from the dimension\nexisting_dimension as (\n    \n    select\n        user_sk,\n        user_id,\n        firstname,\n        lastname,\n        email,\n        address,\n        acquisition_channel,\n        country,\n        gender,\n        age_group,\n        creation_date,\n        last_activity_date,\n        churn,\n        batch_id,\n        dbt_valid_from,\n        dbt_valid_to,\n        is_current_version,\n        dbt_change_type,\n        current_timestamp() as processed_at  -- Add processed_at for consistency\n    from MYDB.LIVE_DATA.dim_users_scd2\n    where is_current_version = true\n    \n),\n\n-- Identify new and changed records\nchanges_to_apply as (\n    select\n        s.*,\n        case\n            when e.user_sk is null then 'insert'\n            when e.firstname != s.firstname \n              or e.lastname != s.lastname\n              or e.email != s.email_hash\n              or e.address != s.address\n              or e.acquisition_channel != s.canal\n              or e.country != s.country\n              or e.gender != s.gender\n              or e.age_group != s.age_group\n              or e.churn != s.churn then 'update'\n            else 'no_change'\n        end as change_type\n    from source_data s\n    left join existing_dimension e \n        on s.user_id = e.user_id\n)\n\n-- For initial load, just insert all records\n\n\n-- For incremental loads, handle inserts and updates\nselect * from (\n    -- New or updated records\n    select\n        md5(cast(coalesce(cast(user_id as TEXT), '_dbt_utils_surrogate_key_null_') || '-' || coalesce(cast(dbt_updated_at as TEXT), '_dbt_utils_surrogate_key_null_') as TEXT)) as user_sk,\n        user_id,\n        firstname,\n        lastname,\n        email_hash as email,\n        address,\n        canal as acquisition_channel,\n        country,\n        gender,\n        age_group,\n        creation_date,\n        last_activity_date,\n        churn,\n        batch_id,\n        dbt_updated_at as dbt_valid_from,\n        cast(null as timestamp_ntz) as dbt_valid_to,\n        true as is_current_version,\n        change_type as dbt_change_type,\n        processed_at\n    from changes_to_apply\n    where change_type in ('insert', 'update')\n    \n    union all\n    \n    -- Expire old versions of updated records\n    select\n        e.user_sk,\n        e.user_id,\n        e.firstname,\n        e.lastname,\n        e.email,\n        e.address,\n        e.acquisition_channel,\n        e.country,\n        e.gender,\n        e.age_group,\n        e.creation_date,\n        e.last_activity_date,\n        e.churn,\n        e.batch_id,\n        e.dbt_valid_from,\n        current_timestamp() as dbt_valid_to,\n        false as is_current_version,\n        'expire' as dbt_change_type,\n        e.processed_at\n    from existing_dimension e\n    inner join changes_to_apply c \n        on e.user_id = c.user_id\n        and c.change_type = 'update'\n)\n\n", "relation_name": "MYDB.LIVE_DATA.dim_users_scd2", "batch_results": null}, {"status": "error", "timing": [{"name": "compile", "started_at": "2025-06-04T10:12:34.757716Z", "completed_at": "2025-06-04T10:12:34.833119Z"}, {"name": "execute", "started_at": "2025-06-04T10:12:34.835132Z", "completed_at": "2025-06-04T10:12:35.269052Z"}], "thread_id": "Thread-1 (worker)", "execution_time": 0.5293455123901367, "adapter_response": {}, "message": "Database Error in model dim_users (models/marts/dim_users.sql)\n  091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.\n  compiled code at target/run/live_c360/models/marts/dim_users.sql", "failures": null, "unique_id": "model.live_c360.dim_users", "compiled": true, "compiled_code": "\n\n-- User dimension table for live data pipeline\n-- Provides a clean, analytics-ready view of user data\n\nwith users_base as (\n    select * from MYDB.LIVE_DATA.stg_users\n),\n\nuser_metrics as (\n    select\n        user_id,\n        count(*) as total_orders,\n        sum(amount) as total_spent,\n        avg(amount) as avg_order_value,\n        min(transaction_date) as first_order_date,\n        max(transaction_date) as last_order_date,\n        sum(item_count) as total_items_purchased\n    from MYDB.LIVE_DATA.stg_orders\n    group by user_id\n),\n\nuser_events as (\n    select\n        user_id,\n        count(*) as total_events,\n        count(distinct session_id) as total_sessions,\n        count(distinct event_date) as active_days,\n        min(event_timestamp) as first_event_date,\n        max(event_timestamp) as last_event_date,\n        \n        -- Event type counts\n        sum(case when action = 'view' then 1 else 0 end) as view_events,\n        sum(case when action = 'click' then 1 else 0 end) as click_events,\n        sum(case when action = 'log' then 1 else 0 end) as login_events,\n        sum(case when action = 'purchase' then 1 else 0 end) as purchase_events,\n        \n        -- Platform usage\n        sum(case when device_category = 'Mobile' then 1 else 0 end) as mobile_events,\n        sum(case when device_category = 'Web' then 1 else 0 end) as web_events\n        \n    from MYDB.LIVE_DATA.stg_events\n    group by user_id\n),\n\nfinal as (\n    select\n        -- User identifiers\n        u.user_id,\n        u.email_hash,\n        \n        -- Personal information\n        u.firstname,\n        u.lastname,\n        u.address,\n        u.acquisition_channel,\n        u.country,\n        u.gender,\n        u.age_group,\n        \n        -- Dates\n        u.creation_date,\n        u.last_activity_date,\n        \n        -- Behavioral flags\n        u.is_churned,\n        u.activity_segment,\n        \n        -- Derived user metrics\n        u.days_since_creation,\n        u.days_since_last_activity,\n        \n        -- Order metrics\n        coalesce(om.total_orders, 0) as total_orders,\n        coalesce(om.total_spent, 0) as total_spent,\n        coalesce(om.avg_order_value, 0) as avg_order_value,\n        om.first_order_date,\n        om.last_order_date,\n        coalesce(om.total_items_purchased, 0) as total_items_purchased,\n        \n        -- Event metrics\n        coalesce(ue.total_events, 0) as total_events,\n        coalesce(ue.total_sessions, 0) as total_sessions,\n        coalesce(ue.active_days, 0) as active_days,\n        ue.first_event_date,\n        ue.last_event_date,\n        \n        -- Event type metrics\n        coalesce(ue.view_events, 0) as view_events,\n        coalesce(ue.click_events, 0) as click_events,\n        coalesce(ue.login_events, 0) as login_events,\n        coalesce(ue.purchase_events, 0) as purchase_events,\n        \n        -- Platform metrics\n        coalesce(ue.mobile_events, 0) as mobile_events,\n        coalesce(ue.web_events, 0) as web_events,\n        \n        -- Calculated metrics\n        case \n            when om.total_orders > 0 then round(ue.total_events::float / om.total_orders, 2)\n            else 0\n        end as events_per_order,\n        \n        case \n            when ue.total_sessions > 0 then round(ue.total_events::float / ue.total_sessions, 2)\n            else 0\n        end as events_per_session,\n        \n        case \n            when om.total_orders > 0 then \n                datediff('day', om.first_order_date, om.last_order_date)::float / om.total_orders\n            else 0\n        end as avg_days_between_orders,\n        \n        -- Customer segments\n        case \n            when om.total_orders = 0 then 'No Orders'\n            when om.total_orders = 1 then 'One-time Buyer'\n            when om.total_orders <= 3 then 'Occasional Buyer'\n            when om.total_orders <= 10 then 'Regular Buyer'\n            else 'Frequent Buyer'\n        end as purchase_segment,\n        \n        case \n            when om.total_spent = 0 then 'No Spend'\n            when om.total_spent < 50 then 'Low Value'\n            when om.total_spent < 200 then 'Medium Value'\n            when om.total_spent < 500 then 'High Value'\n            else 'Premium Value'\n        end as value_segment,\n        \n        case \n            when ue.mobile_events > ue.web_events then 'Mobile Preferred'\n            when ue.web_events > ue.mobile_events then 'Web Preferred'\n            when ue.mobile_events = ue.web_events and ue.mobile_events > 0 then 'Multi-Platform'\n            else 'Unknown'\n        end as platform_preference,\n        \n        -- Data quality flags\n        u.has_future_creation_date,\n        u.has_invalid_activity_date,\n        \n        -- Metadata\n        u.batch_id,\n        u.generated_at,\n        u.processed_at,\n        current_timestamp() as mart_created_at\n        \n    from users_base u\n    left join user_metrics om on u.user_id = om.user_id\n    left join user_events ue on u.user_id = ue.user_id\n)\n\nselect * from final", "relation_name": "MYDB.LIVE_DATA.dim_users", "batch_results": null}, {"status": "error", "timing": [{"name": "compile", "started_at": "2025-06-04T10:12:34.815043Z", "completed_at": "2025-06-04T10:12:34.834013Z"}, {"name": "execute", "started_at": "2025-06-04T10:12:34.862300Z", "completed_at": "2025-06-04T10:12:35.355419Z"}], "thread_id": "Thread-3 (worker)", "execution_time": 0.6089155673980713, "adapter_response": {}, "message": "Database Error in model fact_orders (models/marts/fact_orders.sql)\n  091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.\n  compiled code at target/run/live_c360/models/marts/fact_orders.sql", "failures": null, "unique_id": "model.live_c360.fact_orders", "compiled": true, "compiled_code": "\n\nwith \n-- Get the 90th percentile order amount for high-value threshold\norder_thresholds as (\n    select \n        percentile_cont(0.9) within group (order by amount) as high_value_threshold\n    from MYDB.LIVE_DATA.stg_orders\n    where amount is not null\n),\n\n-- Get basic order metrics\norder_metrics as (\n    select\n        -- Primary key and foreign key\n        order_id,\n        user_id,\n        \n        -- Core order details\n        transaction_date,\n        item_count,\n        amount as order_amount,\n        \n        -- Basic metrics\n        row_number() over (partition by user_id order by transaction_date) as user_order_sequence,\n        \n        -- Time since last order\n        datediff(\n            'day',\n            lag(transaction_date) over (partition by user_id order by transaction_date),\n            transaction_date\n        ) as days_since_previous_order,\n        \n        -- Running totals\n        sum(amount) over (order by transaction_date) as running_total_amount,\n        count(*) over (order by date_trunc('day', transaction_date)) as running_order_count\n        \n    from MYDB.LIVE_DATA.stg_orders\n    where transaction_date is not null\n    qualify row_number() over (partition by order_id order by generated_at desc) = 1\n)\n\n-- Final select with basic segmentation\nselect\n    om.*,\n    \n    -- Simple customer type\n    case \n        when user_order_sequence = 1 then 'New Customer'\n        when days_since_previous_order is null then 'New Customer'\n        when days_since_previous_order <= 30 then 'Repeat Customer (30d)'\n        else 'Reactivated Customer'\n    end as customer_type,\n    \n    -- High value flag (using pre-calculated threshold)\n    case \n        when order_amount > (select high_value_threshold from order_thresholds) then true\n        else false\n    end as is_high_value_order,\n    \n    -- Large order flag\n    case \n        when item_count > 10 then true\n        else false\n    end as is_large_order,\n    \n    -- Time of day\n    case \n        when extract(hour from transaction_date) between 6 and 11 then 'Morning'\n        when extract(hour from transaction_date) between 12 and 17 then 'Afternoon'\n        when extract(hour from transaction_date) between 18 and 21 then 'Evening'\n        else 'Night'\n    end as time_of_day\n    \nfrom order_metrics om", "relation_name": "MYDB.LIVE_DATA.fact_orders", "batch_results": null}, {"status": "error", "timing": [{"name": "compile", "started_at": "2025-06-04T10:12:34.820506Z", "completed_at": "2025-06-04T10:12:34.850762Z"}, {"name": "execute", "started_at": "2025-06-04T10:12:34.922894Z", "completed_at": "2025-06-04T10:12:35.625101Z"}], "thread_id": "Thread-4 (worker)", "execution_time": 0.8770203590393066, "adapter_response": {}, "message": "Database Error in model mv_fact_orders (models/marts/mv_fact_orders.sql)\n  091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.\n  compiled code at target/run/live_c360/models/marts/mv_fact_orders.sql", "failures": null, "unique_id": "model.live_c360.mv_fact_orders", "compiled": true, "compiled_code": "\n\nwith \n-- Get the 90th percentile order amount for high-value threshold\norder_thresholds as (\n    select \n        percentile_cont(0.9) within group (order by amount) as high_value_threshold\n    from MYDB.LIVE_DATA.stg_orders\n    where amount is not null\n),\n\n-- Get basic order metrics\norder_metrics as (\n    select\n        -- Primary key and foreign key\n        order_id,\n        user_id,\n        \n        -- Core order details\n        transaction_date,\n        item_count,\n        amount as order_amount,\n        \n        -- Basic metrics\n        row_number() over (partition by user_id order by transaction_date) as user_order_sequence,\n        \n        -- Time since last order\n        datediff(\n            'day',\n            lag(transaction_date) over (partition by user_id order by transaction_date),\n            transaction_date\n        ) as days_since_previous_order,\n        \n        -- Running totals\n        sum(amount) over (order by transaction_date) as running_total_amount,\n        count(*) over (order by date_trunc('day', transaction_date)) as running_order_count,\n        \n        -- Include the generated_at for incremental logic if needed\n        generated_at\n        \n    from MYDB.LIVE_DATA.stg_orders\n    where transaction_date is not null\n    qualify row_number() over (partition by order_id order by generated_at desc) = 1\n)\n\n-- Final select with basic segmentation\nselect\n    om.*,\n    \n    -- Simple customer type\n    case \n        when user_order_sequence = 1 then 'New Customer'\n        when days_since_previous_order is null then 'New Customer'\n        when days_since_previous_order <= 30 then 'Repeat Customer (30d)'\n        else 'Reactivated Customer'\n    end as customer_type,\n    \n    -- High value flag (using pre-calculated threshold)\n    case \n        when order_amount > (select high_value_threshold from order_thresholds) then true\n        else false\n    end as is_high_value_order,\n    \n    -- Large order flag\n    case \n        when item_count > 10 then true\n        else false\n    end as is_large_order,\n    \n    -- Time of day\n    case \n        when extract(hour from transaction_date) between 6 and 11 then 'Morning'\n        when extract(hour from transaction_date) between 12 and 17 then 'Afternoon'\n        when extract(hour from transaction_date) between 18 and 21 then 'Evening'\n        else 'Night'\n    end as time_of_day,\n    \n    -- Add a timestamp for when this record was created\n    current_timestamp() as dbt_updated_at\n    \nfrom order_metrics om", "relation_name": "MYDB.LIVE_DATA_ANALYTICS.mv_fact_orders", "batch_results": null}], "elapsed_time": 2.976806879043579, "args": {"log_file_max_bytes": 10485760, "which": "run", "exclude": [], "cache_selected_only": false, "log_path": "/opt/airflow/workspace/dbt_live/logs", "partial_parse": true, "quiet": false, "empty": false, "partial_parse_file_diff": true, "require_resource_names_without_spaces": false, "require_yaml_configuration_for_mf_time_spines": false, "profiles_dir": "/opt/airflow/workspace/dbt_live", "require_explicit_package_overrides_for_builtin_materializations": true, "require_batched_execution_for_custom_microbatch_strategy": false, "static_parser": true, "populate_cache": true, "use_colors_file": true, "select": ["marts"], "source_freshness_run_project_hooks": false, "log_format": "default", "warn_error_options": {"include": [], "exclude": []}, "require_nested_cumulative_type_params": false, "project_dir": "/opt/airflow/workspace/dbt_live", "show_resource_report": false, "use_colors": true, "skip_nodes_if_on_run_start_fails": false, "favor_state": false, "log_level_file": "debug", "macro_debugging": false, "print": true, "strict_mode": false, "defer": false, "log_level": "info", "write_json": true, "send_anonymous_usage_stats": true, "printer_width": 80, "indirect_selection": "eager", "state_modified_compare_more_unrendered_values": false, "version_check": true, "log_format_file": "debug", "vars": {}, "state_modified_compare_vars": false, "introspect": true, "invocation_command": "dbt run --models marts"}}