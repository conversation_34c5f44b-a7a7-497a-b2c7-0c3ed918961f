{"metadata": {"dbt_schema_version": "https://schemas.getdbt.com/dbt/run-results/v6.json", "dbt_version": "1.9.6", "generated_at": "2025-06-04T10:33:06.596412Z", "invocation_id": "6d4e62dc-fdcc-44e4-ae1b-081f8b08ad5e", "env": {}}, "results": [{"status": "pass", "timing": [{"name": "compile", "started_at": "2025-06-04T10:32:57.919172Z", "completed_at": "2025-06-04T10:32:57.979281Z"}, {"name": "execute", "started_at": "2025-06-04T10:32:57.979281Z", "completed_at": "2025-06-04T10:32:59.651193Z"}], "thread_id": "Thread-1 (worker)", "execution_time": 1.7320210933685303, "adapter_response": {"_message": "SUCCESS 1", "code": "SUCCESS", "rows_affected": 1, "query_id": "01bccd58-3204-7fc5-0002-4ad6000632be"}, "message": null, "failures": 0, "unique_id": "test.live_c360.dbt_utils_source_accepted_range_live_external_ext_live_orders_amount__False__0.50380c3cc8", "compiled": true, "compiled_code": "\n\nwith meet_condition as(\n  select *\n  from MYDB.LIVE_DATA.ext_live_orders\n),\n\nvalidation_errors as (\n  select *\n  from meet_condition\n  where\n    -- never true, defaults to an empty result set. Exists to ensure any combo of the `or` clauses below succeeds\n    1 = 2\n    -- records with a value >= min_value are permitted. The `not` flips this to find records that don't meet the rule.\n    or not amount > 0\n)\n\nselect *\nfrom validation_errors\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.dbt_utils_source_accepted_rang_038429b5ca81df79a58a497617b75c52", "batch_results": null}, {"status": "pass", "timing": [{"name": "compile", "started_at": "2025-06-04T10:32:57.952554Z", "completed_at": "2025-06-04T10:32:57.979281Z"}, {"name": "execute", "started_at": "2025-06-04T10:32:58.019110Z", "completed_at": "2025-06-04T10:33:00.285568Z"}], "thread_id": "Thread-3 (worker)", "execution_time": 2.366396427154541, "adapter_response": {"_message": "SUCCESS 1", "code": "SUCCESS", "rows_affected": 1, "query_id": "01bccd59-3204-7ffc-0002-4ad6000661ca"}, "message": null, "failures": 0, "unique_id": "test.live_c360.source_accepted_values_live_external_ext_live_events_action__view__click__log__purchase.6a0240ce21", "compiled": true, "compiled_code": "\n    \n    \n\nwith all_values as (\n\n    select\n        action as value_field,\n        count(*) as n_records\n\n    from MYDB.LIVE_DATA.ext_live_events\n    group by action\n\n)\n\nselect *\nfrom all_values\nwhere value_field not in (\n    'view','click','log','purchase'\n)\n\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.source_accepted_values_live_ex_e2a83a0f133a781a89c6730f46beefed", "batch_results": null}, {"status": "pass", "timing": [{"name": "compile", "started_at": "2025-06-04T10:32:57.945942Z", "completed_at": "2025-06-04T10:32:57.979281Z"}, {"name": "execute", "started_at": "2025-06-04T10:32:58.019110Z", "completed_at": "2025-06-04T10:33:00.352790Z"}], "thread_id": "Thread-2 (worker)", "execution_time": 2.4336180686950684, "adapter_response": {"_message": "SUCCESS 1", "code": "SUCCESS", "rows_affected": 1, "query_id": "01bccd58-3204-7fc5-0002-4ad6000632c2"}, "message": null, "failures": 0, "unique_id": "test.live_c360.dbt_utils_source_accepted_range_live_external_ext_live_orders_item_count__100__1.93502d99cd", "compiled": true, "compiled_code": "\n\nwith meet_condition as(\n  select *\n  from MYDB.LIVE_DATA.ext_live_orders\n),\n\nvalidation_errors as (\n  select *\n  from meet_condition\n  where\n    -- never true, defaults to an empty result set. Exists to ensure any combo of the `or` clauses below succeeds\n    1 = 2\n    -- records with a value >= min_value are permitted. The `not` flips this to find records that don't meet the rule.\n    or not item_count >= 1\n    -- records with a value <= max_value are permitted. The `not` flips this to find records that don't meet the rule.\n    or not item_count <= 100\n)\n\nselect *\nfrom validation_errors\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.dbt_utils_source_accepted_rang_2fa5c5eaa1e5c8f251141e01fd7779e8", "batch_results": null}, {"status": "pass", "timing": [{"name": "compile", "started_at": "2025-06-04T10:32:57.969251Z", "completed_at": "2025-06-04T10:32:58.006001Z"}, {"name": "execute", "started_at": "2025-06-04T10:32:58.029110Z", "completed_at": "2025-06-04T10:33:00.686194Z"}], "thread_id": "Thread-4 (worker)", "execution_time": 2.767021894454956, "adapter_response": {"_message": "SUCCESS 1", "code": "SUCCESS", "rows_affected": 1, "query_id": "01bccd59-3204-7f80-0002-4ad6000671d2"}, "message": null, "failures": 0, "unique_id": "test.live_c360.source_accepted_values_live_external_ext_live_events_platform__ios__android__web__None.553832ca75", "compiled": true, "compiled_code": "\n    \n    \n\nwith all_values as (\n\n    select\n        platform as value_field,\n        count(*) as n_records\n\n    from MYDB.LIVE_DATA.ext_live_events\n    group by platform\n\n)\n\nselect *\nfrom all_values\nwhere value_field not in (\n    'ios','android','web','None'\n)\n\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.source_accepted_values_live_ex_255d50e75759bebae188ba25e47211e0", "batch_results": null}, {"status": "pass", "timing": [{"name": "compile", "started_at": "2025-06-04T10:32:59.661203Z", "completed_at": "2025-06-04T10:32:59.671289Z"}, {"name": "execute", "started_at": "2025-06-04T10:32:59.671289Z", "completed_at": "2025-06-04T10:33:00.854694Z"}], "thread_id": "Thread-1 (worker)", "execution_time": 1.2035012245178223, "adapter_response": {"_message": "SUCCESS 1", "code": "SUCCESS", "rows_affected": 1, "query_id": "01bccd59-3204-7eab-0002-4ad600061306"}, "message": null, "failures": 0, "unique_id": "test.live_c360.source_not_null_live_external_ext_live_events_event_id.ff0aeb9e92", "compiled": true, "compiled_code": "\n    \n    \n\n\n\nselect *\nfrom MYDB.LIVE_DATA.ext_live_events\nwhere event_id is null\n\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.source_not_null_live_external_ext_live_events_event_id", "batch_results": null}, {"status": "pass", "timing": [{"name": "compile", "started_at": "2025-06-04T10:33:00.285568Z", "completed_at": "2025-06-04T10:33:00.295570Z"}, {"name": "execute", "started_at": "2025-06-04T10:33:00.295570Z", "completed_at": "2025-06-04T10:33:01.636214Z"}], "thread_id": "Thread-3 (worker)", "execution_time": 1.3506455421447754, "adapter_response": {"_message": "SUCCESS 1", "code": "SUCCESS", "rows_affected": 1, "query_id": "01bccd59-3204-7f69-0002-4ad60005d292"}, "message": null, "failures": 0, "unique_id": "test.live_c360.source_not_null_live_external_ext_live_events_generated_at.96d76fb529", "compiled": true, "compiled_code": "\n    \n    \n\n\n\nselect *\nfrom MYDB.LIVE_DATA.ext_live_events\nwhere generated_at is null\n\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.source_not_null_live_external_ext_live_events_generated_at", "batch_results": null}, {"status": "pass", "timing": [{"name": "compile", "started_at": "2025-06-04T10:33:00.864702Z", "completed_at": "2025-06-04T10:33:00.874704Z"}, {"name": "execute", "started_at": "2025-06-04T10:33:00.874704Z", "completed_at": "2025-06-04T10:33:01.857233Z"}], "thread_id": "Thread-1 (worker)", "execution_time": 0.9925308227539062, "adapter_response": {"_message": "SUCCESS 1", "code": "SUCCESS", "rows_affected": 1, "query_id": "01bccd59-3204-7fc5-0002-4ad6000632ca"}, "message": null, "failures": 0, "unique_id": "test.live_c360.source_not_null_live_external_ext_live_orders_amount.679c5c05e3", "compiled": true, "compiled_code": "\n    \n    \n\n\n\nselect *\nfrom MYDB.LIVE_DATA.ext_live_orders\nwhere amount is null\n\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.source_not_null_live_external_ext_live_orders_amount", "batch_results": null}, {"status": "pass", "timing": [{"name": "compile", "started_at": "2025-06-04T10:33:00.352790Z", "completed_at": "2025-06-04T10:33:00.362788Z"}, {"name": "execute", "started_at": "2025-06-04T10:33:00.362788Z", "completed_at": "2025-06-04T10:33:01.877163Z"}], "thread_id": "Thread-2 (worker)", "execution_time": 1.5243737697601318, "adapter_response": {"_message": "SUCCESS 1", "code": "SUCCESS", "rows_affected": 1, "query_id": "01bccd59-3204-7fc5-0002-4ad6000632ce"}, "message": null, "failures": 0, "unique_id": "test.live_c360.source_not_null_live_external_ext_live_events_session_id.342957c8ec", "compiled": true, "compiled_code": "\n    \n    \n\n\n\nselect *\nfrom MYDB.LIVE_DATA.ext_live_events\nwhere session_id is null\n\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.source_not_null_live_external_ext_live_events_session_id", "batch_results": null}, {"status": "pass", "timing": [{"name": "compile", "started_at": "2025-06-04T10:33:00.692606Z", "completed_at": "2025-06-04T10:33:00.695786Z"}, {"name": "execute", "started_at": "2025-06-04T10:33:00.695786Z", "completed_at": "2025-06-04T10:33:02.153086Z"}], "thread_id": "Thread-4 (worker)", "execution_time": 1.46793794631958, "adapter_response": {"_message": "SUCCESS 1", "code": "SUCCESS", "rows_affected": 1, "query_id": "01bccd59-3204-7f80-0002-4ad6000671da"}, "message": null, "failures": 0, "unique_id": "test.live_c360.source_not_null_live_external_ext_live_events_user_id.f112ba0802", "compiled": true, "compiled_code": "\n    \n    \n\n\n\nselect *\nfrom MYDB.LIVE_DATA.ext_live_events\nwhere user_id is null\n\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.source_not_null_live_external_ext_live_events_user_id", "batch_results": null}, {"status": "pass", "timing": [{"name": "compile", "started_at": "2025-06-04T10:33:01.636214Z", "completed_at": "2025-06-04T10:33:01.646246Z"}, {"name": "execute", "started_at": "2025-06-04T10:33:01.646246Z", "completed_at": "2025-06-04T10:33:02.701736Z"}], "thread_id": "Thread-3 (worker)", "execution_time": 1.0660395622253418, "adapter_response": {"_message": "SUCCESS 1", "code": "SUCCESS", "rows_affected": 1, "query_id": "01bccd59-3204-7fc5-0002-4ad6000632d6"}, "message": null, "failures": 0, "unique_id": "test.live_c360.source_not_null_live_external_ext_live_orders_generated_at.a66c929306", "compiled": true, "compiled_code": "\n    \n    \n\n\n\nselect *\nfrom MYDB.LIVE_DATA.ext_live_orders\nwhere generated_at is null\n\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.source_not_null_live_external_ext_live_orders_generated_at", "batch_results": null}, {"status": "pass", "timing": [{"name": "compile", "started_at": "2025-06-04T10:33:01.860871Z", "completed_at": "2025-06-04T10:33:01.877163Z"}, {"name": "execute", "started_at": "2025-06-04T10:33:01.877163Z", "completed_at": "2025-06-04T10:33:02.845587Z"}], "thread_id": "Thread-1 (worker)", "execution_time": 0.9847168922424316, "adapter_response": {"_message": "SUCCESS 1", "code": "SUCCESS", "rows_affected": 1, "query_id": "01bccd59-3204-800f-0002-4ad60006517e"}, "message": null, "failures": 0, "unique_id": "test.live_c360.source_not_null_live_external_ext_live_orders_id.43cabbf964", "compiled": true, "compiled_code": "\n    \n    \n\n\n\nselect *\nfrom MYDB.LIVE_DATA.ext_live_orders\nwhere id is null\n\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.source_not_null_live_external_ext_live_orders_id", "batch_results": null}, {"status": "pass", "timing": [{"name": "compile", "started_at": "2025-06-04T10:33:01.906005Z", "completed_at": "2025-06-04T10:33:01.906005Z"}, {"name": "execute", "started_at": "2025-06-04T10:33:01.916005Z", "completed_at": "2025-06-04T10:33:02.860478Z"}], "thread_id": "Thread-2 (worker)", "execution_time": 0.95465087890625, "adapter_response": {"_message": "SUCCESS 1", "code": "SUCCESS", "rows_affected": 1, "query_id": "01bccd59-3204-800f-0002-4ad60006517a"}, "message": null, "failures": 0, "unique_id": "test.live_c360.source_not_null_live_external_ext_live_orders_item_count.7dac40310e", "compiled": true, "compiled_code": "\n    \n    \n\n\n\nselect *\nfrom MYDB.LIVE_DATA.ext_live_orders\nwhere item_count is null\n\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.source_not_null_live_external_ext_live_orders_item_count", "batch_results": null}, {"status": "pass", "timing": [{"name": "compile", "started_at": "2025-06-04T10:33:02.159072Z", "completed_at": "2025-06-04T10:33:02.159072Z"}, {"name": "execute", "started_at": "2025-06-04T10:33:02.159072Z", "completed_at": "2025-06-04T10:33:03.246184Z"}], "thread_id": "Thread-4 (worker)", "execution_time": 1.0884811878204346, "adapter_response": {"_message": "SUCCESS 1", "code": "SUCCESS", "rows_affected": 1, "query_id": "01bccd59-3204-7eab-0002-4ad600061312"}, "message": null, "failures": 0, "unique_id": "test.live_c360.source_not_null_live_external_ext_live_orders_user_id.129a7fc228", "compiled": true, "compiled_code": "\n    \n    \n\n\n\nselect *\nfrom MYDB.LIVE_DATA.ext_live_orders\nwhere user_id is null\n\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.source_not_null_live_external_ext_live_orders_user_id", "batch_results": null}, {"status": "pass", "timing": [{"name": "compile", "started_at": "2025-06-04T10:33:02.708850Z", "completed_at": "2025-06-04T10:33:02.716220Z"}, {"name": "execute", "started_at": "2025-06-04T10:33:02.716733Z", "completed_at": "2025-06-04T10:33:03.855093Z"}], "thread_id": "Thread-3 (worker)", "execution_time": 1.1478562355041504, "adapter_response": {"_message": "SUCCESS 1", "code": "SUCCESS", "rows_affected": 1, "query_id": "01bccd59-3204-7f80-0002-4ad6000671e2"}, "message": null, "failures": 0, "unique_id": "test.live_c360.source_not_null_live_external_ext_live_users_email.d823bafd9b", "compiled": true, "compiled_code": "\n    \n    \n\n\n\nselect *\nfrom MYDB.LIVE_DATA.ext_live_users\nwhere email is null\n\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.source_not_null_live_external_ext_live_users_email", "batch_results": null}, {"status": "pass", "timing": [{"name": "compile", "started_at": "2025-06-04T10:33:02.880780Z", "completed_at": "2025-06-04T10:33:02.888185Z"}, {"name": "execute", "started_at": "2025-06-04T10:33:02.914810Z", "completed_at": "2025-06-04T10:33:03.880379Z"}], "thread_id": "Thread-1 (worker)", "execution_time": 1.0071337223052979, "adapter_response": {"_message": "SUCCESS 1", "code": "SUCCESS", "rows_affected": 1, "query_id": "01bccd59-3204-7ffc-0002-4ad6000661e6"}, "message": null, "failures": 0, "unique_id": "test.live_c360.source_not_null_live_external_ext_live_users_generated_at.60675c4e9c", "compiled": true, "compiled_code": "\n    \n    \n\n\n\nselect *\nfrom MYDB.LIVE_DATA.ext_live_users\nwhere generated_at is null\n\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.source_not_null_live_external_ext_live_users_generated_at", "batch_results": null}, {"status": "pass", "timing": [{"name": "compile", "started_at": "2025-06-04T10:33:02.904862Z", "completed_at": "2025-06-04T10:33:02.927349Z"}, {"name": "execute", "started_at": "2025-06-04T10:33:02.927349Z", "completed_at": "2025-06-04T10:33:04.022622Z"}], "thread_id": "Thread-2 (worker)", "execution_time": 1.134437084197998, "adapter_response": {"_message": "SUCCESS 1", "code": "SUCCESS", "rows_affected": 1, "query_id": "01bccd59-3204-800f-0002-4ad600065186"}, "message": null, "failures": 0, "unique_id": "test.live_c360.source_not_null_live_external_ext_live_users_id.b0694be37c", "compiled": true, "compiled_code": "\n    \n    \n\n\n\nselect *\nfrom MYDB.LIVE_DATA.ext_live_users\nwhere id is null\n\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.source_not_null_live_external_ext_live_users_id", "batch_results": null}, {"status": "warn", "timing": [{"name": "compile", "started_at": "2025-06-04T10:33:03.256190Z", "completed_at": "2025-06-04T10:33:03.266186Z"}, {"name": "execute", "started_at": "2025-06-04T10:33:03.266186Z", "completed_at": "2025-06-04T10:33:04.957541Z"}], "thread_id": "Thread-4 (worker)", "execution_time": 1.7013509273529053, "adapter_response": {"_message": "SUCCESS 1", "code": "SUCCESS", "rows_affected": 1, "query_id": "01bccd59-3204-7eab-0002-4ad60006131a"}, "message": "Got 396000 results, configured to warn if != 0", "failures": 396000, "unique_id": "test.live_c360.source_relationships_live_external_ext_live_events_user_id__id__source_live_external_ext_live_users_.ba15555d86", "compiled": true, "compiled_code": "\n    \n    \n\nwith child as (\n    select user_id as from_field\n    from MYDB.LIVE_DATA.ext_live_events\n    where user_id is not null\n),\n\nparent as (\n    select id as to_field\n    from MYDB.LIVE_DATA.ext_live_users\n)\n\nselect\n    from_field\n\nfrom child\nleft join parent\n    on child.from_field = parent.to_field\n\nwhere parent.to_field is null\n\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.source_relationships_live_exte_f8dcab25f08d3d0cecc66acc19c0f320", "batch_results": null}, {"status": "pass", "timing": [{"name": "compile", "started_at": "2025-06-04T10:33:04.036252Z", "completed_at": "2025-06-04T10:33:04.046286Z"}, {"name": "execute", "started_at": "2025-06-04T10:33:04.046286Z", "completed_at": "2025-06-04T10:33:05.197475Z"}], "thread_id": "Thread-2 (worker)", "execution_time": 1.1612229347229004, "adapter_response": {"_message": "SUCCESS 1", "code": "SUCCESS", "rows_affected": 1, "query_id": "01bccd59-3204-7f80-0002-4ad6000671ee"}, "message": null, "failures": 0, "unique_id": "test.live_c360.source_unique_live_external_ext_live_orders_id.82585e5b4d", "compiled": true, "compiled_code": "\n    \n    \n\nselect\n    id as unique_field,\n    count(*) as n_records\n\nfrom MYDB.LIVE_DATA.ext_live_orders\nwhere id is not null\ngroup by id\nhaving count(*) > 1\n\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.source_unique_live_external_ext_live_orders_id", "batch_results": null}, {"status": "pass", "timing": [{"name": "compile", "started_at": "2025-06-04T10:33:03.906597Z", "completed_at": "2025-06-04T10:33:03.916598Z"}, {"name": "execute", "started_at": "2025-06-04T10:33:03.916598Z", "completed_at": "2025-06-04T10:33:05.330435Z"}], "thread_id": "Thread-1 (worker)", "execution_time": 1.4348461627960205, "adapter_response": {"_message": "SUCCESS 1", "code": "SUCCESS", "rows_affected": 1, "query_id": "01bccd59-3204-7fc5-0002-4ad6000632e2"}, "message": null, "failures": 0, "unique_id": "test.live_c360.source_unique_live_external_ext_live_events_event_id.eeb702f8fc", "compiled": true, "compiled_code": "\n    \n    \n\nselect\n    event_id as unique_field,\n    count(*) as n_records\n\nfrom MYDB.LIVE_DATA.ext_live_events\nwhere event_id is not null\ngroup by event_id\nhaving count(*) > 1\n\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.source_unique_live_external_ext_live_events_event_id", "batch_results": null}, {"status": "warn", "timing": [{"name": "compile", "started_at": "2025-06-04T10:33:03.857669Z", "completed_at": "2025-06-04T10:33:03.881379Z"}, {"name": "execute", "started_at": "2025-06-04T10:33:03.883597Z", "completed_at": "2025-06-04T10:33:05.552825Z"}], "thread_id": "Thread-3 (worker)", "execution_time": 1.6951563358306885, "adapter_response": {"_message": "SUCCESS 1", "code": "SUCCESS", "rows_affected": 1, "query_id": "01bccd59-3204-7eab-0002-4ad60006131e"}, "message": "Got 72000 results, configured to warn if != 0", "failures": 72000, "unique_id": "test.live_c360.source_relationships_live_external_ext_live_orders_user_id__id__source_live_external_ext_live_users_.8d45efa758", "compiled": true, "compiled_code": "\n    \n    \n\nwith child as (\n    select user_id as from_field\n    from MYDB.LIVE_DATA.ext_live_orders\n    where user_id is not null\n),\n\nparent as (\n    select id as to_field\n    from MYDB.LIVE_DATA.ext_live_users\n)\n\nselect\n    from_field\n\nfrom child\nleft join parent\n    on child.from_field = parent.to_field\n\nwhere parent.to_field is null\n\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.source_relationships_live_exte_3905f58a8f4f802f3d4269e076a3f201", "batch_results": null}, {"status": "pass", "timing": [{"name": "compile", "started_at": "2025-06-04T10:33:04.961542Z", "completed_at": "2025-06-04T10:33:04.967415Z"}, {"name": "execute", "started_at": "2025-06-04T10:33:04.969618Z", "completed_at": "2025-06-04T10:33:05.996265Z"}], "thread_id": "Thread-4 (worker)", "execution_time": 1.0357239246368408, "adapter_response": {"_message": "SUCCESS 1", "code": "SUCCESS", "rows_affected": 1, "query_id": "01bccd59-3204-7f69-0002-4ad60005d2ae"}, "message": null, "failures": 0, "unique_id": "test.live_c360.source_unique_live_external_ext_live_users_id.4af1be4e1f", "compiled": true, "compiled_code": "\n    \n    \n\nselect\n    id as unique_field,\n    count(*) as n_records\n\nfrom MYDB.LIVE_DATA.ext_live_users\nwhere id is not null\ngroup by id\nhaving count(*) > 1\n\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.source_unique_live_external_ext_live_users_id", "batch_results": null}], "elapsed_time": 11.10870909690857, "args": {"log_level_file": "debug", "which": "test", "static_parser": true, "send_anonymous_usage_stats": true, "source_freshness_run_project_hooks": false, "select": [], "printer_width": 80, "quiet": false, "project_dir": "G:\\github\\S3 dbt-snowflake c360\\experiment2\\dbt_live", "log_format_file": "debug", "require_batched_execution_for_custom_microbatch_strategy": false, "state_modified_compare_more_unrendered_values": false, "version_check": true, "log_path": "G:\\github\\S3 dbt-snowflake c360\\experiment2\\dbt_live\\logs", "log_format": "default", "introspect": true, "skip_nodes_if_on_run_start_fails": false, "favor_state": false, "invocation_command": "dbt test", "print": true, "defer": false, "require_explicit_package_overrides_for_builtin_materializations": true, "log_level": "info", "vars": {}, "exclude": [], "partial_parse": true, "cache_selected_only": false, "require_yaml_configuration_for_mf_time_spines": false, "partial_parse_file_diff": true, "indirect_selection": "eager", "profiles_dir": "G:\\github\\S3 dbt-snowflake c360\\experiment2\\dbt_live", "macro_debugging": false, "use_colors_file": true, "resource_types": [], "use_colors": true, "state_modified_compare_vars": false, "show_resource_report": false, "strict_mode": false, "require_nested_cumulative_type_params": false, "populate_cache": true, "log_file_max_bytes": 10485760, "exclude_resource_types": [], "write_json": true, "require_resource_names_without_spaces": false, "warn_error_options": {"include": [], "exclude": []}}}