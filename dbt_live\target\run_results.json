{"metadata": {"dbt_schema_version": "https://schemas.getdbt.com/dbt/run-results/v6.json", "dbt_version": "1.9.6", "generated_at": "2025-06-04T10:38:16.978793Z", "invocation_id": "0f093716-af37-462f-b4aa-7df10c0a3f77", "env": {}}, "results": [{"status": "pass", "timing": [{"name": "compile", "started_at": "2025-06-04T10:38:08.656337Z", "completed_at": "2025-06-04T10:38:08.718740Z"}, {"name": "execute", "started_at": "2025-06-04T10:38:08.769387Z", "completed_at": "2025-06-04T10:38:10.662850Z"}], "thread_id": "Thread-1 (worker)", "execution_time": 2.0065131187438965, "adapter_response": {"_message": "SUCCESS 1", "code": "SUCCESS", "rows_affected": 1, "query_id": "01bccd5e-3204-7eab-0002-4ad600061362"}, "message": null, "failures": 0, "unique_id": "test.live_c360.dbt_utils_source_accepted_range_live_external_ext_live_orders_amount__False__0.50380c3cc8", "compiled": true, "compiled_code": "\n\nwith meet_condition as(\n  select *\n  from MYDB.LIVE_DATA.ext_live_orders\n),\n\nvalidation_errors as (\n  select *\n  from meet_condition\n  where\n    -- never true, defaults to an empty result set. Exists to ensure any combo of the `or` clauses below succeeds\n    1 = 2\n    -- records with a value >= min_value are permitted. The `not` flips this to find records that don't meet the rule.\n    or not amount > 0\n)\n\nselect *\nfrom validation_errors\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.dbt_utils_source_accepted_rang_038429b5ca81df79a58a497617b75c52", "batch_results": null}, {"status": "pass", "timing": [{"name": "compile", "started_at": "2025-06-04T10:38:08.684950Z", "completed_at": "2025-06-04T10:38:08.718740Z"}, {"name": "execute", "started_at": "2025-06-04T10:38:08.718740Z", "completed_at": "2025-06-04T10:38:10.728525Z"}], "thread_id": "Thread-2 (worker)", "execution_time": 2.072187900543213, "adapter_response": {"_message": "SUCCESS 1", "code": "SUCCESS", "rows_affected": 1, "query_id": "01bccd5e-3204-7f80-0002-4ad600067236"}, "message": null, "failures": 0, "unique_id": "test.live_c360.dbt_utils_source_accepted_range_live_external_ext_live_orders_item_count__100__1.93502d99cd", "compiled": true, "compiled_code": "\n\nwith meet_condition as(\n  select *\n  from MYDB.LIVE_DATA.ext_live_orders\n),\n\nvalidation_errors as (\n  select *\n  from meet_condition\n  where\n    -- never true, defaults to an empty result set. Exists to ensure any combo of the `or` clauses below succeeds\n    1 = 2\n    -- records with a value >= min_value are permitted. The `not` flips this to find records that don't meet the rule.\n    or not item_count >= 1\n    -- records with a value <= max_value are permitted. The `not` flips this to find records that don't meet the rule.\n    or not item_count <= 100\n)\n\nselect *\nfrom validation_errors\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.dbt_utils_source_accepted_rang_2fa5c5eaa1e5c8f251141e01fd7779e8", "batch_results": null}, {"status": "pass", "timing": [{"name": "compile", "started_at": "2025-06-04T10:38:08.694951Z", "completed_at": "2025-06-04T10:38:08.718740Z"}, {"name": "execute", "started_at": "2025-06-04T10:38:08.753689Z", "completed_at": "2025-06-04T10:38:10.772291Z"}], "thread_id": "Thread-3 (worker)", "execution_time": 2.1159539222717285, "adapter_response": {"_message": "SUCCESS 1", "code": "SUCCESS", "rows_affected": 1, "query_id": "01bccd5e-3204-7f80-0002-4ad60006723a"}, "message": null, "failures": 0, "unique_id": "test.live_c360.source_accepted_values_live_external_ext_live_events_action__view__click__log__purchase.6a0240ce21", "compiled": true, "compiled_code": "\n    \n    \n\nwith all_values as (\n\n    select\n        action as value_field,\n        count(*) as n_records\n\n    from MYDB.LIVE_DATA.ext_live_events\n    group by action\n\n)\n\nselect *\nfrom all_values\nwhere value_field not in (\n    'view','click','log','purchase'\n)\n\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.source_accepted_values_live_ex_e2a83a0f133a781a89c6730f46beefed", "batch_results": null}, {"status": "pass", "timing": [{"name": "compile", "started_at": "2025-06-04T10:38:08.701382Z", "completed_at": "2025-06-04T10:38:08.728747Z"}, {"name": "execute", "started_at": "2025-06-04T10:38:08.778162Z", "completed_at": "2025-06-04T10:38:11.035787Z"}], "thread_id": "Thread-4 (worker)", "execution_time": 2.3794503211975098, "adapter_response": {"_message": "SUCCESS 1", "code": "SUCCESS", "rows_affected": 1, "query_id": "01bccd5e-3204-7f69-0002-4ad60005d30e"}, "message": null, "failures": 0, "unique_id": "test.live_c360.source_accepted_values_live_external_ext_live_events_platform__ios__android__web__None.553832ca75", "compiled": true, "compiled_code": "\n    \n    \n\nwith all_values as (\n\n    select\n        platform as value_field,\n        count(*) as n_records\n\n    from MYDB.LIVE_DATA.ext_live_events\n    group by platform\n\n)\n\nselect *\nfrom all_values\nwhere value_field not in (\n    'ios','android','web','None'\n)\n\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.source_accepted_values_live_ex_255d50e75759bebae188ba25e47211e0", "batch_results": null}, {"status": "pass", "timing": [{"name": "compile", "started_at": "2025-06-04T10:38:10.662850Z", "completed_at": "2025-06-04T10:38:10.672851Z"}, {"name": "execute", "started_at": "2025-06-04T10:38:10.682853Z", "completed_at": "2025-06-04T10:38:12.465358Z"}], "thread_id": "Thread-1 (worker)", "execution_time": 1.8025076389312744, "adapter_response": {"_message": "SUCCESS 1", "code": "SUCCESS", "rows_affected": 1, "query_id": "01bccd5e-3204-7f80-0002-4ad600067242"}, "message": null, "failures": 0, "unique_id": "test.live_c360.source_not_null_live_external_ext_live_events_event_id.ff0aeb9e92", "compiled": true, "compiled_code": "\n    \n    \n\n\n\nselect *\nfrom MYDB.LIVE_DATA.ext_live_events\nwhere event_id is null\n\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.source_not_null_live_external_ext_live_events_event_id", "batch_results": null}, {"status": "pass", "timing": [{"name": "compile", "started_at": "2025-06-04T10:38:10.728525Z", "completed_at": "2025-06-04T10:38:10.735670Z"}, {"name": "execute", "started_at": "2025-06-04T10:38:10.735670Z", "completed_at": "2025-06-04T10:38:12.545302Z"}], "thread_id": "Thread-2 (worker)", "execution_time": 1.8167774677276611, "adapter_response": {"_message": "SUCCESS 1", "code": "SUCCESS", "rows_affected": 1, "query_id": "01bccd5e-3204-7eab-0002-4ad60006136a"}, "message": null, "failures": 0, "unique_id": "test.live_c360.source_not_null_live_external_ext_live_events_generated_at.96d76fb529", "compiled": true, "compiled_code": "\n    \n    \n\n\n\nselect *\nfrom MYDB.LIVE_DATA.ext_live_events\nwhere generated_at is null\n\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.source_not_null_live_external_ext_live_events_generated_at", "batch_results": null}, {"status": "pass", "timing": [{"name": "compile", "started_at": "2025-06-04T10:38:10.782225Z", "completed_at": "2025-06-04T10:38:10.790767Z"}, {"name": "execute", "started_at": "2025-06-04T10:38:10.790767Z", "completed_at": "2025-06-04T10:38:12.597394Z"}], "thread_id": "Thread-3 (worker)", "execution_time": 1.8151686191558838, "adapter_response": {"_message": "SUCCESS 1", "code": "SUCCESS", "rows_affected": 1, "query_id": "01bccd5e-3204-7f80-0002-4ad600067246"}, "message": null, "failures": 0, "unique_id": "test.live_c360.source_not_null_live_external_ext_live_events_session_id.342957c8ec", "compiled": true, "compiled_code": "\n    \n    \n\n\n\nselect *\nfrom MYDB.LIVE_DATA.ext_live_events\nwhere session_id is null\n\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.source_not_null_live_external_ext_live_events_session_id", "batch_results": null}, {"status": "pass", "timing": [{"name": "compile", "started_at": "2025-06-04T10:38:11.035787Z", "completed_at": "2025-06-04T10:38:11.045787Z"}, {"name": "execute", "started_at": "2025-06-04T10:38:11.045787Z", "completed_at": "2025-06-04T10:38:12.683632Z"}], "thread_id": "Thread-4 (worker)", "execution_time": 1.6478445529937744, "adapter_response": {"_message": "SUCCESS 1", "code": "SUCCESS", "rows_affected": 1, "query_id": "01bccd5e-3204-7f80-0002-4ad60006724a"}, "message": null, "failures": 0, "unique_id": "test.live_c360.source_not_null_live_external_ext_live_events_user_id.f112ba0802", "compiled": true, "compiled_code": "\n    \n    \n\n\n\nselect *\nfrom MYDB.LIVE_DATA.ext_live_events\nwhere user_id is null\n\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.source_not_null_live_external_ext_live_events_user_id", "batch_results": null}, {"status": "pass", "timing": [{"name": "compile", "started_at": "2025-06-04T10:38:12.465358Z", "completed_at": "2025-06-04T10:38:12.475355Z"}, {"name": "execute", "started_at": "2025-06-04T10:38:12.485355Z", "completed_at": "2025-06-04T10:38:13.324306Z"}], "thread_id": "Thread-1 (worker)", "execution_time": 0.8589482307434082, "adapter_response": {"_message": "SUCCESS 1", "code": "SUCCESS", "rows_affected": 1, "query_id": "01bccd5e-3204-7f69-0002-4ad60005d31a"}, "message": null, "failures": 0, "unique_id": "test.live_c360.source_not_null_live_external_ext_live_orders_amount.679c5c05e3", "compiled": true, "compiled_code": "\n    \n    \n\n\n\nselect *\nfrom MYDB.LIVE_DATA.ext_live_orders\nwhere amount is null\n\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.source_not_null_live_external_ext_live_orders_amount", "batch_results": null}, {"status": "pass", "timing": [{"name": "compile", "started_at": "2025-06-04T10:38:12.637596Z", "completed_at": "2025-06-04T10:38:12.648238Z"}, {"name": "execute", "started_at": "2025-06-04T10:38:12.648238Z", "completed_at": "2025-06-04T10:38:13.495808Z"}], "thread_id": "Thread-3 (worker)", "execution_time": 0.8642127513885498, "adapter_response": {"_message": "SUCCESS 1", "code": "SUCCESS", "rows_affected": 1, "query_id": "01bccd5e-3204-7f69-0002-4ad60005d31e"}, "message": null, "failures": 0, "unique_id": "test.live_c360.source_not_null_live_external_ext_live_orders_id.43cabbf964", "compiled": true, "compiled_code": "\n    \n    \n\n\n\nselect *\nfrom MYDB.LIVE_DATA.ext_live_orders\nwhere id is null\n\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.source_not_null_live_external_ext_live_orders_id", "batch_results": null}, {"status": "pass", "timing": [{"name": "compile", "started_at": "2025-06-04T10:38:12.692338Z", "completed_at": "2025-06-04T10:38:12.702346Z"}, {"name": "execute", "started_at": "2025-06-04T10:38:12.702346Z", "completed_at": "2025-06-04T10:38:13.564999Z"}], "thread_id": "Thread-4 (worker)", "execution_time": 0.8726611137390137, "adapter_response": {"_message": "SUCCESS 1", "code": "SUCCESS", "rows_affected": 1, "query_id": "01bccd5e-3204-7eab-0002-4ad60006137a"}, "message": null, "failures": 0, "unique_id": "test.live_c360.source_not_null_live_external_ext_live_orders_item_count.7dac40310e", "compiled": true, "compiled_code": "\n    \n    \n\n\n\nselect *\nfrom MYDB.LIVE_DATA.ext_live_orders\nwhere item_count is null\n\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.source_not_null_live_external_ext_live_orders_item_count", "batch_results": null}, {"status": "pass", "timing": [{"name": "compile", "started_at": "2025-06-04T10:38:12.566965Z", "completed_at": "2025-06-04T10:38:12.594362Z"}, {"name": "execute", "started_at": "2025-06-04T10:38:12.597394Z", "completed_at": "2025-06-04T10:38:13.574986Z"}], "thread_id": "Thread-2 (worker)", "execution_time": 1.0197083950042725, "adapter_response": {"_message": "SUCCESS 1", "code": "SUCCESS", "rows_affected": 1, "query_id": "01bccd5e-3204-800f-0002-4ad6000651e2"}, "message": null, "failures": 0, "unique_id": "test.live_c360.source_not_null_live_external_ext_live_orders_generated_at.a66c929306", "compiled": true, "compiled_code": "\n    \n    \n\n\n\nselect *\nfrom MYDB.LIVE_DATA.ext_live_orders\nwhere generated_at is null\n\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.source_not_null_live_external_ext_live_orders_generated_at", "batch_results": null}, {"status": "pass", "timing": [{"name": "compile", "started_at": "2025-06-04T10:38:13.334310Z", "completed_at": "2025-06-04T10:38:13.344308Z"}, {"name": "execute", "started_at": "2025-06-04T10:38:13.344308Z", "completed_at": "2025-06-04T10:38:13.964502Z"}], "thread_id": "Thread-1 (worker)", "execution_time": 0.6301915645599365, "adapter_response": {"_message": "SUCCESS 1", "code": "SUCCESS", "rows_affected": 1, "query_id": "01bccd5e-3204-7eab-0002-4ad600061382"}, "message": null, "failures": 0, "unique_id": "test.live_c360.source_not_null_live_external_ext_live_orders_user_id.129a7fc228", "compiled": true, "compiled_code": "\n    \n    \n\n\n\nselect *\nfrom MYDB.LIVE_DATA.ext_live_orders\nwhere user_id is null\n\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.source_not_null_live_external_ext_live_orders_user_id", "batch_results": null}, {"status": "pass", "timing": [{"name": "compile", "started_at": "2025-06-04T10:38:13.574986Z", "completed_at": "2025-06-04T10:38:13.600142Z"}, {"name": "execute", "started_at": "2025-06-04T10:38:13.609503Z", "completed_at": "2025-06-04T10:38:14.375736Z"}], "thread_id": "Thread-4 (worker)", "execution_time": 0.8007493019104004, "adapter_response": {"_message": "SUCCESS 1", "code": "SUCCESS", "rows_affected": 1, "query_id": "01bccd5e-3204-7eab-0002-4ad600061386"}, "message": null, "failures": 0, "unique_id": "test.live_c360.source_not_null_live_external_ext_live_users_generated_at.60675c4e9c", "compiled": true, "compiled_code": "\n    \n    \n\n\n\nselect *\nfrom MYDB.LIVE_DATA.ext_live_users\nwhere generated_at is null\n\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.source_not_null_live_external_ext_live_users_generated_at", "batch_results": null}, {"status": "pass", "timing": [{"name": "compile", "started_at": "2025-06-04T10:38:13.505318Z", "completed_at": "2025-06-04T10:38:13.505318Z"}, {"name": "execute", "started_at": "2025-06-04T10:38:13.505318Z", "completed_at": "2025-06-04T10:38:14.389252Z"}], "thread_id": "Thread-3 (worker)", "execution_time": 0.8839340209960938, "adapter_response": {"_message": "SUCCESS 1", "code": "SUCCESS", "rows_affected": 1, "query_id": "01bccd5e-3204-7eab-0002-4ad60006138a"}, "message": null, "failures": 0, "unique_id": "test.live_c360.source_not_null_live_external_ext_live_users_email.d823bafd9b", "compiled": true, "compiled_code": "\n    \n    \n\n\n\nselect *\nfrom MYDB.LIVE_DATA.ext_live_users\nwhere email is null\n\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.source_not_null_live_external_ext_live_users_email", "batch_results": null}, {"status": "pass", "timing": [{"name": "compile", "started_at": "2025-06-04T10:38:13.603499Z", "completed_at": "2025-06-04T10:38:13.620417Z"}, {"name": "execute", "started_at": "2025-06-04T10:38:13.624427Z", "completed_at": "2025-06-04T10:38:14.413083Z"}], "thread_id": "Thread-2 (worker)", "execution_time": 0.8380966186523438, "adapter_response": {"_message": "SUCCESS 1", "code": "SUCCESS", "rows_affected": 1, "query_id": "01bccd5e-3204-7f69-0002-4ad60005d322"}, "message": null, "failures": 0, "unique_id": "test.live_c360.source_not_null_live_external_ext_live_users_id.b0694be37c", "compiled": true, "compiled_code": "\n    \n    \n\n\n\nselect *\nfrom MYDB.LIVE_DATA.ext_live_users\nwhere id is null\n\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.source_not_null_live_external_ext_live_users_id", "batch_results": null}, {"status": "pass", "timing": [{"name": "compile", "started_at": "2025-06-04T10:38:14.447585Z", "completed_at": "2025-06-04T10:38:14.460386Z"}, {"name": "execute", "started_at": "2025-06-04T10:38:14.460386Z", "completed_at": "2025-06-04T10:38:15.653804Z"}], "thread_id": "Thread-2 (worker)", "execution_time": 1.2178027629852295, "adapter_response": {"_message": "SUCCESS 1", "code": "SUCCESS", "rows_affected": 1, "query_id": "01bccd5e-3204-7ffc-0002-4ad60006624e"}, "message": null, "failures": 0, "unique_id": "test.live_c360.source_unique_live_external_ext_live_orders_id.82585e5b4d", "compiled": true, "compiled_code": "\n    \n    \n\nselect\n    id as unique_field,\n    count(*) as n_records\n\nfrom MYDB.LIVE_DATA.ext_live_orders\nwhere id is not null\ngroup by id\nhaving count(*) > 1\n\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.source_unique_live_external_ext_live_orders_id", "batch_results": null}, {"status": "pass", "timing": [{"name": "compile", "started_at": "2025-06-04T10:38:14.420194Z", "completed_at": "2025-06-04T10:38:14.436999Z"}, {"name": "execute", "started_at": "2025-06-04T10:38:14.436999Z", "completed_at": "2025-06-04T10:38:15.984622Z"}], "thread_id": "Thread-3 (worker)", "execution_time": 1.5715384483337402, "adapter_response": {"_message": "SUCCESS 1", "code": "SUCCESS", "rows_affected": 1, "query_id": "01bccd5e-3204-800f-0002-4ad6000651f6"}, "message": null, "failures": 0, "unique_id": "test.live_c360.source_unique_live_external_ext_live_events_event_id.eeb702f8fc", "compiled": true, "compiled_code": "\n    \n    \n\nselect\n    event_id as unique_field,\n    count(*) as n_records\n\nfrom MYDB.LIVE_DATA.ext_live_events\nwhere event_id is not null\ngroup by event_id\nhaving count(*) > 1\n\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.source_unique_live_external_ext_live_events_event_id", "batch_results": null}, {"status": "warn", "timing": [{"name": "compile", "started_at": "2025-06-04T10:38:13.972268Z", "completed_at": "2025-06-04T10:38:13.986525Z"}, {"name": "execute", "started_at": "2025-06-04T10:38:13.986525Z", "completed_at": "2025-06-04T10:38:16.123764Z"}], "thread_id": "Thread-1 (worker)", "execution_time": 2.1545217037200928, "adapter_response": {"_message": "SUCCESS 1", "code": "SUCCESS", "rows_affected": 1, "query_id": "01bccd5e-3204-800f-0002-4ad6000651fa"}, "message": "Got 396000 results, configured to warn if != 0", "failures": 396000, "unique_id": "test.live_c360.source_relationships_live_external_ext_live_events_user_id__id__source_live_external_ext_live_users_.ba15555d86", "compiled": true, "compiled_code": "\n    \n    \n\nwith child as (\n    select user_id as from_field\n    from MYDB.LIVE_DATA.ext_live_events\n    where user_id is not null\n),\n\nparent as (\n    select id as to_field\n    from MYDB.LIVE_DATA.ext_live_users\n)\n\nselect\n    from_field\n\nfrom child\nleft join parent\n    on child.from_field = parent.to_field\n\nwhere parent.to_field is null\n\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.source_relationships_live_exte_f8dcab25f08d3d0cecc66acc19c0f320", "batch_results": null}, {"status": "warn", "timing": [{"name": "compile", "started_at": "2025-06-04T10:38:14.379302Z", "completed_at": "2025-06-04T10:38:14.389252Z"}, {"name": "execute", "started_at": "2025-06-04T10:38:14.397286Z", "completed_at": "2025-06-04T10:38:16.129766Z"}], "thread_id": "Thread-4 (worker)", "execution_time": 1.750464916229248, "adapter_response": {"_message": "SUCCESS 1", "code": "SUCCESS", "rows_affected": 1, "query_id": "01bccd5e-3204-7f69-0002-4ad60005d336"}, "message": "Got 72000 results, configured to warn if != 0", "failures": 72000, "unique_id": "test.live_c360.source_relationships_live_external_ext_live_orders_user_id__id__source_live_external_ext_live_users_.8d45efa758", "compiled": true, "compiled_code": "\n    \n    \n\nwith child as (\n    select user_id as from_field\n    from MYDB.LIVE_DATA.ext_live_orders\n    where user_id is not null\n),\n\nparent as (\n    select id as to_field\n    from MYDB.LIVE_DATA.ext_live_users\n)\n\nselect\n    from_field\n\nfrom child\nleft join parent\n    on child.from_field = parent.to_field\n\nwhere parent.to_field is null\n\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.source_relationships_live_exte_3905f58a8f4f802f3d4269e076a3f201", "batch_results": null}, {"status": "pass", "timing": [{"name": "compile", "started_at": "2025-06-04T10:38:15.663400Z", "completed_at": "2025-06-04T10:38:15.676579Z"}, {"name": "execute", "started_at": "2025-06-04T10:38:15.677577Z", "completed_at": "2025-06-04T10:38:16.413731Z"}], "thread_id": "Thread-2 (worker)", "execution_time": 0.7523391246795654, "adapter_response": {"_message": "SUCCESS 1", "code": "SUCCESS", "rows_affected": 1, "query_id": "01bccd5e-3204-7ffc-0002-4ad600066252"}, "message": null, "failures": 0, "unique_id": "test.live_c360.source_unique_live_external_ext_live_users_id.4af1be4e1f", "compiled": true, "compiled_code": "\n    \n    \n\nselect\n    id as unique_field,\n    count(*) as n_records\n\nfrom MYDB.LIVE_DATA.ext_live_users\nwhere id is not null\ngroup by id\nhaving count(*) > 1\n\n\n", "relation_name": "MYDB.LIVE_DATA_dbt_test__audit.source_unique_live_external_ext_live_users_id", "batch_results": null}], "elapsed_time": 10.45048999786377, "args": {"log_level": "info", "log_file_max_bytes": 10485760, "write_json": true, "indirect_selection": "eager", "state_modified_compare_vars": false, "macro_debugging": false, "partial_parse_file_diff": true, "quiet": false, "favor_state": false, "select": [], "send_anonymous_usage_stats": true, "introspect": true, "log_path": "G:\\github\\S3 dbt-snowflake c360\\experiment2\\dbt_live\\logs", "show_resource_report": false, "require_batched_execution_for_custom_microbatch_strategy": false, "printer_width": 80, "require_explicit_package_overrides_for_builtin_materializations": true, "use_colors_file": true, "project_dir": "G:\\github\\S3 dbt-snowflake c360\\experiment2\\dbt_live", "source_freshness_run_project_hooks": false, "defer": false, "populate_cache": true, "warn_error_options": {"include": [], "exclude": []}, "require_yaml_configuration_for_mf_time_spines": false, "which": "test", "log_format_file": "debug", "resource_types": [], "use_colors": true, "version_check": true, "static_parser": true, "invocation_command": "dbt test", "log_level_file": "debug", "partial_parse": true, "print": true, "exclude_resource_types": [], "require_resource_names_without_spaces": false, "skip_nodes_if_on_run_start_fails": false, "state_modified_compare_more_unrendered_values": false, "cache_selected_only": false, "exclude": [], "vars": {}, "strict_mode": false, "profiles_dir": "G:\\github\\S3 dbt-snowflake c360\\experiment2\\dbt_live", "require_nested_cumulative_type_params": false, "log_format": "default"}}