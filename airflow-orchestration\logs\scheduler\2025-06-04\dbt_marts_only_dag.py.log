[2025-06-04T10:06:59.762+0000] {processor.py:161} INFO - Started process (PID=47) to work on /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:06:59.765+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_marts_only_dag.py for tasks to queue
[2025-06-04T10:06:59.778+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:06:59.778+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:07:00.023+0000] {processor.py:840} INFO - DAG(s) 'dbt_marts_only' retrieved from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:07:00.533+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:07:00.533+0000] {override.py:1769} INFO - Created Permission View: can edit on DAG:dbt_marts_only
[2025-06-04T10:07:00.546+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:07:00.546+0000] {override.py:1769} INFO - Created Permission View: can delete on DAG:dbt_marts_only
[2025-06-04T10:07:00.555+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:07:00.555+0000] {override.py:1769} INFO - Created Permission View: can read on DAG:dbt_marts_only
[2025-06-04T10:07:00.556+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:07:00.556+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:07:00.572+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:07:00.572+0000] {dag.py:3058} INFO - Creating ORM DAG for dbt_marts_only
[2025-06-04T10:07:00.586+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:07:00.585+0000] {dag.py:3823} INFO - Setting next_dagrun for dbt_marts_only to 2025-06-03 03:00:00+00:00, run_after=2025-06-04 03:00:00+00:00
[2025-06-04T10:07:00.620+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_marts_only_dag.py took 0.867 seconds
[2025-06-04T10:07:31.394+0000] {processor.py:161} INFO - Started process (PID=64) to work on /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:07:31.399+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_marts_only_dag.py for tasks to queue
[2025-06-04T10:07:31.402+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:07:31.401+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:07:31.440+0000] {processor.py:840} INFO - DAG(s) 'dbt_marts_only' retrieved from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:07:31.483+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:07:31.483+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:07:31.507+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:07:31.506+0000] {dag.py:3823} INFO - Setting next_dagrun for dbt_marts_only to 2025-06-03 03:00:00+00:00, run_after=2025-06-04 03:00:00+00:00
[2025-06-04T10:07:31.545+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_marts_only_dag.py took 0.155 seconds
[2025-06-04T10:08:02.437+0000] {processor.py:161} INFO - Started process (PID=75) to work on /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:08:02.440+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_marts_only_dag.py for tasks to queue
[2025-06-04T10:08:02.446+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:08:02.446+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:08:02.489+0000] {processor.py:840} INFO - DAG(s) 'dbt_marts_only' retrieved from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:08:02.533+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:08:02.532+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:08:02.586+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_marts_only_dag.py took 0.157 seconds
[2025-06-04T10:08:33.187+0000] {processor.py:161} INFO - Started process (PID=86) to work on /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:08:33.189+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_marts_only_dag.py for tasks to queue
[2025-06-04T10:08:33.191+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:08:33.191+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:08:33.222+0000] {processor.py:840} INFO - DAG(s) 'dbt_marts_only' retrieved from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:08:33.268+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:08:33.267+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:08:33.337+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_marts_only_dag.py took 0.154 seconds
[2025-06-04T10:09:04.303+0000] {processor.py:161} INFO - Started process (PID=97) to work on /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:09:04.305+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_marts_only_dag.py for tasks to queue
[2025-06-04T10:09:04.308+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:09:04.307+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:09:04.339+0000] {processor.py:840} INFO - DAG(s) 'dbt_marts_only' retrieved from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:09:04.384+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:09:04.384+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:09:04.436+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_marts_only_dag.py took 0.137 seconds
[2025-06-04T10:09:35.126+0000] {processor.py:161} INFO - Started process (PID=108) to work on /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:09:35.131+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_marts_only_dag.py for tasks to queue
[2025-06-04T10:09:35.133+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:09:35.133+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:09:35.166+0000] {processor.py:840} INFO - DAG(s) 'dbt_marts_only' retrieved from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:09:35.211+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:09:35.211+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:09:35.272+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_marts_only_dag.py took 0.151 seconds
[2025-06-04T10:10:05.949+0000] {processor.py:161} INFO - Started process (PID=119) to work on /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:10:05.952+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_marts_only_dag.py for tasks to queue
[2025-06-04T10:10:05.968+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:10:05.967+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:10:06.042+0000] {processor.py:840} INFO - DAG(s) 'dbt_marts_only' retrieved from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:10:06.118+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:10:06.118+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:10:06.173+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_marts_only_dag.py took 0.232 seconds
[2025-06-04T10:10:36.925+0000] {processor.py:161} INFO - Started process (PID=130) to work on /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:10:36.930+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_marts_only_dag.py for tasks to queue
[2025-06-04T10:10:36.932+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:10:36.932+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:10:36.970+0000] {processor.py:840} INFO - DAG(s) 'dbt_marts_only' retrieved from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:10:37.011+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:10:37.011+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:10:37.072+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_marts_only_dag.py took 0.153 seconds
[2025-06-04T10:11:07.689+0000] {processor.py:161} INFO - Started process (PID=141) to work on /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:11:07.691+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_marts_only_dag.py for tasks to queue
[2025-06-04T10:11:07.695+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:11:07.694+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:11:07.725+0000] {processor.py:840} INFO - DAG(s) 'dbt_marts_only' retrieved from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:11:07.769+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:11:07.769+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:11:07.818+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_marts_only_dag.py took 0.133 seconds
[2025-06-04T10:11:38.429+0000] {processor.py:161} INFO - Started process (PID=152) to work on /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:11:38.430+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_marts_only_dag.py for tasks to queue
[2025-06-04T10:11:38.435+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:11:38.434+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:11:38.477+0000] {processor.py:840} INFO - DAG(s) 'dbt_marts_only' retrieved from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:11:38.540+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:11:38.540+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:11:38.591+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_marts_only_dag.py took 0.167 seconds
[2025-06-04T10:12:09.281+0000] {processor.py:161} INFO - Started process (PID=163) to work on /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:12:09.283+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_marts_only_dag.py for tasks to queue
[2025-06-04T10:12:09.288+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:12:09.287+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:12:09.320+0000] {processor.py:840} INFO - DAG(s) 'dbt_marts_only' retrieved from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:12:09.363+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:12:09.363+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:12:09.410+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_marts_only_dag.py took 0.133 seconds
[2025-06-04T10:12:40.012+0000] {processor.py:161} INFO - Started process (PID=174) to work on /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:12:40.016+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_marts_only_dag.py for tasks to queue
[2025-06-04T10:12:40.019+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:12:40.018+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:12:40.052+0000] {processor.py:840} INFO - DAG(s) 'dbt_marts_only' retrieved from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:12:40.093+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:12:40.093+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:12:40.122+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:12:40.122+0000] {dag.py:3823} INFO - Setting next_dagrun for dbt_marts_only to 2025-06-04 03:00:00+00:00, run_after=2025-06-05 03:00:00+00:00
[2025-06-04T10:12:40.144+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_marts_only_dag.py took 0.137 seconds
[2025-06-04T10:13:10.690+0000] {processor.py:161} INFO - Started process (PID=185) to work on /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:13:10.692+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_marts_only_dag.py for tasks to queue
[2025-06-04T10:13:10.694+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:13:10.694+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:13:10.728+0000] {processor.py:840} INFO - DAG(s) 'dbt_marts_only' retrieved from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:13:10.769+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:13:10.768+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:13:10.794+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:13:10.794+0000] {dag.py:3823} INFO - Setting next_dagrun for dbt_marts_only to 2025-06-04 03:00:00+00:00, run_after=2025-06-05 03:00:00+00:00
[2025-06-04T10:13:10.818+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_marts_only_dag.py took 0.133 seconds
[2025-06-04T10:13:41.367+0000] {processor.py:161} INFO - Started process (PID=196) to work on /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:13:41.369+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_marts_only_dag.py for tasks to queue
[2025-06-04T10:13:41.372+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:13:41.372+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:13:41.407+0000] {processor.py:840} INFO - DAG(s) 'dbt_marts_only' retrieved from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:13:41.448+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:13:41.448+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:13:41.475+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:13:41.475+0000] {dag.py:3823} INFO - Setting next_dagrun for dbt_marts_only to 2025-06-04 03:00:00+00:00, run_after=2025-06-05 03:00:00+00:00
[2025-06-04T10:13:41.498+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_marts_only_dag.py took 0.135 seconds
[2025-06-04T10:14:12.319+0000] {processor.py:161} INFO - Started process (PID=207) to work on /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:14:12.326+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_marts_only_dag.py for tasks to queue
[2025-06-04T10:14:12.337+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:14:12.332+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:14:12.365+0000] {processor.py:840} INFO - DAG(s) 'dbt_marts_only' retrieved from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:14:12.405+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:14:12.405+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:14:12.432+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:14:12.431+0000] {dag.py:3823} INFO - Setting next_dagrun for dbt_marts_only to 2025-06-04 03:00:00+00:00, run_after=2025-06-05 03:00:00+00:00
[2025-06-04T10:14:12.458+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_marts_only_dag.py took 0.143 seconds
[2025-06-04T10:14:43.048+0000] {processor.py:161} INFO - Started process (PID=218) to work on /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:14:43.050+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_marts_only_dag.py for tasks to queue
[2025-06-04T10:14:43.052+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:14:43.052+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:14:43.095+0000] {processor.py:840} INFO - DAG(s) 'dbt_marts_only' retrieved from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:14:43.140+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:14:43.140+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:14:43.167+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:14:43.167+0000] {dag.py:3823} INFO - Setting next_dagrun for dbt_marts_only to 2025-06-04 03:00:00+00:00, run_after=2025-06-05 03:00:00+00:00
[2025-06-04T10:14:43.203+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_marts_only_dag.py took 0.160 seconds
[2025-06-04T10:15:13.716+0000] {processor.py:161} INFO - Started process (PID=229) to work on /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:15:13.718+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_marts_only_dag.py for tasks to queue
[2025-06-04T10:15:13.720+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:15:13.720+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:15:13.749+0000] {processor.py:840} INFO - DAG(s) 'dbt_marts_only' retrieved from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:15:13.792+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:15:13.792+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:15:13.817+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:15:13.817+0000] {dag.py:3823} INFO - Setting next_dagrun for dbt_marts_only to 2025-06-04 03:00:00+00:00, run_after=2025-06-05 03:00:00+00:00
[2025-06-04T10:15:13.842+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_marts_only_dag.py took 0.130 seconds
[2025-06-04T10:15:44.507+0000] {processor.py:161} INFO - Started process (PID=240) to work on /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:15:44.509+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_marts_only_dag.py for tasks to queue
[2025-06-04T10:15:44.512+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:15:44.512+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:15:44.557+0000] {processor.py:840} INFO - DAG(s) 'dbt_marts_only' retrieved from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:15:44.630+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:15:44.630+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:15:44.910+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:15:44.910+0000] {dag.py:3823} INFO - Setting next_dagrun for dbt_marts_only to 2025-06-04 03:00:00+00:00, run_after=2025-06-05 03:00:00+00:00
[2025-06-04T10:15:44.931+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_marts_only_dag.py took 0.428 seconds
[2025-06-04T10:16:15.394+0000] {processor.py:161} INFO - Started process (PID=251) to work on /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:16:15.400+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_marts_only_dag.py for tasks to queue
[2025-06-04T10:16:15.404+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:16:15.404+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:16:15.439+0000] {processor.py:840} INFO - DAG(s) 'dbt_marts_only' retrieved from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:16:15.490+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:16:15.490+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:16:15.742+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:16:15.741+0000] {dag.py:3823} INFO - Setting next_dagrun for dbt_marts_only to 2025-06-04 03:00:00+00:00, run_after=2025-06-05 03:00:00+00:00
[2025-06-04T10:16:15.775+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_marts_only_dag.py took 0.385 seconds
[2025-06-04T10:16:46.608+0000] {processor.py:161} INFO - Started process (PID=262) to work on /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:16:46.609+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_marts_only_dag.py for tasks to queue
[2025-06-04T10:16:46.613+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:16:46.613+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:16:46.644+0000] {processor.py:840} INFO - DAG(s) 'dbt_marts_only' retrieved from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:16:46.688+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:16:46.688+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:16:46.714+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:16:46.714+0000] {dag.py:3823} INFO - Setting next_dagrun for dbt_marts_only to 2025-06-04 03:00:00+00:00, run_after=2025-06-05 03:00:00+00:00
[2025-06-04T10:16:46.735+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_marts_only_dag.py took 0.133 seconds
[2025-06-04T10:17:17.394+0000] {processor.py:161} INFO - Started process (PID=273) to work on /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:17:17.396+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_marts_only_dag.py for tasks to queue
[2025-06-04T10:17:17.400+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:17:17.400+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:17:17.429+0000] {processor.py:840} INFO - DAG(s) 'dbt_marts_only' retrieved from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:17:17.470+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:17:17.469+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:17:17.495+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:17:17.495+0000] {dag.py:3823} INFO - Setting next_dagrun for dbt_marts_only to 2025-06-04 03:00:00+00:00, run_after=2025-06-05 03:00:00+00:00
[2025-06-04T10:17:17.515+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_marts_only_dag.py took 0.126 seconds
[2025-06-04T10:17:48.229+0000] {processor.py:161} INFO - Started process (PID=284) to work on /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:17:48.233+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_marts_only_dag.py for tasks to queue
[2025-06-04T10:17:48.237+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:17:48.237+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:17:48.274+0000] {processor.py:840} INFO - DAG(s) 'dbt_marts_only' retrieved from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:17:48.315+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:17:48.315+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:17:48.339+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:17:48.338+0000] {dag.py:3823} INFO - Setting next_dagrun for dbt_marts_only to 2025-06-04 03:00:00+00:00, run_after=2025-06-05 03:00:00+00:00
[2025-06-04T10:17:48.361+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_marts_only_dag.py took 0.136 seconds
[2025-06-04T10:18:18.936+0000] {processor.py:161} INFO - Started process (PID=295) to work on /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:18:18.937+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_marts_only_dag.py for tasks to queue
[2025-06-04T10:18:18.942+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:18:18.941+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:18:18.979+0000] {processor.py:840} INFO - DAG(s) 'dbt_marts_only' retrieved from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:18:19.023+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:18:19.022+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:18:19.047+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:18:19.047+0000] {dag.py:3823} INFO - Setting next_dagrun for dbt_marts_only to 2025-06-04 03:00:00+00:00, run_after=2025-06-05 03:00:00+00:00
[2025-06-04T10:18:19.071+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_marts_only_dag.py took 0.140 seconds
[2025-06-04T10:18:49.661+0000] {processor.py:161} INFO - Started process (PID=306) to work on /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:18:49.663+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_marts_only_dag.py for tasks to queue
[2025-06-04T10:18:49.668+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:18:49.667+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:18:49.725+0000] {processor.py:840} INFO - DAG(s) 'dbt_marts_only' retrieved from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:18:49.768+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:18:49.768+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:18:49.792+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:18:49.792+0000] {dag.py:3823} INFO - Setting next_dagrun for dbt_marts_only to 2025-06-04 03:00:00+00:00, run_after=2025-06-05 03:00:00+00:00
[2025-06-04T10:18:49.857+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_marts_only_dag.py took 0.201 seconds
[2025-06-04T10:19:20.430+0000] {processor.py:161} INFO - Started process (PID=317) to work on /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:19:20.432+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_marts_only_dag.py for tasks to queue
[2025-06-04T10:19:20.435+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:19:20.434+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:19:20.462+0000] {processor.py:840} INFO - DAG(s) 'dbt_marts_only' retrieved from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:19:20.507+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:19:20.507+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:19:20.531+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:19:20.531+0000] {dag.py:3823} INFO - Setting next_dagrun for dbt_marts_only to 2025-06-04 03:00:00+00:00, run_after=2025-06-05 03:00:00+00:00
[2025-06-04T10:19:20.553+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_marts_only_dag.py took 0.127 seconds
[2025-06-04T10:19:51.125+0000] {processor.py:161} INFO - Started process (PID=327) to work on /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:19:51.127+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_marts_only_dag.py for tasks to queue
[2025-06-04T10:19:51.131+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:19:51.131+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:19:51.159+0000] {processor.py:840} INFO - DAG(s) 'dbt_marts_only' retrieved from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:19:51.204+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:19:51.204+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:19:51.232+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:19:51.232+0000] {dag.py:3823} INFO - Setting next_dagrun for dbt_marts_only to 2025-06-04 03:00:00+00:00, run_after=2025-06-05 03:00:00+00:00
[2025-06-04T10:19:51.256+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_marts_only_dag.py took 0.135 seconds
[2025-06-04T10:20:21.828+0000] {processor.py:161} INFO - Started process (PID=338) to work on /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:20:21.830+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_marts_only_dag.py for tasks to queue
[2025-06-04T10:20:21.833+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:20:21.832+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:20:21.867+0000] {processor.py:840} INFO - DAG(s) 'dbt_marts_only' retrieved from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:20:21.914+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:20:21.914+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:20:21.938+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:20:21.937+0000] {dag.py:3823} INFO - Setting next_dagrun for dbt_marts_only to 2025-06-04 03:00:00+00:00, run_after=2025-06-05 03:00:00+00:00
[2025-06-04T10:20:21.985+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_marts_only_dag.py took 0.164 seconds
[2025-06-04T10:20:52.727+0000] {processor.py:161} INFO - Started process (PID=349) to work on /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:20:52.729+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_marts_only_dag.py for tasks to queue
[2025-06-04T10:20:52.735+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:20:52.735+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:20:52.769+0000] {processor.py:840} INFO - DAG(s) 'dbt_marts_only' retrieved from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:20:52.826+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:20:52.825+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:20:52.872+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:20:52.871+0000] {dag.py:3823} INFO - Setting next_dagrun for dbt_marts_only to 2025-06-04 03:00:00+00:00, run_after=2025-06-05 03:00:00+00:00
[2025-06-04T10:20:52.899+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_marts_only_dag.py took 0.178 seconds
[2025-06-04T10:21:23.475+0000] {processor.py:161} INFO - Started process (PID=360) to work on /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:21:23.477+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_marts_only_dag.py for tasks to queue
[2025-06-04T10:21:23.480+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:21:23.479+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:21:23.516+0000] {processor.py:840} INFO - DAG(s) 'dbt_marts_only' retrieved from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:21:23.572+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:21:23.572+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:21:23.607+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:21:23.607+0000] {dag.py:3823} INFO - Setting next_dagrun for dbt_marts_only to 2025-06-04 03:00:00+00:00, run_after=2025-06-05 03:00:00+00:00
[2025-06-04T10:21:23.639+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_marts_only_dag.py took 0.169 seconds
[2025-06-04T10:21:54.280+0000] {processor.py:161} INFO - Started process (PID=371) to work on /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:21:54.281+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_marts_only_dag.py for tasks to queue
[2025-06-04T10:21:54.285+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:21:54.285+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:21:54.322+0000] {processor.py:840} INFO - DAG(s) 'dbt_marts_only' retrieved from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:21:54.369+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:21:54.369+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:21:54.398+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:21:54.397+0000] {dag.py:3823} INFO - Setting next_dagrun for dbt_marts_only to 2025-06-04 03:00:00+00:00, run_after=2025-06-05 03:00:00+00:00
[2025-06-04T10:21:54.421+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_marts_only_dag.py took 0.147 seconds
[2025-06-04T10:22:25.085+0000] {processor.py:161} INFO - Started process (PID=382) to work on /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:22:25.087+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_marts_only_dag.py for tasks to queue
[2025-06-04T10:22:25.092+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:22:25.090+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:22:25.122+0000] {processor.py:840} INFO - DAG(s) 'dbt_marts_only' retrieved from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:22:25.164+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:22:25.164+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:22:25.187+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:22:25.187+0000] {dag.py:3823} INFO - Setting next_dagrun for dbt_marts_only to 2025-06-04 03:00:00+00:00, run_after=2025-06-05 03:00:00+00:00
[2025-06-04T10:22:25.213+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_marts_only_dag.py took 0.132 seconds
[2025-06-04T10:22:55.744+0000] {processor.py:161} INFO - Started process (PID=393) to work on /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:22:55.749+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_marts_only_dag.py for tasks to queue
[2025-06-04T10:22:55.752+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:22:55.752+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:22:55.783+0000] {processor.py:840} INFO - DAG(s) 'dbt_marts_only' retrieved from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:22:55.826+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:22:55.824+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:22:55.851+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:22:55.851+0000] {dag.py:3823} INFO - Setting next_dagrun for dbt_marts_only to 2025-06-04 03:00:00+00:00, run_after=2025-06-05 03:00:00+00:00
[2025-06-04T10:22:55.876+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_marts_only_dag.py took 0.138 seconds
[2025-06-04T10:23:26.438+0000] {processor.py:161} INFO - Started process (PID=404) to work on /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:23:26.440+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_marts_only_dag.py for tasks to queue
[2025-06-04T10:23:26.443+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:23:26.443+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:23:26.479+0000] {processor.py:840} INFO - DAG(s) 'dbt_marts_only' retrieved from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:23:26.527+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:23:26.527+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:23:26.557+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:23:26.557+0000] {dag.py:3823} INFO - Setting next_dagrun for dbt_marts_only to 2025-06-04 03:00:00+00:00, run_after=2025-06-05 03:00:00+00:00
[2025-06-04T10:23:26.583+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_marts_only_dag.py took 0.149 seconds
[2025-06-04T10:23:57.218+0000] {processor.py:161} INFO - Started process (PID=415) to work on /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:23:57.220+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_marts_only_dag.py for tasks to queue
[2025-06-04T10:23:57.223+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:23:57.222+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:23:57.280+0000] {processor.py:840} INFO - DAG(s) 'dbt_marts_only' retrieved from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:23:57.340+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:23:57.340+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:23:57.369+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:23:57.369+0000] {dag.py:3823} INFO - Setting next_dagrun for dbt_marts_only to 2025-06-04 03:00:00+00:00, run_after=2025-06-05 03:00:00+00:00
[2025-06-04T10:23:57.395+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_marts_only_dag.py took 0.183 seconds
[2025-06-04T10:24:27.590+0000] {processor.py:161} INFO - Started process (PID=426) to work on /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:24:27.592+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_marts_only_dag.py for tasks to queue
[2025-06-04T10:24:27.596+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:24:27.594+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:24:27.638+0000] {processor.py:840} INFO - DAG(s) 'dbt_marts_only' retrieved from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:24:27.686+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:24:27.685+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:24:27.713+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:24:27.713+0000] {dag.py:3823} INFO - Setting next_dagrun for dbt_marts_only to 2025-06-04 03:00:00+00:00, run_after=2025-06-05 03:00:00+00:00
[2025-06-04T10:24:27.735+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_marts_only_dag.py took 0.150 seconds
[2025-06-04T10:24:58.578+0000] {processor.py:161} INFO - Started process (PID=437) to work on /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:24:58.580+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_marts_only_dag.py for tasks to queue
[2025-06-04T10:24:58.588+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:24:58.588+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:24:58.637+0000] {processor.py:840} INFO - DAG(s) 'dbt_marts_only' retrieved from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:24:58.690+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:24:58.689+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:24:58.719+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:24:58.719+0000] {dag.py:3823} INFO - Setting next_dagrun for dbt_marts_only to 2025-06-04 03:00:00+00:00, run_after=2025-06-05 03:00:00+00:00
[2025-06-04T10:24:58.740+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_marts_only_dag.py took 0.168 seconds
[2025-06-04T10:25:29.229+0000] {processor.py:161} INFO - Started process (PID=448) to work on /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:25:29.231+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_marts_only_dag.py for tasks to queue
[2025-06-04T10:25:29.235+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:25:29.234+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:25:29.267+0000] {processor.py:840} INFO - DAG(s) 'dbt_marts_only' retrieved from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:25:29.320+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:25:29.320+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:25:29.347+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:25:29.346+0000] {dag.py:3823} INFO - Setting next_dagrun for dbt_marts_only to 2025-06-04 03:00:00+00:00, run_after=2025-06-05 03:00:00+00:00
[2025-06-04T10:25:29.374+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_marts_only_dag.py took 0.150 seconds
[2025-06-04T10:25:59.567+0000] {processor.py:161} INFO - Started process (PID=459) to work on /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:25:59.574+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_marts_only_dag.py for tasks to queue
[2025-06-04T10:25:59.578+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:25:59.577+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:25:59.609+0000] {processor.py:840} INFO - DAG(s) 'dbt_marts_only' retrieved from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:25:59.652+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:25:59.652+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:25:59.679+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:25:59.679+0000] {dag.py:3823} INFO - Setting next_dagrun for dbt_marts_only to 2025-06-04 03:00:00+00:00, run_after=2025-06-05 03:00:00+00:00
[2025-06-04T10:25:59.709+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_marts_only_dag.py took 0.147 seconds
[2025-06-04T10:26:29.777+0000] {processor.py:161} INFO - Started process (PID=470) to work on /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:26:29.779+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_marts_only_dag.py for tasks to queue
[2025-06-04T10:26:29.784+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:26:29.783+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:26:29.815+0000] {processor.py:840} INFO - DAG(s) 'dbt_marts_only' retrieved from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:26:29.857+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:26:29.856+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:26:29.883+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:26:29.883+0000] {dag.py:3823} INFO - Setting next_dagrun for dbt_marts_only to 2025-06-04 03:00:00+00:00, run_after=2025-06-05 03:00:00+00:00
[2025-06-04T10:26:29.904+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_marts_only_dag.py took 0.133 seconds
[2025-06-04T10:27:00.982+0000] {processor.py:161} INFO - Started process (PID=481) to work on /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:27:00.985+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_marts_only_dag.py for tasks to queue
[2025-06-04T10:27:00.988+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:27:00.988+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:27:01.023+0000] {processor.py:840} INFO - DAG(s) 'dbt_marts_only' retrieved from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:27:01.071+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:27:01.071+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:27:01.095+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:27:01.094+0000] {dag.py:3823} INFO - Setting next_dagrun for dbt_marts_only to 2025-06-04 03:00:00+00:00, run_after=2025-06-05 03:00:00+00:00
[2025-06-04T10:27:01.120+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_marts_only_dag.py took 0.142 seconds
[2025-06-04T10:27:31.218+0000] {processor.py:161} INFO - Started process (PID=492) to work on /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:27:31.220+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_marts_only_dag.py for tasks to queue
[2025-06-04T10:27:31.222+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:27:31.222+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:27:31.263+0000] {processor.py:840} INFO - DAG(s) 'dbt_marts_only' retrieved from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:27:31.308+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:27:31.307+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:27:31.338+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:27:31.338+0000] {dag.py:3823} INFO - Setting next_dagrun for dbt_marts_only to 2025-06-04 03:00:00+00:00, run_after=2025-06-05 03:00:00+00:00
[2025-06-04T10:27:31.361+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_marts_only_dag.py took 0.148 seconds
[2025-06-04T10:28:02.182+0000] {processor.py:161} INFO - Started process (PID=503) to work on /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:28:02.184+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_marts_only_dag.py for tasks to queue
[2025-06-04T10:28:02.188+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:28:02.186+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:28:02.217+0000] {processor.py:840} INFO - DAG(s) 'dbt_marts_only' retrieved from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:28:02.292+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:28:02.289+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:28:02.343+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:28:02.343+0000] {dag.py:3823} INFO - Setting next_dagrun for dbt_marts_only to 2025-06-04 03:00:00+00:00, run_after=2025-06-05 03:00:00+00:00
[2025-06-04T10:28:02.380+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_marts_only_dag.py took 0.203 seconds
[2025-06-04T10:28:33.063+0000] {processor.py:161} INFO - Started process (PID=514) to work on /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:28:33.064+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_marts_only_dag.py for tasks to queue
[2025-06-04T10:28:33.067+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:28:33.066+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:28:33.158+0000] {processor.py:840} INFO - DAG(s) 'dbt_marts_only' retrieved from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:28:33.230+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:28:33.230+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:28:33.263+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:28:33.262+0000] {dag.py:3823} INFO - Setting next_dagrun for dbt_marts_only to 2025-06-04 03:00:00+00:00, run_after=2025-06-05 03:00:00+00:00
[2025-06-04T10:28:33.294+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_marts_only_dag.py took 0.236 seconds
[2025-06-04T10:29:03.356+0000] {processor.py:161} INFO - Started process (PID=525) to work on /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:29:03.358+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_marts_only_dag.py for tasks to queue
[2025-06-04T10:29:03.360+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:29:03.360+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:29:03.387+0000] {processor.py:840} INFO - DAG(s) 'dbt_marts_only' retrieved from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:29:03.427+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:29:03.427+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:29:03.452+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:29:03.452+0000] {dag.py:3823} INFO - Setting next_dagrun for dbt_marts_only to 2025-06-04 03:00:00+00:00, run_after=2025-06-05 03:00:00+00:00
[2025-06-04T10:29:03.478+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_marts_only_dag.py took 0.125 seconds
[2025-06-04T10:29:34.274+0000] {processor.py:161} INFO - Started process (PID=536) to work on /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:29:34.276+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_marts_only_dag.py for tasks to queue
[2025-06-04T10:29:34.279+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:29:34.279+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:29:34.328+0000] {processor.py:840} INFO - DAG(s) 'dbt_marts_only' retrieved from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:29:34.376+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:29:34.376+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:29:34.404+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:29:34.404+0000] {dag.py:3823} INFO - Setting next_dagrun for dbt_marts_only to 2025-06-04 03:00:00+00:00, run_after=2025-06-05 03:00:00+00:00
[2025-06-04T10:29:34.430+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_marts_only_dag.py took 0.160 seconds
[2025-06-04T10:30:05.100+0000] {processor.py:161} INFO - Started process (PID=555) to work on /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:30:05.106+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_marts_only_dag.py for tasks to queue
[2025-06-04T10:30:05.109+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:30:05.109+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:30:05.141+0000] {processor.py:840} INFO - DAG(s) 'dbt_marts_only' retrieved from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:30:05.201+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:30:05.201+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:30:05.239+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:30:05.239+0000] {dag.py:3823} INFO - Setting next_dagrun for dbt_marts_only to 2025-06-04 03:00:00+00:00, run_after=2025-06-05 03:00:00+00:00
[2025-06-04T10:30:05.284+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_marts_only_dag.py took 0.188 seconds
[2025-06-04T10:30:35.980+0000] {processor.py:161} INFO - Started process (PID=566) to work on /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:30:35.982+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_marts_only_dag.py for tasks to queue
[2025-06-04T10:30:35.985+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:30:35.985+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:30:36.021+0000] {processor.py:840} INFO - DAG(s) 'dbt_marts_only' retrieved from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:30:36.065+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:30:36.065+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:30:36.089+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:30:36.089+0000] {dag.py:3823} INFO - Setting next_dagrun for dbt_marts_only to 2025-06-04 03:00:00+00:00, run_after=2025-06-05 03:00:00+00:00
[2025-06-04T10:30:36.113+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_marts_only_dag.py took 0.137 seconds
[2025-06-04T10:31:06.798+0000] {processor.py:161} INFO - Started process (PID=578) to work on /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:31:06.800+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_marts_only_dag.py for tasks to queue
[2025-06-04T10:31:06.803+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:31:06.802+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:31:06.846+0000] {processor.py:840} INFO - DAG(s) 'dbt_marts_only' retrieved from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:31:06.935+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:31:06.935+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:31:07.016+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:31:07.016+0000] {dag.py:3823} INFO - Setting next_dagrun for dbt_marts_only to 2025-06-04 03:00:00+00:00, run_after=2025-06-05 03:00:00+00:00
[2025-06-04T10:31:07.060+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_marts_only_dag.py took 0.267 seconds
[2025-06-04T10:31:37.160+0000] {processor.py:161} INFO - Started process (PID=589) to work on /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:31:37.163+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_marts_only_dag.py for tasks to queue
[2025-06-04T10:31:37.176+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:31:37.175+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:31:37.258+0000] {processor.py:840} INFO - DAG(s) 'dbt_marts_only' retrieved from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:31:37.332+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:31:37.332+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:31:37.370+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:31:37.370+0000] {dag.py:3823} INFO - Setting next_dagrun for dbt_marts_only to 2025-06-04 03:00:00+00:00, run_after=2025-06-05 03:00:00+00:00
[2025-06-04T10:31:37.401+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_marts_only_dag.py took 0.252 seconds
[2025-06-04T10:32:08.298+0000] {processor.py:161} INFO - Started process (PID=600) to work on /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:32:08.300+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_marts_only_dag.py for tasks to queue
[2025-06-04T10:32:08.303+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:32:08.302+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:32:08.331+0000] {processor.py:840} INFO - DAG(s) 'dbt_marts_only' retrieved from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:32:08.376+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:32:08.376+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:32:08.400+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:32:08.400+0000] {dag.py:3823} INFO - Setting next_dagrun for dbt_marts_only to 2025-06-04 03:00:00+00:00, run_after=2025-06-05 03:00:00+00:00
[2025-06-04T10:32:08.424+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_marts_only_dag.py took 0.129 seconds
[2025-06-04T10:32:38.540+0000] {processor.py:161} INFO - Started process (PID=611) to work on /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:32:38.542+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_marts_only_dag.py for tasks to queue
[2025-06-04T10:32:38.549+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:32:38.549+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:32:38.587+0000] {processor.py:840} INFO - DAG(s) 'dbt_marts_only' retrieved from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:32:38.637+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:32:38.636+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:32:38.845+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:32:38.845+0000] {dag.py:3823} INFO - Setting next_dagrun for dbt_marts_only to 2025-06-04 03:00:00+00:00, run_after=2025-06-05 03:00:00+00:00
[2025-06-04T10:32:38.878+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_marts_only_dag.py took 0.343 seconds
[2025-06-04T10:33:09.798+0000] {processor.py:161} INFO - Started process (PID=622) to work on /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:33:09.800+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_marts_only_dag.py for tasks to queue
[2025-06-04T10:33:09.804+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:33:09.803+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:33:09.877+0000] {processor.py:840} INFO - DAG(s) 'dbt_marts_only' retrieved from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:33:09.928+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:33:09.928+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:33:09.959+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:33:09.959+0000] {dag.py:3823} INFO - Setting next_dagrun for dbt_marts_only to 2025-06-04 03:00:00+00:00, run_after=2025-06-05 03:00:00+00:00
[2025-06-04T10:33:09.991+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_marts_only_dag.py took 0.199 seconds
[2025-06-04T10:33:40.924+0000] {processor.py:161} INFO - Started process (PID=633) to work on /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:33:40.925+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_marts_only_dag.py for tasks to queue
[2025-06-04T10:33:40.929+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:33:40.929+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:33:40.956+0000] {processor.py:840} INFO - DAG(s) 'dbt_marts_only' retrieved from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:33:40.997+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:33:40.996+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:33:41.022+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:33:41.022+0000] {dag.py:3823} INFO - Setting next_dagrun for dbt_marts_only to 2025-06-04 03:00:00+00:00, run_after=2025-06-05 03:00:00+00:00
[2025-06-04T10:33:41.048+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_marts_only_dag.py took 0.129 seconds
[2025-06-04T10:34:11.607+0000] {processor.py:161} INFO - Started process (PID=644) to work on /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:34:11.609+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/dbt_marts_only_dag.py for tasks to queue
[2025-06-04T10:34:11.612+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:34:11.611+0000] {dagbag.py:538} INFO - Filling up the DagBag from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:34:11.643+0000] {processor.py:840} INFO - DAG(s) 'dbt_marts_only' retrieved from /opt/airflow/dags/dbt_marts_only_dag.py
[2025-06-04T10:34:11.693+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:34:11.693+0000] {dag.py:3036} INFO - Sync 1 DAGs
[2025-06-04T10:34:11.717+0000] {logging_mixin.py:188} INFO - [2025-06-04T10:34:11.717+0000] {dag.py:3823} INFO - Setting next_dagrun for dbt_marts_only to 2025-06-04 03:00:00+00:00, run_after=2025-06-05 03:00:00+00:00
[2025-06-04T10:34:11.743+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/dbt_marts_only_dag.py took 0.141 seconds
