[2025-06-04T04:50:01.629+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=non-requeueable deps ti=<TaskInstance: simple_etl_pipeline.run_dbt_models manual__2025-06-04T04:44:47.701793+00:00 [queued]>
[2025-06-04T04:50:01.639+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=requeueable deps ti=<TaskInstance: simple_etl_pipeline.run_dbt_models manual__2025-06-04T04:44:47.701793+00:00 [queued]>
[2025-06-04T04:50:01.640+0000] {taskinstance.py:2170} INFO - Starting attempt 2 of 2
[2025-06-04T04:50:01.652+0000] {taskinstance.py:2191} INFO - Executing <Task(PythonOperator): run_dbt_models> on 2025-06-04 04:44:47.701793+00:00
[2025-06-04T04:50:01.658+0000] {standard_task_runner.py:60} INFO - Started process 307 to run task
[2025-06-04T04:50:01.662+0000] {standard_task_runner.py:87} INFO - Running: ['***', 'tasks', 'run', 'simple_etl_pipeline', 'run_dbt_models', 'manual__2025-06-04T04:44:47.701793+00:00', '--job-id', '80', '--raw', '--subdir', 'DAGS_FOLDER/simple_etl_dag.py', '--cfg-path', '/tmp/tmp97hodlec']
[2025-06-04T04:50:01.663+0000] {standard_task_runner.py:88} INFO - Job 80: Subtask run_dbt_models
[2025-06-04T04:50:01.714+0000] {task_command.py:423} INFO - Running <TaskInstance: simple_etl_pipeline.run_dbt_models manual__2025-06-04T04:44:47.701793+00:00 [running]> on host 8fb7c8dc5097
[2025-06-04T04:50:01.790+0000] {taskinstance.py:2480} INFO - Exporting env vars: AIRFLOW_CTX_DAG_OWNER='data-team' AIRFLOW_CTX_DAG_ID='simple_etl_pipeline' AIRFLOW_CTX_TASK_ID='run_dbt_models' AIRFLOW_CTX_EXECUTION_DATE='2025-06-04T04:44:47.701793+00:00' AIRFLOW_CTX_TRY_NUMBER='2' AIRFLOW_CTX_DAG_RUN_ID='manual__2025-06-04T04:44:47.701793+00:00'
[2025-06-04T04:50:01.791+0000] {simple_etl_dag.py:78} INFO - Running dbt run using Python API
[2025-06-04T04:50:03.147+0000] {simple_etl_dag.py:109} INFO - Executing dbt run
[2025-06-04T04:50:03.246+0000] {logging_mixin.py:188} INFO - 04:50:03  Running with dbt=1.9.6
[2025-06-04T04:50:04.235+0000] {logging_mixin.py:188} INFO - 04:50:04  Registered adapter: snowflake=1.9.4
[2025-06-04T04:50:08.472+0000] {logging_mixin.py:188} INFO - 04:50:08  Encountered an error:
'dbt_snowflake://macros/apply_grants.sql'
[2025-06-04T04:50:08.474+0000] {logging_mixin.py:188} INFO - 04:50:08  Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/cli/requires.py", line 153, in wrapper
    result, success = func(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/cli/requires.py", line 103, in wrapper
    return func(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/cli/requires.py", line 235, in wrapper
    return func(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/cli/requires.py", line 264, in wrapper
    return func(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/cli/requires.py", line 311, in wrapper
    return func(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/cli/requires.py", line 327, in wrapper
    setup_manifest(ctx, write=write, write_perf_info=write_perf_info)
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/cli/requires.py", line 351, in setup_manifest
    ctx.obj["manifest"] = parse_manifest(
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/parser/manifest.py", line 2069, in parse_manifest
    manifest = ManifestLoader.get_full_manifest(
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/parser/manifest.py", line 312, in get_full_manifest
    manifest = loader.load()
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/parser/manifest.py", line 381, in load
    self.load_and_parse_macros(project_parser_files)
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/parser/manifest.py", line 683, in load_and_parse_macros
    block = FileBlock(self.manifest.files[file_id])
KeyError: 'dbt_snowflake://macros/apply_grants.sql'
[2025-06-04T04:50:09.425+0000] {simple_etl_dag.py:116} ERROR - dbt run failed
[2025-06-04T04:50:09.430+0000] {taskinstance.py:2698} ERROR - Task failed with exception
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/models/taskinstance.py", line 433, in _execute_task
    result = execute_callable(context=context, **execute_callable_kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/operators/python.py", line 199, in execute
    return_value = self.execute_callable()
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/operators/python.py", line 216, in execute_callable
    return self.python_callable(*self.op_args, **self.op_kwargs)
  File "/opt/airflow/dags/simple_etl_dag.py", line 219, in run_dbt_models
    return run_dbt_command_python('run', **context)
  File "/opt/airflow/dags/simple_etl_dag.py", line 117, in run_dbt_command_python
    raise Exception(f"dbt {command_type} failed")
Exception: dbt run failed
[2025-06-04T04:50:09.444+0000] {taskinstance.py:1138} INFO - Marking task as FAILED. dag_id=simple_etl_pipeline, task_id=run_dbt_models, execution_date=20250604T044447, start_date=20250604T045001, end_date=20250604T045009
[2025-06-04T04:50:09.467+0000] {standard_task_runner.py:107} ERROR - Failed to execute job 80 for task run_dbt_models (dbt run failed; 307)
[2025-06-04T04:50:09.542+0000] {local_task_job_runner.py:234} INFO - Task exited with return code 1
[2025-06-04T04:50:09.567+0000] {taskinstance.py:3280} INFO - 0 downstream tasks scheduled from follow-on schedule check
