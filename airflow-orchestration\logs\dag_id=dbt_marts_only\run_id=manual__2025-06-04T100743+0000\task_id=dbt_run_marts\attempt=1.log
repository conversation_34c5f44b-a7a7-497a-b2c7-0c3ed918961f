[2025-06-04T10:10:17.562+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=non-requeueable deps ti=<TaskInstance: dbt_marts_only.dbt_run_marts manual__2025-06-04T10:07:43+00:00 [queued]>
[2025-06-04T10:10:17.572+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=requeueable deps ti=<TaskInstance: dbt_marts_only.dbt_run_marts manual__2025-06-04T10:07:43+00:00 [queued]>
[2025-06-04T10:10:17.573+0000] {taskinstance.py:2170} INFO - Starting attempt 1 of 2
[2025-06-04T10:10:17.586+0000] {taskinstance.py:2191} INFO - Executing <Task(PythonOperator): dbt_run_marts> on 2025-06-04 10:07:43+00:00
[2025-06-04T10:10:17.592+0000] {standard_task_runner.py:60} INFO - Started process 137 to run task
[2025-06-04T10:10:17.596+0000] {standard_task_runner.py:87} INFO - Running: ['***', 'tasks', 'run', 'dbt_marts_only', 'dbt_run_marts', 'manual__2025-06-04T10:07:43+00:00', '--job-id', '119', '--raw', '--subdir', 'DAGS_FOLDER/dbt_marts_only_dag.py', '--cfg-path', '/tmp/tmpccfxt7s8']
[2025-06-04T10:10:17.598+0000] {standard_task_runner.py:88} INFO - Job 119: Subtask dbt_run_marts
[2025-06-04T10:10:17.657+0000] {task_command.py:423} INFO - Running <TaskInstance: dbt_marts_only.dbt_run_marts manual__2025-06-04T10:07:43+00:00 [running]> on host bad0c90122e4
[2025-06-04T10:10:17.739+0000] {taskinstance.py:2480} INFO - Exporting env vars: AIRFLOW_CTX_DAG_OWNER='data-team' AIRFLOW_CTX_DAG_ID='dbt_marts_only' AIRFLOW_CTX_TASK_ID='dbt_run_marts' AIRFLOW_CTX_EXECUTION_DATE='2025-06-04T10:07:43+00:00' AIRFLOW_CTX_TRY_NUMBER='1' AIRFLOW_CTX_DAG_RUN_ID='manual__2025-06-04T10:07:43+00:00'
[2025-06-04T10:10:17.740+0000] {dbt_marts_only_dag.py:129} INFO - 🏪 Running dbt marts models...
[2025-06-04T10:10:17.741+0000] {dbt_marts_only_dag.py:62} INFO - 🚀 Running dbt run
[2025-06-04T10:10:17.743+0000] {dbt_marts_only_dag.py:82} INFO - 📋 Executing: dbt run --models marts
[2025-06-04T10:10:26.769+0000] {dbt_marts_only_dag.py:95} INFO - 📄 stdout: [0m10:10:19  Running with dbt=1.9.6
[0m10:10:20  Registered adapter: snowflake=1.9.4
[0m10:10:22  [[33mWARNING[0m]: Configuration paths exist in your dbt_project.yml file which do not apply to any resources.
There are 3 unused configuration paths:
- models.live_c360.incremental
- seeds.live_c360
- snapshots.live_c360
[0m10:10:22  Found 12 models, 21 data tests, 3 sources, 590 macros
[0m10:10:22  
[0m10:10:22  Concurrency: 4 threads (target='live')
[0m10:10:22  
[0m10:10:23  1 of 4 START sql table model LIVE_DATA.dim_users ............................... [RUN]
[0m10:10:23  2 of 4 START sql incremental model LIVE_DATA.dim_users_scd2 .................... [RUN]
[0m10:10:23  3 of 4 START sql table model LIVE_DATA.fact_orders ............................. [RUN]
[0m10:10:23  4 of 4 START sql view model LIVE_DATA_ANALYTICS.mv_fact_orders ................. [RUN]
[0m10:10:24  3 of 4 ERROR creating sql table model LIVE_DATA.fact_orders .................... [[31mERROR[0m in 0.43s]
[0m10:10:24  1 of 4 ERROR creating sql table model LIVE_DATA.dim_users ...................... [[31mERROR[0m in 0.45s]
[0m10:10:24  2 of 4 ERROR creating sql incremental model LIVE_DATA.dim_users_scd2 ........... [[31mERROR[0m in 0.89s]
[0m10:10:24  4 of 4 ERROR creating sql view model LIVE_DATA_ANALYTICS.mv_fact_orders ........ [[31mERROR[0m in 0.90s]
[0m10:10:25  
[0m10:10:25  Finished running 1 incremental model, 2 table models, 1 view model in 0 hours 0 minutes and 2.61 seconds (2.61s).
[0m10:10:25  
[0m10:10:25  [31mCompleted with 4 errors, 0 partial successes, and 0 warnings:[0m
[0m10:10:25  
[0m10:10:25    Database Error in model fact_orders (models/marts/fact_orders.sql)
  091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/marts/fact_orders.sql
[0m10:10:25  
[0m10:10:25    Database Error in model dim_users (models/marts/dim_users.sql)
  091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
  compiled code at target/run/live_c360/models/marts/dim_users.sql
[0m10:10:25  
[0m10:10:25    Database Error in model dim_users_scd2 (models/marts/dim_users_scd2.sql)
  091093 (55000): External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
[0m10:10:25  
[0m10:10:25    Database Error in model mv_fact_orders (models/marts/mv_fact_orders.sql)
  091093 (55000): External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/marts/mv_fact_orders.sql
[0m10:10:25  
[0m10:10:25  Done. PASS=0 WARN=0 ERROR=4 SKIP=0 TOTAL=4

[2025-06-04T10:10:26.771+0000] {dbt_marts_only_dag.py:110} ERROR - ❌ dbt run failed with return code 1
[2025-06-04T10:10:26.772+0000] {dbt_marts_only_dag.py:111} ERROR - ❌ Error output: 
[2025-06-04T10:10:26.773+0000] {dbt_marts_only_dag.py:120} ERROR - ❌ dbt run failed: dbt run failed with return code 1: 
[2025-06-04T10:10:26.774+0000] {taskinstance.py:2698} ERROR - Task failed with exception
Traceback (most recent call last):
  File "/opt/airflow/dags/dbt_marts_only_dag.py", line 112, in run_dbt_command
    raise Exception(f"{error_msg}: {result.stderr}")
Exception: dbt run failed with return code 1: 

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/models/taskinstance.py", line 433, in _execute_task
    result = execute_callable(context=context, **execute_callable_kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/operators/python.py", line 199, in execute
    return_value = self.execute_callable()
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/operators/python.py", line 216, in execute_callable
    return self.python_callable(*self.op_args, **self.op_kwargs)
  File "/opt/airflow/dags/dbt_marts_only_dag.py", line 130, in dbt_run_marts
    return run_dbt_command('run', models='marts', **context)
  File "/opt/airflow/dags/dbt_marts_only_dag.py", line 121, in run_dbt_command
    raise Exception(f"dbt {command_type} failed: {e}")
Exception: dbt run failed: dbt run failed with return code 1: 
[2025-06-04T10:10:26.789+0000] {taskinstance.py:1138} INFO - Marking task as UP_FOR_RETRY. dag_id=dbt_marts_only, task_id=dbt_run_marts, execution_date=20250604T100743, start_date=20250604T101017, end_date=20250604T101026
[2025-06-04T10:10:26.807+0000] {standard_task_runner.py:107} ERROR - Failed to execute job 119 for task dbt_run_marts (dbt run failed: dbt run failed with return code 1: ; 137)
[2025-06-04T10:10:26.838+0000] {local_task_job_runner.py:234} INFO - Task exited with return code 1
[2025-06-04T10:10:26.861+0000] {taskinstance.py:3280} INFO - 0 downstream tasks scheduled from follow-on schedule check
