#!/usr/bin/env python3
"""
End-to-End Daily Pipeline Runner
Orchestrates the complete daily data pipeline from generation to analytics.
"""

import subprocess
import logging
import time
import yaml
import os
from datetime import datetime, timedelta
from pathlib import Path
import sys

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class End2EndPipelineRunner:
    def __init__(self, config_file='config/live_pipeline_config.yml'):
        """Initialize the end-to-end pipeline runner."""
        self.config_file = config_file
        self.config = self._load_config()
        self.start_time = datetime.now()
        logger.info("🚀 End-to-End Pipeline Runner initialized")

    def _load_config(self):
        """Load configuration from YAML file."""
        try:
            with open(self.config_file, 'r') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            logger.error(f"❌ Configuration file {self.config_file} not found")
            raise

    def step_1_generate_daily_data(self, target_date=None):
        """Step 1: Generate daily data and upload to S3."""
        logger.info("📊 STEP 1: Generating daily data...")
        
        try:
            from daily_data_generator import DailyDataGenerator
            
            generator = DailyDataGenerator(self.config_file)
            result = generator.generate_daily_data(target_date)
            
            if result['success']:
                logger.info(f"✅ Step 1 completed: {result['total_records']:,} records generated")
                return result
            else:
                logger.error(f"❌ Step 1 failed: {result.get('error', 'Unknown error')}")
                return None
                
        except Exception as e:
            logger.error(f"❌ Step 1 failed with exception: {e}")
            return None

    def step_2_verify_s3_data(self, generation_result):
        """Step 2: Verify data was uploaded to S3 correctly."""
        logger.info("🔍 STEP 2: Verifying S3 data...")
        
        try:
            import boto3
            s3_client = boto3.client('s3')
            bucket_name = self.config['s3']['input_bucket']
            
            verification_results = {}
            
            for data_type, s3_key in generation_result['files'].items():
                try:
                    response = s3_client.head_object(Bucket=bucket_name, Key=s3_key)
                    file_size = response['ContentLength']
                    last_modified = response['LastModified']
                    
                    verification_results[data_type] = {
                        'exists': True,
                        'size_bytes': file_size,
                        'size_mb': round(file_size / (1024 * 1024), 2),
                        'last_modified': last_modified.isoformat()
                    }
                    
                    logger.info(f"✅ {data_type}: {verification_results[data_type]['size_mb']} MB")
                    
                except Exception as e:
                    verification_results[data_type] = {
                        'exists': False,
                        'error': str(e)
                    }
                    logger.error(f"❌ {data_type}: {e}")
            
            all_verified = all(result['exists'] for result in verification_results.values())
            
            if all_verified:
                logger.info("✅ Step 2 completed: All files verified in S3")
            else:
                logger.error("❌ Step 2 failed: Some files missing in S3")
            
            return verification_results
            
        except Exception as e:
            logger.error(f"❌ Step 2 failed with exception: {e}")
            return None

    def step_3_refresh_snowflake_stages(self):
        """Step 3: Refresh Snowflake external stages and recreate if needed."""
        logger.info("❄️ STEP 3: Refreshing Snowflake external stages...")

        try:
            import snowflake.connector

            connection_params = {
                'account': self.config['snowflake']['account'],
                'user': self.config['snowflake']['user'],
                'password': 'Asdfjkll1234!@#$',  # From experiment 1
                'warehouse': self.config['snowflake']['warehouse'],
                'database': self.config['snowflake']['database'],
                'schema': self.config['snowflake']['schema'],
                'role': self.config['snowflake']['role']
            }

            conn = snowflake.connector.connect(**connection_params)
            cursor = conn.cursor()

            # Set context
            cursor.execute(f"USE DATABASE {self.config['snowflake']['database']}")
            cursor.execute(f"USE SCHEMA {self.config['snowflake']['schema']}")

            # Note: External infrastructure should be created using create_external_tables_simple.py if needed

            # Refresh external tables (if auto-refresh is not working)
            external_tables = ['EXT_LIVE_USERS', 'EXT_LIVE_ORDERS', 'EXT_LIVE_EVENTS']

            refresh_results = {}
            
            for table in external_tables:
                try:
                    # First check if table exists and is accessible
                    cursor.execute(f"SHOW TABLES LIKE '{table}'")
                    table_exists = len(cursor.fetchall()) > 0

                    if not table_exists:
                        refresh_results[table] = {'error': 'Table does not exist or is not accessible'}
                        logger.warning(f"⚠️ {table}: Table does not exist or is not accessible")
                        continue

                    # Check current record count
                    try:
                        cursor.execute(f"SELECT COUNT(*) FROM {table}")
                        count_before = cursor.fetchone()[0]
                    except Exception as count_error:
                        # If we can't count, the table might be invalid
                        logger.warning(f"⚠️ {table}: Cannot count records - {count_error}")
                        count_before = 0

                    # Try to refresh external table
                    try:
                        cursor.execute(f"ALTER EXTERNAL TABLE {table} REFRESH")
                        refresh_success = True
                    except Exception as refresh_error:
                        logger.warning(f"⚠️ {table}: Refresh failed - {refresh_error}")
                        refresh_success = False

                    # Check new record count if refresh was successful
                    if refresh_success:
                        try:
                            cursor.execute(f"SELECT COUNT(*) FROM {table}")
                            count_after = cursor.fetchone()[0]
                        except Exception:
                            count_after = count_before
                    else:
                        count_after = count_before

                    refresh_results[table] = {
                        'count_before': count_before,
                        'count_after': count_after,
                        'new_records': count_after - count_before,
                        'refresh_success': refresh_success
                    }

                    if refresh_success:
                        logger.info(f"✅ {table}: {count_before:,} → {count_after:,} (+{count_after - count_before:,})")
                    else:
                        logger.warning(f"⚠️ {table}: Refresh failed but continuing pipeline")

                except Exception as e:
                    refresh_results[table] = {'error': str(e)}
                    logger.warning(f"⚠️ {table}: {e} - continuing pipeline")
            
            cursor.close()
            conn.close()
            
            logger.info("✅ Step 3 completed: Snowflake stages refreshed")
            return refresh_results
            
        except Exception as e:
            logger.error(f"❌ Step 3 failed with exception: {e}")
            return None

    def step_4_run_dbt_staging(self):
        """Step 4: Run dbt staging models."""
        logger.info("🏗️ STEP 4: Running dbt staging models...")

        try:
            # Change to dbt directory
            original_dir = os.getcwd()
            dbt_dir = Path('dbt_live')
            os.chdir(dbt_dir)

            # Run staging models
            logger.info("Running dbt staging models...")
            result = subprocess.run(['dbt', 'run', '--models', 'staging'], capture_output=True, text=True, timeout=180)

            dbt_results = {
                'staging_run': {
                    'returncode': result.returncode,
                    'stdout': result.stdout,
                    'stderr': result.stderr
                }
            }

            # Change back to original directory
            os.chdir(original_dir)

            if result.returncode == 0:
                logger.info("✅ Step 4 completed: dbt staging models executed")
                return dbt_results
            else:
                logger.error(f"❌ dbt staging run failed")
                logger.error(f"Error output: {result.stderr}")
                logger.error(f"Standard output: {result.stdout}")
                return None

        except subprocess.TimeoutExpired:
            logger.error("❌ Step 4 failed: dbt staging timeout")
            os.chdir(original_dir)
            return None
        except Exception as e:
            logger.error(f"❌ Step 4 failed with exception: {e}")
            os.chdir(original_dir)
            return None

    def step_5_run_dbt_marts(self):
        """Step 5: Run dbt marts models."""
        logger.info("🏪 STEP 5: Running dbt marts models...")

        try:
            # Change to dbt directory
            original_dir = os.getcwd()
            dbt_dir = Path('dbt_live')
            os.chdir(dbt_dir)

            # Run marts models
            logger.info("Running dbt marts models...")
            result = subprocess.run(['dbt', 'run', '--models', 'marts'], capture_output=True, text=True, timeout=300)

            dbt_results = {
                'marts_run': {
                    'returncode': result.returncode,
                    'stdout': result.stdout,
                    'stderr': result.stderr
                }
            }

            # Change back to original directory
            os.chdir(original_dir)

            if result.returncode == 0:
                logger.info("✅ Step 5 completed: dbt marts models executed")
                return dbt_results
            else:
                logger.error(f"❌ dbt marts run failed")
                logger.error(f"Error output: {result.stderr}")
                logger.error(f"Standard output: {result.stdout}")
                return None

        except subprocess.TimeoutExpired:
            logger.error("❌ Step 5 failed: dbt marts timeout")
            os.chdir(original_dir)
            return None
        except Exception as e:
            logger.error(f"❌ Step 5 failed with exception: {e}")
            os.chdir(original_dir)
            return None

    def step_6_run_dbt_monitoring(self):
        """Step 6: Run dbt monitoring models."""
        logger.info("📊 STEP 6: Running dbt monitoring models...")

        try:
            # Change to dbt directory
            original_dir = os.getcwd()
            dbt_dir = Path('dbt_live')
            os.chdir(dbt_dir)

            # Run monitoring models
            logger.info("Running dbt monitoring models...")
            result = subprocess.run(['dbt', 'run', '--models', 'monitoring'], capture_output=True, text=True, timeout=180)

            dbt_results = {
                'monitoring_run': {
                    'returncode': result.returncode,
                    'stdout': result.stdout,
                    'stderr': result.stderr
                }
            }

            # Change back to original directory
            os.chdir(original_dir)

            if result.returncode == 0:
                logger.info("✅ Step 6 completed: dbt monitoring models executed")
                return dbt_results
            else:
                logger.error(f"❌ dbt monitoring run failed")
                logger.error(f"Error output: {result.stderr}")
                logger.error(f"Standard output: {result.stdout}")
                return None

        except subprocess.TimeoutExpired:
            logger.error("❌ Step 6 failed: dbt monitoring timeout")
            os.chdir(original_dir)
            return None
        except Exception as e:
            logger.error(f"❌ Step 6 failed with exception: {e}")
            os.chdir(original_dir)
            return None

    def step_7_run_dbt_tests(self):
        """Step 7: Run dbt tests for all models."""
        logger.info("🧪 STEP 7: Running dbt tests...")

        try:
            # Change to dbt directory
            original_dir = os.getcwd()
            dbt_dir = Path('dbt_live')
            os.chdir(dbt_dir)

            # Run all tests
            logger.info("Running dbt tests...")
            result = subprocess.run(['dbt', 'test'], capture_output=True, text=True, timeout=300)

            dbt_results = {
                'test': {
                    'returncode': result.returncode,
                    'stdout': result.stdout,
                    'stderr': result.stderr
                }
            }

            # Change back to original directory
            os.chdir(original_dir)

            if result.returncode == 0:
                logger.info("✅ Step 7 completed: All dbt tests passed")
                return dbt_results
            else:
                logger.warning("⚠️ Some dbt tests failed, but continuing...")
                logger.warning(f"Test output: {result.stdout}")
                return dbt_results  # Continue even if some tests fail

        except subprocess.TimeoutExpired:
            logger.error("❌ Step 7 failed: dbt tests timeout")
            os.chdir(original_dir)
            return None
        except Exception as e:
            logger.error(f"❌ Step 7 failed with exception: {e}")
            os.chdir(original_dir)
            return None

    def step_8_validate_results(self):
        """Step 8: Validate final results and generate comprehensive summary."""
        logger.info("✅ STEP 8: Validating results...")

        try:
            import snowflake.connector

            connection_params = {
                'account': self.config['snowflake']['account'],
                'user': self.config['snowflake']['user'],
                'password': 'Asdfjkll1234!@#$',
                'warehouse': self.config['snowflake']['warehouse'],
                'database': self.config['snowflake']['database'],
                'schema': self.config['snowflake']['schema'],
                'role': self.config['snowflake']['role']
            }

            conn = snowflake.connector.connect(**connection_params)
            cursor = conn.cursor()

            # Set context
            cursor.execute(f"USE DATABASE {self.config['snowflake']['database']}")
            cursor.execute(f"USE SCHEMA {self.config['snowflake']['schema']}")

            validation_results = {}

            # Check external tables
            logger.info("📋 Validating External Tables:")
            external_tables = ['EXT_LIVE_USERS', 'EXT_LIVE_ORDERS', 'EXT_LIVE_EVENTS']
            for table in external_tables:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    validation_results[f'external_{table.lower()}'] = count
                    logger.info(f"📊 {table}: {count:,} records")
                except Exception as e:
                    validation_results[f'external_{table.lower()}'] = f"Error: {e}"
                    logger.warning(f"⚠️ {table}: {e}")

            # Check staging models
            logger.info("📋 Validating Staging Models:")
            staging_models = ['STG_USERS', 'STG_ORDERS', 'STG_EVENTS']
            for model in staging_models:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {model}")
                    count = cursor.fetchone()[0]
                    validation_results[f'staging_{model.lower()}'] = count
                    logger.info(f"📊 {model}: {count:,} records")
                except Exception as e:
                    validation_results[f'staging_{model.lower()}'] = f"Error: {e}"
                    logger.warning(f"⚠️ {model}: {e}")

            # Check marts models
            logger.info("📋 Validating Marts Models:")
            marts_models = ['DIM_USERS', 'DIM_USERS_SCD2', 'FACT_ORDERS']
            for model in marts_models:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {model}")
                    count = cursor.fetchone()[0]
                    validation_results[f'marts_{model.lower()}'] = count
                    logger.info(f"📊 {model}: {count:,} records")
                except Exception as e:
                    validation_results[f'marts_{model.lower()}'] = f"Error: {e}"
                    logger.warning(f"⚠️ {model}: {e}")

            # Check materialized view in analytics schema
            logger.info("📋 Validating Analytics Views:")
            try:
                cursor.execute("USE SCHEMA LIVE_DATA_ANALYTICS")
                cursor.execute("SELECT COUNT(*) FROM MV_FACT_ORDERS")
                count = cursor.fetchone()[0]
                validation_results['analytics_mv_fact_orders'] = count
                logger.info(f"📊 MV_FACT_ORDERS: {count:,} records")
                cursor.execute("USE SCHEMA LIVE_DATA")  # Switch back
            except Exception as e:
                validation_results['analytics_mv_fact_orders'] = f"Error: {e}"
                logger.warning(f"⚠️ MV_FACT_ORDERS: {e}")

            # Check monitoring models
            logger.info("📋 Validating Monitoring Models:")
            monitoring_models = ['DATA_QUALITY_HEALTH', 'DBT_TEST_HEALTH', 'ETL_HEALTH_DASHBOARD',
                                'PIPELINE_RUNTIME_HEALTH', 'QUERY_HISTORY_HEALTH']
            for model in monitoring_models:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {model}")
                    count = cursor.fetchone()[0]
                    validation_results[f'monitoring_{model.lower()}'] = count
                    logger.info(f"📊 {model}: {count:,} records")
                except Exception as e:
                    validation_results[f'monitoring_{model.lower()}'] = f"Error: {e}"
                    logger.warning(f"⚠️ {model}: {e}")
            
            # Data freshness check
            logger.info("📋 Checking Data Freshness:")
            try:
                cursor.execute("SELECT MAX(generated_at) FROM EXT_LIVE_USERS")
                latest_data = cursor.fetchone()[0]
                if latest_data:
                    data_age_hours = (datetime.now() - latest_data).total_seconds() / 3600
                    validation_results['data_freshness_hours'] = round(data_age_hours, 2)
                    logger.info(f"📅 Data freshness: {data_age_hours:.1f} hours old")
                else:
                    validation_results['data_freshness_hours'] = "No data found"
                    logger.warning("⚠️ No data found for freshness check")
            except Exception as e:
                validation_results['data_freshness_hours'] = f"Error: {e}"
                logger.warning(f"⚠️ Data freshness check failed: {e}")

            # Summary statistics
            total_models_checked = len([k for k in validation_results.keys() if not k.startswith('data_freshness')])
            successful_models = len([v for v in validation_results.values() if isinstance(v, int) and v > 0])
            failed_models = total_models_checked - successful_models

            logger.info(f"📊 Validation Summary: {successful_models}/{total_models_checked} models validated successfully")
            if failed_models > 0:
                logger.warning(f"⚠️ {failed_models} models had validation issues")

            cursor.close()
            conn.close()

            logger.info("✅ Step 8 completed: Results validated")
            return validation_results

        except Exception as e:
            logger.error(f"❌ Step 8 failed with exception: {e}")
            return None

    def run_end2end_pipeline(self, target_date=None):
        """Run the complete end-to-end pipeline."""
        logger.info("🚀 Starting End-to-End Daily Pipeline...")
        logger.info(f"⏰ Pipeline started at: {self.start_time}")
        
        if target_date:
            logger.info(f"📅 Target date: {target_date}")
        else:
            target_date = datetime.now().date()
            logger.info(f"📅 Target date: {target_date} (today)")
        
        pipeline_results = {
            'start_time': self.start_time.isoformat(),
            'target_date': target_date.isoformat(),
            'steps': {}
        }
        
        # Step 1: Generate daily data
        step1_result = self.step_1_generate_daily_data(target_date)
        pipeline_results['steps']['step1_generate_data'] = step1_result
        
        if not step1_result:
            logger.error("💥 Pipeline failed at Step 1")
            return pipeline_results
        
        # Step 2: Verify S3 data
        step2_result = self.step_2_verify_s3_data(step1_result)
        pipeline_results['steps']['step2_verify_s3'] = step2_result
        
        if not step2_result:
            logger.error("💥 Pipeline failed at Step 2")
            return pipeline_results
        
        # Step 3: Refresh Snowflake stages
        step3_result = self.step_3_refresh_snowflake_stages()
        pipeline_results['steps']['step3_refresh_stages'] = step3_result

        # Step 4: Run dbt staging models
        step4_result = self.step_4_run_dbt_staging()
        pipeline_results['steps']['step4_dbt_staging'] = step4_result

        if not step4_result:
            logger.error("💥 Pipeline failed at Step 4 (dbt staging)")
            return pipeline_results

        # Step 5: Run dbt marts models
        step5_result = self.step_5_run_dbt_marts()
        pipeline_results['steps']['step5_dbt_marts'] = step5_result

        if not step5_result:
            logger.error("💥 Pipeline failed at Step 5 (dbt marts)")
            return pipeline_results

        # Step 6: Run dbt monitoring models
        step6_result = self.step_6_run_dbt_monitoring()
        pipeline_results['steps']['step6_dbt_monitoring'] = step6_result

        if not step6_result:
            logger.error("💥 Pipeline failed at Step 6 (dbt monitoring)")
            return pipeline_results

        # Step 7: Run dbt tests
        step7_result = self.step_7_run_dbt_tests()
        pipeline_results['steps']['step7_dbt_tests'] = step7_result

        # Note: We continue even if tests fail, as they are warnings

        # Step 8: Validate results
        step8_result = self.step_8_validate_results()
        pipeline_results['steps']['step8_validate_results'] = step8_result
        
        # Pipeline summary
        end_time = datetime.now()
        duration = end_time - self.start_time
        
        pipeline_results['end_time'] = end_time.isoformat()
        pipeline_results['duration_seconds'] = duration.total_seconds()
        pipeline_results['duration_minutes'] = round(duration.total_seconds() / 60, 2)
        
        logger.info(f"\n{'='*60}")
        logger.info("📋 END-TO-END PIPELINE SUMMARY")
        logger.info(f"{'='*60}")
        logger.info(f"⏰ Start time: {self.start_time}")
        logger.info(f"⏰ End time: {end_time}")
        logger.info(f"⏱️ Duration: {pipeline_results['duration_minutes']} minutes")
        logger.info(f"📅 Target date: {target_date}")
        
        if step1_result:
            logger.info(f"📊 Records generated: {step1_result['total_records']:,}")
        
        if step8_result:
            logger.info("📈 Final record counts:")
            for key, value in step8_result.items():
                if isinstance(value, int):
                    logger.info(f"   {key}: {value:,}")

        # Determine overall success
        success_steps = [
            step1_result is not None,
            step2_result is not None,
            step4_result is not None,  # staging
            step5_result is not None,  # marts
            step6_result is not None,  # monitoring
            step8_result is not None   # validation
        ]
        
        overall_success = all(success_steps)
        pipeline_results['success'] = overall_success
        
        if overall_success:
            logger.info("🎉 END-TO-END PIPELINE COMPLETED SUCCESSFULLY!")
        else:
            logger.error("💥 END-TO-END PIPELINE COMPLETED WITH ERRORS")
        
        return pipeline_results

def main():
    """Main function for command line usage."""
    import argparse
    
    parser = argparse.ArgumentParser(description='End-to-End Daily Pipeline Runner')
    parser.add_argument('--date', type=str, help='Target date (YYYY-MM-DD), defaults to today')
    parser.add_argument('--save-results', action='store_true', help='Save results to file')
    
    args = parser.parse_args()
    
    runner = End2EndPipelineRunner()
    
    if args.date:
        target_date = datetime.strptime(args.date, '%Y-%m-%d').date()
    else:
        target_date = None
    
    results = runner.run_end2end_pipeline(target_date)
    
    if args.save_results:
        results_file = f"pipeline_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.yaml"
        with open(results_file, 'w') as f:
            yaml.dump(results, f, default_flow_style=False, indent=2)
        logger.info(f"📄 Results saved to {results_file}")
    
    sys.exit(0 if results['success'] else 1)

if __name__ == "__main__":
    main()
