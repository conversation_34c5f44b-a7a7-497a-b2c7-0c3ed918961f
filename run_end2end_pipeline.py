#!/usr/bin/env python3
"""
End-to-End Daily Pipeline Runner
Orchestrates the complete daily data pipeline from generation to analytics.
"""

import subprocess
import logging
import time
import yaml
import os
from datetime import datetime, timedelta
from pathlib import Path
import sys

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class End2EndPipelineRunner:
    def __init__(self, config_file='config/live_pipeline_config.yml'):
        """Initialize the end-to-end pipeline runner."""
        self.config_file = config_file
        self.config = self._load_config()
        self.start_time = datetime.now()
        logger.info("🚀 End-to-End Pipeline Runner initialized")

    def _load_config(self):
        """Load configuration from YAML file."""
        try:
            with open(self.config_file, 'r') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            logger.error(f"❌ Configuration file {self.config_file} not found")
            raise

    def step_1_generate_daily_data(self, target_date=None):
        """Step 1: Generate daily data and upload to S3."""
        logger.info("📊 STEP 1: Generating daily data...")
        
        try:
            from daily_data_generator import DailyDataGenerator
            
            generator = DailyDataGenerator(self.config_file)
            result = generator.generate_daily_data(target_date)
            
            if result['success']:
                logger.info(f"✅ Step 1 completed: {result['total_records']:,} records generated")
                return result
            else:
                logger.error(f"❌ Step 1 failed: {result.get('error', 'Unknown error')}")
                return None
                
        except Exception as e:
            logger.error(f"❌ Step 1 failed with exception: {e}")
            return None

    def step_2_verify_s3_data(self, generation_result):
        """Step 2: Verify data was uploaded to S3 correctly."""
        logger.info("🔍 STEP 2: Verifying S3 data...")
        
        try:
            import boto3
            s3_client = boto3.client('s3')
            bucket_name = self.config['s3']['input_bucket']
            
            verification_results = {}
            
            for data_type, s3_key in generation_result['files'].items():
                try:
                    response = s3_client.head_object(Bucket=bucket_name, Key=s3_key)
                    file_size = response['ContentLength']
                    last_modified = response['LastModified']
                    
                    verification_results[data_type] = {
                        'exists': True,
                        'size_bytes': file_size,
                        'size_mb': round(file_size / (1024 * 1024), 2),
                        'last_modified': last_modified.isoformat()
                    }
                    
                    logger.info(f"✅ {data_type}: {verification_results[data_type]['size_mb']} MB")
                    
                except Exception as e:
                    verification_results[data_type] = {
                        'exists': False,
                        'error': str(e)
                    }
                    logger.error(f"❌ {data_type}: {e}")
            
            all_verified = all(result['exists'] for result in verification_results.values())
            
            if all_verified:
                logger.info("✅ Step 2 completed: All files verified in S3")
            else:
                logger.error("❌ Step 2 failed: Some files missing in S3")
            
            return verification_results
            
        except Exception as e:
            logger.error(f"❌ Step 2 failed with exception: {e}")
            return None

    def step_3_refresh_snowflake_stages(self):
        """Step 3: Refresh Snowflake external stages to pick up new data."""
        logger.info("❄️ STEP 3: Refreshing Snowflake external stages...")
        
        try:
            import snowflake.connector
            
            connection_params = {
                'account': self.config['snowflake']['account'],
                'user': self.config['snowflake']['user'],
                'password': 'Asdfjkll1234!@#$',  # From experiment 1
                'warehouse': self.config['snowflake']['warehouse'],
                'database': self.config['snowflake']['database'],
                'schema': self.config['snowflake']['schema'],
                'role': self.config['snowflake']['role']
            }
            
            conn = snowflake.connector.connect(**connection_params)
            cursor = conn.cursor()
            
            # Set context
            cursor.execute(f"USE DATABASE {self.config['snowflake']['database']}")
            cursor.execute(f"USE SCHEMA {self.config['snowflake']['schema']}")
            
            # Refresh external tables (if auto-refresh is not working)
            external_tables = ['EXT_LIVE_USERS', 'EXT_LIVE_ORDERS', 'EXT_LIVE_EVENTS']
            
            refresh_results = {}
            
            for table in external_tables:
                try:
                    # Check current record count
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count_before = cursor.fetchone()[0]
                    
                    # Refresh external table
                    cursor.execute(f"ALTER EXTERNAL TABLE {table} REFRESH")
                    
                    # Check new record count
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count_after = cursor.fetchone()[0]
                    
                    refresh_results[table] = {
                        'count_before': count_before,
                        'count_after': count_after,
                        'new_records': count_after - count_before
                    }
                    
                    logger.info(f"✅ {table}: {count_before:,} → {count_after:,} (+{count_after - count_before:,})")
                    
                except Exception as e:
                    refresh_results[table] = {'error': str(e)}
                    logger.error(f"❌ {table}: {e}")
            
            cursor.close()
            conn.close()
            
            logger.info("✅ Step 3 completed: Snowflake stages refreshed")
            return refresh_results
            
        except Exception as e:
            logger.error(f"❌ Step 3 failed with exception: {e}")
            return None

    def step_4_run_dbt_pipeline(self):
        """Step 4: Run dbt pipeline to process new data."""
        logger.info("🔧 STEP 4: Running dbt pipeline...")
        
        try:
            # Change to dbt directory
            original_dir = os.getcwd()
            dbt_dir = Path('dbt_live')
            os.chdir(dbt_dir)
            
            dbt_results = {}
            
            # Run dbt models
            logger.info("Running dbt models...")
            result = subprocess.run(['dbt', 'run'], capture_output=True, text=True, timeout=300)
            
            dbt_results['run'] = {
                'returncode': result.returncode,
                'stdout': result.stdout,
                'stderr': result.stderr
            }
            
            if result.returncode == 0:
                logger.info("✅ dbt run successful")
                
                # Run dbt tests
                logger.info("Running dbt tests...")
                test_result = subprocess.run(['dbt', 'test'], capture_output=True, text=True, timeout=180)
                
                dbt_results['test'] = {
                    'returncode': test_result.returncode,
                    'stdout': test_result.stdout,
                    'stderr': test_result.stderr
                }
                
                if test_result.returncode == 0:
                    logger.info("✅ dbt tests passed")
                    success = True
                else:
                    logger.warning("⚠️ Some dbt tests failed, but continuing...")
                    success = True  # Continue even if some tests fail
            else:
                logger.error(f"❌ dbt run failed")
                success = False
            
            # Change back to original directory
            os.chdir(original_dir)
            
            if success:
                logger.info("✅ Step 4 completed: dbt pipeline executed")
            else:
                logger.error("❌ Step 4 failed: dbt pipeline errors")
            
            return dbt_results
            
        except subprocess.TimeoutExpired:
            logger.error("❌ Step 4 failed: dbt pipeline timeout")
            os.chdir(original_dir)
            return None
        except Exception as e:
            logger.error(f"❌ Step 4 failed with exception: {e}")
            os.chdir(original_dir)
            return None

    def step_5_validate_results(self):
        """Step 5: Validate final results and generate summary."""
        logger.info("✅ STEP 5: Validating results...")
        
        try:
            import snowflake.connector
            
            connection_params = {
                'account': self.config['snowflake']['account'],
                'user': self.config['snowflake']['user'],
                'password': 'Asdfjkll1234!@#$',
                'warehouse': self.config['snowflake']['warehouse'],
                'database': self.config['snowflake']['database'],
                'schema': self.config['snowflake']['schema'],
                'role': self.config['snowflake']['role']
            }
            
            conn = snowflake.connector.connect(**connection_params)
            cursor = conn.cursor()
            
            # Set context
            cursor.execute(f"USE DATABASE {self.config['snowflake']['database']}")
            cursor.execute(f"USE SCHEMA {self.config['snowflake']['schema']}")
            
            validation_results = {}
            
            # Check external tables
            external_tables = ['EXT_LIVE_USERS', 'EXT_LIVE_ORDERS', 'EXT_LIVE_EVENTS']
            for table in external_tables:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    validation_results[f'external_{table.lower()}'] = count
                    logger.info(f"📊 {table}: {count:,} records")
                except Exception as e:
                    validation_results[f'external_{table.lower()}'] = f"Error: {e}"
            
            # Check dbt models
            dbt_models = ['STG_USERS', 'STG_ORDERS', 'STG_EVENTS', 'DIM_USERS']
            for model in dbt_models:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {model}")
                    count = cursor.fetchone()[0]
                    validation_results[f'dbt_{model.lower()}'] = count
                    logger.info(f"📊 {model}: {count:,} records")
                except Exception as e:
                    validation_results[f'dbt_{model.lower()}'] = f"Error: {e}"
            
            # Data freshness check
            try:
                cursor.execute("SELECT MAX(generated_at) FROM EXT_LIVE_USERS")
                latest_data = cursor.fetchone()[0]
                if latest_data:
                    data_age_hours = (datetime.now() - latest_data).total_seconds() / 3600
                    validation_results['data_freshness_hours'] = round(data_age_hours, 2)
                    logger.info(f"📅 Data freshness: {data_age_hours:.1f} hours old")
            except Exception as e:
                validation_results['data_freshness_hours'] = f"Error: {e}"
            
            cursor.close()
            conn.close()
            
            logger.info("✅ Step 5 completed: Results validated")
            return validation_results
            
        except Exception as e:
            logger.error(f"❌ Step 5 failed with exception: {e}")
            return None

    def run_end2end_pipeline(self, target_date=None):
        """Run the complete end-to-end pipeline."""
        logger.info("🚀 Starting End-to-End Daily Pipeline...")
        logger.info(f"⏰ Pipeline started at: {self.start_time}")
        
        if target_date:
            logger.info(f"📅 Target date: {target_date}")
        else:
            target_date = datetime.now().date()
            logger.info(f"📅 Target date: {target_date} (today)")
        
        pipeline_results = {
            'start_time': self.start_time.isoformat(),
            'target_date': target_date.isoformat(),
            'steps': {}
        }
        
        # Step 1: Generate daily data
        step1_result = self.step_1_generate_daily_data(target_date)
        pipeline_results['steps']['step1_generate_data'] = step1_result
        
        if not step1_result:
            logger.error("💥 Pipeline failed at Step 1")
            return pipeline_results
        
        # Step 2: Verify S3 data
        step2_result = self.step_2_verify_s3_data(step1_result)
        pipeline_results['steps']['step2_verify_s3'] = step2_result
        
        if not step2_result:
            logger.error("💥 Pipeline failed at Step 2")
            return pipeline_results
        
        # Step 3: Refresh Snowflake stages
        step3_result = self.step_3_refresh_snowflake_stages()
        pipeline_results['steps']['step3_refresh_stages'] = step3_result
        
        # Step 4: Run dbt pipeline
        step4_result = self.step_4_run_dbt_pipeline()
        pipeline_results['steps']['step4_dbt_pipeline'] = step4_result
        
        if not step4_result:
            logger.error("💥 Pipeline failed at Step 4")
            return pipeline_results
        
        # Step 5: Validate results
        step5_result = self.step_5_validate_results()
        pipeline_results['steps']['step5_validate_results'] = step5_result
        
        # Pipeline summary
        end_time = datetime.now()
        duration = end_time - self.start_time
        
        pipeline_results['end_time'] = end_time.isoformat()
        pipeline_results['duration_seconds'] = duration.total_seconds()
        pipeline_results['duration_minutes'] = round(duration.total_seconds() / 60, 2)
        
        logger.info(f"\n{'='*60}")
        logger.info("📋 END-TO-END PIPELINE SUMMARY")
        logger.info(f"{'='*60}")
        logger.info(f"⏰ Start time: {self.start_time}")
        logger.info(f"⏰ End time: {end_time}")
        logger.info(f"⏱️ Duration: {pipeline_results['duration_minutes']} minutes")
        logger.info(f"📅 Target date: {target_date}")
        
        if step1_result:
            logger.info(f"📊 Records generated: {step1_result['total_records']:,}")
        
        if step5_result:
            logger.info("📈 Final record counts:")
            for key, value in step5_result.items():
                if isinstance(value, int):
                    logger.info(f"   {key}: {value:,}")
        
        # Determine overall success
        success_steps = [
            step1_result is not None,
            step2_result is not None,
            step4_result is not None,
            step5_result is not None
        ]
        
        overall_success = all(success_steps)
        pipeline_results['success'] = overall_success
        
        if overall_success:
            logger.info("🎉 END-TO-END PIPELINE COMPLETED SUCCESSFULLY!")
        else:
            logger.error("💥 END-TO-END PIPELINE COMPLETED WITH ERRORS")
        
        return pipeline_results

def main():
    """Main function for command line usage."""
    import argparse
    
    parser = argparse.ArgumentParser(description='End-to-End Daily Pipeline Runner')
    parser.add_argument('--date', type=str, help='Target date (YYYY-MM-DD), defaults to today')
    parser.add_argument('--save-results', action='store_true', help='Save results to file')
    
    args = parser.parse_args()
    
    runner = End2EndPipelineRunner()
    
    if args.date:
        target_date = datetime.strptime(args.date, '%Y-%m-%d').date()
    else:
        target_date = None
    
    results = runner.run_end2end_pipeline(target_date)
    
    if args.save_results:
        results_file = f"pipeline_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.yaml"
        with open(results_file, 'w') as f:
            yaml.dump(results, f, default_flow_style=False, indent=2)
        logger.info(f"📄 Results saved to {results_file}")
    
    sys.exit(0 if results['success'] else 1)

if __name__ == "__main__":
    main()
