[2025-06-04T05:18:36.838+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=non-requeueable deps ti=<TaskInstance: simple_etl_pipeline.run_dbt_models manual__2025-06-04T05:13:10.079396+00:00 [queued]>
[2025-06-04T05:18:36.851+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=requeueable deps ti=<TaskInstance: simple_etl_pipeline.run_dbt_models manual__2025-06-04T05:13:10.079396+00:00 [queued]>
[2025-06-04T05:18:36.852+0000] {taskinstance.py:2170} INFO - Starting attempt 2 of 2
[2025-06-04T05:18:36.867+0000] {taskinstance.py:2191} INFO - Executing <Task(PythonOperator): run_dbt_models> on 2025-06-04 05:13:10.079396+00:00
[2025-06-04T05:18:36.874+0000] {standard_task_runner.py:60} INFO - Started process 710 to run task
[2025-06-04T05:18:36.881+0000] {standard_task_runner.py:87} INFO - Running: ['***', 'tasks', 'run', 'simple_etl_pipeline', 'run_dbt_models', 'manual__2025-06-04T05:13:10.079396+00:00', '--job-id', '89', '--raw', '--subdir', 'DAGS_FOLDER/simple_etl_dag.py', '--cfg-path', '/tmp/tmpn4f5ebvx']
[2025-06-04T05:18:36.883+0000] {standard_task_runner.py:88} INFO - Job 89: Subtask run_dbt_models
[2025-06-04T05:18:36.948+0000] {task_command.py:423} INFO - Running <TaskInstance: simple_etl_pipeline.run_dbt_models manual__2025-06-04T05:13:10.079396+00:00 [running]> on host 8fb7c8dc5097
[2025-06-04T05:18:37.049+0000] {taskinstance.py:2480} INFO - Exporting env vars: AIRFLOW_CTX_DAG_OWNER='data-team' AIRFLOW_CTX_DAG_ID='simple_etl_pipeline' AIRFLOW_CTX_TASK_ID='run_dbt_models' AIRFLOW_CTX_EXECUTION_DATE='2025-06-04T05:13:10.079396+00:00' AIRFLOW_CTX_TRY_NUMBER='2' AIRFLOW_CTX_DAG_RUN_ID='manual__2025-06-04T05:13:10.079396+00:00'
[2025-06-04T05:18:37.052+0000] {simple_etl_dag.py:78} INFO - Running dbt run using Python API
[2025-06-04T05:18:38.710+0000] {simple_etl_dag.py:109} INFO - Executing dbt run
[2025-06-04T05:18:38.848+0000] {logging_mixin.py:188} INFO - 05:18:38  Running with dbt=1.9.6
[2025-06-04T05:18:40.093+0000] {logging_mixin.py:188} INFO - 05:18:40  Registered adapter: snowflake=1.9.4
[2025-06-04T05:18:41.797+0000] {logging_mixin.py:188} INFO - 05:18:41  [WARNING]: Configuration paths exist in your dbt_project.yml file which do not apply to any resources.
There are 3 unused configuration paths:
- models.live_c360.incremental
- seeds.live_c360
- snapshots.live_c360
[2025-06-04T05:18:42.070+0000] {logging_mixin.py:188} INFO - 05:18:42  Found 12 models, 21 data tests, 3 sources, 590 macros
[2025-06-04T05:18:42.075+0000] {logging_mixin.py:188} INFO - 05:18:42
[2025-06-04T05:18:42.078+0000] {logging_mixin.py:188} INFO - 05:18:42  Concurrency: 4 threads (target='live')
[2025-06-04T05:18:42.080+0000] {logging_mixin.py:188} INFO - 05:18:42
[2025-06-04T05:18:42.134+0000] {connection.py:486} INFO - Snowflake Connector for Python Version: 3.15.0, Python Version: 3.10.13, Platform: Linux-********-microsoft-standard-WSL2-x86_64-with-glibc2.36
[2025-06-04T05:18:42.135+0000] {connection.py:1391} INFO - Connecting to GLOBAL Snowflake domain
[2025-06-04T05:18:42.137+0000] {connection.py:486} INFO - Snowflake Connector for Python Version: 3.15.0, Python Version: 3.10.13, Platform: Linux-********-microsoft-standard-WSL2-x86_64-with-glibc2.36
[2025-06-04T05:18:42.141+0000] {connection.py:1391} INFO - Connecting to GLOBAL Snowflake domain
[2025-06-04T05:18:42.799+0000] {connection.py:486} INFO - Snowflake Connector for Python Version: 3.15.0, Python Version: 3.10.13, Platform: Linux-********-microsoft-standard-WSL2-x86_64-with-glibc2.36
[2025-06-04T05:18:42.800+0000] {connection.py:1391} INFO - Connecting to GLOBAL Snowflake domain
[2025-06-04T05:18:43.734+0000] {logging_mixin.py:188} INFO - 05:18:43  1 of 12 START sql table model LIVE_DATA.dbt_test_health ........................ [RUN]
[2025-06-04T05:18:43.736+0000] {logging_mixin.py:188} INFO - 05:18:43  2 of 12 START sql table model LIVE_DATA.pipeline_runtime_health ................ [RUN]
[2025-06-04T05:18:43.737+0000] {logging_mixin.py:188} INFO - 05:18:43  3 of 12 START sql table model LIVE_DATA.query_history_health ................... [RUN]
[2025-06-04T05:18:43.739+0000] {logging_mixin.py:188} INFO - 05:18:43  4 of 12 START sql view model LIVE_DATA.stg_events .............................. [RUN]
[2025-06-04T05:18:43.935+0000] {connection.py:486} INFO - Snowflake Connector for Python Version: 3.15.0, Python Version: 3.10.13, Platform: Linux-********-microsoft-standard-WSL2-x86_64-with-glibc2.36
[2025-06-04T05:18:43.937+0000] {connection.py:1391} INFO - Connecting to GLOBAL Snowflake domain
[2025-06-04T05:18:44.440+0000] {logging_mixin.py:188} INFO - 05:18:44  4 of 12 ERROR creating sql view model LIVE_DATA.stg_events ..................... [ERROR in 0.69s]
[2025-06-04T05:18:44.445+0000] {logging_mixin.py:188} INFO - 05:18:44  5 of 12 START sql view model LIVE_DATA.stg_orders .............................. [RUN]
[2025-06-04T05:18:44.625+0000] {logging_mixin.py:188} INFO - 05:18:44  5 of 12 ERROR creating sql view model LIVE_DATA.stg_orders ..................... [ERROR in 0.17s]
[2025-06-04T05:18:44.662+0000] {logging_mixin.py:188} INFO - 05:18:44  6 of 12 START sql view model LIVE_DATA.stg_users ............................... [RUN]
[2025-06-04T05:18:44.664+0000] {logging_mixin.py:188} INFO - 05:18:44  1 of 12 OK created sql table model LIVE_DATA.dbt_test_health ................... [SUCCESS 1 in 0.92s]
[2025-06-04T05:18:44.679+0000] {logging_mixin.py:188} INFO - 05:18:44  7 of 12 SKIP relation LIVE_DATA.fact_orders .................................... [SKIP]
[2025-06-04T05:18:44.684+0000] {logging_mixin.py:188} INFO - 05:18:44  8 of 12 SKIP relation LIVE_DATA_ANALYTICS.mv_fact_orders ....................... [SKIP]
[2025-06-04T05:18:44.842+0000] {logging_mixin.py:188} INFO - 05:18:44  6 of 12 ERROR creating sql view model LIVE_DATA.stg_users ...................... [ERROR in 0.17s]
[2025-06-04T05:18:44.851+0000] {logging_mixin.py:188} INFO - 05:18:44  9 of 12 SKIP relation LIVE_DATA.dim_users ...................................... [SKIP]
[2025-06-04T05:18:44.852+0000] {logging_mixin.py:188} INFO - 05:18:44  10 of 12 SKIP relation LIVE_DATA.dim_users_scd2 ................................ [SKIP]
[2025-06-04T05:18:44.858+0000] {logging_mixin.py:188} INFO - 05:18:44  11 of 12 SKIP relation LIVE_DATA.data_quality_health ........................... [SKIP]
[2025-06-04T05:18:44.865+0000] {logging_mixin.py:188} INFO - 05:18:44  12 of 12 SKIP relation LIVE_DATA.etl_health_dashboard .......................... [SKIP]
[2025-06-04T05:18:46.614+0000] {logging_mixin.py:188} INFO - 05:18:46  3 of 12 OK created sql table model LIVE_DATA.query_history_health .............. [SUCCESS 1 in 2.87s]
[2025-06-04T05:18:46.830+0000] {logging_mixin.py:188} INFO - 05:18:46  2 of 12 OK created sql table model LIVE_DATA.pipeline_runtime_health ........... [SUCCESS 1 in 3.09s]
[2025-06-04T05:18:47.139+0000] {logging_mixin.py:188} INFO - 05:18:47
[2025-06-04T05:18:47.141+0000] {logging_mixin.py:188} INFO - 05:18:47  Finished running 1 incremental model, 7 table models, 4 view models in 0 hours 0 minutes and 5.06 seconds (5.06s).
[2025-06-04T05:18:47.243+0000] {logging_mixin.py:188} INFO - 05:18:47
[2025-06-04T05:18:47.245+0000] {logging_mixin.py:188} INFO - 05:18:47  Completed with 3 errors, 0 partial successes, and 0 warnings:
[2025-06-04T05:18:47.247+0000] {logging_mixin.py:188} INFO - 05:18:47
[2025-06-04T05:18:47.248+0000] {logging_mixin.py:188} INFO - 05:18:47    Database Error in model stg_events (models/staging/stg_events.sql)
  091093 (55000): 01bccc1e-3204-7f80-0002-4ad6000670d6: External table EXT_LIVE_EVENTS marked invalid. Stage S3_LIVE_EVENTS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_events.sql
[2025-06-04T05:18:47.251+0000] {logging_mixin.py:188} INFO - 05:18:47
[2025-06-04T05:18:47.253+0000] {logging_mixin.py:188} INFO - 05:18:47    Database Error in model stg_orders (models/staging/stg_orders.sql)
  091093 (55000): 01bccc1e-3204-7fc5-0002-4ad60006315e: External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_orders.sql
[2025-06-04T05:18:47.255+0000] {logging_mixin.py:188} INFO - 05:18:47
[2025-06-04T05:18:47.256+0000] {logging_mixin.py:188} INFO - 05:18:47    Database Error in model stg_users (models/staging/stg_users.sql)
  091093 (55000): 01bccc1e-3204-7ee0-0002-4ad60005c126: External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_users.sql
[2025-06-04T05:18:47.258+0000] {logging_mixin.py:188} INFO - 05:18:47
[2025-06-04T05:18:47.259+0000] {logging_mixin.py:188} INFO - 05:18:47  Done. PASS=3 WARN=0 ERROR=3 SKIP=6 TOTAL=12
[2025-06-04T05:18:48.361+0000] {simple_etl_dag.py:116} ERROR - dbt run failed
[2025-06-04T05:18:48.363+0000] {taskinstance.py:2698} ERROR - Task failed with exception
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/models/taskinstance.py", line 433, in _execute_task
    result = execute_callable(context=context, **execute_callable_kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/operators/python.py", line 199, in execute
    return_value = self.execute_callable()
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/operators/python.py", line 216, in execute_callable
    return self.python_callable(*self.op_args, **self.op_kwargs)
  File "/opt/airflow/dags/simple_etl_dag.py", line 219, in run_dbt_models
    return run_dbt_command_python('run', **context)
  File "/opt/airflow/dags/simple_etl_dag.py", line 117, in run_dbt_command_python
    raise Exception(f"dbt {command_type} failed")
Exception: dbt run failed
[2025-06-04T05:18:48.373+0000] {taskinstance.py:1138} INFO - Marking task as FAILED. dag_id=simple_etl_pipeline, task_id=run_dbt_models, execution_date=20250604T051310, start_date=20250604T051836, end_date=20250604T051848
[2025-06-04T05:18:48.386+0000] {standard_task_runner.py:107} ERROR - Failed to execute job 89 for task run_dbt_models (dbt run failed; 710)
[2025-06-04T05:18:48.459+0000] {local_task_job_runner.py:234} INFO - Task exited with return code 1
[2025-06-04T05:18:48.483+0000] {taskinstance.py:3280} INFO - 0 downstream tasks scheduled from follow-on schedule check
