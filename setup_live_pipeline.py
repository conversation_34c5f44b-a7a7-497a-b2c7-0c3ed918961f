#!/usr/bin/env python3
"""
Complete Live Data Pipeline Setup
Orchestrates the entire setup process for the live data pipeline.
"""

import subprocess
import logging
import time
import yaml
from pathlib import Path
import sys
import os

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class LivePipelineSetup:
    def __init__(self, config_file='config/live_pipeline_config.yml'):
        """Initialize the live pipeline setup."""
        self.config_file = config_file
        self.config = self._load_config()
        logger.info("✅ Live Pipeline Setup initialized")

    def _load_config(self):
        """Load configuration from YAML file."""
        try:
            with open(self.config_file, 'r') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            logger.error(f"❌ Configuration file {self.config_file} not found")
            raise

    def check_prerequisites(self):
        """Check if all prerequisites are met."""
        logger.info("🔍 Checking prerequisites...")
        
        prerequisites = {
            'aws_cli': 'aws --version',
            'python': 'python --version',
            'dbt': 'dbt --version'
        }
        
        missing = []
        
        for name, command in prerequisites.items():
            try:
                result = subprocess.run(command.split(), capture_output=True, text=True)
                if result.returncode == 0:
                    logger.info(f"✅ {name}: Available")
                else:
                    missing.append(name)
                    logger.error(f"❌ {name}: Not available")
            except FileNotFoundError:
                missing.append(name)
                logger.error(f"❌ {name}: Not found")
        
        if missing:
            logger.error(f"❌ Missing prerequisites: {', '.join(missing)}")
            return False
        
        logger.info("✅ All prerequisites met")
        return True

    def install_dependencies(self):
        """Install required Python packages."""
        logger.info("📦 Installing Python dependencies...")
        
        packages = [
            'dbt-core>=1.6.0',
            'dbt-snowflake>=1.6.0',
            'snowflake-connector-python>=3.0.0',
            'boto3>=1.26.0',
            'pandas>=1.5.0',
            'numpy>=1.24.0',
            'faker>=19.0.0',
            'pyyaml>=6.0'
        ]
        
        try:
            for package in packages:
                logger.info(f"Installing {package}...")
                subprocess.run([sys.executable, '-m', 'pip', 'install', package], 
                             check=True, capture_output=True)
            
            logger.info("✅ All dependencies installed successfully")
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ Failed to install dependencies: {e}")
            return False

    def setup_aws_credentials(self):
        """Set up AWS credentials for Snowflake."""
        logger.info("🔐 Setting up AWS credentials...")
        
        try:
            # Import and run AWS setup
            from setup_aws_credentials import AWSCredentialsSetup
            
            aws_setup = AWSCredentialsSetup(self.config_file)
            result = aws_setup.setup_aws_credentials()
            
            logger.info("✅ AWS credentials setup completed")
            return result['role_arn']
            
        except Exception as e:
            logger.error(f"❌ AWS credentials setup failed: {e}")
            return None

    def setup_snowflake_stages(self, role_arn):
        """Set up Snowflake external stages."""
        logger.info("❄️ Setting up Snowflake external stages...")
        
        try:
            # Import and run Snowflake setup
            from create_snowflake_stages import SnowflakeStagesSetup
            
            snowflake_setup = SnowflakeStagesSetup(self.config_file)
            result = snowflake_setup.setup_snowflake_stages(role_arn)
            
            logger.info("✅ Snowflake external stages setup completed")
            return result
            
        except Exception as e:
            logger.error(f"❌ Snowflake stages setup failed: {e}")
            return None

    def setup_dbt_project(self):
        """Set up dbt project."""
        logger.info("🔧 Setting up dbt project...")
        
        try:
            # Change to dbt directory
            dbt_dir = Path('dbt_live')
            os.chdir(dbt_dir)
            
            # Test dbt connection
            logger.info("Testing dbt connection...")
            result = subprocess.run(['dbt', 'debug'], capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info("✅ dbt connection successful")
            else:
                logger.warning(f"⚠️ dbt debug output: {result.stdout}")
                logger.warning(f"⚠️ dbt debug errors: {result.stderr}")
            
            # Change back to original directory
            os.chdir('..')
            
            logger.info("✅ dbt project setup completed")
            return True
            
        except Exception as e:
            logger.error(f"❌ dbt project setup failed: {e}")
            os.chdir('..')  # Ensure we're back in the original directory
            return False

    def generate_test_data(self, batches=2):
        """Generate initial test data."""
        logger.info(f"📊 Generating {batches} batches of test data...")
        
        try:
            # Import and run data generator
            from live_data_generator import LiveDataGenerator
            
            generator = LiveDataGenerator(self.config_file)
            results = generator.run_continuous_generation(
                interval_minutes=1, 
                max_batches=batches
            )
            
            successful_batches = sum(1 for r in results if r['status'] == 'success')
            logger.info(f"✅ Generated {successful_batches}/{batches} batches successfully")
            
            return successful_batches > 0
            
        except Exception as e:
            logger.error(f"❌ Test data generation failed: {e}")
            return False

    def test_dbt_pipeline(self):
        """Test the dbt pipeline with generated data."""
        logger.info("🧪 Testing dbt pipeline...")
        
        try:
            # Change to dbt directory
            dbt_dir = Path('dbt_live')
            os.chdir(dbt_dir)
            
            # Run dbt models
            logger.info("Running dbt models...")
            result = subprocess.run(['dbt', 'run'], capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info("✅ dbt run successful")
                
                # Run dbt tests
                logger.info("Running dbt tests...")
                test_result = subprocess.run(['dbt', 'test'], capture_output=True, text=True)
                
                if test_result.returncode == 0:
                    logger.info("✅ dbt tests passed")
                    success = True
                else:
                    logger.warning(f"⚠️ Some dbt tests failed: {test_result.stdout}")
                    success = True  # Continue even if some tests fail
            else:
                logger.error(f"❌ dbt run failed: {result.stdout}")
                success = False
            
            # Change back to original directory
            os.chdir('..')
            
            return success
            
        except Exception as e:
            logger.error(f"❌ dbt pipeline test failed: {e}")
            os.chdir('..')  # Ensure we're back in the original directory
            return False

    def create_monitoring_dashboard(self):
        """Create monitoring dashboard configuration."""
        logger.info("📊 Creating monitoring dashboard...")
        
        dashboard_config = {
            'pipeline_metrics': {
                'data_freshness': 'SELECT MAX(generated_at) FROM EXT_LIVE_USERS',
                'record_counts': {
                    'users': 'SELECT COUNT(*) FROM EXT_LIVE_USERS',
                    'orders': 'SELECT COUNT(*) FROM EXT_LIVE_ORDERS',
                    'events': 'SELECT COUNT(*) FROM EXT_LIVE_EVENTS'
                },
                'data_quality': {
                    'null_users': 'SELECT COUNT(*) FROM EXT_LIVE_USERS WHERE id IS NULL',
                    'invalid_amounts': 'SELECT COUNT(*) FROM EXT_LIVE_ORDERS WHERE amount <= 0',
                    'future_dates': 'SELECT COUNT(*) FROM EXT_LIVE_EVENTS WHERE date > CURRENT_TIMESTAMP()'
                }
            },
            'alerts': {
                'data_freshness_threshold_minutes': 30,
                'min_records_per_batch': 50,
                'max_null_percentage': 5.0
            }
        }
        
        # Save dashboard configuration
        dashboard_file = Path('monitoring/dashboard_config.yml')
        dashboard_file.parent.mkdir(exist_ok=True)
        
        with open(dashboard_file, 'w') as f:
            yaml.dump(dashboard_config, f, default_flow_style=False, indent=2)
        
        logger.info(f"✅ Monitoring dashboard configuration saved to {dashboard_file}")
        return True

    def run_complete_setup(self):
        """Run the complete pipeline setup process."""
        logger.info("🚀 Starting complete live data pipeline setup...")
        
        setup_steps = [
            ("Prerequisites Check", self.check_prerequisites),
            ("Install Dependencies", self.install_dependencies),
            ("AWS Credentials Setup", self.setup_aws_credentials),
            ("dbt Project Setup", self.setup_dbt_project),
            ("Generate Test Data", lambda: self.generate_test_data(2)),
            ("Test dbt Pipeline", self.test_dbt_pipeline),
            ("Create Monitoring", self.create_monitoring_dashboard)
        ]
        
        results = {}
        role_arn = None
        
        for step_name, step_function in setup_steps:
            logger.info(f"\n{'='*50}")
            logger.info(f"🔄 Step: {step_name}")
            logger.info(f"{'='*50}")
            
            try:
                if step_name == "AWS Credentials Setup":
                    role_arn = step_function()
                    results[step_name] = role_arn is not None
                    if role_arn:
                        logger.info(f"✅ AWS Role ARN: {role_arn}")
                elif step_name == "Snowflake Stages Setup" and role_arn:
                    results[step_name] = self.setup_snowflake_stages(role_arn) is not None
                else:
                    results[step_name] = step_function()
                
                if results[step_name]:
                    logger.info(f"✅ {step_name} completed successfully")
                else:
                    logger.error(f"❌ {step_name} failed")
                    
            except Exception as e:
                logger.error(f"❌ {step_name} failed with exception: {e}")
                results[step_name] = False
        
        # Setup summary
        logger.info(f"\n{'='*50}")
        logger.info("📋 SETUP SUMMARY")
        logger.info(f"{'='*50}")
        
        successful_steps = sum(1 for success in results.values() if success)
        total_steps = len(results)
        
        for step, success in results.items():
            status = "✅ SUCCESS" if success else "❌ FAILED"
            logger.info(f"{step:<25} {status}")
        
        logger.info(f"\nOverall Success Rate: {successful_steps}/{total_steps} steps")
        
        if successful_steps == total_steps:
            logger.info("🎉 Live data pipeline setup completed successfully!")
            self._print_next_steps()
        else:
            logger.error("💥 Pipeline setup completed with some failures")
            self._print_troubleshooting_steps()
        
        return successful_steps == total_steps

    def _print_next_steps(self):
        """Print next steps after successful setup."""
        logger.info("\n🎯 NEXT STEPS:")
        logger.info("1. Run continuous data generation:")
        logger.info("   python live_data_generator.py --continuous")
        logger.info("2. Monitor pipeline in dbt:")
        logger.info("   cd dbt_live && dbt run && dbt test")
        logger.info("3. Set up scheduling (cron/Airflow) for automated runs")
        logger.info("4. Configure monitoring alerts")

    def _print_troubleshooting_steps(self):
        """Print troubleshooting steps for failed setup."""
        logger.info("\n🔧 TROUBLESHOOTING:")
        logger.info("1. Check AWS CLI configuration: aws configure list")
        logger.info("2. Verify Snowflake connection: cd dbt_live && dbt debug")
        logger.info("3. Check S3 bucket permissions")
        logger.info("4. Review logs above for specific error messages")

def main():
    """Main function for command line usage."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Live Data Pipeline Setup')
    parser.add_argument('--skip-deps', action='store_true', help='Skip dependency installation')
    parser.add_argument('--test-only', action='store_true', help='Only run tests, skip setup')
    
    args = parser.parse_args()
    
    setup = LivePipelineSetup()
    
    if args.test_only:
        logger.info("🧪 Running tests only...")
        success = setup.test_dbt_pipeline()
    else:
        success = setup.run_complete_setup()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
