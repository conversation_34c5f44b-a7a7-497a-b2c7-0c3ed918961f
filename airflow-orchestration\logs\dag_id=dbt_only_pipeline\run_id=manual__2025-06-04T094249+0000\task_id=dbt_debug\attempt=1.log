[2025-06-04T09:49:50.420+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=non-requeueable deps ti=<TaskInstance: dbt_only_pipeline.dbt_debug manual__2025-06-04T09:42:49+00:00 [queued]>
[2025-06-04T09:49:50.432+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=requeueable deps ti=<TaskInstance: dbt_only_pipeline.dbt_debug manual__2025-06-04T09:42:49+00:00 [queued]>
[2025-06-04T09:49:50.433+0000] {taskinstance.py:2170} INFO - Starting attempt 1 of 3
[2025-06-04T09:49:50.449+0000] {taskinstance.py:2191} INFO - Executing <Task(PythonOperator): dbt_debug> on 2025-06-04 09:42:49+00:00
[2025-06-04T09:49:50.455+0000] {standard_task_runner.py:60} INFO - Started process 4331 to run task
[2025-06-04T09:49:50.460+0000] {standard_task_runner.py:87} INFO - Running: ['***', 'tasks', 'run', 'dbt_only_pipeline', 'dbt_debug', 'manual__2025-06-04T09:42:49+00:00', '--job-id', '110', '--raw', '--subdir', 'DAGS_FOLDER/dbt_only_dag.py', '--cfg-path', '/tmp/tmp01hhmh81']
[2025-06-04T09:49:50.462+0000] {standard_task_runner.py:88} INFO - Job 110: Subtask dbt_debug
[2025-06-04T09:49:50.538+0000] {task_command.py:423} INFO - Running <TaskInstance: dbt_only_pipeline.dbt_debug manual__2025-06-04T09:42:49+00:00 [running]> on host 8fb7c8dc5097
[2025-06-04T09:49:50.720+0000] {taskinstance.py:2480} INFO - Exporting env vars: AIRFLOW_CTX_DAG_OWNER='data-team' AIRFLOW_CTX_DAG_ID='dbt_only_pipeline' AIRFLOW_CTX_TASK_ID='dbt_debug' AIRFLOW_CTX_EXECUTION_DATE='2025-06-04T09:42:49+00:00' AIRFLOW_CTX_TRY_NUMBER='1' AIRFLOW_CTX_DAG_RUN_ID='manual__2025-06-04T09:42:49+00:00'
[2025-06-04T09:49:50.722+0000] {dbt_only_dag.py:135} INFO - 🔍 Running dbt debug to validate configuration...
[2025-06-04T09:49:50.722+0000] {dbt_only_dag.py:68} INFO - 🚀 Running dbt debug
[2025-06-04T09:49:50.723+0000] {dbt_only_dag.py:88} INFO - 📋 Executing: dbt debug
[2025-06-04T09:49:56.218+0000] {dbt_only_dag.py:101} INFO - 📄 stdout: [0m09:49:52  Running with dbt=1.9.6
[0m09:49:52  dbt version: 1.9.6
[0m09:49:52  python version: 3.10.13
[0m09:49:52  python path: /usr/local/bin/python
[0m09:49:52  os info: Linux-********-microsoft-standard-WSL2-x86_64-with-glibc2.36
[0m09:49:53  Using profiles dir at /opt/***/workspace/dbt_live
[0m09:49:53  Using profiles.yml file at /opt/***/workspace/dbt_live/profiles.yml
[0m09:49:53  Using dbt_project.yml file at /opt/***/workspace/dbt_live/dbt_project.yml
[0m09:49:53  adapter type: snowflake
[0m09:49:53  adapter version: 1.9.4
[0m09:49:53  Configuration:
[0m09:49:53    profiles.yml file [[32mOK found and valid[0m]
[0m09:49:53    dbt_project.yml file [[32mOK found and valid[0m]
[0m09:49:53  Required dependencies:
[0m09:49:53   - git [[32mOK found[0m]

[0m09:49:53  Connection:
[0m09:49:53    account: SVLFKJI-IX89869
[0m09:49:53    user: XINBINZHANG
[0m09:49:53    database: MYDB
[0m09:49:53    warehouse: COMPUTE_WH
[0m09:49:53    role: ACCOUNTADMIN
[0m09:49:53    schema: LIVE_DATA
[0m09:49:53    authenticator: None
[0m09:49:53    oauth_client_id: None
[0m09:49:53    query_tag: None
[0m09:49:53    client_session_keep_alive: False
[0m09:49:53    host: None
[0m09:49:53    port: None
[0m09:49:53    proxy_host: None
[0m09:49:53    proxy_port: None
[0m09:49:53    protocol: None
[0m09:49:53    connect_retries: 1
[0m09:49:53    connect_timeout: None
[0m09:49:53    retry_on_database_errors: False
[0m09:49:53    retry_all: False
[0m09:49:53    insecure_mode: False
[0m09:49:53    reuse_connections: True
[0m09:49:53  Registered adapter: snowflake=1.9.4
[0m09:49:54    Connection test: [[32mOK connection ok[0m]

[0m09:49:54  [32mAll checks passed![0m

[2025-06-04T09:49:56.219+0000] {dbt_only_dag.py:107} INFO - ✅ dbt debug completed successfully
[2025-06-04T09:49:56.220+0000] {python.py:201} INFO - Done. Returned value was: {'success': True, 'command': 'dbt debug', 'stdout': '\x1b[0m09:49:52  Running with dbt=1.9.6\n\x1b[0m09:49:52  dbt version: 1.9.6\n\x1b[0m09:49:52  python version: 3.10.13\n\x1b[0m09:49:52  python path: /usr/local/bin/python\n\x1b[0m09:49:52  os info: Linux-********-microsoft-standard-WSL2-x86_64-with-glibc2.36\n\x1b[0m09:49:53  Using profiles dir at /opt/***/workspace/dbt_live\n\x1b[0m09:49:53  Using profiles.yml file at /opt/***/workspace/dbt_live/profiles.yml\n\x1b[0m09:49:53  Using dbt_project.yml file at /opt/***/workspace/dbt_live/dbt_project.yml\n\x1b[0m09:49:53  adapter type: snowflake\n\x1b[0m09:49:53  adapter version: 1.9.4\n\x1b[0m09:49:53  Configuration:\n\x1b[0m09:49:53    profiles.yml file [\x1b[32mOK found and valid\x1b[0m]\n\x1b[0m09:49:53    dbt_project.yml file [\x1b[32mOK found and valid\x1b[0m]\n\x1b[0m09:49:53  Required dependencies:\n\x1b[0m09:49:53   - git [\x1b[32mOK found\x1b[0m]\n\n\x1b[0m09:49:53  Connection:\n\x1b[0m09:49:53    account: SVLFKJI-IX89869\n\x1b[0m09:49:53    user: XINBINZHANG\n\x1b[0m09:49:53    database: MYDB\n\x1b[0m09:49:53    warehouse: COMPUTE_WH\n\x1b[0m09:49:53    role: ACCOUNTADMIN\n\x1b[0m09:49:53    schema: LIVE_DATA\n\x1b[0m09:49:53    authenticator: None\n\x1b[0m09:49:53    oauth_client_id: None\n\x1b[0m09:49:53    query_tag: None\n\x1b[0m09:49:53    client_session_keep_alive: False\n\x1b[0m09:49:53    host: None\n\x1b[0m09:49:53    port: None\n\x1b[0m09:49:53    proxy_host: None\n\x1b[0m09:49:53    proxy_port: None\n\x1b[0m09:49:53    protocol: None\n\x1b[0m09:49:53    connect_retries: 1\n\x1b[0m09:49:53    connect_timeout: None\n\x1b[0m09:49:53    retry_on_database_errors: False\n\x1b[0m09:49:53    retry_all: False\n\x1b[0m09:49:53    insecure_mode: False\n\x1b[0m09:49:53    reuse_connections: True\n\x1b[0m09:49:53  Registered adapter: snowflake=1.9.4\n\x1b[0m09:49:54    Connection test: [\x1b[32mOK connection ok\x1b[0m]\n\n\x1b[0m09:49:54  \x1b[32mAll checks passed!\x1b[0m\n', 'stderr': ''}
[2025-06-04T09:49:56.247+0000] {taskinstance.py:1138} INFO - Marking task as SUCCESS. dag_id=dbt_only_pipeline, task_id=dbt_debug, execution_date=20250604T094249, start_date=20250604T094950, end_date=20250604T094956
[2025-06-04T09:49:56.269+0000] {local_task_job_runner.py:234} INFO - Task exited with return code 0
[2025-06-04T09:49:56.299+0000] {taskinstance.py:3280} INFO - 1 downstream tasks scheduled from follow-on schedule check
