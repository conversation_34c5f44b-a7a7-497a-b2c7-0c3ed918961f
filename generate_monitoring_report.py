#!/usr/bin/env python3
"""
ETL Monitoring Report Generator
Creates comprehensive monitoring reports for stakeholders
"""

import snowflake.connector
import pandas as pd
import yaml
from datetime import datetime, timedelta
import os
import json

def load_snowflake_config():
    """Load Snowflake connection configuration"""
    config_path = os.path.join('dbt_live', 'profiles.yml')
    
    with open(config_path, 'r') as file:
        profiles = yaml.safe_load(file)
    
    live_config = profiles['live_c360']['outputs']['live']
    
    return {
        'account': live_config['account'],
        'user': live_config['user'],
        'password': live_config['password'],
        'warehouse': live_config['warehouse'],
        'database': live_config['database'],
        'schema': live_config['schema'],
        'role': live_config['role']
    }

def connect_to_snowflake():
    """Create Snowflake connection"""
    config = load_snowflake_config()
    
    conn = snowflake.connector.connect(
        account=config['account'],
        user=config['user'],
        password=config['password'],
        warehouse=config['warehouse'],
        database=config['database'],
        schema=config['schema'],
        role=config['role']
    )
    
    return conn

def generate_executive_summary():
    """Generate executive summary of pipeline health"""
    conn = connect_to_snowflake()
    
    try:
        # Get overall metrics
        summary_query = """
        SELECT 
            COUNT(*) as total_tables_monitored,
            AVG(overall_quality_score) as avg_quality_score,
            SUM(total_rows) as total_records_processed,
            SUM(data_quality_issues) as total_quality_issues,
            MAX(check_timestamp) as last_check_time
        FROM MYDB.LIVE_DATA.data_quality_health
        """
        
        summary = pd.read_sql(summary_query, conn).iloc[0]
        
        # Get pipeline performance
        performance_query = """
        SELECT 
            COUNT(DISTINCT execution_date) as days_monitored,
            AVG(pipeline_success_rate) as avg_success_rate,
            AVG(pipeline_duration_ms) / 1000 / 60 as avg_duration_minutes
        FROM MYDB.LIVE_DATA.pipeline_runtime_health
        WHERE execution_date >= current_date - 7
        """
        
        performance = pd.read_sql(performance_query, conn).iloc[0]
        
        return {
            'total_tables': int(summary['TOTAL_TABLES_MONITORED']),
            'avg_quality_score': float(summary['AVG_QUALITY_SCORE']),
            'total_records': int(summary['TOTAL_RECORDS_PROCESSED']),
            'quality_issues': int(summary['TOTAL_QUALITY_ISSUES']),
            'last_check': summary['LAST_CHECK_TIME'],
            'days_monitored': int(performance['DAYS_MONITORED']) if performance['DAYS_MONITORED'] else 0,
            'avg_success_rate': float(performance['AVG_SUCCESS_RATE']) if performance['AVG_SUCCESS_RATE'] else 100,
            'avg_duration_minutes': float(performance['AVG_DURATION_MINUTES']) if performance['AVG_DURATION_MINUTES'] else 0
        }
        
    finally:
        conn.close()

def generate_detailed_metrics():
    """Generate detailed metrics for technical teams"""
    conn = connect_to_snowflake()
    
    try:
        # Table-level metrics
        table_metrics_query = """
        SELECT 
            table_name,
            layer,
            total_rows,
            overall_quality_score,
            quality_status,
            freshness_status,
            volume_change_pct,
            latest_data_timestamp
        FROM MYDB.LIVE_DATA.data_quality_health
        ORDER BY layer, table_name
        """
        
        table_metrics = pd.read_sql(table_metrics_query, conn)
        
        # Recent query performance
        query_performance_query = """
        SELECT 
            execution_date,
            query_category,
            total_queries,
            failed_queries,
            avg_elapsed_time_ms / 1000 as avg_elapsed_time_seconds,
            overall_health_score
        FROM MYDB.LIVE_DATA.query_history_health
        WHERE execution_date >= current_date - 7
        ORDER BY execution_date DESC, query_category
        """
        
        query_performance = pd.read_sql(query_performance_query, conn)
        
        return {
            'table_metrics': table_metrics.to_dict('records'),
            'query_performance': query_performance.to_dict('records')
        }
        
    finally:
        conn.close()

def create_html_report(summary, detailed_metrics):
    """Create HTML report"""
    html_template = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>ETL Health Monitoring Report</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; }}
            .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
            .metric {{ display: inline-block; margin: 10px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }}
            .excellent {{ background-color: #d4edda; }}
            .good {{ background-color: #fff3cd; }}
            .poor {{ background-color: #f8d7da; }}
            table {{ border-collapse: collapse; width: 100%; margin: 20px 0; }}
            th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
            th {{ background-color: #f2f2f2; }}
        </style>
    </head>
    <body>
        <div class="header">
            <h1>🏥 ETL Health Monitoring Report</h1>
            <p>Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            <p>Last Data Check: {summary['last_check']}</p>
        </div>
        
        <h2>📊 Executive Summary</h2>
        <div class="metric excellent">
            <h3>Tables Monitored</h3>
            <p>{summary['total_tables']}</p>
        </div>
        <div class="metric {'excellent' if summary['avg_quality_score'] >= 95 else 'good' if summary['avg_quality_score'] >= 85 else 'poor'}">
            <h3>Avg Quality Score</h3>
            <p>{summary['avg_quality_score']:.1f}%</p>
        </div>
        <div class="metric excellent">
            <h3>Records Processed</h3>
            <p>{summary['total_records']:,}</p>
        </div>
        <div class="metric {'excellent' if summary['quality_issues'] == 0 else 'poor'}">
            <h3>Quality Issues</h3>
            <p>{summary['quality_issues']:,}</p>
        </div>
        
        <h2>📋 Table Quality Details</h2>
        <table>
            <tr>
                <th>Table</th>
                <th>Layer</th>
                <th>Rows</th>
                <th>Quality Score</th>
                <th>Status</th>
                <th>Freshness</th>
                <th>Volume Change</th>
            </tr>
    """
    
    for table in detailed_metrics['table_metrics']:
        html_template += f"""
            <tr>
                <td>{table['TABLE_NAME']}</td>
                <td>{table['LAYER']}</td>
                <td>{table['TOTAL_ROWS']:,}</td>
                <td>{table['OVERALL_QUALITY_SCORE']:.0f}%</td>
                <td>{table['QUALITY_STATUS']}</td>
                <td>{table['FRESHNESS_STATUS']}</td>
                <td>{table['VOLUME_CHANGE_PCT']:+.1f}%</td>
            </tr>
        """
    
    html_template += """
        </table>
        
        <h2>⚡ Performance Metrics</h2>
        <table>
            <tr>
                <th>Date</th>
                <th>Category</th>
                <th>Total Queries</th>
                <th>Failed</th>
                <th>Avg Time (sec)</th>
                <th>Health Score</th>
            </tr>
    """
    
    for perf in detailed_metrics['query_performance']:
        html_template += f"""
            <tr>
                <td>{perf['EXECUTION_DATE']}</td>
                <td>{perf['QUERY_CATEGORY']}</td>
                <td>{perf['TOTAL_QUERIES']}</td>
                <td>{perf['FAILED_QUERIES']}</td>
                <td>{perf['AVG_ELAPSED_TIME_SECONDS']:.1f}</td>
                <td>{perf['OVERALL_HEALTH_SCORE']:.0f}%</td>
            </tr>
        """
    
    html_template += """
        </table>
    </body>
    </html>
    """
    
    return html_template

def main():
    """Generate comprehensive monitoring report"""
    print("📊 Generating ETL monitoring report...")
    
    try:
        # Generate summary and detailed metrics
        summary = generate_executive_summary()
        detailed_metrics = generate_detailed_metrics()
        
        # Create reports
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # JSON report for APIs/automation
        json_report = {
            'generated_at': datetime.now().isoformat(),
            'summary': summary,
            'detailed_metrics': detailed_metrics
        }
        
        json_filename = f"etl_health_report_{timestamp}.json"
        with open(json_filename, 'w') as f:
            json.dump(json_report, f, indent=2, default=str)
        
        # HTML report for stakeholders
        html_report = create_html_report(summary, detailed_metrics)
        html_filename = f"etl_health_report_{timestamp}.html"
        with open(html_filename, 'w') as f:
            f.write(html_report)
        
        print(f"✅ Reports generated:")
        print(f"   📄 JSON: {json_filename}")
        print(f"   🌐 HTML: {html_filename}")
        
        # Print summary to console
        print(f"\n📊 EXECUTIVE SUMMARY")
        print(f"   Tables Monitored: {summary['total_tables']}")
        print(f"   Avg Quality Score: {summary['avg_quality_score']:.1f}%")
        print(f"   Records Processed: {summary['total_records']:,}")
        print(f"   Quality Issues: {summary['quality_issues']:,}")
        print(f"   Pipeline Success Rate: {summary['avg_success_rate']:.1f}%")
        
    except Exception as e:
        print(f"❌ Error generating report: {str(e)}")

if __name__ == "__main__":
    main()
