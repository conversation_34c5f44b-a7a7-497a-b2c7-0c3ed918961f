[2025-06-04T05:26:36.494+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=non-requeueable deps ti=<TaskInstance: simple_etl_pipeline.refresh_snowflake_stages manual__2025-06-04T05:26:33.910798+00:00 [queued]>
[2025-06-04T05:26:36.504+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=requeueable deps ti=<TaskInstance: simple_etl_pipeline.refresh_snowflake_stages manual__2025-06-04T05:26:33.910798+00:00 [queued]>
[2025-06-04T05:26:36.504+0000] {taskinstance.py:2170} INFO - Starting attempt 1 of 2
[2025-06-04T05:26:36.516+0000] {taskinstance.py:2191} INFO - Executing <Task(PythonOperator): refresh_snowflake_stages> on 2025-06-04 05:26:33.910798+00:00
[2025-06-04T05:26:36.521+0000] {standard_task_runner.py:60} INFO - Started process 873 to run task
[2025-06-04T05:26:36.525+0000] {standard_task_runner.py:87} INFO - Running: ['***', 'tasks', 'run', 'simple_etl_pipeline', 'refresh_snowflake_stages', 'manual__2025-06-04T05:26:33.910798+00:00', '--job-id', '97', '--raw', '--subdir', 'DAGS_FOLDER/simple_etl_dag.py', '--cfg-path', '/tmp/tmpvsc3xmg8']
[2025-06-04T05:26:36.527+0000] {standard_task_runner.py:88} INFO - Job 97: Subtask refresh_snowflake_stages
[2025-06-04T05:26:36.592+0000] {task_command.py:423} INFO - Running <TaskInstance: simple_etl_pipeline.refresh_snowflake_stages manual__2025-06-04T05:26:33.910798+00:00 [running]> on host 8fb7c8dc5097
[2025-06-04T05:26:36.680+0000] {taskinstance.py:2480} INFO - Exporting env vars: AIRFLOW_CTX_DAG_OWNER='data-team' AIRFLOW_CTX_DAG_ID='simple_etl_pipeline' AIRFLOW_CTX_TASK_ID='refresh_snowflake_stages' AIRFLOW_CTX_EXECUTION_DATE='2025-06-04T05:26:33.910798+00:00' AIRFLOW_CTX_TRY_NUMBER='1' AIRFLOW_CTX_DAG_RUN_ID='manual__2025-06-04T05:26:33.910798+00:00'
[2025-06-04T05:26:36.683+0000] {simple_etl_dag.py:192} INFO - Refreshing Snowflake external stages...
[2025-06-04T05:26:37.412+0000] {create_snowflake_stages.py:36} INFO - ✅ Snowflake Stages Setup initialized
[2025-06-04T05:26:37.416+0000] {create_snowflake_stages.py:408} INFO - ✅ Test queries saved to config/test_queries.sql
[2025-06-04T05:26:37.417+0000] {create_snowflake_stages.py:311} INFO - 🚀 Starting Snowflake external stages setup...
[2025-06-04T05:26:37.417+0000] {create_snowflake_stages.py:315} INFO - 🔍 Role ARN not provided, attempting to get/create it...
[2025-06-04T05:26:37.521+0000] {create_snowflake_stages.py:304} ERROR - ❌ Failed to get/create role ARN: Unable to locate credentials
[2025-06-04T05:26:37.522+0000] {create_snowflake_stages.py:306} WARNING - ⚠️ Using development placeholder role ARN
[2025-06-04T05:26:37.523+0000] {create_snowflake_stages.py:50} INFO - 🔄 Connecting to Snowflake...
[2025-06-04T05:26:37.523+0000] {connection.py:486} INFO - Snowflake Connector for Python Version: 3.15.0, Python Version: 3.10.13, Platform: Linux-6.6.87.1-microsoft-standard-WSL2-x86_64-with-glibc2.36
[2025-06-04T05:26:37.524+0000] {connection.py:1391} INFO - Connecting to GLOBAL Snowflake domain
[2025-06-04T05:26:39.051+0000] {create_snowflake_stages.py:58} INFO - ✅ Connected to Snowflake successfully
[2025-06-04T05:26:39.052+0000] {create_snowflake_stages.py:69} INFO - 🔄 Creating schema LIVE_DATA...
[2025-06-04T05:26:39.242+0000] {create_snowflake_stages.py:74} INFO - ✅ Schema LIVE_DATA ready
[2025-06-04T05:26:39.242+0000] {create_snowflake_stages.py:83} INFO - 🔄 Creating storage integration...
[2025-06-04T05:26:40.758+0000] {create_snowflake_stages.py:100} INFO - ✅ Storage integration created successfully
[2025-06-04T05:26:40.817+0000] {create_snowflake_stages.py:107} INFO -    STORAGE_AWS_IAM_USER_ARN: String
[2025-06-04T05:26:40.818+0000] {create_snowflake_stages.py:107} INFO -    STORAGE_AWS_EXTERNAL_ID: String
[2025-06-04T05:26:40.819+0000] {create_snowflake_stages.py:116} INFO - 🔄 Creating file format...
[2025-06-04T05:26:40.920+0000] {create_snowflake_stages.py:136} INFO - ✅ File format created successfully
[2025-06-04T05:26:40.921+0000] {create_snowflake_stages.py:152} INFO - 🔄 Creating stage S3_LIVE_USERS_STAGE...
[2025-06-04T05:26:41.156+0000] {create_snowflake_stages.py:163} INFO - ✅ Stage S3_LIVE_USERS_STAGE created successfully
[2025-06-04T05:26:41.156+0000] {create_snowflake_stages.py:152} INFO - 🔄 Creating stage S3_LIVE_ORDERS_STAGE...
[2025-06-04T05:26:41.293+0000] {create_snowflake_stages.py:163} INFO - ✅ Stage S3_LIVE_ORDERS_STAGE created successfully
[2025-06-04T05:26:41.293+0000] {create_snowflake_stages.py:152} INFO - 🔄 Creating stage S3_LIVE_EVENTS_STAGE...
[2025-06-04T05:26:41.411+0000] {create_snowflake_stages.py:163} INFO - ✅ Stage S3_LIVE_EVENTS_STAGE created successfully
[2025-06-04T05:26:41.411+0000] {create_snowflake_stages.py:172} INFO - 🔄 Creating external tables...
[2025-06-04T05:26:41.961+0000] {create_snowflake_stages.py:241} ERROR - ❌ Failed to create external tables: 003167 (42601): 01bccc26-3204-7f69-0002-4ad60005d18a: Error assuming AWS_ROLE:
User: arn:aws:iam::184862803517:user/400z0000-s is not authorized to perform: sts:AssumeRole on resource: arn:aws:iam::365542662955:role/SnowflakeS3AccessRole
[2025-06-04T05:26:41.961+0000] {create_snowflake_stages.py:339} WARNING - ⚠️ External tables creation failed (IAM role issue): 003167 (42601): 01bccc26-3204-7f69-0002-4ad60005d18a: Error assuming AWS_ROLE:
User: arn:aws:iam::184862803517:user/400z0000-s is not authorized to perform: sts:AssumeRole on resource: arn:aws:iam::365542662955:role/SnowflakeS3AccessRole
[2025-06-04T05:26:41.962+0000] {create_snowflake_stages.py:340} INFO - 📝 Continuing without external tables - stages are ready for dbt
[2025-06-04T05:26:41.962+0000] {create_snowflake_stages.py:347} INFO - ⏭️ Skipping stage tests (external tables not created)
[2025-06-04T05:26:42.032+0000] {create_snowflake_stages.py:353} INFO - 🎉 Snowflake external stages setup completed successfully!
[2025-06-04T05:26:42.034+0000] {create_snowflake_stages.py:422} INFO - 
📋 AIRFLOW SETUP SUMMARY:
[2025-06-04T05:26:42.035+0000] {create_snowflake_stages.py:423} INFO - ✅ Storage Integration: S3_LIVE_INTEGRATION
[2025-06-04T05:26:42.038+0000] {create_snowflake_stages.py:424} INFO - ✅ File Format: CSV_LIVE_FORMAT
[2025-06-04T05:26:42.039+0000] {create_snowflake_stages.py:425} INFO - ✅ External Stages: S3_LIVE_USERS_STAGE, S3_LIVE_ORDERS_STAGE, S3_LIVE_EVENTS_STAGE
[2025-06-04T05:26:42.040+0000] {create_snowflake_stages.py:429} INFO - ⚠️ External Tables: Not created (IAM role configuration needed)
[2025-06-04T05:26:42.041+0000] {create_snowflake_stages.py:430} INFO - ✅ IAM Role ARN: arn:aws:iam::365542662955:role/SnowflakeS3AccessRole
[2025-06-04T05:26:42.042+0000] {simple_etl_dag.py:207} INFO - Stages refreshed successfully: {'storage_integration': 'S3_LIVE_INTEGRATION', 'file_format': 'CSV_LIVE_FORMAT', 'stages': ['S3_LIVE_USERS_STAGE', 'S3_LIVE_ORDERS_STAGE', 'S3_LIVE_EVENTS_STAGE'], 'role_arn': 'arn:aws:iam::365542662955:role/SnowflakeS3AccessRole', 'external_tables_created': False}
[2025-06-04T05:26:42.042+0000] {python.py:201} INFO - Done. Returned value was: Stage refresh successful: {'storage_integration': 'S3_LIVE_INTEGRATION', 'file_format': 'CSV_LIVE_FORMAT', 'stages': ['S3_LIVE_USERS_STAGE', 'S3_LIVE_ORDERS_STAGE', 'S3_LIVE_EVENTS_STAGE'], 'role_arn': 'arn:aws:iam::365542662955:role/SnowflakeS3AccessRole', 'external_tables_created': False}
[2025-06-04T05:26:42.099+0000] {taskinstance.py:1138} INFO - Marking task as SUCCESS. dag_id=simple_etl_pipeline, task_id=refresh_snowflake_stages, execution_date=20250604T052633, start_date=20250604T052636, end_date=20250604T052642
[2025-06-04T05:26:42.166+0000] {local_task_job_runner.py:234} INFO - Task exited with return code 0
[2025-06-04T05:26:42.237+0000] {taskinstance.py:3280} INFO - 1 downstream tasks scheduled from follow-on schedule check
