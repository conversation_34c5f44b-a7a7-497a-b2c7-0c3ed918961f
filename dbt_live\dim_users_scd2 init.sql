{{
    config(
        materialized='incremental',
        unique_key='user_sk',
        tags=['marts', 'dimension', 'scd2'],
        description='Slowly Changing Dimension Type 2 for users',
        transient=False
    )
}}

with 

-- Get the latest user data from staging
source_data as (
    select 
        user_id,
        firstname,
        lastname,
        email_hash,
        address,
        acquisition_channel as canal,
        country,
        gender,
        age_group,
        creation_date,
        last_activity_date,
        is_churned as churn,
        batch_id,
        current_timestamp() as dbt_updated_at,
        -- Create a hash of all attributes to detect changes
        md5(concat_ws('||',
            coalesce(cast(firstname as string), ''),
            coalesce(cast(lastname as string), ''),
            coalesce(cast(email_hash as string), ''),
            coalesce(cast(address as string), ''),
            coalesce(cast(acquisition_channel as string), ''),
            coalesce(cast(country as string), ''),
            coalesce(cast(gender as string), ''),
            coalesce(cast(age_group as string), ''),
            coalesce(cast(creation_date as string), ''),
            coalesce(cast(last_activity_date as string), ''),
            coalesce(cast(is_churned as string), '')
        )) as row_hash
    from {{ ref('stg_users') }}
    where user_id is not null
    qualify row_number() over (partition by user_id order by generated_at desc) = 1
    
    {% if is_incremental() %}
    -- Only process users that are new or have changed since the last run
    and user_id in (
        select user_id 
        from {{ ref('stg_users') }}
        where generated_at > (select max(dbt_updated_at) from {{ this }} where is_current_version = true)
    )
    {% endif %}
),

-- Get the current version of each user from the dimension
existing_dimension as (
    select * 
    from {{ this }}
    where is_current_version = true
),

-- Identify new and changed records
changes_to_apply as (
    select
        s.*,
        case
            when e.user_sk is null then 'insert'
            when e.row_hash != s.row_hash then 'update'
            else 'no_change'
        end as change_type
    from source_data s
    left join existing_dimension e 
        on s.user_id = e.user_id
)

-- Generate the final SCD2 output
select
    -- Business key
    {{ dbt_utils.generate_surrogate_key(['user_id', 'dbt_updated_at']) }} as user_sk,
    
    -- Natural key
    user_id,
    
    -- User attributes
    firstname,
    lastname,
    email_hash as email,
    address,
    canal as acquisition_channel,
    country,
    gender,
    age_group,
    
    -- Metadata
    creation_date,
    last_activity_date,
    churn,
    batch_id,
    
    -- SCD2 tracking columns
    dbt_updated_at as dbt_valid_from,
    cast(null as timestamp_ntz) as dbt_valid_to,
    true as is_current_version,
    
    -- Track the change
    change_type as dbt_change_type,
    
    -- Store the hash for future change detection
    row_hash
    
from changes_to_apply
where change_type in ('insert', 'update')

{% if is_incremental() %}

union all

-- Expire the old versions of changed records
select
    e.user_sk,
    e.user_id,
    e.firstname,
    e.lastname,
    e.email,
    e.address,
    e.acquisition_channel,
    e.country,
    e.gender,
    e.age_group,
    e.creation_date,
    e.last_activity_date,
    e.churn,
    e.batch_id,
    e.dbt_valid_from,
    current_timestamp() as dbt_valid_to,
    false as is_current_version,
    'expire' as dbt_change_type,
    e.row_hash
from existing_dimension e
inner join changes_to_apply c 
    on e.user_id = c.user_id
    and c.change_type = 'update'
    and e.is_current_version = true
{% endif %}
