#!/usr/bin/env python3
"""
Simple Data Generator for Docker Airflow Testing
No external dependencies required - uses only Python standard library
"""

import os
import json
import random
import datetime
from pathlib import Path

def generate_simple_data():
    """Generate simple test data without external dependencies"""
    
    # Create data directory if it doesn't exist
    data_dir = Path("data")
    data_dir.mkdir(exist_ok=True)
    
    # Generate current date
    current_date = datetime.datetime.now().strftime("%Y-%m-%d")
    
    # Simple data generation using only standard library
    users = []
    transactions = []
    
    # Generate 100 users
    first_names = ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]
    last_names = ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]
    
    for i in range(100):
        user = {
            "user_id": f"user_{i+1:03d}",
            "first_name": random.choice(first_names),
            "last_name": random.choice(last_names),
            "email": f"user{i+1}@example.com",
            "registration_date": current_date,
            "age": random.randint(18, 80),
            "city": random.choice(["New York", "Los Angeles", "Chicago", "Houston", "Phoenix"])
        }
        users.append(user)
    
    # Generate 500 transactions
    for i in range(500):
        transaction = {
            "transaction_id": f"txn_{i+1:05d}",
            "user_id": f"user_{random.randint(1, 100):03d}",
            "amount": round(random.uniform(10.0, 1000.0), 2),
            "transaction_date": current_date,
            "category": random.choice(["food", "shopping", "entertainment", "transport", "utilities"]),
            "status": random.choice(["completed", "pending", "failed"])
        }
        transactions.append(transaction)
    
    # Save to JSON files
    users_file = data_dir / f"users_{current_date}.json"
    transactions_file = data_dir / f"transactions_{current_date}.json"
    
    with open(users_file, 'w') as f:
        json.dump(users, f, indent=2)
    
    with open(transactions_file, 'w') as f:
        json.dump(transactions, f, indent=2)
    
    print(f"✅ Generated {len(users)} users and {len(transactions)} transactions")
    print(f"📁 Files created:")
    print(f"   - {users_file}")
    print(f"   - {transactions_file}")
    
    return {
        "users_count": len(users),
        "transactions_count": len(transactions),
        "files_created": [str(users_file), str(transactions_file)]
    }

if __name__ == "__main__":
    print("🚀 Starting simple data generation...")
    result = generate_simple_data()
    print("🎉 Data generation completed successfully!")
    print(f"📊 Summary: {result}")
