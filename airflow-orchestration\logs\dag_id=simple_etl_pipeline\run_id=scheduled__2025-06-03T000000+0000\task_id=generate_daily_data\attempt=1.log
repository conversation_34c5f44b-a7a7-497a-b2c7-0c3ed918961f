[2025-06-04T01:55:39.633+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=non-requeueable deps ti=<TaskInstance: simple_etl_pipeline.generate_daily_data scheduled__2025-06-03T00:00:00+00:00 [queued]>
[2025-06-04T01:55:39.641+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=requeueable deps ti=<TaskInstance: simple_etl_pipeline.generate_daily_data scheduled__2025-06-03T00:00:00+00:00 [queued]>
[2025-06-04T01:55:39.641+0000] {taskinstance.py:2170} INFO - Starting attempt 1 of 2
[2025-06-04T01:55:39.654+0000] {taskinstance.py:2191} INFO - Executing <Task(PythonOperator): generate_daily_data> on 2025-06-03 00:00:00+00:00
[2025-06-04T01:55:39.759+0000] {standard_task_runner.py:60} INFO - Started process 294 to run task
[2025-06-04T01:55:39.769+0000] {standard_task_runner.py:87} INFO - Running: ['***', 'tasks', 'run', 'simple_etl_pipeline', 'generate_daily_data', 'scheduled__2025-06-03T00:00:00+00:00', '--job-id', '3', '--raw', '--subdir', 'DAGS_FOLDER/simple_etl_dag.py', '--cfg-path', '/tmp/tmpbnsw7ifv']
[2025-06-04T01:55:39.770+0000] {standard_task_runner.py:88} INFO - Job 3: Subtask generate_daily_data
[2025-06-04T01:55:39.832+0000] {task_command.py:423} INFO - Running <TaskInstance: simple_etl_pipeline.generate_daily_data scheduled__2025-06-03T00:00:00+00:00 [running]> on host 0c73d38e3af1
[2025-06-04T01:55:39.906+0000] {taskinstance.py:2480} INFO - Exporting env vars: AIRFLOW_CTX_DAG_OWNER='data-team' AIRFLOW_CTX_DAG_ID='simple_etl_pipeline' AIRFLOW_CTX_TASK_ID='generate_daily_data' AIRFLOW_CTX_EXECUTION_DATE='2025-06-03T00:00:00+00:00' AIRFLOW_CTX_TRY_NUMBER='1' AIRFLOW_CTX_DAG_RUN_ID='scheduled__2025-06-03T00:00:00+00:00'
[2025-06-04T01:55:39.909+0000] {simple_etl_dag.py:48} INFO - Running script: G:\github\S3 dbt-snowflake c360\experiment2/daily_data_generator.py
[2025-06-04T01:55:39.910+0000] {simple_etl_dag.py:51} ERROR - Script not found: G:\github\S3 dbt-snowflake c360\experiment2/daily_data_generator.py
[2025-06-04T01:55:39.911+0000] {taskinstance.py:2698} ERROR - Task failed with exception
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/models/taskinstance.py", line 433, in _execute_task
    result = execute_callable(context=context, **execute_callable_kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/operators/python.py", line 199, in execute
    return_value = self.execute_callable()
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/operators/python.py", line 216, in execute_callable
    return self.python_callable(*self.op_args, **self.op_kwargs)
  File "/opt/airflow/dags/simple_etl_dag.py", line 109, in generate_data
    return run_python_script('daily_data_generator.py', **context)
  File "/opt/airflow/dags/simple_etl_dag.py", line 52, in run_python_script
    raise FileNotFoundError(f"Script not found: {script_path}")
FileNotFoundError: Script not found: G:\github\S3 dbt-snowflake c360\experiment2/daily_data_generator.py
[2025-06-04T01:55:39.923+0000] {taskinstance.py:1138} INFO - Marking task as UP_FOR_RETRY. dag_id=simple_etl_pipeline, task_id=generate_daily_data, execution_date=20250603T000000, start_date=20250604T015539, end_date=20250604T015539
[2025-06-04T01:55:39.936+0000] {standard_task_runner.py:107} ERROR - Failed to execute job 3 for task generate_daily_data (Script not found: G:\github\S3 dbt-snowflake c360\experiment2/daily_data_generator.py; 294)
[2025-06-04T01:55:39.984+0000] {local_task_job_runner.py:234} INFO - Task exited with return code 1
[2025-06-04T01:55:40.007+0000] {taskinstance.py:3280} INFO - 0 downstream tasks scheduled from follow-on schedule check
