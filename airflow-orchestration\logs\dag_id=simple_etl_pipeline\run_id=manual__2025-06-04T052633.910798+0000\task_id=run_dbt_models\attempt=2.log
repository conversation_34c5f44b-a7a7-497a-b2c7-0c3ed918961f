[2025-06-04T05:31:58.389+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=non-requeueable deps ti=<TaskInstance: simple_etl_pipeline.run_dbt_models manual__2025-06-04T05:26:33.910798+00:00 [queued]>
[2025-06-04T05:31:58.398+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=requeueable deps ti=<TaskInstance: simple_etl_pipeline.run_dbt_models manual__2025-06-04T05:26:33.910798+00:00 [queued]>
[2025-06-04T05:31:58.399+0000] {taskinstance.py:2170} INFO - Starting attempt 2 of 2
[2025-06-04T05:31:58.411+0000] {taskinstance.py:2191} INFO - Executing <Task(PythonOperator): run_dbt_models> on 2025-06-04 05:26:33.910798+00:00
[2025-06-04T05:31:58.416+0000] {standard_task_runner.py:60} INFO - Started process 971 to run task
[2025-06-04T05:31:58.421+0000] {standard_task_runner.py:87} INFO - Running: ['***', 'tasks', 'run', 'simple_etl_pipeline', 'run_dbt_models', 'manual__2025-06-04T05:26:33.910798+00:00', '--job-id', '99', '--raw', '--subdir', 'DAGS_FOLDER/simple_etl_dag.py', '--cfg-path', '/tmp/tmp5j8wz1as']
[2025-06-04T05:31:58.422+0000] {standard_task_runner.py:88} INFO - Job 99: Subtask run_dbt_models
[2025-06-04T05:31:58.474+0000] {task_command.py:423} INFO - Running <TaskInstance: simple_etl_pipeline.run_dbt_models manual__2025-06-04T05:26:33.910798+00:00 [running]> on host 8fb7c8dc5097
[2025-06-04T05:31:58.546+0000] {taskinstance.py:2480} INFO - Exporting env vars: AIRFLOW_CTX_DAG_OWNER='data-team' AIRFLOW_CTX_DAG_ID='simple_etl_pipeline' AIRFLOW_CTX_TASK_ID='run_dbt_models' AIRFLOW_CTX_EXECUTION_DATE='2025-06-04T05:26:33.910798+00:00' AIRFLOW_CTX_TRY_NUMBER='2' AIRFLOW_CTX_DAG_RUN_ID='manual__2025-06-04T05:26:33.910798+00:00'
[2025-06-04T05:31:58.548+0000] {simple_etl_dag.py:78} INFO - Running dbt run using Python API
[2025-06-04T05:31:59.958+0000] {simple_etl_dag.py:109} INFO - Executing dbt run
[2025-06-04T05:32:00.057+0000] {logging_mixin.py:188} INFO - 05:32:00  Running with dbt=1.9.6
[2025-06-04T05:32:00.949+0000] {logging_mixin.py:188} INFO - 05:32:00  Registered adapter: snowflake=1.9.4
[2025-06-04T05:32:04.749+0000] {logging_mixin.py:188} INFO - 05:32:04  [WARNING]: Configuration paths exist in your dbt_project.yml file which do not apply to any resources.
There are 3 unused configuration paths:
- models.live_c360.incremental
- seeds.live_c360
- snapshots.live_c360
[2025-06-04T05:32:05.015+0000] {logging_mixin.py:188} INFO - 05:32:05  Found 12 models, 21 data tests, 3 sources, 590 macros
[2025-06-04T05:32:05.020+0000] {logging_mixin.py:188} INFO - 05:32:05
[2025-06-04T05:32:05.022+0000] {logging_mixin.py:188} INFO - 05:32:05  Concurrency: 4 threads (target='live')
[2025-06-04T05:32:05.024+0000] {logging_mixin.py:188} INFO - 05:32:05
[2025-06-04T05:32:05.079+0000] {connection.py:486} INFO - Snowflake Connector for Python Version: 3.15.0, Python Version: 3.10.13, Platform: Linux-********-microsoft-standard-WSL2-x86_64-with-glibc2.36
[2025-06-04T05:32:05.080+0000] {connection.py:1391} INFO - Connecting to GLOBAL Snowflake domain
[2025-06-04T05:32:05.084+0000] {connection.py:486} INFO - Snowflake Connector for Python Version: 3.15.0, Python Version: 3.10.13, Platform: Linux-********-microsoft-standard-WSL2-x86_64-with-glibc2.36
[2025-06-04T05:32:05.085+0000] {connection.py:1391} INFO - Connecting to GLOBAL Snowflake domain
[2025-06-04T05:32:05.822+0000] {connection.py:486} INFO - Snowflake Connector for Python Version: 3.15.0, Python Version: 3.10.13, Platform: Linux-********-microsoft-standard-WSL2-x86_64-with-glibc2.36
[2025-06-04T05:32:05.824+0000] {connection.py:1391} INFO - Connecting to GLOBAL Snowflake domain
[2025-06-04T05:32:06.518+0000] {logging_mixin.py:188} INFO - 05:32:06  1 of 12 START sql table model LIVE_DATA.dbt_test_health ........................ [RUN]
[2025-06-04T05:32:06.520+0000] {logging_mixin.py:188} INFO - 05:32:06  2 of 12 START sql table model LIVE_DATA.pipeline_runtime_health ................ [RUN]
[2025-06-04T05:32:06.522+0000] {logging_mixin.py:188} INFO - 05:32:06  3 of 12 START sql table model LIVE_DATA.query_history_health ................... [RUN]
[2025-06-04T05:32:06.524+0000] {logging_mixin.py:188} INFO - 05:32:06  4 of 12 START sql view model LIVE_DATA.stg_events .............................. [RUN]
[2025-06-04T05:32:06.707+0000] {connection.py:486} INFO - Snowflake Connector for Python Version: 3.15.0, Python Version: 3.10.13, Platform: Linux-********-microsoft-standard-WSL2-x86_64-with-glibc2.36
[2025-06-04T05:32:06.708+0000] {connection.py:1391} INFO - Connecting to GLOBAL Snowflake domain
[2025-06-04T05:32:07.284+0000] {logging_mixin.py:188} INFO - 05:32:07  4 of 12 ERROR creating sql view model LIVE_DATA.stg_events ..................... [ERROR in 0.75s]
[2025-06-04T05:32:07.294+0000] {logging_mixin.py:188} INFO - 05:32:07  5 of 12 START sql view model LIVE_DATA.stg_orders .............................. [RUN]
[2025-06-04T05:32:07.509+0000] {logging_mixin.py:188} INFO - 05:32:07  5 of 12 ERROR creating sql view model LIVE_DATA.stg_orders ..................... [ERROR in 0.21s]
[2025-06-04T05:32:07.515+0000] {logging_mixin.py:188} INFO - 05:32:07  6 of 12 START sql view model LIVE_DATA.stg_users ............................... [RUN]
[2025-06-04T05:32:07.741+0000] {logging_mixin.py:188} INFO - 05:32:07  1 of 12 OK created sql table model LIVE_DATA.dbt_test_health ................... [SUCCESS 1 in 1.20s]
[2025-06-04T05:32:07.746+0000] {logging_mixin.py:188} INFO - 05:32:07  7 of 12 SKIP relation LIVE_DATA.fact_orders .................................... [SKIP]
[2025-06-04T05:32:07.751+0000] {logging_mixin.py:188} INFO - 05:32:07  8 of 12 SKIP relation LIVE_DATA_ANALYTICS.mv_fact_orders ....................... [SKIP]
[2025-06-04T05:32:07.795+0000] {logging_mixin.py:188} INFO - 05:32:07  6 of 12 ERROR creating sql view model LIVE_DATA.stg_users ...................... [ERROR in 0.28s]
[2025-06-04T05:32:07.800+0000] {logging_mixin.py:188} INFO - 05:32:07  9 of 12 SKIP relation LIVE_DATA.dim_users ...................................... [SKIP]
[2025-06-04T05:32:07.803+0000] {logging_mixin.py:188} INFO - 05:32:07  10 of 12 SKIP relation LIVE_DATA.dim_users_scd2 ................................ [SKIP]
[2025-06-04T05:32:07.808+0000] {logging_mixin.py:188} INFO - 05:32:07  11 of 12 SKIP relation LIVE_DATA.data_quality_health ........................... [SKIP]
[2025-06-04T05:32:07.812+0000] {logging_mixin.py:188} INFO - 05:32:07  12 of 12 SKIP relation LIVE_DATA.etl_health_dashboard .......................... [SKIP]
[2025-06-04T05:32:09.106+0000] {logging_mixin.py:188} INFO - 05:32:09  3 of 12 OK created sql table model LIVE_DATA.query_history_health .............. [SUCCESS 1 in 2.57s]
[2025-06-04T05:32:09.115+0000] {logging_mixin.py:188} INFO - 05:32:09  2 of 12 OK created sql table model LIVE_DATA.pipeline_runtime_health ........... [SUCCESS 1 in 2.58s]
[2025-06-04T05:32:09.484+0000] {logging_mixin.py:188} INFO - 05:32:09
[2025-06-04T05:32:09.486+0000] {logging_mixin.py:188} INFO - 05:32:09  Finished running 1 incremental model, 7 table models, 4 view models in 0 hours 0 minutes and 4.46 seconds (4.46s).
[2025-06-04T05:32:09.596+0000] {logging_mixin.py:188} INFO - 05:32:09
[2025-06-04T05:32:09.598+0000] {logging_mixin.py:188} INFO - 05:32:09  Completed with 3 errors, 0 partial successes, and 0 warnings:
[2025-06-04T05:32:09.600+0000] {logging_mixin.py:188} INFO - 05:32:09
[2025-06-04T05:32:09.602+0000] {logging_mixin.py:188} INFO - 05:32:09    Database Error in model stg_events (models/staging/stg_events.sql)
  091093 (55000): 01bccc2c-3204-7f69-0002-4ad60005d196: External table EXT_LIVE_EVENTS marked invalid. Stage S3_LIVE_EVENTS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_events.sql
[2025-06-04T05:32:09.604+0000] {logging_mixin.py:188} INFO - 05:32:09
[2025-06-04T05:32:09.607+0000] {logging_mixin.py:188} INFO - 05:32:09    Database Error in model stg_orders (models/staging/stg_orders.sql)
  091093 (55000): 01bccc2c-3204-7fc5-0002-4ad600063182: External table EXT_LIVE_ORDERS marked invalid. Stage S3_LIVE_ORDERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_orders.sql
[2025-06-04T05:32:09.609+0000] {logging_mixin.py:188} INFO - 05:32:09
[2025-06-04T05:32:09.611+0000] {logging_mixin.py:188} INFO - 05:32:09    Database Error in model stg_users (models/staging/stg_users.sql)
  091093 (55000): 01bccc2c-3204-7eab-0002-4ad6000611ce: External table EXT_LIVE_USERS marked invalid. Stage S3_LIVE_USERS_STAGE dropped.
  compiled code at target/run/live_c360/models/staging/stg_users.sql
[2025-06-04T05:32:09.612+0000] {logging_mixin.py:188} INFO - 05:32:09
[2025-06-04T05:32:09.614+0000] {logging_mixin.py:188} INFO - 05:32:09  Done. PASS=3 WARN=0 ERROR=3 SKIP=6 TOTAL=12
[2025-06-04T05:32:10.807+0000] {simple_etl_dag.py:116} ERROR - dbt run failed
[2025-06-04T05:32:10.808+0000] {taskinstance.py:2698} ERROR - Task failed with exception
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/models/taskinstance.py", line 433, in _execute_task
    result = execute_callable(context=context, **execute_callable_kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/operators/python.py", line 199, in execute
    return_value = self.execute_callable()
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/operators/python.py", line 216, in execute_callable
    return self.python_callable(*self.op_args, **self.op_kwargs)
  File "/opt/airflow/dags/simple_etl_dag.py", line 219, in run_dbt_models
    return run_dbt_command_python('run', **context)
  File "/opt/airflow/dags/simple_etl_dag.py", line 117, in run_dbt_command_python
    raise Exception(f"dbt {command_type} failed")
Exception: dbt run failed
[2025-06-04T05:32:10.818+0000] {taskinstance.py:1138} INFO - Marking task as FAILED. dag_id=simple_etl_pipeline, task_id=run_dbt_models, execution_date=20250604T052633, start_date=20250604T053158, end_date=20250604T053210
[2025-06-04T05:32:10.834+0000] {standard_task_runner.py:107} ERROR - Failed to execute job 99 for task run_dbt_models (dbt run failed; 971)
[2025-06-04T05:32:10.880+0000] {local_task_job_runner.py:234} INFO - Task exited with return code 1
[2025-06-04T05:32:10.908+0000] {taskinstance.py:3280} INFO - 0 downstream tasks scheduled from follow-on schedule check
