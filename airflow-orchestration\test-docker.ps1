# Simple Docker Test Script
Write-Host "🐳 Testing Docker setup..." -ForegroundColor Cyan

# Add Docker to PATH
$env:PATH += ";C:\Program Files\Docker\Docker\resources\bin"

# Wait for Docker to be ready
Write-Host "⏳ Waiting for Docker daemon to be ready..." -ForegroundColor Yellow
$maxAttempts = 30
$attempt = 0

do {
    $attempt++
    Write-Host "Attempt $attempt/$maxAttempts..." -ForegroundColor Gray
    
    try {
        $result = docker info 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Docker daemon is ready!" -ForegroundColor Green
            break
        }
    } catch {
        # Continue waiting
    }
    
    if ($attempt -ge $maxAttempts) {
        Write-Host "❌ Docker daemon failed to start after $maxAttempts attempts" -ForegroundColor Red
        Write-Host "Please check Docker Desktop is running and try again." -ForegroundColor Yellow
        exit 1
    }
    
    Start-Sleep 5
} while ($true)

# Test basic Docker functionality
Write-Host "🧪 Testing Docker functionality..." -ForegroundColor Yellow

# Test with a simple container
Write-Host "Running hello-world container..." -ForegroundColor Gray
docker run --rm hello-world

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Docker is working correctly!" -ForegroundColor Green
} else {
    Write-Host "❌ Docker test failed" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "🎉 Docker is ready for Airflow!" -ForegroundColor Green
Write-Host "You can now run: docker compose up airflow-init" -ForegroundColor Cyan
