

-- ETL Health Check: Pipeline Runtime Monitoring
-- Tracks pipeline execution times, success rates, and performance trends

with dbt_run_history as (
    -- Extract dbt-specific queries from query history
    select
        query_id,
        query_text,
        start_time,
        end_time,
        total_elapsed_time,
        execution_status,
        error_message,
        warehouse_name,
        user_name,
        
        -- Extract model name from dbt queries
        case 
            when query_text ilike '%create or replace%view%stg_%' then 
                regexp_substr(query_text, 'view\\s+[^.]+\\.([^\\s]+)', 1, 1, 'i', 1)
            when query_text ilike '%create or replace%table%dim_%' then 
                regexp_substr(query_text, 'table\\s+[^.]+\\.([^\\s]+)', 1, 1, 'i', 1)
            when query_text ilike '%create or replace%table%fact_%' then 
                regexp_substr(query_text, 'table\\s+[^.]+\\.([^\\s]+)', 1, 1, 'i', 1)
            when query_text ilike '%merge into%' then 
                regexp_substr(query_text, 'merge\\s+into\\s+[^.]+\\.([^\\s]+)', 1, 1, 'i', 1)
            else 'unknown'
        end as model_name,
        
        -- Categorize model types
        case 
            when query_text ilike '%stg_%' then 'staging'
            when query_text ilike '%dim_%' then 'dimension'
            when query_text ilike '%fact_%' then 'fact'
            when query_text ilike '%mv_%' then 'materialized_view'
            else 'other'
        end as model_type,
        
        date(start_time) as execution_date,
        extract(hour from start_time) as execution_hour
        
    from snowflake.account_usage.query_history
    where start_time >= current_date - 7
      and database_name = 'MYDB'
      and (query_text ilike '%dbt%' or query_text ilike '%create%table%' or query_text ilike '%create%view%')
      and query_text not ilike '%information_schema%'
      and query_text not ilike '%show%'
),

pipeline_runs as (
    -- Group queries into pipeline runs (by date and hour)
    select
        execution_date,
        execution_hour,
        model_type,
        warehouse_name,
        
        -- Run metrics
        count(*) as models_executed,
        sum(case when execution_status = 'SUCCESS' then 1 else 0 end) as successful_models,
        sum(case when execution_status != 'SUCCESS' then 1 else 0 end) as failed_models,
        
        -- Timing metrics
        sum(total_elapsed_time) as total_pipeline_time_ms,
        avg(total_elapsed_time) as avg_model_time_ms,
        max(total_elapsed_time) as max_model_time_ms,
        min(start_time) as pipeline_start_time,
        max(end_time) as pipeline_end_time,
        
        -- Calculate actual pipeline duration
        datediff('millisecond', min(start_time), max(end_time)) as pipeline_duration_ms,
        
        -- Error details
        listagg(distinct error_message, '; ') within group (order by error_message) as error_messages
        
    from dbt_run_history
    where model_name != 'unknown'
    group by execution_date, execution_hour, model_type, warehouse_name
),

model_performance as (
    -- Individual model performance tracking
    select
        model_name,
        model_type,
        execution_date,
        
        -- Performance metrics
        count(*) as execution_count,
        avg(total_elapsed_time) as avg_execution_time_ms,
        max(total_elapsed_time) as max_execution_time_ms,
        min(total_elapsed_time) as min_execution_time_ms,
        stddev(total_elapsed_time) as stddev_execution_time_ms,
        
        -- Success rate
        round((sum(case when execution_status = 'SUCCESS' then 1 else 0 end)::float / count(*)) * 100, 2) as success_rate,
        
        -- Latest execution
        max(start_time) as latest_execution_time,
        
        -- Performance trend (compare to previous day)
        lag(avg(total_elapsed_time)) over (partition by model_name order by execution_date) as prev_day_avg_time
        
    from dbt_run_history
    where model_name != 'unknown'
    group by model_name, model_type, execution_date
),

health_metrics as (
    select
        pr.*,
        
        -- Success rate
        round((successful_models::float / models_executed) * 100, 2) as pipeline_success_rate,
        
        -- Performance scores
        case 
            when avg_model_time_ms <= 30000 then 100  -- < 30 seconds
            when avg_model_time_ms <= 120000 then 80  -- < 2 minutes
            when avg_model_time_ms <= 300000 then 60  -- < 5 minutes
            else 30
        end as performance_score,
        
        case 
            when pipeline_success_rate = 100 then 100
            when pipeline_success_rate >= 95 then 90
            when pipeline_success_rate >= 90 then 80
            when pipeline_success_rate >= 80 then 70
            else 50
        end as reliability_score,
        
        -- Duration score (based on total pipeline time)
        case 
            when pipeline_duration_ms <= 300000 then 100  -- < 5 minutes
            when pipeline_duration_ms <= 600000 then 80   -- < 10 minutes
            when pipeline_duration_ms <= 1200000 then 60  -- < 20 minutes
            else 30
        end as duration_score
        
    from pipeline_runs pr
)

select
    execution_date,
    execution_hour,
    model_type,
    warehouse_name,
    models_executed,
    successful_models,
    failed_models,
    pipeline_success_rate,
    total_pipeline_time_ms,
    avg_model_time_ms,
    max_model_time_ms,
    pipeline_duration_ms,
    pipeline_start_time,
    pipeline_end_time,
    performance_score,
    reliability_score,
    duration_score,
    
    -- Overall pipeline health score
    round((reliability_score * 0.5 + performance_score * 0.3 + duration_score * 0.2), 0) as overall_pipeline_health_score,
    
    -- Pipeline status
    case 
        when failed_models = 0 and performance_score >= 80 then 'Healthy'
        when failed_models = 0 and performance_score >= 60 then 'Good'
        when failed_models <= 1 and performance_score >= 60 then 'Fair'
        when failed_models <= 2 then 'Poor'
        else 'Critical'
    end as pipeline_status,
    
    error_messages,
    current_timestamp() as health_check_timestamp
    
from health_metrics
order by execution_date desc, execution_hour desc, model_type