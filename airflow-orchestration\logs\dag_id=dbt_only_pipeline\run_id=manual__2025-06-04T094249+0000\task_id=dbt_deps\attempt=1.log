[2025-06-04T09:49:57.625+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=non-requeueable deps ti=<TaskInstance: dbt_only_pipeline.dbt_deps manual__2025-06-04T09:42:49+00:00 [queued]>
[2025-06-04T09:49:57.633+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=requeueable deps ti=<TaskInstance: dbt_only_pipeline.dbt_deps manual__2025-06-04T09:42:49+00:00 [queued]>
[2025-06-04T09:49:57.634+0000] {taskinstance.py:2170} INFO - Starting attempt 1 of 3
[2025-06-04T09:49:57.647+0000] {taskinstance.py:2191} INFO - Executing <Task(PythonOperator): dbt_deps> on 2025-06-04 09:42:49+00:00
[2025-06-04T09:49:57.652+0000] {standard_task_runner.py:60} INFO - Started process 4348 to run task
[2025-06-04T09:49:57.657+0000] {standard_task_runner.py:87} INFO - Running: ['***', 'tasks', 'run', 'dbt_only_pipeline', 'dbt_deps', 'manual__2025-06-04T09:42:49+00:00', '--job-id', '111', '--raw', '--subdir', 'DAGS_FOLDER/dbt_only_dag.py', '--cfg-path', '/tmp/tmpoub2xsli']
[2025-06-04T09:49:57.658+0000] {standard_task_runner.py:88} INFO - Job 111: Subtask dbt_deps
[2025-06-04T09:49:57.724+0000] {task_command.py:423} INFO - Running <TaskInstance: dbt_only_pipeline.dbt_deps manual__2025-06-04T09:42:49+00:00 [running]> on host 8fb7c8dc5097
[2025-06-04T09:49:57.807+0000] {taskinstance.py:2480} INFO - Exporting env vars: AIRFLOW_CTX_DAG_OWNER='data-team' AIRFLOW_CTX_DAG_ID='dbt_only_pipeline' AIRFLOW_CTX_TASK_ID='dbt_deps' AIRFLOW_CTX_EXECUTION_DATE='2025-06-04T09:42:49+00:00' AIRFLOW_CTX_TRY_NUMBER='1' AIRFLOW_CTX_DAG_RUN_ID='manual__2025-06-04T09:42:49+00:00'
[2025-06-04T09:49:57.808+0000] {dbt_only_dag.py:140} INFO - 📦 Installing dbt dependencies...
[2025-06-04T09:49:57.809+0000] {dbt_only_dag.py:68} INFO - 🚀 Running dbt deps
[2025-06-04T09:49:57.810+0000] {dbt_only_dag.py:88} INFO - 📋 Executing: dbt deps
[2025-06-04T09:50:04.867+0000] {dbt_only_dag.py:101} INFO - 📄 stdout: [0m09:49:59  Running with dbt=1.9.6
[0m09:50:00  Installing dbt-labs/dbt_utils
[0m09:50:03  Installed from version 1.1.1
[0m09:50:03  Updated version available: 1.3.0
[0m09:50:03  
[0m09:50:03  Updates available for packages: ['dbt-labs/dbt_utils']                 
Update your versions in packages.yml, then run dbt deps

[2025-06-04T09:50:04.868+0000] {dbt_only_dag.py:107} INFO - ✅ dbt deps completed successfully
[2025-06-04T09:50:04.869+0000] {python.py:201} INFO - Done. Returned value was: {'success': True, 'command': 'dbt deps', 'stdout': "\x1b[0m09:49:59  Running with dbt=1.9.6\n\x1b[0m09:50:00  Installing dbt-labs/dbt_utils\n\x1b[0m09:50:03  Installed from version 1.1.1\n\x1b[0m09:50:03  Updated version available: 1.3.0\n\x1b[0m09:50:03  \n\x1b[0m09:50:03  Updates available for packages: ['dbt-labs/dbt_utils']                 \nUpdate your versions in packages.yml, then run dbt deps\n", 'stderr': ''}
[2025-06-04T09:50:04.893+0000] {taskinstance.py:1138} INFO - Marking task as SUCCESS. dag_id=dbt_only_pipeline, task_id=dbt_deps, execution_date=20250604T094249, start_date=20250604T094957, end_date=20250604T095004
[2025-06-04T09:50:04.929+0000] {local_task_job_runner.py:234} INFO - Task exited with return code 0
[2025-06-04T09:50:04.960+0000] {taskinstance.py:3280} INFO - 1 downstream tasks scheduled from follow-on schedule check
