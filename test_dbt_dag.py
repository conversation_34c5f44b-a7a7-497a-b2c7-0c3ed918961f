#!/usr/bin/env python3
"""
Test script for dbt-only DAG
Validates DAG structure and basic functionality
"""

import sys
import os
from pathlib import Path

def test_dbt_dag():
    """Test the dbt-only DAG"""
    
    print("🧪 Testing dbt-only DAG...")
    
    # Add the DAGs directory to Python path
    dags_dir = Path("airflow-orchestration/dags")
    if dags_dir.exists():
        sys.path.insert(0, str(dags_dir.absolute()))
    else:
        print("❌ DAGs directory not found")
        return False
    
    try:
        # Import the DAG
        print("📦 Importing dbt_only_dag...")
        import dbt_only_dag
        
        # Check if DAG is defined
        if hasattr(dbt_only_dag, 'dag'):
            dag = dbt_only_dag.dag
            print(f"✅ DAG found: {dag.dag_id}")
            print(f"   Description: {dag.description}")
            print(f"   Schedule: {dag.schedule_interval}")
            print(f"   Tags: {dag.tags}")
            
            # Check tasks
            tasks = dag.tasks
            print(f"📋 Found {len(tasks)} tasks:")
            
            for task in tasks:
                print(f"   • {task.task_id} ({task.__class__.__name__})")
            
            # Check task dependencies
            print("🔗 Task dependencies:")
            for task in tasks:
                if task.upstream_task_ids:
                    upstream = ", ".join(task.upstream_task_ids)
                    print(f"   {task.task_id} ← {upstream}")
            
            # Validate expected tasks exist
            expected_tasks = [
                'start_dbt_pipeline',
                'dbt_debug',
                'dbt_deps',
                'dbt_run_staging',
                'dbt_run_marts',
                'dbt_run_monitoring',
                'dbt_test_staging',
                'dbt_test_marts',
                'dbt_test_all',
                'validate_dbt_results',
                'end_dbt_pipeline'
            ]
            
            task_ids = [task.task_id for task in tasks]
            missing_tasks = [task for task in expected_tasks if task not in task_ids]
            
            if missing_tasks:
                print(f"⚠️ Missing expected tasks: {missing_tasks}")
            else:
                print("✅ All expected tasks found")
            
            # Test DAG validation
            print("🔍 Running DAG validation...")
            dag.test()
            print("✅ DAG validation passed")
            
            return True
            
        else:
            print("❌ DAG object not found in module")
            return False
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing DAG: {e}")
        return False

def test_dbt_functions():
    """Test individual dbt functions"""
    
    print("\n🧪 Testing dbt functions...")
    
    try:
        # Import the module
        sys.path.insert(0, "airflow-orchestration/dags")
        import dbt_only_dag
        
        # Test utility functions
        workspace_path = dbt_only_dag.get_workspace_path()
        print(f"📁 Workspace path: {workspace_path}")
        
        dbt_path = dbt_only_dag.get_dbt_project_path()
        print(f"📁 dbt project path: {dbt_path}")
        
        # Check if paths exist (they might not in test environment)
        if os.path.exists(workspace_path):
            print("✅ Workspace path exists")
        else:
            print("⚠️ Workspace path doesn't exist (expected in test environment)")
        
        if os.path.exists(dbt_path):
            print("✅ dbt project path exists")
        else:
            print("⚠️ dbt project path doesn't exist (expected in test environment)")
        
        print("✅ Function tests completed")
        return True
        
    except Exception as e:
        print(f"❌ Error testing functions: {e}")
        return False

def main():
    """Main test function"""
    
    print("🚀 Starting dbt DAG tests...\n")
    
    # Test DAG structure
    dag_test_passed = test_dbt_dag()
    
    # Test functions
    function_test_passed = test_dbt_functions()
    
    # Summary
    print("\n📊 Test Summary:")
    print(f"   DAG Structure: {'✅ PASS' if dag_test_passed else '❌ FAIL'}")
    print(f"   Functions: {'✅ PASS' if function_test_passed else '❌ FAIL'}")
    
    if dag_test_passed and function_test_passed:
        print("\n🎉 All tests passed! The dbt-only DAG is ready to use.")
        return True
    else:
        print("\n❌ Some tests failed. Please review the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
