
  
    

create or replace transient table MYDB.LIVE_DATA.query_history_health
    

    
    as (

-- ETL Health Check: Query History Monitoring
-- Tracks query performance, failures, and runtime metrics

with query_history as (
    select
        query_id,
        query_text,
        database_name,
        schema_name,
        query_type,
        session_id,
        user_name,
        role_name,
        warehouse_name,
        warehouse_size,
        warehouse_type,
        cluster_number,
        query_tag,
        execution_status,
        error_code,
        error_message,
        start_time,
        end_time,
        total_elapsed_time,
        bytes_scanned,
        percentage_scanned_from_cache,
        bytes_written,
        bytes_written_to_result,
        bytes_read_from_result,
        rows_produced,
        rows_inserted,
        rows_updated,
        rows_deleted,
        rows_unloaded,
        bytes_deleted,
        partitions_scanned,
        partitions_total,
        bytes_spilled_to_local_storage,
        bytes_spilled_to_remote_storage,
        bytes_sent_over_the_network,
        compilation_time,
        execution_time,
        queued_provisioning_time,
        queued_repair_time,
        queued_overload_time,
        transaction_blocked_time,
        outbound_data_transfer_cloud,
        outbound_data_transfer_region,
        outbound_data_transfer_bytes,
        inbound_data_transfer_cloud,
        inbound_data_transfer_region,
        inbound_data_transfer_bytes,
        list_external_files_time,
        credits_used_cloud_services
    from snowflake.account_usage.query_history
    where start_time >= current_date - 7  -- Last 7 days
      and database_name = 'MYDB'  -- Focus on our database
),

etl_queries as (
    select
        *,
        -- Categorize queries
        case 
            when query_text ilike '%dbt%' then 'dbt'
            when query_text ilike '%insert%' or query_text ilike '%update%' or query_text ilike '%merge%' then 'ETL_DML'
            when query_text ilike '%create table%' or query_text ilike '%create view%' then 'ETL_DDL'
            when query_text ilike '%copy into%' then 'Data_Load'
            when query_text ilike '%select%' and query_text not ilike '%insert%' then 'Analytics'
            else 'Other'
        end as query_category,
        
        -- Performance flags
        case when total_elapsed_time > 300000 then true else false end as is_long_running,  -- > 5 minutes
        case when execution_status = 'FAIL' then true else false end as is_failed,
        case when bytes_spilled_to_local_storage > 0 or bytes_spilled_to_remote_storage > 0 then true else false end as has_spill,
        case when percentage_scanned_from_cache < 50 then true else false end as low_cache_hit,
        
        -- Time segments
        date(start_time) as execution_date,
        extract(hour from start_time) as execution_hour,
        case 
            when extract(hour from start_time) between 6 and 11 then 'Morning'
            when extract(hour from start_time) between 12 and 17 then 'Afternoon'
            when extract(hour from start_time) between 18 and 22 then 'Evening'
            else 'Night'
        end as time_segment
        
    from query_history
),

health_metrics as (
    select
        execution_date,
        query_category,
        time_segment,
        warehouse_name,
        
        -- Count metrics
        count(*) as total_queries,
        sum(case when is_failed then 1 else 0 end) as failed_queries,
        sum(case when is_long_running then 1 else 0 end) as long_running_queries,
        sum(case when has_spill then 1 else 0 end) as queries_with_spill,
        sum(case when low_cache_hit then 1 else 0 end) as low_cache_queries,
        
        -- Performance metrics
        avg(total_elapsed_time) as avg_elapsed_time_ms,
        max(total_elapsed_time) as max_elapsed_time_ms,
        percentile_cont(0.95) within group (order by total_elapsed_time) as p95_elapsed_time_ms,
        
        -- Data metrics
        sum(bytes_scanned) as total_bytes_scanned,
        sum(rows_produced) as total_rows_produced,
        avg(percentage_scanned_from_cache) as avg_cache_hit_rate,
        
        -- Cost metrics
        sum(credits_used_cloud_services) as total_credits_used,
        
        -- Latest execution
        max(start_time) as latest_execution_time
        
    from etl_queries
    group by execution_date, query_category, time_segment, warehouse_name
)

select
    *,
    -- Health scores (0-100)
    case 
        when failed_queries = 0 then 100
        when failed_queries::float / total_queries <= 0.01 then 95
        when failed_queries::float / total_queries <= 0.05 then 80
        when failed_queries::float / total_queries <= 0.10 then 60
        else 30
    end as reliability_score,
    
    case 
        when avg_elapsed_time_ms <= 30000 then 100  -- < 30 seconds
        when avg_elapsed_time_ms <= 120000 then 80  -- < 2 minutes
        when avg_elapsed_time_ms <= 300000 then 60  -- < 5 minutes
        else 30
    end as performance_score,
    
    case 
        when avg_cache_hit_rate >= 80 then 100
        when avg_cache_hit_rate >= 60 then 80
        when avg_cache_hit_rate >= 40 then 60
        else 30
    end as efficiency_score,
    
    -- Overall health score
    round((
        (case when failed_queries = 0 then 100 when failed_queries::float / total_queries <= 0.01 then 95 when failed_queries::float / total_queries <= 0.05 then 80 when failed_queries::float / total_queries <= 0.10 then 60 else 30 end) * 0.4 +
        (case when avg_elapsed_time_ms <= 30000 then 100 when avg_elapsed_time_ms <= 120000 then 80 when avg_elapsed_time_ms <= 300000 then 60 else 30 end) * 0.3 +
        (case when avg_cache_hit_rate >= 80 then 100 when avg_cache_hit_rate >= 60 then 80 when avg_cache_hit_rate >= 40 then 60 else 30 end) * 0.3
    ), 0) as overall_health_score,
    
    current_timestamp() as health_check_timestamp
    
from health_metrics
order by execution_date desc, query_category, warehouse_name
    )
;


  