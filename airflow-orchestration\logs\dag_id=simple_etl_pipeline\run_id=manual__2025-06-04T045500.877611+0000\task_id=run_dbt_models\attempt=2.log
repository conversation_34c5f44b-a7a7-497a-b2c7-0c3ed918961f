[2025-06-04T05:00:15.024+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=non-requeueable deps ti=<TaskInstance: simple_etl_pipeline.run_dbt_models manual__2025-06-04T04:55:00.877611+00:00 [queued]>
[2025-06-04T05:00:15.035+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=requeueable deps ti=<TaskInstance: simple_etl_pipeline.run_dbt_models manual__2025-06-04T04:55:00.877611+00:00 [queued]>
[2025-06-04T05:00:15.036+0000] {taskinstance.py:2170} INFO - Starting attempt 2 of 2
[2025-06-04T05:00:15.047+0000] {taskinstance.py:2191} INFO - Executing <Task(PythonOperator): run_dbt_models> on 2025-06-04 04:55:00.877611+00:00
[2025-06-04T05:00:15.054+0000] {standard_task_runner.py:60} INFO - Started process 445 to run task
[2025-06-04T05:00:15.058+0000] {standard_task_runner.py:87} INFO - Running: ['***', 'tasks', 'run', 'simple_etl_pipeline', 'run_dbt_models', 'manual__2025-06-04T04:55:00.877611+00:00', '--job-id', '84', '--raw', '--subdir', 'DAGS_FOLDER/simple_etl_dag.py', '--cfg-path', '/tmp/tmpqupxx9s2']
[2025-06-04T05:00:15.059+0000] {standard_task_runner.py:88} INFO - Job 84: Subtask run_dbt_models
[2025-06-04T05:00:15.110+0000] {task_command.py:423} INFO - Running <TaskInstance: simple_etl_pipeline.run_dbt_models manual__2025-06-04T04:55:00.877611+00:00 [running]> on host 8fb7c8dc5097
[2025-06-04T05:00:15.184+0000] {taskinstance.py:2480} INFO - Exporting env vars: AIRFLOW_CTX_DAG_OWNER='data-team' AIRFLOW_CTX_DAG_ID='simple_etl_pipeline' AIRFLOW_CTX_TASK_ID='run_dbt_models' AIRFLOW_CTX_EXECUTION_DATE='2025-06-04T04:55:00.877611+00:00' AIRFLOW_CTX_TRY_NUMBER='2' AIRFLOW_CTX_DAG_RUN_ID='manual__2025-06-04T04:55:00.877611+00:00'
[2025-06-04T05:00:15.185+0000] {simple_etl_dag.py:78} INFO - Running dbt run using Python API
[2025-06-04T05:00:16.692+0000] {simple_etl_dag.py:109} INFO - Executing dbt run
[2025-06-04T05:00:16.785+0000] {logging_mixin.py:188} INFO - 05:00:16  Running with dbt=1.9.6
[2025-06-04T05:00:18.639+0000] {logging_mixin.py:188} INFO - 05:00:18  Registered adapter: snowflake=1.9.4
[2025-06-04T05:00:22.043+0000] {logging_mixin.py:188} INFO - 05:00:22  Encountered an error:
'dbt_snowflake://macros/apply_grants.sql'
[2025-06-04T05:00:22.044+0000] {logging_mixin.py:188} INFO - 05:00:22  Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/cli/requires.py", line 153, in wrapper
    result, success = func(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/cli/requires.py", line 103, in wrapper
    return func(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/cli/requires.py", line 235, in wrapper
    return func(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/cli/requires.py", line 264, in wrapper
    return func(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/cli/requires.py", line 311, in wrapper
    return func(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/cli/requires.py", line 327, in wrapper
    setup_manifest(ctx, write=write, write_perf_info=write_perf_info)
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/cli/requires.py", line 351, in setup_manifest
    ctx.obj["manifest"] = parse_manifest(
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/parser/manifest.py", line 2069, in parse_manifest
    manifest = ManifestLoader.get_full_manifest(
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/parser/manifest.py", line 312, in get_full_manifest
    manifest = loader.load()
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/parser/manifest.py", line 381, in load
    self.load_and_parse_macros(project_parser_files)
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/parser/manifest.py", line 683, in load_and_parse_macros
    block = FileBlock(self.manifest.files[file_id])
KeyError: 'dbt_snowflake://macros/apply_grants.sql'
[2025-06-04T05:00:23.014+0000] {simple_etl_dag.py:116} ERROR - dbt run failed
[2025-06-04T05:00:23.015+0000] {taskinstance.py:2698} ERROR - Task failed with exception
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/models/taskinstance.py", line 433, in _execute_task
    result = execute_callable(context=context, **execute_callable_kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/operators/python.py", line 199, in execute
    return_value = self.execute_callable()
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/operators/python.py", line 216, in execute_callable
    return self.python_callable(*self.op_args, **self.op_kwargs)
  File "/opt/airflow/dags/simple_etl_dag.py", line 219, in run_dbt_models
    return run_dbt_command_python('run', **context)
  File "/opt/airflow/dags/simple_etl_dag.py", line 117, in run_dbt_command_python
    raise Exception(f"dbt {command_type} failed")
Exception: dbt run failed
[2025-06-04T05:00:23.025+0000] {taskinstance.py:1138} INFO - Marking task as FAILED. dag_id=simple_etl_pipeline, task_id=run_dbt_models, execution_date=20250604T045500, start_date=20250604T050015, end_date=20250604T050023
[2025-06-04T05:00:23.037+0000] {standard_task_runner.py:107} ERROR - Failed to execute job 84 for task run_dbt_models (dbt run failed; 445)
[2025-06-04T05:00:23.103+0000] {local_task_job_runner.py:234} INFO - Task exited with return code 1
[2025-06-04T05:00:23.125+0000] {taskinstance.py:3280} INFO - 0 downstream tasks scheduled from follow-on schedule check
