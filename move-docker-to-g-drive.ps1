# Move Docker to G: Drive Script
Write-Host "💾 Moving Docker to G: Drive for Space Optimization..." -ForegroundColor Green

# Check current space usage
Write-Host "📊 Current Drive Space:" -ForegroundColor Yellow
$drives = Get-WmiObject -Class Win32_LogicalDisk | Where-Object {$_.DeviceID -eq "C:" -or $_.DeviceID -eq "G:"}
foreach ($drive in $drives) {
    $freeGB = [math]::Round($drive.FreeSpace/1GB, 2)
    $totalGB = [math]::Round($drive.Size/1GB, 2)
    Write-Host "   $($drive.DeviceID) - Free: $freeGB GB / Total: $totalGB GB" -ForegroundColor White
}

# Step 1: Stop Airflow services
Write-Host "🛑 Stopping Airflow services..." -ForegroundColor Yellow
try {
    cd "airflow-orchestration"
    $env:PATH += ";C:\Program Files\Docker\Docker\resources\bin"
    docker compose down
    Write-Host "✅ Airflow services stopped" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Could not stop Airflow services (may not be running)" -ForegroundColor Yellow
}

# Step 2: Create G:\Docker directory
Write-Host "📁 Creating G:\Docker directory..." -ForegroundColor Yellow
try {
    New-Item -ItemType Directory -Force -Path "G:\Docker" | Out-Null
    Write-Host "✅ G:\Docker directory created" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to create G:\Docker directory: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 3: Instructions for manual steps
Write-Host ""
Write-Host "📋 MANUAL STEPS REQUIRED:" -ForegroundColor Cyan
Write-Host "Docker Desktop settings need to be changed manually." -ForegroundColor White
Write-Host ""
Write-Host "Please follow these steps:" -ForegroundColor Yellow
Write-Host "1. 🐳 Open Docker Desktop" -ForegroundColor White
Write-Host "2. ⚙️ Click Settings (gear icon)" -ForegroundColor White
Write-Host "3. 📂 Go to Resources → Advanced" -ForegroundColor White
Write-Host "4. 📍 Find 'Disk image location'" -ForegroundColor White
Write-Host "5. 🔍 Click 'Browse' and select: G:\Docker" -ForegroundColor White
Write-Host "6. ✅ Click 'Apply & Restart'" -ForegroundColor White
Write-Host "7. ⏳ Wait for Docker to restart (may take 10-30 minutes)" -ForegroundColor White
Write-Host ""

# Step 4: Wait for user confirmation
Write-Host "⏸️ Press any key AFTER you have completed the Docker Desktop settings change..." -ForegroundColor Cyan
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

# Step 5: Verify Docker is working
Write-Host "🧪 Testing Docker functionality..." -ForegroundColor Yellow
try {
    $dockerVersion = docker --version
    Write-Host "✅ Docker version: $dockerVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker not accessible. Please ensure Docker Desktop has restarted." -ForegroundColor Red
    exit 1
}

# Step 6: Test basic Docker functionality
Write-Host "🧪 Testing Docker with hello-world..." -ForegroundColor Yellow
try {
    $result = docker run --rm hello-world 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Docker hello-world test passed" -ForegroundColor Green
    } else {
        Write-Host "⚠️ Docker hello-world test had issues" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️ Could not run Docker hello-world test" -ForegroundColor Yellow
}

# Step 7: Restart Airflow services
Write-Host "🚀 Restarting Airflow services..." -ForegroundColor Yellow
try {
    cd "airflow-orchestration"
    docker compose up -d
    Start-Sleep 10
    
    # Check service status
    $services = docker compose ps --format "{{.Service}}: {{.Status}}"
    Write-Host "📊 Airflow Services Status:" -ForegroundColor Cyan
    foreach ($service in $services) {
        if ($service -like "*healthy*") {
            Write-Host "   ✅ $service" -ForegroundColor Green
        } elseif ($service -like "*starting*") {
            Write-Host "   ⏳ $service" -ForegroundColor Yellow
        } else {
            Write-Host "   ⚠️ $service" -ForegroundColor Yellow
        }
    }
} catch {
    Write-Host "❌ Failed to restart Airflow services: $($_.Exception.Message)" -ForegroundColor Red
}

# Step 8: Test Airflow web interface
Write-Host "🌐 Testing Airflow web interface..." -ForegroundColor Yellow
try {
    Start-Sleep 5
    $response = Invoke-WebRequest -Uri "http://localhost:8080" -TimeoutSec 10 -UseBasicParsing
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ Airflow web interface accessible" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠️ Airflow web interface not yet ready (may need more time)" -ForegroundColor Yellow
}

# Step 9: Check final space usage
Write-Host "📊 Final Drive Space:" -ForegroundColor Yellow
$drives = Get-WmiObject -Class Win32_LogicalDisk | Where-Object {$_.DeviceID -eq "C:" -or $_.DeviceID -eq "G:"}
foreach ($drive in $drives) {
    $freeGB = [math]::Round($drive.FreeSpace/1GB, 2)
    $totalGB = [math]::Round($drive.Size/1GB, 2)
    Write-Host "   $($drive.DeviceID) - Free: $freeGB GB / Total: $totalGB GB" -ForegroundColor White
}

# Final summary
Write-Host ""
Write-Host "🎉 Docker Migration Summary:" -ForegroundColor Green
Write-Host "✅ G:\Docker directory created" -ForegroundColor White
Write-Host "✅ Docker functionality verified" -ForegroundColor White
Write-Host "✅ Airflow services restarted" -ForegroundColor White
Write-Host ""
Write-Host "🎯 Next Steps:" -ForegroundColor Cyan
Write-Host "1. 🌐 Open: http://localhost:8080" -ForegroundColor White
Write-Host "2. 🔑 Login: airflow / airflow" -ForegroundColor White
Write-Host "3. 📋 Verify DAGs are visible" -ForegroundColor White
Write-Host "4. 🧪 Test a DAG execution" -ForegroundColor White
Write-Host ""
Write-Host "💡 If Airflow services are still starting, wait a few minutes and check:" -ForegroundColor Yellow
Write-Host "   docker compose ps" -ForegroundColor White
