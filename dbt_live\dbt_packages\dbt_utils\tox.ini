[tox]
skipsdist = True
envlist = lint_all, testenv

[testenv]
passenv =
    # postgres env vars
    POSTGRES_HOST
    POSTGRES_USER
    DBT_ENV_SECRET_POSTGRES_PASS
    POSTGRES_PORT
    POSTGRES_DATABASE
    POSTGRES_SCHEMA
    # snowflake env vars
    SNOWFLAKE_ACCOUNT
    SNOWFLAKE_USER
    DBT_ENV_SECRET_SNOWFLAKE_PASS
    SNOWFLAKE_ROLE
    SNOWFLAKE_DATABASE
    SNOWFLAKE_WAREHOUSE
    SNOWFLAKE_SCHEMA
    # redshift
    REDSHIFT_HOST
    REDSHIFT_USER
    DBT_ENV_SECRET_REDSHIFT_PASS
    REDSHIFT_DATABASE
    REDSHIFT_SCHEMA
    REDSHIFT_PORT
    # bigquery
    BIGQUERY_PROJECT
    BIGQUERY_KEYFILE_JSON
    BIGQUERY_SCHEMA

# Snowflake integration tests for centralized dbt testing
# run dbt commands directly, assumes dbt is already installed in environment
[testenv:dbt_integration_snowflake]
changedir = integration_tests
allowlist_externals = 
    dbt
skip_install = true
commands =
    dbt --version
    dbt debug --target snowflake
    dbt deps --target snowflake
    dbt build --target snowflake --full-refresh


# Postgres integration tests for centralized dbt testing
# run dbt commands directly, assumes dbt is already installed in environment
[testenv:dbt_integration_postgres]
changedir = integration_tests
allowlist_externals = 
    dbt
skip_install = true
commands =
    dbt --version
    dbt debug --target postgres
    dbt deps --target postgres
    dbt build --target postgres --full-refresh

# BigQuery integration tests for centralized dbt testing
# run dbt commands directly, assumes dbt is already installed in environment
[testenv:dbt_integration_bigquery]
changedir = integration_tests
allowlist_externals = 
    dbt
skip_install = true
commands =
    dbt --version
    dbt debug --target bigquery
    dbt deps --target bigquery
    dbt build --target bigquery --full-refresh

# redshift integration tests for centralized dbt testing
# run dbt commands directly, assumes dbt is already installed in environment
[testenv:dbt_integration_redshift]
changedir = integration_tests
allowlist_externals = 
    dbt
skip_install = true
commands =
    dbt --version
    dbt debug --target redshift
    dbt deps --target redshift
    dbt build --target redshift --full-refresh
