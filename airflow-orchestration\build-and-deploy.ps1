# Build and Deploy Airflow with dbt-core
# PowerShell script to build custom Docker image and deploy Airflow

Write-Host "🚀 Building and Deploying Airflow with dbt-core..." -ForegroundColor Green

# Check if Docker is running
Write-Host "🔍 Checking Docker status..." -ForegroundColor Yellow
try {
    docker version | Out-Null
    Write-Host "✅ Docker is running" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker is not running. Please start Docker Desktop." -ForegroundColor Red
    exit 1
}

# Navigate to airflow-orchestration directory
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $scriptPath

Write-Host "📁 Working directory: $(Get-Location)" -ForegroundColor Cyan

# Stop existing containers
Write-Host "🛑 Stopping existing containers..." -ForegroundColor Yellow
docker compose down

# Remove old images (optional - uncomment if you want to force rebuild)
# Write-Host "🗑️ Removing old images..." -ForegroundColor Yellow
# docker compose down --rmi all

# Build the custom image
Write-Host "🔨 Building custom Airflow image with dbt-core..." -ForegroundColor Yellow
docker compose build

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Docker build failed!" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Docker build completed successfully!" -ForegroundColor Green

# Initialize Airflow
Write-Host "🔧 Initializing Airflow..." -ForegroundColor Yellow
docker compose up airflow-init

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Airflow initialization failed!" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Airflow initialization completed!" -ForegroundColor Green

# Start all services
Write-Host "🚀 Starting Airflow services..." -ForegroundColor Yellow
docker compose up -d

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Failed to start Airflow services!" -ForegroundColor Red
    exit 1
}

# Wait for services to be ready
Write-Host "⏳ Waiting for services to be ready..." -ForegroundColor Yellow
Start-Sleep -Seconds 30

# Check service status
Write-Host "📊 Checking service status..." -ForegroundColor Yellow
docker compose ps

# Test dbt installation
Write-Host "🧪 Testing dbt installation..." -ForegroundColor Yellow
$dbtTest = docker compose exec -T airflow-webserver dbt --version 2>&1

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ dbt is installed and working!" -ForegroundColor Green
    Write-Host "dbt version: $dbtTest" -ForegroundColor Cyan
} else {
    Write-Host "⚠️ dbt test failed, but services are running" -ForegroundColor Yellow
    Write-Host "Error: $dbtTest" -ForegroundColor Red
}

Write-Host "`n🎉 Deployment completed!" -ForegroundColor Green
Write-Host "📱 Airflow UI: http://localhost:8080" -ForegroundColor Cyan
Write-Host "👤 Username: airflow" -ForegroundColor Cyan
Write-Host "🔑 Password: airflow" -ForegroundColor Cyan

Write-Host "`n📋 Next steps:" -ForegroundColor Yellow
Write-Host "1. Open http://localhost:8080 in your browser" -ForegroundColor White
Write-Host "2. Login with airflow/airflow" -ForegroundColor White
Write-Host "3. Enable the simple_etl_pipeline DAG" -ForegroundColor White
Write-Host "4. Trigger a manual run to test dbt integration" -ForegroundColor White

Write-Host "`n🔧 Useful commands:" -ForegroundColor Yellow
Write-Host "- View logs: docker compose logs -f" -ForegroundColor White
Write-Host "- Stop services: docker compose down" -ForegroundColor White
Write-Host "- Restart services: docker compose restart" -ForegroundColor White
Write-Host "- Test dbt: docker compose exec airflow-webserver dbt --version" -ForegroundColor White
