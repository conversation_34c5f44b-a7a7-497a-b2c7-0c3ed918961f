#!/usr/bin/env python3
"""
Pipeline Diagnostics Tool
Checks all prerequisites and dependencies for the end-to-end pipeline.
"""

import os
import sys
import subprocess
import importlib
import yaml
import boto3
from pathlib import Path

def print_header(title):
    """Print a formatted header."""
    print(f"\n{'='*60}")
    print(f"🔍 {title}")
    print(f"{'='*60}")

def check_python_packages():
    """Check if required Python packages are installed."""
    print_header("Python Package Dependencies")
    
    required_packages = [
        'pandas', 'numpy', 'yaml', 'boto3', 'snowflake.connector',
        'dbt.cli', 'faker', 'structlog'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'yaml':
                import yaml
            elif package == 'snowflake.connector':
                import snowflake.connector
            elif package == 'dbt.cli':
                import dbt.cli
            else:
                importlib.import_module(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - MISSING")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n💡 Install missing packages:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ All Python packages are installed")
    return True

def check_aws_configuration():
    """Check AWS configuration and credentials."""
    print_header("AWS Configuration")
    
    try:
        # Check AWS CLI
        result = subprocess.run(['aws', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ AWS CLI: {result.stdout.strip()}")
        else:
            print("❌ AWS CLI not found")
            return False
    except FileNotFoundError:
        print("❌ AWS CLI not installed")
        return False
    
    try:
        # Check credentials
        session = boto3.Session()
        credentials = session.get_credentials()
        
        if credentials:
            print("✅ AWS credentials found")
            
            # Test S3 access
            s3_client = boto3.client('s3')
            buckets = s3_client.list_buckets()
            print(f"✅ S3 access verified ({len(buckets['Buckets'])} buckets)")
            
            # Check specific bucket
            bucket_name = "lake-loader-input-365542662955-20250525-001439"
            try:
                s3_client.head_bucket(Bucket=bucket_name)
                print(f"✅ Target bucket accessible: {bucket_name}")
            except Exception as e:
                print(f"⚠️ Target bucket issue: {e}")
                
        else:
            print("❌ AWS credentials not found")
            print("💡 Run: aws configure")
            return False
            
    except Exception as e:
        print(f"❌ AWS configuration error: {e}")
        return False
    
    return True

def check_snowflake_connection():
    """Check Snowflake connection."""
    print_header("Snowflake Connection")
    
    try:
        import snowflake.connector
        
        # Load config
        config_file = Path("config/live_pipeline_config.yml")
        if not config_file.exists():
            print("❌ Configuration file not found: config/live_pipeline_config.yml")
            return False
        
        with open(config_file, 'r') as f:
            config = yaml.safe_load(f)
        
        connection_params = {
            'account': config['snowflake']['account'],
            'user': config['snowflake']['user'],
            'password': 'Asdfjkll1234!@#$',
            'warehouse': config['snowflake']['warehouse'],
            'database': config['snowflake']['database'],
            'schema': config['snowflake']['schema'],
            'role': config['snowflake']['role']
        }
        
        print(f"✅ Config loaded: {config['snowflake']['account']}")
        
        # Test connection
        conn = snowflake.connector.connect(**connection_params)
        cursor = conn.cursor()
        
        cursor.execute("SELECT CURRENT_VERSION()")
        version = cursor.fetchone()[0]
        print(f"✅ Snowflake connection successful: {version}")
        
        # Check database and schema
        cursor.execute(f"USE DATABASE {config['snowflake']['database']}")
        cursor.execute(f"USE SCHEMA {config['snowflake']['schema']}")
        print(f"✅ Database/Schema accessible: {config['snowflake']['database']}.{config['snowflake']['schema']}")
        
        # Check external tables
        external_tables = ['EXT_LIVE_USERS', 'EXT_LIVE_ORDERS', 'EXT_LIVE_EVENTS']
        missing_tables = []
        
        for table in external_tables:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"✅ {table}: {count:,} records")
            except Exception as e:
                print(f"❌ {table}: {e}")
                missing_tables.append(table)
        
        cursor.close()
        conn.close()
        
        if missing_tables:
            print(f"\n💡 Create missing external tables:")
            print(f"python create_external_tables_simple.py")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Snowflake connection failed: {e}")
        return False

def check_dbt_configuration():
    """Check dbt configuration."""
    print_header("dbt Configuration")
    
    try:
        # Check dbt installation
        result = subprocess.run(['dbt', '--version'], capture_output=True, text=True, cwd='dbt_live')
        if result.returncode == 0:
            print(f"✅ dbt installed: {result.stdout.strip()}")
        else:
            print("❌ dbt not found or not working")
            return False
    except FileNotFoundError:
        print("❌ dbt command not found")
        return False
    
    # Check dbt project
    dbt_project = Path("dbt_live/dbt_project.yml")
    if dbt_project.exists():
        print("✅ dbt project file found")
    else:
        print("❌ dbt project file missing")
        return False
    
    # Check profiles
    profiles_file = Path("dbt_live/profiles.yml")
    if profiles_file.exists():
        print("✅ dbt profiles file found")
    else:
        print("❌ dbt profiles file missing")
        return False
    
    # Test dbt debug
    try:
        result = subprocess.run(['dbt', 'debug'], capture_output=True, text=True, cwd='dbt_live', timeout=30)
        if "All checks passed!" in result.stdout:
            print("✅ dbt debug passed")
            return True
        else:
            print("⚠️ dbt debug issues:")
            print(result.stdout)
            return False
    except subprocess.TimeoutExpired:
        print("⚠️ dbt debug timed out")
        return False
    except Exception as e:
        print(f"❌ dbt debug failed: {e}")
        return False

def check_file_structure():
    """Check required files and directories."""
    print_header("File Structure")
    
    required_files = [
        "config/live_pipeline_config.yml",
        "run_end2end_pipeline.py",
        "daily_data_generator.py",
        "create_external_tables_simple.py",
        "dbt_live/dbt_project.yml",
        "dbt_live/profiles.yml"
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - MISSING")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n💡 Missing files need to be created or restored")
        return False
    
    return True

def run_quick_test():
    """Run a quick test of the pipeline components."""
    print_header("Quick Pipeline Test")
    
    try:
        # Test data generator import
        sys.path.append('.')
        from daily_data_generator import DailyDataGenerator
        print("✅ Data generator import successful")
        
        # Test pipeline runner import
        from run_end2end_pipeline import End2EndPipelineRunner
        print("✅ Pipeline runner import successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Import test failed: {e}")
        return False

def main():
    """Main diagnostic function."""
    print("🔍 End-to-End Pipeline Diagnostics")
    print("=" * 60)
    
    checks = [
        ("Python Packages", check_python_packages),
        ("AWS Configuration", check_aws_configuration),
        ("Snowflake Connection", check_snowflake_connection),
        ("dbt Configuration", check_dbt_configuration),
        ("File Structure", check_file_structure),
        ("Quick Test", run_quick_test)
    ]
    
    results = {}
    
    for check_name, check_func in checks:
        try:
            results[check_name] = check_func()
        except Exception as e:
            print(f"❌ {check_name} check crashed: {e}")
            results[check_name] = False
    
    # Summary
    print_header("Diagnostic Summary")
    
    all_passed = True
    for check_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status} {check_name}")
        if not passed:
            all_passed = False
    
    print(f"\n{'='*60}")
    if all_passed:
        print("🎉 ALL CHECKS PASSED - Pipeline ready to run!")
        print("💡 Run: python run_end2end_pipeline.py --save-results")
    else:
        print("⚠️ SOME CHECKS FAILED - Fix issues before running pipeline")
        print("💡 See error messages above for specific fixes needed")
    print(f"{'='*60}")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
