#!/usr/bin/env python3
"""
Fix External Tables Script
Refreshes Snowflake external tables to resolve stage issues
Run this script when you get "External table marked invalid" errors
"""

import os
import yaml
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def refresh_external_tables():
    """Refresh all external tables to fix stage issues"""
    
    logger.info("🔄 Starting external table refresh...")
    
    try:
        import snowflake.connector
        
        # Load configuration
        config_file = Path("config/live_pipeline_config.yml")
        if not config_file.exists():
            logger.error(f"❌ Config file not found: {config_file}")
            return False
        
        with open(config_file, 'r') as f:
            config = yaml.safe_load(f)
        
        # Connection parameters
        connection_params = {
            'account': config['snowflake']['account'],
            'user': config['snowflake']['user'],
            'password': 'Asdfjkll1234!@#$',
            'warehouse': config['snowflake']['warehouse'],
            'database': config['snowflake']['database'],
            'schema': config['snowflake']['schema'],
            'role': config['snowflake']['role']
        }
        
        logger.info(f"🔗 Connecting to Snowflake: {config['snowflake']['account']}")
        conn = snowflake.connector.connect(**connection_params)
        cursor = conn.cursor()
        
        # Set context
        cursor.execute(f"USE DATABASE {config['snowflake']['database']}")
        cursor.execute(f"USE SCHEMA {config['snowflake']['schema']}")
        
        # External tables to refresh
        external_tables = ['EXT_LIVE_USERS', 'EXT_LIVE_ORDERS', 'EXT_LIVE_EVENTS']
        
        logger.info(f"📋 Found {len(external_tables)} external tables to refresh")
        
        refresh_results = {}
        
        for table in external_tables:
            try:
                logger.info(f"🔄 Processing {table}...")
                
                # Check if table exists
                cursor.execute(f"SHOW TABLES LIKE '{table}'")
                if not cursor.fetchone():
                    logger.warning(f"⚠️ Table {table} not found, skipping...")
                    continue
                
                # Get current count (might fail if table is invalid)
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count_before = cursor.fetchone()[0]
                    logger.info(f"📊 Current records in {table}: {count_before:,}")
                except Exception as e:
                    logger.warning(f"⚠️ Could not count records in {table}: {e}")
                    count_before = "unknown"
                
                # Refresh external table
                logger.info(f"🔄 Refreshing {table}...")
                cursor.execute(f"ALTER EXTERNAL TABLE {table} REFRESH")
                
                # Get new count
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count_after = cursor.fetchone()[0]
                
                if isinstance(count_before, int):
                    new_records = count_after - count_before
                    logger.info(f"✅ {table}: {count_before:,} → {count_after:,} (+{new_records:,})")
                else:
                    logger.info(f"✅ {table}: Now has {count_after:,} records")
                
                refresh_results[table] = {
                    'status': 'success',
                    'count_before': count_before,
                    'count_after': count_after,
                    'new_records': new_records if isinstance(count_before, int) else 'N/A'
                }
                
            except Exception as e:
                logger.error(f"❌ Failed to refresh {table}: {e}")
                refresh_results[table] = {
                    'status': 'error',
                    'error': str(e)
                }
        
        cursor.close()
        conn.close()
        
        # Summary
        successful = [table for table, result in refresh_results.items() if result.get('status') == 'success']
        failed = [table for table, result in refresh_results.items() if result.get('status') == 'error']
        
        logger.info("\n📊 Refresh Summary:")
        logger.info(f"   ✅ Successful: {len(successful)} tables")
        logger.info(f"   ❌ Failed: {len(failed)} tables")
        
        if successful:
            logger.info("   Successful tables:")
            for table in successful:
                result = refresh_results[table]
                logger.info(f"     • {table}: {result['count_after']} records")
        
        if failed:
            logger.info("   Failed tables:")
            for table in failed:
                result = refresh_results[table]
                logger.info(f"     • {table}: {result['error']}")
        
        return len(successful) > 0
        
    except ImportError:
        logger.error("❌ snowflake-connector-python not installed")
        logger.info("💡 Install with: pip install snowflake-connector-python")
        return False
    except Exception as e:
        logger.error(f"❌ External table refresh failed: {e}")
        return False

def check_dbt_staging_models():
    """Check if dbt staging models can run after refresh"""
    
    logger.info("\n🧪 Testing dbt staging models...")
    
    dbt_dir = Path("dbt_live")
    if not dbt_dir.exists():
        logger.error(f"❌ dbt directory not found: {dbt_dir}")
        return False
    
    try:
        import subprocess
        
        # Change to dbt directory
        original_cwd = os.getcwd()
        os.chdir(dbt_dir)
        
        try:
            # Test dbt connection
            logger.info("🔍 Testing dbt connection...")
            result = subprocess.run(['dbt', 'debug'], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                logger.info("✅ dbt connection successful")
            else:
                logger.warning(f"⚠️ dbt debug issues: {result.stderr}")
            
            # Try to run staging models
            logger.info("🏗️ Testing staging models...")
            result = subprocess.run(['dbt', 'run', '--models', 'staging'], capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                logger.info("✅ dbt staging models ran successfully!")
                logger.info("🎉 External table refresh was successful!")
                return True
            else:
                logger.error(f"❌ dbt staging models still failing: {result.stderr}")
                return False
                
        finally:
            os.chdir(original_cwd)
            
    except subprocess.TimeoutExpired:
        logger.error("❌ dbt command timed out")
        return False
    except Exception as e:
        logger.error(f"❌ Error testing dbt models: {e}")
        return False

def main():
    """Main function"""
    
    logger.info("🚀 External Table Fix Script")
    logger.info("=" * 50)
    
    # Step 1: Refresh external tables
    refresh_success = refresh_external_tables()
    
    if not refresh_success:
        logger.error("❌ External table refresh failed")
        return False
    
    # Step 2: Test dbt staging models
    dbt_success = check_dbt_staging_models()
    
    if dbt_success:
        logger.info("\n🎉 SUCCESS! External tables are now working.")
        logger.info("💡 You can now run your dbt-only DAG successfully.")
    else:
        logger.error("\n❌ External tables refreshed but dbt models still failing.")
        logger.info("💡 Check dbt logs for additional issues.")
    
    return dbt_success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
