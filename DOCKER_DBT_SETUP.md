# Docker dbt Setup Guide

## Current Status

The Airflow pipeline now includes **mock mode** functionality that allows it to run successfully even when dbt is not installed in the Docker container. This is a temporary solution for development and testing.

## Mock Mode Behavior

When dbt is not available in the Docker container, the pipeline will:

1. ✅ **Continue running** without failing
2. 🎭 **Simulate dbt commands** with clear logging
3. ⚠️ **Log warnings** that transformations are not actually running
4. 📝 **Provide instructions** for installing dbt for production use

### Mock Mode Log Example
```
INFO - 🎭 MOCK MODE: Simulating 'dbt run'
WARNING - ⚠️ This is a simulation - no actual dbt transformations are running
INFO - 📝 To run real dbt commands, install dbt-core in the Airflow container
INFO - ✅ MOCK: dbt run simulation completed successfully
```

## Installing dbt in Docker (Production Setup)

For production use, you should install dbt-core in the Airflow Docker container.

### Option 1: Custom Dockerfile

Create a custom Dockerfile extending the Airflow image:

```dockerfile
FROM apache/airflow:2.7.0-python3.10

# Switch to root to install packages
USER root

# Install system dependencies if needed
RUN apt-get update && apt-get install -y \
    git \
    && rm -rf /var/lib/apt/lists/*

# Switch back to airflow user
USER airflow

# Install dbt-core and dbt-snowflake
RUN pip install --no-cache-dir \
    dbt-core==1.9.6 \
    dbt-snowflake==1.9.4

# Copy dbt profiles and project files
COPY --chown=airflow:root dbt_live/ /opt/airflow/workspace/dbt_live/
```

### Option 2: Docker Compose with Requirements

Add to your `docker-compose.yml`:

```yaml
services:
  airflow-webserver:
    build:
      context: .
      dockerfile: Dockerfile.airflow
    environment:
      - _AIRFLOW_DB_UPGRADE=true
      - _AIRFLOW_WWW_USER_CREATE=true
    volumes:
      - ./dbt_live:/opt/airflow/workspace/dbt_live
      - ./requirements.txt:/requirements.txt
```

Create `requirements.txt`:
```
dbt-core==1.9.6
dbt-snowflake==1.9.4
```

### Option 3: Runtime Installation

Add to your DAG initialization:

```python
def install_dbt_if_missing():
    """Install dbt if not available"""
    try:
        import dbt.cli.main
        logging.info("dbt is already installed")
    except ImportError:
        logging.info("Installing dbt-core...")
        subprocess.run([
            "pip", "install", "dbt-core==1.9.6", "dbt-snowflake==1.9.4"
        ], check=True)
```

## Verification

After installing dbt, verify it works:

```bash
# In the Docker container
dbt --version
dbt debug --project-dir /opt/airflow/workspace/dbt_live
```

## Current Pipeline Behavior

| Environment | dbt Available | Behavior |
|-------------|---------------|----------|
| Local Development | ✅ Yes | Real dbt commands execute |
| Docker (no dbt) | ❌ No | Mock mode with warnings |
| Docker (with dbt) | ✅ Yes | Real dbt commands execute |

## Next Steps

1. **For Development**: Mock mode allows pipeline testing without dbt setup
2. **For Production**: Install dbt-core in Docker container using one of the options above
3. **For Testing**: Use mock mode to verify pipeline logic and flow

## Troubleshooting

### Common Issues

1. **Permission errors**: Ensure proper user permissions in Docker
2. **Path issues**: Verify dbt project path is correctly mounted
3. **Connection issues**: Check Snowflake credentials in profiles.yml

### Debug Commands

```bash
# Check if dbt is installed
python -c "import dbt.cli.main; print('dbt available')"

# Test dbt connection
dbt debug --project-dir /opt/airflow/workspace/dbt_live

# Check mounted volumes
ls -la /opt/airflow/workspace/dbt_live
```

## Benefits of Current Approach

✅ **Graceful degradation**: Pipeline continues even without dbt
✅ **Clear feedback**: Obvious logging about what's happening
✅ **Easy transition**: Install dbt to switch from mock to real execution
✅ **Development friendly**: Test pipeline logic without full dbt setup
✅ **Production ready**: Full dbt support when properly configured
