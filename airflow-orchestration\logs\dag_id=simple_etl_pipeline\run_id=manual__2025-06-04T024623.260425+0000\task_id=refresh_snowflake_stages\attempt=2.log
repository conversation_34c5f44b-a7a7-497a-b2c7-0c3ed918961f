[2025-06-04T02:51:32.565+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=non-requeueable deps ti=<TaskInstance: simple_etl_pipeline.refresh_snowflake_stages manual__2025-06-04T02:46:23.260425+00:00 [queued]>
[2025-06-04T02:51:32.578+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=requeueable deps ti=<TaskInstance: simple_etl_pipeline.refresh_snowflake_stages manual__2025-06-04T02:46:23.260425+00:00 [queued]>
[2025-06-04T02:51:32.579+0000] {taskinstance.py:2170} INFO - Starting attempt 2 of 2
[2025-06-04T02:51:32.596+0000] {taskinstance.py:2191} INFO - Executing <Task(PythonOperator): refresh_snowflake_stages> on 2025-06-04 02:46:23.260425+00:00
[2025-06-04T02:51:32.605+0000] {standard_task_runner.py:60} INFO - Started process 229 to run task
[2025-06-04T02:51:32.612+0000] {standard_task_runner.py:87} INFO - Running: ['***', 'tasks', 'run', 'simple_etl_pipeline', 'refresh_snowflake_stages', 'manual__2025-06-04T02:46:23.260425+00:00', '--job-id', '18', '--raw', '--subdir', 'DAGS_FOLDER/simple_etl_dag.py', '--cfg-path', '/tmp/tmpexrdjobb']
[2025-06-04T02:51:32.614+0000] {standard_task_runner.py:88} INFO - Job 18: Subtask refresh_snowflake_stages
[2025-06-04T02:51:32.680+0000] {task_command.py:423} INFO - Running <TaskInstance: simple_etl_pipeline.refresh_snowflake_stages manual__2025-06-04T02:46:23.260425+00:00 [running]> on host 9fdd53a423c8
[2025-06-04T02:51:32.770+0000] {taskinstance.py:2480} INFO - Exporting env vars: AIRFLOW_CTX_DAG_OWNER='data-team' AIRFLOW_CTX_DAG_ID='simple_etl_pipeline' AIRFLOW_CTX_TASK_ID='refresh_snowflake_stages' AIRFLOW_CTX_EXECUTION_DATE='2025-06-04T02:46:23.260425+00:00' AIRFLOW_CTX_TRY_NUMBER='2' AIRFLOW_CTX_DAG_RUN_ID='manual__2025-06-04T02:46:23.260425+00:00'
[2025-06-04T02:51:32.771+0000] {simple_etl_dag.py:48} INFO - Running script: /opt/***/workspace/create_snowflake_stages.py
[2025-06-04T02:51:33.784+0000] {simple_etl_dag.py:64} ERROR - Script failed: usage: create_snowflake_stages.py [-h] --role-arn ROLE_ARN
create_snowflake_stages.py: error: the following arguments are required: --role-arn

[2025-06-04T02:51:33.785+0000] {taskinstance.py:2698} ERROR - Task failed with exception
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/models/taskinstance.py", line 433, in _execute_task
    result = execute_callable(context=context, **execute_callable_kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/operators/python.py", line 199, in execute
    return_value = self.execute_callable()
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/operators/python.py", line 216, in execute_callable
    return self.python_callable(*self.op_args, **self.op_kwargs)
  File "/opt/airflow/dags/simple_etl_dag.py", line 113, in refresh_stages
    return run_python_script('create_snowflake_stages.py', **context)
  File "/opt/airflow/dags/simple_etl_dag.py", line 65, in run_python_script
    raise Exception(f"Script failed: {result.stderr}")
Exception: Script failed: usage: create_snowflake_stages.py [-h] --role-arn ROLE_ARN
create_snowflake_stages.py: error: the following arguments are required: --role-arn

[2025-06-04T02:51:33.798+0000] {taskinstance.py:1138} INFO - Marking task as FAILED. dag_id=simple_etl_pipeline, task_id=refresh_snowflake_stages, execution_date=20250604T024623, start_date=20250604T025132, end_date=20250604T025133
[2025-06-04T02:51:33.812+0000] {standard_task_runner.py:107} ERROR - Failed to execute job 18 for task refresh_snowflake_stages (Script failed: usage: create_snowflake_stages.py [-h] --role-arn ROLE_ARN
create_snowflake_stages.py: error: the following arguments are required: --role-arn
; 229)
[2025-06-04T02:51:33.834+0000] {local_task_job_runner.py:234} INFO - Task exited with return code 1
[2025-06-04T02:51:33.859+0000] {taskinstance.py:3280} INFO - 0 downstream tasks scheduled from follow-on schedule check
