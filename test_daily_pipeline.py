#!/usr/bin/env python3
"""
Test Daily Pipeline
Quick test script to validate the daily pipeline components.
"""

import logging
import sys
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_daily_data_generation():
    """Test daily data generation."""
    logger.info("🧪 Testing daily data generation...")
    
    try:
        from daily_data_generator import DailyDataGenerator
        
        generator = DailyDataGenerator()
        
        # Generate data for yesterday (to avoid conflicts with today's data)
        test_date = datetime.now().date() - timedelta(days=1)
        
        result = generator.generate_daily_data(test_date)
        
        if result['success']:
            logger.info(f"✅ Daily data generation test passed")
            logger.info(f"📊 Generated {result['total_records']:,} records for {test_date}")
            return True
        else:
            logger.error(f"❌ Daily data generation test failed: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Daily data generation test failed with exception: {e}")
        return False

def test_s3_connectivity():
    """Test S3 connectivity."""
    logger.info("🧪 Testing S3 connectivity...")
    
    try:
        import boto3
        
        s3_client = boto3.client('s3')
        
        # Test basic S3 access
        response = s3_client.list_buckets()
        logger.info(f"✅ S3 connectivity test passed - found {len(response['Buckets'])} buckets")
        
        # Test specific bucket access
        bucket_name = 'lake-loader-input-365542662955-20250525-001439'
        try:
            s3_client.head_bucket(Bucket=bucket_name)
            logger.info(f"✅ Target bucket access confirmed: {bucket_name}")
            return True
        except Exception as e:
            logger.error(f"❌ Target bucket access failed: {e}")
            return False
            
    except Exception as e:
        logger.error(f"❌ S3 connectivity test failed: {e}")
        return False

def test_snowflake_connectivity():
    """Test Snowflake connectivity."""
    logger.info("🧪 Testing Snowflake connectivity...")
    
    try:
        import snowflake.connector
        
        connection_params = {
            'account': 'SVLFKJI-IX89869',
            'user': 'XINBINZHANG',
            'password': '****************',
            'warehouse': 'COMPUTE_WH',
            'database': 'MYDB',
            'schema': 'LIVE_DATA',
            'role': 'ACCOUNTADMIN'
        }
        
        conn = snowflake.connector.connect(**connection_params)
        cursor = conn.cursor()
        
        # Test basic query
        cursor.execute("SELECT CURRENT_TIMESTAMP()")
        result = cursor.fetchone()
        
        cursor.close()
        conn.close()
        
        logger.info(f"✅ Snowflake connectivity test passed - current time: {result[0]}")
        return True
        
    except Exception as e:
        logger.error(f"❌ Snowflake connectivity test failed: {e}")
        return False

def test_dbt_setup():
    """Test dbt setup."""
    logger.info("🧪 Testing dbt setup...")
    
    try:
        import subprocess
        import os
        from pathlib import Path
        
        # Change to dbt directory
        original_dir = os.getcwd()
        dbt_dir = Path('dbt_live')
        
        if not dbt_dir.exists():
            logger.error(f"❌ dbt directory not found: {dbt_dir}")
            return False
        
        os.chdir(dbt_dir)
        
        # Test dbt debug
        result = subprocess.run(['dbt', 'debug'], capture_output=True, text=True, timeout=30)
        
        os.chdir(original_dir)
        
        if result.returncode == 0:
            logger.info("✅ dbt setup test passed")
            return True
        else:
            logger.error(f"❌ dbt setup test failed: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        logger.error("❌ dbt setup test failed: timeout")
        os.chdir(original_dir)
        return False
    except Exception as e:
        logger.error(f"❌ dbt setup test failed: {e}")
        os.chdir(original_dir)
        return False

def test_end2end_pipeline():
    """Test the complete end-to-end pipeline."""
    logger.info("🧪 Testing end-to-end pipeline...")
    
    try:
        from run_end2end_pipeline import End2EndPipelineRunner
        
        runner = End2EndPipelineRunner()
        
        # Run pipeline for yesterday
        test_date = datetime.now().date() - timedelta(days=1)
        
        results = runner.run_end2end_pipeline(test_date)
        
        if results['success']:
            logger.info("✅ End-to-end pipeline test passed")
            logger.info(f"⏱️ Pipeline duration: {results['duration_minutes']} minutes")
            return True
        else:
            logger.error("❌ End-to-end pipeline test failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ End-to-end pipeline test failed: {e}")
        return False

def run_all_tests():
    """Run all tests."""
    logger.info("🚀 Starting Daily Pipeline Tests...")
    logger.info("=" * 50)
    
    tests = [
        ("S3 Connectivity", test_s3_connectivity),
        ("Snowflake Connectivity", test_snowflake_connectivity),
        ("dbt Setup", test_dbt_setup),
        ("Daily Data Generation", test_daily_data_generation),
        ("End-to-End Pipeline", test_end2end_pipeline)
    ]
    
    results = {}
    
    for test_name, test_function in tests:
        logger.info(f"\n🔄 Running: {test_name}")
        logger.info("-" * 30)
        
        try:
            results[test_name] = test_function()
        except Exception as e:
            logger.error(f"❌ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    logger.info("\n" + "=" * 50)
    logger.info("📋 TEST SUMMARY")
    logger.info("=" * 50)
    
    passed_tests = sum(1 for result in results.values() if result)
    total_tests = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{test_name:<25} {status}")
    
    logger.info(f"\nOverall: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        logger.info("🎉 All tests passed! Daily pipeline is ready.")
        return True
    else:
        logger.error("💥 Some tests failed. Check the logs above.")
        return False

def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Test Daily Pipeline')
    parser.add_argument('--test', choices=['s3', 'snowflake', 'dbt', 'generation', 'end2end', 'all'], 
                       default='all', help='Specific test to run')
    
    args = parser.parse_args()
    
    if args.test == 's3':
        success = test_s3_connectivity()
    elif args.test == 'snowflake':
        success = test_snowflake_connectivity()
    elif args.test == 'dbt':
        success = test_dbt_setup()
    elif args.test == 'generation':
        success = test_daily_data_generation()
    elif args.test == 'end2end':
        success = test_end2end_pipeline()
    else:  # all
        success = run_all_tests()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
