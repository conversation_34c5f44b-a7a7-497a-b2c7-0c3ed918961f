# Port Configuration Guide

This document outlines the port assignments for different services in the project to avoid conflicts.

## Service Port Assignments

| Service | Port | URL | Credentials |
|---------|------|-----|-------------|
| **Airflow** | 8080 | http://localhost:8080 | airflow/airflow |
| **dbt docs** | 8088 | http://localhost:8088 | None |

## dbt Documentation Server

### Quick Start
```bash
cd dbt_live

# Option 1: Use convenient scripts
.\serve_docs.ps1      # PowerShell
serve_docs.bat        # Command Prompt

# Option 2: Manual command
dbt docs serve --port 8088
```

### Why Port 8088?
- **Port 8080**: Reserved for Airflow web interface
- **Port 8088**: Chosen for dbt docs to avoid conflicts
- **Alternative**: If 8088 is also in use, try 8089 or any available port

### Custom Port Usage
```bash
# Use a different port if needed
dbt docs serve --port 8089
dbt docs serve --port 9000
```

## Port Conflict Resolution

### Check Port Usage
```powershell
# Windows - Check if port is in use
netstat -an | findstr :8088
netstat -an | findstr :8080

# If port is busy, choose another port
dbt docs serve --port 8089
```

### Common Port Conflicts
- **8080**: Airflow, Jenkins, Tomcat
- **8088**: Hadoop NameNode (if running locally)
- **8000**: Django development server
- **3000**: React development server
- **5000**: Flask development server

### Recommended Alternative Ports
- **8088**: Primary choice for dbt docs
- **8089**: Secondary choice
- **9000**: Tertiary choice
- **8888**: Alternative (commonly used for Jupyter)

## Environment-Specific Configuration

### Development
- Airflow: http://localhost:8080
- dbt docs: http://localhost:8088

### Production
- Configure reverse proxy or load balancer
- Use proper domain names
- Implement SSL/TLS

## Troubleshooting

### "Port already in use" Error
1. Check what's using the port:
   ```bash
   netstat -an | findstr :8088
   ```

2. Choose a different port:
   ```bash
   dbt docs serve --port 8089
   ```

3. Or stop the conflicting service if safe to do so

### Browser Cache Issues
- Clear browser cache if docs don't update
- Use incognito/private browsing mode
- Hard refresh with Ctrl+F5

## Scripts and Automation

### PowerShell Script Features
- ✅ Automatic documentation generation
- ✅ Port 8088 configuration
- ✅ Error handling
- ✅ User-friendly messages

### Batch Script Features
- ✅ Windows Command Prompt compatibility
- ✅ Same functionality as PowerShell version
- ✅ Error checking and user feedback

## Integration with Development Workflow

### Typical Development Session
1. Start Airflow: `docker-compose up` (port 8080)
2. Work on dbt models in your IDE
3. Test models: `dbt run`, `dbt test`
4. Generate docs: `.\serve_docs.ps1` (port 8088)
5. Review lineage and documentation in browser

### CI/CD Considerations
- Document port assignments in deployment scripts
- Use environment variables for port configuration
- Ensure port availability in deployment environments
