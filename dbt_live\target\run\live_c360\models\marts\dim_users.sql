
  
    

create or replace transient table MYDB.LIVE_DATA.dim_users
    

    
    as (

-- User dimension table for live data pipeline
-- Provides a clean, analytics-ready view of user data

with users_base as (
    select * from MYDB.LIVE_DATA.stg_users
),

user_metrics as (
    select
        user_id,
        count(*) as total_orders,
        sum(amount) as total_spent,
        avg(amount) as avg_order_value,
        min(transaction_date) as first_order_date,
        max(transaction_date) as last_order_date,
        sum(item_count) as total_items_purchased
    from MYDB.LIVE_DATA.stg_orders
    group by user_id
),

user_events as (
    select
        user_id,
        count(*) as total_events,
        count(distinct session_id) as total_sessions,
        count(distinct event_date) as active_days,
        min(event_timestamp) as first_event_date,
        max(event_timestamp) as last_event_date,
        
        -- Event type counts
        sum(case when action = 'view' then 1 else 0 end) as view_events,
        sum(case when action = 'click' then 1 else 0 end) as click_events,
        sum(case when action = 'log' then 1 else 0 end) as login_events,
        sum(case when action = 'purchase' then 1 else 0 end) as purchase_events,
        
        -- Platform usage
        sum(case when device_category = 'Mobile' then 1 else 0 end) as mobile_events,
        sum(case when device_category = 'Web' then 1 else 0 end) as web_events
        
    from MYDB.LIVE_DATA.stg_events
    group by user_id
),

final as (
    select
        -- User identifiers
        u.user_id,
        u.email_hash,
        
        -- Personal information
        u.firstname,
        u.lastname,
        u.address,
        u.acquisition_channel,
        u.country,
        u.gender,
        u.age_group,
        
        -- Dates
        u.creation_date,
        u.last_activity_date,
        
        -- Behavioral flags
        u.is_churned,
        u.activity_segment,
        
        -- Derived user metrics
        u.days_since_creation,
        u.days_since_last_activity,
        
        -- Order metrics
        coalesce(om.total_orders, 0) as total_orders,
        coalesce(om.total_spent, 0) as total_spent,
        coalesce(om.avg_order_value, 0) as avg_order_value,
        om.first_order_date,
        om.last_order_date,
        coalesce(om.total_items_purchased, 0) as total_items_purchased,
        
        -- Event metrics
        coalesce(ue.total_events, 0) as total_events,
        coalesce(ue.total_sessions, 0) as total_sessions,
        coalesce(ue.active_days, 0) as active_days,
        ue.first_event_date,
        ue.last_event_date,
        
        -- Event type metrics
        coalesce(ue.view_events, 0) as view_events,
        coalesce(ue.click_events, 0) as click_events,
        coalesce(ue.login_events, 0) as login_events,
        coalesce(ue.purchase_events, 0) as purchase_events,
        
        -- Platform metrics
        coalesce(ue.mobile_events, 0) as mobile_events,
        coalesce(ue.web_events, 0) as web_events,
        
        -- Calculated metrics
        case 
            when om.total_orders > 0 then round(ue.total_events::float / om.total_orders, 2)
            else 0
        end as events_per_order,
        
        case 
            when ue.total_sessions > 0 then round(ue.total_events::float / ue.total_sessions, 2)
            else 0
        end as events_per_session,
        
        case 
            when om.total_orders > 0 then 
                datediff('day', om.first_order_date, om.last_order_date)::float / om.total_orders
            else 0
        end as avg_days_between_orders,
        
        -- Customer segments
        case 
            when om.total_orders = 0 then 'No Orders'
            when om.total_orders = 1 then 'One-time Buyer'
            when om.total_orders <= 3 then 'Occasional Buyer'
            when om.total_orders <= 10 then 'Regular Buyer'
            else 'Frequent Buyer'
        end as purchase_segment,
        
        case 
            when om.total_spent = 0 then 'No Spend'
            when om.total_spent < 50 then 'Low Value'
            when om.total_spent < 200 then 'Medium Value'
            when om.total_spent < 500 then 'High Value'
            else 'Premium Value'
        end as value_segment,
        
        case 
            when ue.mobile_events > ue.web_events then 'Mobile Preferred'
            when ue.web_events > ue.mobile_events then 'Web Preferred'
            when ue.mobile_events = ue.web_events and ue.mobile_events > 0 then 'Multi-Platform'
            else 'Unknown'
        end as platform_preference,
        
        -- Data quality flags
        u.has_future_creation_date,
        u.has_invalid_activity_date,
        
        -- Metadata
        u.batch_id,
        u.generated_at,
        u.processed_at,
        current_timestamp() as mart_created_at
        
    from users_base u
    left join user_metrics om on u.user_id = om.user_id
    left join user_events ue on u.user_id = ue.user_id
)

select * from final
    )
;


  