# Airflow with dbt-core Docker Setup

This directory contains a custom Airflow Docker setup with dbt-core pre-installed for running dbt transformations in the ETL pipeline.

## 🚀 Quick Start

### Windows (PowerShell)
```powershell
.\build-and-deploy.ps1
```

### Linux/Mac (Bash)
```bash
./build-and-deploy.sh
```

## 📋 What's Included

### Custom Docker Image
- **Base**: Apache Airflow 2.8.1 with Python 3.10
- **dbt-core**: Version 1.9.6
- **dbt-snowflake**: Version 1.9.4
- **Additional packages**: boto3, pandas, faker, etc.

### Environment Configuration
- **dbt profiles directory**: `/opt/airflow/workspace/dbt_live`
- **dbt project directory**: `/opt/airflow/workspace/dbt_live`
- **Workspace mounting**: Local workspace mounted to container

## 🔧 Manual Setup

If you prefer to build and deploy manually:

### 1. Build the Custom Image
```bash
cd airflow-orchestration
docker-compose build
```

### 2. Initialize Airflow
```bash
docker-compose up airflow-init
```

### 3. Start Services
```bash
docker-compose up -d
```

### 4. Verify dbt Installation
```bash
docker-compose exec airflow-webserver dbt --version
```

## 📱 Access Points

- **Airflow UI**: http://localhost:8080
- **Username**: airflow
- **Password**: airflow

## 🧪 Testing dbt Integration

### Test dbt Connection
```bash
docker-compose exec airflow-webserver dbt debug --project-dir /opt/airflow/workspace/dbt_live
```

### Run dbt Models
```bash
docker-compose exec airflow-webserver dbt run --project-dir /opt/airflow/workspace/dbt_live
```

### Run dbt Tests
```bash
docker-compose exec airflow-webserver dbt test --project-dir /opt/airflow/workspace/dbt_live
```

## 🔍 Troubleshooting

### Common Issues

1. **Docker build fails**
   ```bash
   # Clean up and rebuild
   docker-compose down --rmi all
   docker-compose build --no-cache
   ```

2. **dbt not found**
   ```bash
   # Check if dbt is installed
   docker-compose exec airflow-webserver which dbt
   docker-compose exec airflow-webserver pip list | grep dbt
   ```

3. **Permission issues**
   ```bash
   # Check file permissions
   docker-compose exec airflow-webserver ls -la /opt/airflow/workspace/
   ```

4. **Snowflake connection issues**
   ```bash
   # Test Snowflake connection
   docker-compose exec airflow-webserver dbt debug --project-dir /opt/airflow/workspace/dbt_live
   ```

### Useful Commands

```bash
# View all logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f airflow-webserver

# Restart services
docker-compose restart

# Stop all services
docker-compose down

# Enter container shell
docker-compose exec airflow-webserver bash

# Check container status
docker-compose ps
```

## 📁 File Structure

```
airflow-orchestration/
├── Dockerfile              # Custom Airflow image with dbt
├── docker-compose.yml      # Docker Compose configuration
├── requirements.txt        # Python dependencies
├── build-and-deploy.ps1    # Windows deployment script
├── build-and-deploy.sh     # Linux/Mac deployment script
├── dags/                   # Airflow DAGs
├── logs/                   # Airflow logs
└── config/                 # Airflow configuration
```

## 🔄 Updates and Maintenance

### Updating dbt Version
1. Edit `Dockerfile` and `requirements.txt`
2. Update version numbers
3. Rebuild: `docker-compose build --no-cache`

### Adding New Python Packages
1. Add to `requirements.txt`
2. Rebuild the image
3. Restart services

### Backup and Restore
```bash
# Backup volumes
docker-compose down
docker run --rm -v airflow-orchestration_postgres-db-volume:/data -v $(pwd):/backup alpine tar czf /backup/postgres-backup.tar.gz -C /data .

# Restore volumes
docker run --rm -v airflow-orchestration_postgres-db-volume:/data -v $(pwd):/backup alpine tar xzf /backup/postgres-backup.tar.gz -C /data
```

## 🎯 Next Steps

1. **Deploy the setup** using the build script
2. **Access Airflow UI** at http://localhost:8080
3. **Enable the simple_etl_pipeline DAG**
4. **Trigger a test run** to verify dbt integration
5. **Monitor logs** for successful dbt execution

## 📞 Support

If you encounter issues:
1. Check the troubleshooting section above
2. Review Docker and Airflow logs
3. Verify dbt installation and configuration
4. Test Snowflake connectivity
