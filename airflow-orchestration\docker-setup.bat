@echo off
echo Setting up Docker environment for Airflow...

REM Add Docker to PATH
set PATH=%PATH%;C:\Program Files\Docker\Docker\resources\bin

REM Check Docker version
echo Checking Docker version...
docker --version

REM Check Docker Compose version
echo Checking Docker Compose version...
docker compose version

REM Initialize Airflow (first time setup)
echo Initializing Airflow...
docker compose up airflow-init

echo Docker setup complete!
echo.
echo To start Airflow, run: docker compose up -d
echo To stop Airflow, run: docker compose down
echo.
echo Airflow Web UI will be available at: http://localhost:8080
echo Username: airflow
echo Password: airflow
