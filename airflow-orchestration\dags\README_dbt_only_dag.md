# dbt-Only Pipeline DAG

This DAG (`dbt_only_dag.py`) is a dedicated Airflow pipeline for running dbt transformations only. It focuses specifically on dbt model execution, testing, and documentation without any data generation or external dependencies.

## 🎯 Purpose

- **Pure dbt operations**: Only runs dbt commands, no data generation or external API calls
- **Modular execution**: Runs staging, marts, and monitoring models separately
- **Comprehensive testing**: Tests models at each stage
- **Documentation generation**: Optional dbt docs generation
- **Health monitoring**: Built-in validation and health checks

## 📋 DAG Structure

### Task Flow
```
start_dbt_pipeline
    ↓
dbt_debug (health check)
    ↓
dbt_deps (install dependencies)
    ↓
refresh_snowflake_stages (fix external tables)
    ↓
dbt_run_marts → dbt_test_marts
    ↓
dbt_run_monitoring
    ↓
dbt_test_all → validate_dbt_results
    ↓              ↓
dbt_generate_docs (optional)
    ↓
cleanup_dbt_artifacts
    ↓
end_dbt_pipeline
```

**Note**: Staging tasks (`dbt_run_staging` and `dbt_test_staging`) have been removed per user request.

### Task Details

#### Setup Tasks
- **`dbt_debug`**: Validates dbt configuration and Snowflake connection
- **`dbt_deps`**: Installs dbt package dependencies
- **`refresh_snowflake_stages`**: Refreshes external tables to fix stage issues

#### Model Execution Tasks
- **`dbt_run_marts`**: Runs marts models (tables)
- **`dbt_run_monitoring`**: Runs monitoring/health check models

#### Testing Tasks
- **`dbt_test_marts`**: Tests marts models only
- **`dbt_test_all`**: Runs all dbt tests

#### Documentation & Validation
- **`dbt_generate_docs`**: Generates dbt documentation (optional)
- **`validate_dbt_results`**: Validates pipeline execution
- **`cleanup_dbt_artifacts`**: Cleans up old artifacts

## ⚙️ Configuration

### DAG Parameters
The DAG accepts runtime parameters that can be set when triggering manually:

```python
params = {
    'run_staging': True,      # Run staging models
    'run_marts': True,        # Run marts models  
    'run_monitoring': True,   # Run monitoring models
    'run_tests': True,        # Run dbt tests
    'generate_docs': False,   # Generate documentation
    'docs_port': 8088,        # Port for docs server
}
```

### Schedule
- **Default**: Daily at 2:00 AM (`0 2 * * *`)
- **Max Active Runs**: 1 (prevents overlapping executions)
- **Retries**: 2 attempts with 3-minute delays

## 🚀 Usage

### 1. Manual Trigger (Airflow UI)
1. Go to Airflow UI: http://localhost:8080
2. Find `dbt_only_pipeline` DAG
3. Click "Trigger DAG"
4. Optionally modify parameters in the trigger form

### 2. Command Line Trigger
```bash
# Trigger with default parameters
docker-compose exec airflow-webserver airflow dags trigger dbt_only_pipeline

# Trigger with custom parameters
docker-compose exec airflow-webserver airflow dags trigger dbt_only_pipeline \
  --conf '{"generate_docs": true, "run_monitoring": false}'
```

### 3. Scheduled Execution
The DAG runs automatically daily at 2:00 AM. You can modify the schedule in the DAG definition:

```python
schedule_interval='0 2 * * *',  # Daily at 2 AM
# schedule_interval='@hourly',   # Every hour
# schedule_interval=None,        # Manual only
```

## 🔧 Customization

### Running Specific Model Groups
You can modify the task functions to run specific models:

```python
def dbt_run_custom_models(**context):
    """Run custom model selection"""
    return run_dbt_command('run', models='marts.dim_users', **context)
```

### Adding New Model Groups
To add new model groups (e.g., `analytics`):

1. Add a new task function:
```python
def dbt_run_analytics(**context):
    """Run analytics models"""
    return run_dbt_command('run', models='analytics', **context)
```

2. Add the task definition:
```python
run_analytics_task = PythonOperator(
    task_id='dbt_run_analytics',
    python_callable=dbt_run_analytics,
    dag=dag,
)
```

3. Update dependencies:
```python
test_marts_task >> run_analytics_task >> run_monitoring_task
```

## 🐛 Troubleshooting

### Common Issues

1. **External table errors** (Most Common)
   - **Error**: `External table EXT_LIVE_EVENTS marked invalid. Stage dropped.`
   - **Cause**: Snowflake external tables lose connection to S3 stages
   - **Solution**: The DAG includes automatic stage refresh, or run `python fix_external_tables.py`

2. **dbt not found**
   - Ensure dbt is installed in the Airflow container
   - Check the custom Docker image includes dbt-core and dbt-snowflake

3. **Connection errors**
   - Verify `profiles.yml` configuration
   - Check Snowflake credentials and network connectivity
   - Run `dbt_debug` task to diagnose

4. **Model failures**
   - Check individual task logs in Airflow UI
   - Review dbt logs in `/opt/airflow/workspace/dbt_live/logs/`
   - Validate source data availability

5. **Permission errors**
   - Ensure proper file permissions in mounted workspace
   - Check Snowflake role permissions

### Debugging Commands

```bash
# Check dbt installation
docker-compose exec airflow-webserver dbt --version

# Test dbt connection
docker-compose exec airflow-webserver dbt debug --project-dir /opt/airflow/workspace/dbt_live

# Run dbt manually
docker-compose exec airflow-webserver dbt run --project-dir /opt/airflow/workspace/dbt_live

# Check logs
docker-compose exec airflow-webserver ls -la /opt/airflow/workspace/dbt_live/logs/
```

## 📊 Monitoring

### Task Monitoring
- Monitor task status in Airflow UI
- Check task logs for detailed execution information
- Set up email alerts for failures (configure in `default_args`)

### dbt Monitoring
- Review dbt test results in task logs
- Check generated monitoring models in Snowflake
- Use dbt docs for model lineage and documentation

## 🔄 Integration

This DAG can be used:
- **Standalone**: For pure dbt transformations
- **Downstream**: After data ingestion DAGs
- **Upstream**: Before reporting/analytics DAGs
- **Parallel**: With other transformation pipelines

### Example Integration
```python
# In another DAG, trigger this DAG after data ingestion
from airflow.operators.trigger_dagrun import TriggerDagRunOperator

trigger_dbt = TriggerDagRunOperator(
    task_id='trigger_dbt_pipeline',
    trigger_dag_id='dbt_only_pipeline',
    dag=dag,
)

data_ingestion_task >> trigger_dbt
```
