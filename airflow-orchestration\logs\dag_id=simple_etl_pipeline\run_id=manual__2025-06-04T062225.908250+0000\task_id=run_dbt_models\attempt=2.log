[2025-06-04T06:27:51.848+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=non-requeueable deps ti=<TaskInstance: simple_etl_pipeline.run_dbt_models manual__2025-06-04T06:22:25.908250+00:00 [queued]>
[2025-06-04T06:27:51.856+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=requeueable deps ti=<TaskInstance: simple_etl_pipeline.run_dbt_models manual__2025-06-04T06:22:25.908250+00:00 [queued]>
[2025-06-04T06:27:51.858+0000] {taskinstance.py:2170} INFO - Starting attempt 2 of 2
[2025-06-04T06:27:51.868+0000] {taskinstance.py:2191} INFO - Executing <Task(PythonOperator): run_dbt_models> on 2025-06-04 06:22:25.908250+00:00
[2025-06-04T06:27:51.873+0000] {standard_task_runner.py:60} INFO - Started process 1717 to run task
[2025-06-04T06:27:51.877+0000] {standard_task_runner.py:87} INFO - Running: ['***', 'tasks', 'run', 'simple_etl_pipeline', 'run_dbt_models', 'manual__2025-06-04T06:22:25.908250+00:00', '--job-id', '104', '--raw', '--subdir', 'DAGS_FOLDER/simple_etl_dag.py', '--cfg-path', '/tmp/tmpyu_6f_94']
[2025-06-04T06:27:51.879+0000] {standard_task_runner.py:88} INFO - Job 104: Subtask run_dbt_models
[2025-06-04T06:27:51.929+0000] {task_command.py:423} INFO - Running <TaskInstance: simple_etl_pipeline.run_dbt_models manual__2025-06-04T06:22:25.908250+00:00 [running]> on host 8fb7c8dc5097
[2025-06-04T06:27:52.003+0000] {taskinstance.py:2480} INFO - Exporting env vars: AIRFLOW_CTX_DAG_OWNER='data-team' AIRFLOW_CTX_DAG_ID='simple_etl_pipeline' AIRFLOW_CTX_TASK_ID='run_dbt_models' AIRFLOW_CTX_EXECUTION_DATE='2025-06-04T06:22:25.908250+00:00' AIRFLOW_CTX_TRY_NUMBER='2' AIRFLOW_CTX_DAG_RUN_ID='manual__2025-06-04T06:22:25.908250+00:00'
[2025-06-04T06:27:52.004+0000] {simple_etl_dag.py:266} INFO - 🔧 Running dbt models using local successful approach...
[2025-06-04T06:28:04.692+0000] {simple_etl_dag.py:287} ERROR - ❌ dbt run failed: 
[2025-06-04T06:28:04.693+0000] {simple_etl_dag.py:294} ERROR - ❌ dbt run failed: dbt run failed: 
[2025-06-04T06:28:04.694+0000] {taskinstance.py:2698} ERROR - Task failed with exception
Traceback (most recent call last):
  File "/opt/airflow/dags/simple_etl_dag.py", line 288, in run_dbt_models
    raise Exception(f"dbt run failed: {result.stderr}")
Exception: dbt run failed: 

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/models/taskinstance.py", line 433, in _execute_task
    result = execute_callable(context=context, **execute_callable_kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/operators/python.py", line 199, in execute
    return_value = self.execute_callable()
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/operators/python.py", line 216, in execute_callable
    return self.python_callable(*self.op_args, **self.op_kwargs)
  File "/opt/airflow/dags/simple_etl_dag.py", line 295, in run_dbt_models
    raise Exception(f"dbt run failed: {e}")
Exception: dbt run failed: dbt run failed: 
[2025-06-04T06:28:04.706+0000] {taskinstance.py:1138} INFO - Marking task as FAILED. dag_id=simple_etl_pipeline, task_id=run_dbt_models, execution_date=20250604T062225, start_date=20250604T062751, end_date=20250604T062804
[2025-06-04T06:28:04.719+0000] {standard_task_runner.py:107} ERROR - Failed to execute job 104 for task run_dbt_models (dbt run failed: dbt run failed: ; 1717)
[2025-06-04T06:28:04.767+0000] {local_task_job_runner.py:234} INFO - Task exited with return code 1
[2025-06-04T06:28:04.791+0000] {taskinstance.py:3280} INFO - 0 downstream tasks scheduled from follow-on schedule check
