[2025-06-04T10:39:49.743+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=non-requeueable deps ti=<TaskInstance: dbt_marts_only.dbt_run_marts manual__2025-06-04T10:39:38+00:00 [queued]>
[2025-06-04T10:39:49.751+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=requeueable deps ti=<TaskInstance: dbt_marts_only.dbt_run_marts manual__2025-06-04T10:39:38+00:00 [queued]>
[2025-06-04T10:39:49.752+0000] {taskinstance.py:2170} INFO - Starting attempt 1 of 2
[2025-06-04T10:39:49.767+0000] {taskinstance.py:2191} INFO - Executing <Task(PythonOperator): dbt_run_marts> on 2025-06-04 10:39:38+00:00
[2025-06-04T10:39:49.775+0000] {standard_task_runner.py:60} INFO - Started process 558 to run task
[2025-06-04T10:39:49.780+0000] {standard_task_runner.py:87} INFO - Running: ['***', 'tasks', 'run', 'dbt_marts_only', 'dbt_run_marts', 'manual__2025-06-04T10:39:38+00:00', '--job-id', '121', '--raw', '--subdir', 'DAGS_FOLDER/dbt_marts_only_dag.py', '--cfg-path', '/tmp/tmp7hynsbll']
[2025-06-04T10:39:49.782+0000] {standard_task_runner.py:88} INFO - Job 121: Subtask dbt_run_marts
[2025-06-04T10:39:49.846+0000] {task_command.py:423} INFO - Running <TaskInstance: dbt_marts_only.dbt_run_marts manual__2025-06-04T10:39:38+00:00 [running]> on host bad0c90122e4
[2025-06-04T10:39:49.935+0000] {taskinstance.py:2480} INFO - Exporting env vars: AIRFLOW_CTX_DAG_OWNER='data-team' AIRFLOW_CTX_DAG_ID='dbt_marts_only' AIRFLOW_CTX_TASK_ID='dbt_run_marts' AIRFLOW_CTX_EXECUTION_DATE='2025-06-04T10:39:38+00:00' AIRFLOW_CTX_TRY_NUMBER='1' AIRFLOW_CTX_DAG_RUN_ID='manual__2025-06-04T10:39:38+00:00'
[2025-06-04T10:39:49.936+0000] {dbt_marts_only_dag.py:129} INFO - 🏪 Running dbt marts models...
[2025-06-04T10:39:49.939+0000] {dbt_marts_only_dag.py:62} INFO - 🚀 Running dbt run
[2025-06-04T10:39:49.940+0000] {dbt_marts_only_dag.py:82} INFO - 📋 Executing: dbt run --models marts
[2025-06-04T10:39:56.525+0000] {dbt_marts_only_dag.py:95} INFO - 📄 stdout: [0m10:39:51  Running with dbt=1.9.6
[0m10:39:52  Registered adapter: snowflake=1.9.4
[0m10:39:55  Encountered an error:
'dbt_snowflake://macros/catalog.sql'
[0m10:39:55  Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/cli/requires.py", line 153, in wrapper
    result, success = func(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/cli/requires.py", line 103, in wrapper
    return func(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/cli/requires.py", line 235, in wrapper
    return func(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/cli/requires.py", line 264, in wrapper
    return func(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/cli/requires.py", line 311, in wrapper
    return func(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/cli/requires.py", line 327, in wrapper
    setup_manifest(ctx, write=write, write_perf_info=write_perf_info)
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/cli/requires.py", line 351, in setup_manifest
    ctx.obj["manifest"] = parse_manifest(
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/parser/manifest.py", line 2069, in parse_manifest
    manifest = ManifestLoader.get_full_manifest(
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/parser/manifest.py", line 312, in get_full_manifest
    manifest = loader.load()
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/parser/manifest.py", line 381, in load
    self.load_and_parse_macros(project_parser_files)
  File "/home/<USER>/.local/lib/python3.10/site-packages/dbt/parser/manifest.py", line 683, in load_and_parse_macros
    block = FileBlock(self.manifest.files[file_id])
KeyError: 'dbt_snowflake://macros/catalog.sql'


[2025-06-04T10:39:56.526+0000] {dbt_marts_only_dag.py:110} ERROR - ❌ dbt run failed with return code 2
[2025-06-04T10:39:56.526+0000] {dbt_marts_only_dag.py:111} ERROR - ❌ Error output: 
[2025-06-04T10:39:56.527+0000] {dbt_marts_only_dag.py:120} ERROR - ❌ dbt run failed: dbt run failed with return code 2: 
[2025-06-04T10:39:56.529+0000] {taskinstance.py:2698} ERROR - Task failed with exception
Traceback (most recent call last):
  File "/opt/airflow/dags/dbt_marts_only_dag.py", line 112, in run_dbt_command
    raise Exception(f"{error_msg}: {result.stderr}")
Exception: dbt run failed with return code 2: 

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/models/taskinstance.py", line 433, in _execute_task
    result = execute_callable(context=context, **execute_callable_kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/operators/python.py", line 199, in execute
    return_value = self.execute_callable()
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/operators/python.py", line 216, in execute_callable
    return self.python_callable(*self.op_args, **self.op_kwargs)
  File "/opt/airflow/dags/dbt_marts_only_dag.py", line 130, in dbt_run_marts
    return run_dbt_command('run', models='marts', **context)
  File "/opt/airflow/dags/dbt_marts_only_dag.py", line 121, in run_dbt_command
    raise Exception(f"dbt {command_type} failed: {e}")
Exception: dbt run failed: dbt run failed with return code 2: 
[2025-06-04T10:39:56.542+0000] {taskinstance.py:1138} INFO - Marking task as UP_FOR_RETRY. dag_id=dbt_marts_only, task_id=dbt_run_marts, execution_date=20250604T103938, start_date=20250604T103949, end_date=20250604T103956
[2025-06-04T10:39:56.557+0000] {standard_task_runner.py:107} ERROR - Failed to execute job 121 for task dbt_run_marts (dbt run failed: dbt run failed with return code 2: ; 558)
[2025-06-04T10:39:56.568+0000] {local_task_job_runner.py:234} INFO - Task exited with return code 1
[2025-06-04T10:39:56.592+0000] {taskinstance.py:3280} INFO - 0 downstream tasks scheduled from follow-on schedule check
