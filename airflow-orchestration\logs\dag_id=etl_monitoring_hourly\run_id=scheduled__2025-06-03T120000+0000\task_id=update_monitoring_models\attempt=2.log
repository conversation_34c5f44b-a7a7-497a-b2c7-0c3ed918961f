[2025-06-04T03:24:16.964+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=non-requeueable deps ti=<TaskInstance: etl_monitoring_hourly.update_monitoring_models scheduled__2025-06-03T12:00:00+00:00 [queued]>
[2025-06-04T03:24:16.973+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=requeueable deps ti=<TaskInstance: etl_monitoring_hourly.update_monitoring_models scheduled__2025-06-03T12:00:00+00:00 [queued]>
[2025-06-04T03:24:16.974+0000] {taskinstance.py:2170} INFO - Starting attempt 2 of 2
[2025-06-04T03:24:16.987+0000] {taskinstance.py:2191} INFO - Executing <Task(PythonOperator): update_monitoring_models> on 2025-06-03 12:00:00+00:00
[2025-06-04T03:24:16.994+0000] {standard_task_runner.py:60} INFO - Started process 720 to run task
[2025-06-04T03:24:16.998+0000] {standard_task_runner.py:87} INFO - Running: ['***', 'tasks', 'run', 'etl_monitoring_hourly', 'update_monitoring_models', 'scheduled__2025-06-03T12:00:00+00:00', '--job-id', '46', '--raw', '--subdir', 'DAGS_FOLDER/monitoring_dag.py', '--cfg-path', '/tmp/tmpzlzp_nsz']
[2025-06-04T03:24:17.000+0000] {standard_task_runner.py:88} INFO - Job 46: Subtask update_monitoring_models
[2025-06-04T03:24:17.059+0000] {task_command.py:423} INFO - Running <TaskInstance: etl_monitoring_hourly.update_monitoring_models scheduled__2025-06-03T12:00:00+00:00 [running]> on host 9fdd53a423c8
[2025-06-04T03:24:17.138+0000] {taskinstance.py:2480} INFO - Exporting env vars: AIRFLOW_CTX_DAG_OWNER='data-team' AIRFLOW_CTX_DAG_ID='etl_monitoring_hourly' AIRFLOW_CTX_TASK_ID='update_monitoring_models' AIRFLOW_CTX_EXECUTION_DATE='2025-06-03T12:00:00+00:00' AIRFLOW_CTX_TRY_NUMBER='2' AIRFLOW_CTX_DAG_RUN_ID='scheduled__2025-06-03T12:00:00+00:00'
[2025-06-04T03:24:17.140+0000] {monitoring_dag.py:43} INFO - Updating monitoring models...
[2025-06-04T03:24:17.143+0000] {taskinstance.py:2698} ERROR - Task failed with exception
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/models/taskinstance.py", line 433, in _execute_task
    result = execute_callable(context=context, **execute_callable_kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/operators/python.py", line 199, in execute
    return_value = self.execute_callable()
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/operators/python.py", line 216, in execute_callable
    return self.python_callable(*self.op_args, **self.op_kwargs)
  File "/opt/airflow/dags/monitoring_dag.py", line 48, in update_monitoring_models
    result = subprocess.run([
  File "/usr/local/lib/python3.10/subprocess.py", line 503, in run
    with Popen(*popenargs, **kwargs) as process:
  File "/usr/local/lib/python3.10/subprocess.py", line 971, in __init__
    self._execute_child(args, executable, preexec_fn, close_fds,
  File "/usr/local/lib/python3.10/subprocess.py", line 1863, in _execute_child
    raise child_exception_type(errno_num, err_msg, err_filename)
PermissionError: [Errno 13] Permission denied: 'dbt'
[2025-06-04T03:24:17.157+0000] {taskinstance.py:1138} INFO - Marking task as FAILED. dag_id=etl_monitoring_hourly, task_id=update_monitoring_models, execution_date=20250603T120000, start_date=20250604T032416, end_date=20250604T032417
[2025-06-04T03:24:17.169+0000] {standard_task_runner.py:107} ERROR - Failed to execute job 46 for task update_monitoring_models ([Errno 13] Permission denied: 'dbt'; 720)
[2025-06-04T03:24:17.214+0000] {local_task_job_runner.py:234} INFO - Task exited with return code 1
[2025-06-04T03:24:17.245+0000] {taskinstance.py:3280} INFO - 0 downstream tasks scheduled from follow-on schedule check
