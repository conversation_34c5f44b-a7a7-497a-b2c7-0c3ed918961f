# dbt Documentation Server Script
# Serves dbt docs on port 8088 to avoid conflict with Airflow (port 8080)

Write-Host "🚀 Starting dbt documentation server on port 8088..." -ForegroundColor Green
Write-Host "📊 Airflow is running on port 8080" -ForegroundColor Yellow
Write-Host "📖 dbt docs will be available at: http://localhost:8088" -ForegroundColor Cyan
Write-Host ""

# Generate fresh documentation
Write-Host "📝 Generating documentation..." -ForegroundColor Blue
dbt docs generate

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Documentation generated successfully" -ForegroundColor Green
    Write-Host ""
    
    # Serve documentation on port 8088
    Write-Host "🌐 Starting web server on port 8088..." -ForegroundColor Blue
    Write-Host "💡 Press Ctrl+C to stop the server" -ForegroundColor Yellow
    Write-Host ""
    
    dbt docs serve --port 8088
} else {
    Write-Host "❌ Failed to generate documentation" -ForegroundColor Red
    Write-Host "🔍 Please check for errors above and fix them before serving docs" -ForegroundColor Yellow
    exit 1
}
