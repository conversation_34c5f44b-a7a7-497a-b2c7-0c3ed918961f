#!/usr/bin/env python3
"""
Explain dbt Execution: Local vs Remote
Shows exactly where dbt code lives vs where it executes.
"""

import logging
import os
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def explain_dbt_execution():
    """Explain how dbt works: local development, remote execution."""
    
    logger.info("🔧 UNDERSTANDING DBT: LOCAL vs REMOTE")
    logger.info("=" * 60)
    
    # 1. Show local dbt files
    logger.info("\n📁 LOCAL DBT FILES (on your machine):")
    logger.info("-" * 40)
    
    dbt_dir = Path("dbt_live")
    if dbt_dir.exists():
        logger.info(f"✅ dbt project directory: {dbt_dir.absolute()}")
        
        # Show model files
        models_dir = dbt_dir / "models"
        if models_dir.exists():
            logger.info(f"📝 SQL model files:")
            for sql_file in models_dir.rglob("*.sql"):
                relative_path = sql_file.relative_to(dbt_dir)
                file_size = sql_file.stat().st_size
                logger.info(f"   • {relative_path} ({file_size} bytes)")
        
        # Show config files
        config_files = ["dbt_project.yml", "profiles.yml"]
        for config_file in config_files:
            config_path = dbt_dir / config_file
            if config_path.exists():
                logger.info(f"⚙️ {config_file}: {config_path.absolute()}")
    
    # 2. Show what happens during dbt run
    logger.info("\n🚀 WHAT HAPPENS DURING 'dbt run':")
    logger.info("-" * 40)
    logger.info("1. 📖 dbt reads your local SQL files")
    logger.info("2. 🔄 dbt compiles Jinja templates into pure SQL")
    logger.info("3. 📡 dbt connects to Snowflake using profiles.yml")
    logger.info("4. 🏗️ dbt sends CREATE TABLE/VIEW statements to Snowflake")
    logger.info("5. ❄️ Snowflake executes the SQL on its warehouses")
    logger.info("6. 📊 Tables/views are created in Snowflake database")
    
    # 3. Show the compiled SQL
    logger.info("\n🔍 COMPILED SQL (what actually runs in Snowflake):")
    logger.info("-" * 40)
    
    target_dir = dbt_dir / "target" / "compiled" / "live_c360" / "models"
    if target_dir.exists():
        logger.info(f"📁 Compiled SQL location: {target_dir.absolute()}")
        
        # Show a sample compiled model
        for sql_file in target_dir.rglob("*.sql"):
            if "stg_users" in sql_file.name:
                logger.info(f"\n📄 Example compiled SQL ({sql_file.name}):")
                try:
                    with open(sql_file, 'r') as f:
                        content = f.read()
                        # Show first few lines
                        lines = content.split('\n')[:10]
                        for line in lines:
                            logger.info(f"   {line}")
                        if len(content.split('\n')) > 10:
                            logger.info("   ... (truncated)")
                except Exception as e:
                    logger.info(f"   Error reading file: {e}")
                break
    else:
        logger.info("❌ No compiled SQL found. Run 'dbt compile' first.")
    
    # 4. Show where results live
    logger.info("\n📊 WHERE THE RESULTS LIVE:")
    logger.info("-" * 40)
    logger.info("🏠 LOCAL (your machine):")
    logger.info("   • SQL model files (.sql)")
    logger.info("   • Configuration files (.yml)")
    logger.info("   • Compiled SQL (target/ folder)")
    logger.info("   • Logs and documentation")
    logger.info("")
    logger.info("❄️ REMOTE (Snowflake):")
    logger.info("   • Actual data tables")
    logger.info("   • Views and materialized views")
    logger.info("   • Query execution and compute")
    logger.info("   • Data storage and warehousing")
    
    # 5. Compare with Databricks
    logger.info("\n🆚 DATABRICKS vs DBT COMPARISON:")
    logger.info("-" * 40)
    logger.info("DATABRICKS NOTEBOOKS:")
    logger.info("   📝 Code: Stored in Databricks workspace")
    logger.info("   🚀 Execution: Runs on Databricks clusters")
    logger.info("   💾 Data: Stored in DBFS/Delta Lake")
    logger.info("   🔧 Development: Interactive notebooks")
    logger.info("")
    logger.info("DBT + SNOWFLAKE:")
    logger.info("   📝 Code: Stored locally (or git repo)")
    logger.info("   🚀 Execution: Runs on Snowflake warehouses")
    logger.info("   💾 Data: Stored in Snowflake database")
    logger.info("   🔧 Development: SQL files + CLI commands")
    
    # 6. Show the workflow
    logger.info("\n🔄 TYPICAL DBT WORKFLOW:")
    logger.info("-" * 40)
    logger.info("1. 📝 Write SQL models locally (VS Code, etc.)")
    logger.info("2. 🧪 Test connection: dbt debug")
    logger.info("3. 🔄 Compile models: dbt compile")
    logger.info("4. 🚀 Execute models: dbt run")
    logger.info("5. ✅ Test data quality: dbt test")
    logger.info("6. 📚 Generate docs: dbt docs generate")
    logger.info("7. 🔁 Repeat for new changes")
    
    # 7. Show advantages
    logger.info("\n✨ ADVANTAGES OF THIS APPROACH:")
    logger.info("-" * 40)
    logger.info("📝 Version Control: SQL models in git")
    logger.info("🧪 Local Testing: Fast development cycle")
    logger.info("🏗️ Separation of Concerns: Code vs execution")
    logger.info("📊 Scalability: Leverage Snowflake's compute")
    logger.info("🔧 Modularity: Reusable SQL components")
    logger.info("📚 Documentation: Auto-generated lineage")
    
    logger.info("\n" + "=" * 60)
    logger.info("🎯 SUMMARY: dbt is like having Databricks notebooks")
    logger.info("   as local SQL files that execute on Snowflake!")
    logger.info("=" * 60)

def show_dbt_vs_databricks_example():
    """Show side-by-side example of Databricks vs dbt approach."""
    
    logger.info("\n📋 SIDE-BY-SIDE EXAMPLE:")
    logger.info("=" * 60)
    
    logger.info("\n🔵 DATABRICKS APPROACH:")
    logger.info("-" * 30)
    logger.info("# Databricks Notebook Cell")
    logger.info("df = spark.read.table('raw_users')")
    logger.info("df_clean = df.filter(df.age > 0)")
    logger.info("df_clean.write.saveAsTable('clean_users')")
    logger.info("")
    logger.info("📍 Execution: Databricks cluster")
    logger.info("💾 Storage: Delta Lake")
    logger.info("🔧 Development: Interactive notebook")
    
    logger.info("\n❄️ DBT + SNOWFLAKE APPROACH:")
    logger.info("-" * 30)
    logger.info("-- models/clean_users.sql")
    logger.info("SELECT *")
    logger.info("FROM {{ ref('raw_users') }}")
    logger.info("WHERE age > 0")
    logger.info("")
    logger.info("📍 Execution: Snowflake warehouse")
    logger.info("💾 Storage: Snowflake database")
    logger.info("🔧 Development: Local SQL files")
    
    logger.info("\n🎯 BOTH CREATE THE SAME RESULT:")
    logger.info("   A 'clean_users' table with filtered data!")

def main():
    """Main function."""
    explain_dbt_execution()
    show_dbt_vs_databricks_example()

if __name__ == "__main__":
    main()
