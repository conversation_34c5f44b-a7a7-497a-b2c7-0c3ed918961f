# 🚀 End-to-End Pipeline - Quick Reference

## ⚡ **Quick Commands**

### **Run Pipeline**
```bash
cd experiment2
python run_end2end_pipeline.py --save-results
```

### **Setup External Tables** (One-time)
```bash
cd experiment2
python create_external_tables_simple.py
```

### **Check Status**
```bash
# View latest results
cat pipeline_results_*.yaml | tail -20

# Check S3 files
aws s3 ls s3://lake-loader-input-365542662955-20250525-001439/live-data/ --recursive

# Test Snowflake connection
python -c "import snowflake.connector; print('✅ Snowflake connector available')"
```

## 📊 **Expected Results**

### **Success Indicators:**
- ✅ Duration: ~1-2 minutes
- ✅ Records: 95,000 total (5K users, 15K orders, 75K events)
- ✅ Data size: ~18 MB
- ✅ All 5 steps complete
- ✅ Return code: 0

### **Key Metrics:**
```
Step 1: Data Generation    → 95,000 records
Step 2: S3 Verification    → 18.72 MB uploaded
Step 3: Snowflake Refresh  → 3 tables refreshed
Step 4: dbt Pipeline       → 4 models built, tests passed
Step 5: Validation         → All counts verified
```

## 🚨 **Troubleshooting**

### **Common Fixes:**
```bash
# Missing faker
pip install faker

# AWS credentials
aws configure

# External tables missing
python create_external_tables_simple.py

# dbt issues
cd dbt_live && dbt debug
```

### **Emergency Reset:**
```bash
# Recreate external tables
python create_external_tables_simple.py

# Clear S3 cache (if needed)
aws s3 rm s3://bucket/live-data/ --recursive

# Reset dbt
cd dbt_live && dbt clean && dbt deps
```

## 🔄 **Daily Operations**

### **Morning Checklist:**
1. Run pipeline: `python run_end2end_pipeline.py --save-results`
2. Check results file for success
3. Verify record counts in Snowflake
4. Monitor S3 storage usage

### **Weekly Maintenance:**
1. Review pipeline performance trends
2. Check Snowflake credit usage
3. Clean up old result files
4. Update data volumes if needed

## 📈 **Monitoring Queries**

### **Snowflake Health Check:**
```sql
USE DATABASE MYDB;
USE SCHEMA LIVE_DATA;

-- Record counts
SELECT 'EXT_LIVE_USERS' as table_name, COUNT(*) as records FROM EXT_LIVE_USERS
UNION ALL
SELECT 'EXT_LIVE_ORDERS', COUNT(*) FROM EXT_LIVE_ORDERS
UNION ALL
SELECT 'EXT_LIVE_EVENTS', COUNT(*) FROM EXT_LIVE_EVENTS
UNION ALL
SELECT 'STG_USERS', COUNT(*) FROM STG_USERS
UNION ALL
SELECT 'STG_ORDERS', COUNT(*) FROM STG_ORDERS
UNION ALL
SELECT 'STG_EVENTS', COUNT(*) FROM STG_EVENTS
UNION ALL
SELECT 'DIM_USERS', COUNT(*) FROM DIM_USERS;

-- Data freshness
SELECT MAX(generated_at) as latest_data FROM EXT_LIVE_USERS;
```

## 🎯 **Success Criteria**

| Metric | Expected Value | Status |
|--------|---------------|---------|
| Total Duration | 1-2 minutes | ✅ |
| Records Generated | 95,000 | ✅ |
| S3 Upload Size | ~18 MB | ✅ |
| dbt Models Built | 4 models | ✅ |
| dbt Tests | All pass | ✅ |
| External Tables | 3 tables | ✅ |
| Final Validation | All counts match | ✅ |

## 🔗 **Useful Links**

- **dbt docs**: http://localhost:8080
- **Airflow** (if setup): http://localhost:9090
- **Snowflake Console**: https://app.snowflake.com/
- **AWS S3 Console**: https://s3.console.aws.amazon.com/

## 📞 **Support**

### **Log Files:**
- Pipeline results: `pipeline_results_*.yaml`
- dbt logs: `dbt_live/logs/`
- System logs: Check terminal output

### **Key Configuration:**
- Pipeline config: `config/live_pipeline_config.yml`
- dbt profiles: `dbt_live/profiles.yml`
- AWS credentials: `~/.aws/credentials`

---
**🏆 You're running a production-grade modern data stack!**
