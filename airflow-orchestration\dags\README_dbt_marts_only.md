# dbt Marts Only DAG

This DAG (`dbt_marts_only_dag.py`) is a minimal, focused pipeline that runs only the dbt marts models. It's designed for fast, efficient execution of your business logic layer.

## 🎯 Purpose

- **Marts only**: Exclusively runs dbt marts models
- **Fast execution**: Minimal overhead, quick turnaround
- **Business logic focus**: Concentrates on the final business layer
- **Simple workflow**: Straightforward linear execution

## 📋 DAG Structure

### Task Flow
```
start_marts_pipeline
    ↓
dbt_run_marts
    ↓
dbt_test_marts
    ↓
validate_marts_results
    ↓
end_marts_pipeline
```

### Task Details

#### Core Tasks
- **`start_marts_pipeline`**: Start marker
- **`dbt_run_marts`**: Runs only marts models (`dbt run --models marts`)
- **`dbt_test_marts`**: Tests only marts models (`dbt test --models marts`)
- **`validate_marts_results`**: Validates execution results
- **`end_marts_pipeline`**: End marker

## ⚙️ Configuration

### DAG Settings
- **Schedule**: Daily at 3:00 AM (after main data pipeline)
- **Timeout**: 5 minutes per dbt command
- **Retries**: 1 attempt with 2-minute delay
- **Tags**: `dbt`, `marts`, `business-logic`

### Runtime Parameters
```python
params = {
    'run_tests': True,        # Run marts tests
    'validate_results': True, # Validate execution
}
```

## 🚀 Usage

### 1. Manual Trigger (Airflow UI)
1. Go to Airflow UI: http://localhost:8080
2. Find `dbt_marts_only` DAG
3. Click "Trigger DAG"
4. Monitor execution in Graph view

### 2. Command Line Trigger
```bash
# Trigger the DAG
docker-compose exec airflow-webserver airflow dags trigger dbt_marts_only

# Trigger with custom parameters
docker-compose exec airflow-webserver airflow dags trigger dbt_marts_only \
  --conf '{"run_tests": false}'
```

### 3. Scheduled Execution
Runs automatically daily at 3:00 AM. Modify schedule in DAG definition:
```python
schedule_interval='0 3 * * *',  # Daily at 3 AM
```

## 🔧 Customization

### Running Specific Marts Models
Modify the `dbt_run_marts` function to target specific models:

```python
def dbt_run_marts_custom(**context):
    """Run specific marts models"""
    return run_dbt_command('run', models='marts.dim_users marts.fact_orders', **context)
```

### Adding Pre-checks
Add a pre-check task before running marts:

```python
def check_prerequisites(**context):
    """Check if prerequisites are met"""
    # Add your checks here
    logging.info("✅ Prerequisites checked")
    return "Prerequisites OK"

check_task = PythonOperator(
    task_id='check_prerequisites',
    python_callable=check_prerequisites,
    dag=dag,
)

# Update dependencies
start_task >> check_task >> run_marts_task
```

## 🐛 Troubleshooting

### Common Issues

1. **Marts models fail**
   - **Check dependencies**: Ensure staging/source data is available
   - **Review logs**: Check task logs for specific SQL errors
   - **Validate sources**: Ensure external tables are accessible

2. **Timeout errors**
   - **Increase timeout**: Modify timeout in `run_dbt_command`
   - **Optimize models**: Review marts model performance
   - **Check resources**: Ensure Snowflake warehouse has capacity

3. **Test failures**
   - **Review test results**: Check which specific tests failed
   - **Data quality**: Investigate data quality issues
   - **Test logic**: Verify test definitions are correct

### Debug Commands

```bash
# Check dbt marts models manually
docker-compose exec airflow-webserver dbt run --models marts --project-dir /opt/airflow/workspace/dbt_live

# Test marts models manually
docker-compose exec airflow-webserver dbt test --models marts --project-dir /opt/airflow/workspace/dbt_live

# Check marts model compilation
docker-compose exec airflow-webserver dbt compile --models marts --project-dir /opt/airflow/workspace/dbt_live
```

## 📊 Monitoring

### Key Metrics to Monitor
- **Execution time**: How long marts models take to run
- **Success rate**: Percentage of successful executions
- **Test results**: Number of passing/failing tests
- **Model count**: Number of marts models executed

### Logs to Check
- **Task logs**: Individual task execution details
- **dbt logs**: Detailed dbt execution information
- **Validation results**: Model success/failure summary

## 🔄 Integration

### Use Cases

1. **Standalone execution**: Run marts independently
2. **After data ingestion**: Trigger after main ETL pipeline
3. **On-demand refresh**: Manual business logic updates
4. **Testing**: Validate marts models in isolation

### Integration with Other DAGs

```python
# Trigger from another DAG
from airflow.operators.trigger_dagrun import TriggerDagRunOperator

trigger_marts = TriggerDagRunOperator(
    task_id='trigger_marts_only',
    trigger_dag_id='dbt_marts_only',
    dag=your_main_dag,
)

# Wait for completion
from airflow.sensors.external_task import ExternalTaskSensor

wait_for_marts = ExternalTaskSensor(
    task_id='wait_for_marts',
    external_dag_id='dbt_marts_only',
    external_task_id='end_marts_pipeline',
    dag=your_downstream_dag,
)
```

## ✅ Benefits

### Why Use This DAG?

1. **Speed**: Minimal overhead, fast execution
2. **Focus**: Only business logic, no infrastructure concerns
3. **Simplicity**: Easy to understand and maintain
4. **Flexibility**: Can run independently or as part of larger workflow
5. **Debugging**: Isolates marts issues from other pipeline components

### When to Use

- **Business logic updates**: When only marts models need refresh
- **Testing**: Validating marts model changes
- **Performance**: When full pipeline is too slow
- **Debugging**: Isolating marts-specific issues
- **Incremental updates**: When source data hasn't changed

## 🎉 Success Checklist

- [ ] DAG appears in Airflow UI as `dbt_marts_only`
- [ ] Manual trigger executes successfully
- [ ] All marts models run without errors
- [ ] Marts tests pass
- [ ] Validation task completes successfully
- [ ] Execution time is reasonable (< 5 minutes)
- [ ] Logs show expected marts model execution

This minimal DAG provides a fast, focused way to execute your dbt marts models! 🚀
