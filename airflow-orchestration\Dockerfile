# Custom Airflow Docker image with dbt-core installed
FROM apache/airflow:2.8.1-python3.10

# Switch to root user to install system dependencies
USER root

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Switch back to airflow user for Python package installation
USER airflow

# Install dbt-core and dbt-snowflake
RUN pip install --no-cache-dir \
    dbt-core==1.9.6 \
    dbt-snowflake==1.9.4 \
    boto3==1.34.0 \
    snowflake-connector-python==3.15.0 \
    pandas==2.1.4 \
    pyyaml==6.0.1 \
    faker==22.0.0

# Verify dbt installation
RUN dbt --version

# Create workspace directory structure
RUN mkdir -p /opt/airflow/workspace/dbt_live

# Set environment variables for dbt
ENV DBT_PROFILES_DIR=/opt/airflow/workspace/dbt_live
ENV DBT_PROJECT_DIR=/opt/airflow/workspace/dbt_live

# Add dbt to PATH (should already be there, but just in case)
ENV PATH="/home/<USER>/.local/bin:$PATH"
