#!/usr/bin/env python3
"""
Simple External Tables Creation for Snowflake
Creates external tables without storage integration for immediate testing.
"""

import snowflake.connector
import logging
import yaml
import boto3

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_external_tables():
    """Create external tables using AWS credentials directly."""

    # Load configuration
    with open('config/live_pipeline_config.yml', 'r') as f:
        config = yaml.safe_load(f)

    # Get AWS credentials from boto3 session
    session = boto3.Session()
    credentials = session.get_credentials()

    if not credentials:
        logger.error("❌ AWS credentials not found. Please configure AWS CLI.")
        return False

    # Connection parameters
    connection_params = {
        'account': config['snowflake']['account'],
        'user': config['snowflake']['user'],
        'password': 'Asdfjkll1234!@#$',
        'warehouse': config['snowflake']['warehouse'],
        'database': config['snowflake']['database'],
        'schema': config['snowflake']['schema'],
        'role': config['snowflake']['role']
    }

    bucket_name = config['s3']['input_bucket']

    try:
        logger.info("🔄 Connecting to Snowflake...")
        conn = snowflake.connector.connect(**connection_params)
        cursor = conn.cursor()

        # Set context
        cursor.execute(f"USE DATABASE {config['snowflake']['database']}")
        cursor.execute(f"USE SCHEMA {config['snowflake']['schema']}")

        logger.info("✅ Connected to Snowflake successfully")

        # Create file format
        logger.info("🔄 Creating file format...")
        cursor.execute("""
            CREATE OR REPLACE FILE FORMAT CSV_LIVE_FORMAT
              TYPE = 'CSV'
              FIELD_DELIMITER = ','
              RECORD_DELIMITER = '\\n'
              SKIP_HEADER = 1
              FIELD_OPTIONALLY_ENCLOSED_BY = '"'
              TRIM_SPACE = TRUE
              ERROR_ON_COLUMN_COUNT_MISMATCH = FALSE
              NULL_IF = ('NULL', 'null', '', 'N/A', 'n/a')
        """)
        logger.info("✅ File format created")

        # Create external stages with AWS credentials
        logger.info("🔄 Creating external stages...")

        # Users stage
        cursor.execute(f"""
            CREATE OR REPLACE STAGE S3_LIVE_USERS_STAGE
              URL = 's3://{bucket_name}/live-data/users/'
              CREDENTIALS = (AWS_KEY_ID = '{credentials.access_key}'
                           AWS_SECRET_KEY = '{credentials.secret_key}')
              FILE_FORMAT = CSV_LIVE_FORMAT
        """)

        # Orders stage
        cursor.execute(f"""
            CREATE OR REPLACE STAGE S3_LIVE_ORDERS_STAGE
              URL = 's3://{bucket_name}/live-data/orders/'
              CREDENTIALS = (AWS_KEY_ID = '{credentials.access_key}'
                           AWS_SECRET_KEY = '{credentials.secret_key}')
              FILE_FORMAT = CSV_LIVE_FORMAT
        """)

        # Events stage
        cursor.execute(f"""
            CREATE OR REPLACE STAGE S3_LIVE_EVENTS_STAGE
              URL = 's3://{bucket_name}/live-data/events/'
              CREDENTIALS = (AWS_KEY_ID = '{credentials.access_key}'
                           AWS_SECRET_KEY = '{credentials.secret_key}')
              FILE_FORMAT = CSV_LIVE_FORMAT
        """)

        logger.info("✅ External stages created")

        # Create external tables
        logger.info("🔄 Creating external tables...")

        # Users external table
        cursor.execute("""
            CREATE OR REPLACE EXTERNAL TABLE EXT_LIVE_USERS (
              id STRING AS (value:c1::STRING),
              firstname STRING AS (value:c2::STRING),
              lastname STRING AS (value:c3::STRING),
              email STRING AS (value:c4::STRING),
              address STRING AS (value:c5::STRING),
              canal STRING AS (value:c6::STRING),
              country STRING AS (value:c7::STRING),
              creation_date STRING AS (value:c8::STRING),
              last_activity_date STRING AS (value:c9::STRING),
              gender NUMBER AS (value:c10::NUMBER),
              age_group NUMBER AS (value:c11::NUMBER),
              churn BOOLEAN AS (value:c12::BOOLEAN),
              batch_id STRING AS (value:c13::STRING),
              generated_at STRING AS (value:c14::STRING)
            )
            WITH LOCATION = @S3_LIVE_USERS_STAGE
            FILE_FORMAT = CSV_LIVE_FORMAT
            AUTO_REFRESH = TRUE
        """)
        logger.info("✅ EXT_LIVE_USERS created")

        # Orders external table
        cursor.execute("""
            CREATE OR REPLACE EXTERNAL TABLE EXT_LIVE_ORDERS (
              id STRING AS (value:c1::STRING),
              user_id STRING AS (value:c2::STRING),
              transaction_date STRING AS (value:c3::STRING),
              item_count NUMBER AS (value:c4::NUMBER),
              amount NUMBER AS (value:c5::NUMBER),
              batch_id STRING AS (value:c6::STRING),
              generated_at STRING AS (value:c7::STRING)
            )
            WITH LOCATION = @S3_LIVE_ORDERS_STAGE
            FILE_FORMAT = CSV_LIVE_FORMAT
            AUTO_REFRESH = TRUE
        """)
        logger.info("✅ EXT_LIVE_ORDERS created")

        # Events external table
        cursor.execute("""
            CREATE OR REPLACE EXTERNAL TABLE EXT_LIVE_EVENTS (
              user_id STRING AS (value:c1::STRING),
              event_id STRING AS (value:c2::STRING),
              platform STRING AS (value:c3::STRING),
              date STRING AS (value:c4::STRING),
              action STRING AS (value:c5::STRING),
              session_id STRING AS (value:c6::STRING),
              url STRING AS (value:c7::STRING),
              batch_id STRING AS (value:c8::STRING),
              generated_at STRING AS (value:c9::STRING)
            )
            WITH LOCATION = @S3_LIVE_EVENTS_STAGE
            FILE_FORMAT = CSV_LIVE_FORMAT
            AUTO_REFRESH = TRUE
        """)
        logger.info("✅ EXT_LIVE_EVENTS created")

        # Test the tables
        logger.info("🧪 Testing external tables...")

        tables = ['EXT_LIVE_USERS', 'EXT_LIVE_ORDERS', 'EXT_LIVE_EVENTS']
        for table in tables:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                logger.info(f"✅ {table}: {count:,} records")
            except Exception as e:
                logger.warning(f"⚠️ {table}: {e}")

        cursor.close()
        conn.close()

        logger.info("🎉 External tables setup completed successfully!")
        return True

    except Exception as e:
        logger.error(f"❌ Setup failed: {e}")
        return False

if __name__ == "__main__":
    create_external_tables()
