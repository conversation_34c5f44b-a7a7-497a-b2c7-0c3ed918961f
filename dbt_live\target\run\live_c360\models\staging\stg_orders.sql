
  create or replace   view MYDB.LIVE_DATA.stg_orders
  
   as (
    

-- Staging model for live orders data
-- Cleans and standardizes raw order data from S3 external table

with source_data as (
    select * from MYDB.LIVE_DATA.ext_live_orders
),

cleaned_data as (
    select
        -- Primary key
        id as order_id,
        user_id,
        
        -- Transaction details
        try_to_timestamp(transaction_date, 'MM-DD-YYYY HH24:MI:SS') as transaction_date,
        item_count,
        amount,
        
        -- Metadata
        batch_id,
        try_to_timestamp(generated_at) as generated_at,
        current_timestamp() as processed_at
        
    from source_data
    where id is not null  -- Filter out any null order IDs
      and user_id is not null  -- Filter out orders without user IDs
      and amount > 0  -- Filter out invalid amounts
),

final as (
    select
        *,
        -- Derived fields
        round(amount / item_count, 2) as avg_item_price,
        
        -- Date extractions
        date(transaction_date) as transaction_date_only,
        extract(year from transaction_date) as transaction_year,
        extract(month from transaction_date) as transaction_month,
        extract(day from transaction_date) as transaction_day,
        extract(hour from transaction_date) as transaction_hour,
        dayname(transaction_date) as transaction_day_name,
        
        -- Time-based segments
        case 
            when extract(hour from transaction_date) between 6 and 11 then 'Morning'
            when extract(hour from transaction_date) between 12 and 17 then 'Afternoon'
            when extract(hour from transaction_date) between 18 and 22 then 'Evening'
            else 'Night'
        end as time_of_day_segment,
        
        -- Order size segments
        case 
            when amount < 25 then 'Small'
            when amount < 75 then 'Medium'
            when amount < 150 then 'Large'
            else 'Extra Large'
        end as order_size_segment,
        
        -- Item quantity segments
        case 
            when item_count = 1 then 'Single Item'
            when item_count <= 3 then 'Few Items'
            when item_count <= 5 then 'Multiple Items'
            else 'Bulk Order'
        end as quantity_segment,
        
        -- Data quality flags
        case 
            when transaction_date > current_timestamp() then true
            else false
        end as has_future_transaction_date,
        
        case 
            when avg_item_price > 500 then true  -- Flag unusually expensive items
            else false
        end as has_high_item_price,
        
        -- Recency
        datediff('day', transaction_date, current_timestamp()) as days_since_transaction
        
    from cleaned_data
)

select * from final
  );

