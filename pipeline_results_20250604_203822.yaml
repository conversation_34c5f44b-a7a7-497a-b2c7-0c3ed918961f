duration_minutes: 1.55
duration_seconds: 92.919197
end_time: '2025-06-04T20:38:22.512770'
start_time: '2025-06-04T20:36:49.593573'
steps:
  step1_generate_data:
    date: '2025-06-04'
    events_count: 75000
    files:
      events: live-data/events/daily_events_20250604.csv
      orders: live-data/orders/daily_orders_20250604.csv
      users: live-data/users/daily_users_20250604.csv
    orders_count: 15000
    success: true
    total_records: 95000
    users_count: 5000
  step2_verify_s3:
    events:
      exists: true
      last_modified: '2025-06-04T10:37:10+00:00'
      size_bytes: 16321737
      size_mb: 15.57
    orders:
      exists: true
      last_modified: '2025-06-04T10:36:58+00:00'
      size_bytes: 2183968
      size_mb: 2.08
    users:
      exists: true
      last_modified: '2025-06-04T10:36:57+00:00'
      size_bytes: 1119390
      size_mb: 1.07
  step3_refresh_stages:
    EXT_LIVE_EVENTS:
      count_after: 450000
      count_before: 450000
      new_records: 0
      refresh_success: true
    EXT_LIVE_ORDERS:
      count_after: 90000
      count_before: 90000
      new_records: 0
      refresh_success: true
    EXT_LIVE_USERS:
      count_after: 30000
      count_before: 30000
      new_records: 0
      refresh_success: true
  step4_dbt_staging:
    staging_run:
      returncode: 0
      stderr: ''
      stdout: "10:37:25  Running with dbt=1.9.6\n10:37:27  Registered adapter: snowflake=1.9.4\n\
        10:37:28  Found 12 models, 21 data tests, 3 sources, 592 macros\n10:37:28\
        \  \n10:37:28  Concurrency: 4 threads (target='live')\n10:37:28  \n10:37:30\
        \  1 of 3 START sql view model LIVE_DATA.stg_events ...............................\
        \ [RUN]\n10:37:30  2 of 3 START sql view model LIVE_DATA.stg_orders ...............................\
        \ [RUN]\n10:37:30  3 of 3 START sql view model LIVE_DATA.stg_users ................................\
        \ [RUN]\n10:37:32  3 of 3 OK created sql view model LIVE_DATA.stg_users ...........................\
        \ [SUCCESS 1 in 1.07s]\n10:37:32  1 of 3 OK created sql view model LIVE_DATA.stg_events\
        \ .......................... [SUCCESS 1 in 1.20s]\n10:37:32  2 of 3 OK created\
        \ sql view model LIVE_DATA.stg_orders .......................... [SUCCESS\
        \ 1 in 1.23s]\n10:37:33  \n10:37:33  Finished running 3 view models in 0 hours\
        \ 0 minutes and 4.28 seconds (4.28s).\n10:37:33  \n10:37:33  Completed successfully\n\
        10:37:33  \n10:37:33  Done. PASS=3 WARN=0 ERROR=0 SKIP=0 TOTAL=3\n"
  step5_dbt_marts:
    marts_run:
      returncode: 0
      stderr: ''
      stdout: "10:37:36  Running with dbt=1.9.6\n10:37:37  Registered adapter: snowflake=1.9.4\n\
        10:37:39  Found 12 models, 21 data tests, 3 sources, 592 macros\n10:37:39\
        \  \n10:37:39  Concurrency: 4 threads (target='live')\n10:37:39  \n10:37:41\
        \  1 of 4 START sql table model LIVE_DATA.dim_users ...............................\
        \ [RUN]\n10:37:41  2 of 4 START sql incremental model LIVE_DATA.dim_users_scd2\
        \ .................... [RUN]\n10:37:41  3 of 4 START sql table model LIVE_DATA.fact_orders\
        \ ............................. [RUN]\n10:37:41  4 of 4 START sql view model\
        \ LIVE_DATA_ANALYTICS.mv_fact_orders ................. [RUN]\n10:37:42  4\
        \ of 4 OK created sql view model LIVE_DATA_ANALYTICS.mv_fact_orders ............\
        \ [SUCCESS 1 in 1.26s]\n10:37:44  3 of 4 OK created sql table model LIVE_DATA.fact_orders\
        \ ........................ [SUCCESS 1 in 2.68s]\n10:37:45  1 of 4 OK created\
        \ sql table model LIVE_DATA.dim_users .......................... [SUCCESS\
        \ 1 in 3.81s]\n10:37:45  2 of 4 OK created sql incremental model LIVE_DATA.dim_users_scd2\
        \ ............... [SUCCESS 5000 in 4.15s]\n10:37:46  \n10:37:46  Finished\
        \ running 1 incremental model, 2 table models, 1 view model in 0 hours 0 minutes\
        \ and 7.18 seconds (7.18s).\n10:37:46  \n10:37:46  Completed successfully\n\
        10:37:46  \n10:37:46  Done. PASS=4 WARN=0 ERROR=0 SKIP=0 TOTAL=4\n"
  step6_dbt_monitoring:
    monitoring_run:
      returncode: 0
      stderr: ''
      stdout: "10:37:49  Running with dbt=1.9.6\n10:37:51  Registered adapter: snowflake=1.9.4\n\
        10:37:52  Found 12 models, 21 data tests, 3 sources, 592 macros\n10:37:52\
        \  \n10:37:52  Concurrency: 4 threads (target='live')\n10:37:52  \n10:37:55\
        \  1 of 5 START sql table model LIVE_DATA.data_quality_health .....................\
        \ [RUN]\n10:37:55  2 of 5 START sql table model LIVE_DATA.dbt_test_health\
        \ ......................... [RUN]\n10:37:55  3 of 5 START sql table model\
        \ LIVE_DATA.pipeline_runtime_health ................. [RUN]\n10:37:55  4 of\
        \ 5 START sql table model LIVE_DATA.query_history_health ....................\
        \ [RUN]\n10:37:57  2 of 5 OK created sql table model LIVE_DATA.dbt_test_health\
        \ .................... [SUCCESS 1 in 1.84s]\n10:37:59  1 of 5 OK created sql\
        \ table model LIVE_DATA.data_quality_health ................ [SUCCESS 1 in\
        \ 3.70s]\n10:37:59  5 of 5 START sql table model LIVE_DATA.etl_health_dashboard\
        \ .................... [RUN]\n10:37:59  4 of 5 OK created sql table model\
        \ LIVE_DATA.query_history_health ............... [SUCCESS 1 in 3.83s]\n10:37:59\
        \  3 of 5 OK created sql table model LIVE_DATA.pipeline_runtime_health ............\
        \ [SUCCESS 1 in 3.88s]\n10:37:59  5 of 5 OK created sql table model LIVE_DATA.etl_health_dashboard\
        \ ............... [SUCCESS 1 in 0.64s]\n10:38:00  \n10:38:00  Finished running\
        \ 5 table models in 0 hours 0 minutes and 7.55 seconds (7.55s).\n10:38:00\
        \  \n10:38:00  Completed successfully\n10:38:00  \n10:38:00  Done. PASS=5\
        \ WARN=0 ERROR=0 SKIP=0 TOTAL=5\n"
  step7_dbt_tests:
    test:
      returncode: 0
      stderr: ''
      stdout: "10:38:03  Running with dbt=1.9.6\n10:38:05  Registered adapter: snowflake=1.9.4\n\
        10:38:06  Found 12 models, 21 data tests, 3 sources, 592 macros\n10:38:06\
        \  \n10:38:06  Concurrency: 4 threads (target='live')\n10:38:06  \n10:38:08\
        \  1 of 21 START test dbt_utils_source_accepted_range_live_external_ext_live_orders_amount__False__0\
        \  [RUN]\n10:38:08  2 of 21 START test dbt_utils_source_accepted_range_live_external_ext_live_orders_item_count__100__1\
        \  [RUN]\n10:38:08  3 of 21 START test source_accepted_values_live_external_ext_live_events_action__view__click__log__purchase\
        \  [RUN]\n10:38:08  4 of 21 START test source_accepted_values_live_external_ext_live_events_platform__ios__android__web__None\
        \  [RUN]\n10:38:10  1 of 21 PASS dbt_utils_source_accepted_range_live_external_ext_live_orders_amount__False__0\
        \  [PASS in 2.01s]\n10:38:10  5 of 21 START test source_not_null_live_external_ext_live_events_event_id\
        \ ...... [RUN]\n10:38:10  2 of 21 PASS dbt_utils_source_accepted_range_live_external_ext_live_orders_item_count__100__1\
        \  [PASS in 2.07s]\n10:38:10  6 of 21 START test source_not_null_live_external_ext_live_events_generated_at\
        \ .. [RUN]\n10:38:10  3 of 21 PASS source_accepted_values_live_external_ext_live_events_action__view__click__log__purchase\
        \  [PASS in 2.12s]\n10:38:10  7 of 21 START test source_not_null_live_external_ext_live_events_session_id\
        \ .... [RUN]\n10:38:11  4 of 21 PASS source_accepted_values_live_external_ext_live_events_platform__ios__android__web__None\
        \  [PASS in 2.38s]\n10:38:11  8 of 21 START test source_not_null_live_external_ext_live_events_user_id\
        \ ....... [RUN]\n10:38:12  5 of 21 PASS source_not_null_live_external_ext_live_events_event_id\
        \ ............ [PASS in 1.80s]\n10:38:12  9 of 21 START test source_not_null_live_external_ext_live_orders_amount\
        \ ........ [RUN]\n10:38:12  6 of 21 PASS source_not_null_live_external_ext_live_events_generated_at\
        \ ........ [PASS in 1.82s]\n10:38:12  10 of 21 START test source_not_null_live_external_ext_live_orders_generated_at\
        \ . [RUN]\n10:38:12  7 of 21 PASS source_not_null_live_external_ext_live_events_session_id\
        \ .......... [PASS in 1.82s]\n10:38:12  11 of 21 START test source_not_null_live_external_ext_live_orders_id\
        \ ........... [RUN]\n10:38:12  8 of 21 PASS source_not_null_live_external_ext_live_events_user_id\
        \ ............. [PASS in 1.65s]\n10:38:12  12 of 21 START test source_not_null_live_external_ext_live_orders_item_count\
        \ ... [RUN]\n10:38:13  9 of 21 PASS source_not_null_live_external_ext_live_orders_amount\
        \ .............. [PASS in 0.86s]\n10:38:13  13 of 21 START test source_not_null_live_external_ext_live_orders_user_id\
        \ ...... [RUN]\n10:38:13  11 of 21 PASS source_not_null_live_external_ext_live_orders_id\
        \ ................. [PASS in 0.86s]\n10:38:13  14 of 21 START test source_not_null_live_external_ext_live_users_email\
        \ ......... [RUN]\n10:38:13  12 of 21 PASS source_not_null_live_external_ext_live_orders_item_count\
        \ ......... [PASS in 0.87s]\n10:38:13  10 of 21 PASS source_not_null_live_external_ext_live_orders_generated_at\
        \ ....... [PASS in 1.02s]\n10:38:13  15 of 21 START test source_not_null_live_external_ext_live_users_generated_at\
        \ .. [RUN]\n10:38:13  16 of 21 START test source_not_null_live_external_ext_live_users_id\
        \ ............ [RUN]\n10:38:13  13 of 21 PASS source_not_null_live_external_ext_live_orders_user_id\
        \ ............ [PASS in 0.63s]\n10:38:13  17 of 21 START test source_relationships_live_external_ext_live_events_user_id__id__source_live_external_ext_live_users_\
        \  [RUN]\n10:38:14  15 of 21 PASS source_not_null_live_external_ext_live_users_generated_at\
        \ ........ [PASS in 0.80s]\n10:38:14  18 of 21 START test source_relationships_live_external_ext_live_orders_user_id__id__source_live_external_ext_live_users_\
        \  [RUN]\n10:38:14  14 of 21 PASS source_not_null_live_external_ext_live_users_email\
        \ ............... [PASS in 0.88s]\n10:38:14  19 of 21 START test source_unique_live_external_ext_live_events_event_id\
        \ ....... [RUN]\n10:38:14  16 of 21 PASS source_not_null_live_external_ext_live_users_id\
        \ .................. [PASS in 0.84s]\n10:38:14  20 of 21 START test source_unique_live_external_ext_live_orders_id\
        \ ............. [RUN]\n10:38:15  20 of 21 PASS source_unique_live_external_ext_live_orders_id\
        \ ................... [PASS in 1.22s]\n10:38:15  21 of 21 START test source_unique_live_external_ext_live_users_id\
        \ .............. [RUN]\n10:38:15  19 of 21 PASS source_unique_live_external_ext_live_events_event_id\
        \ ............. [PASS in 1.57s]\n10:38:16  17 of 21 WARN 396000 source_relationships_live_external_ext_live_events_user_id__id__source_live_external_ext_live_users_\
        \  [WARN 396000 in 2.15s]\n10:38:16  18 of 21 WARN 72000 source_relationships_live_external_ext_live_orders_user_id__id__source_live_external_ext_live_users_\
        \  [WARN 72000 in 1.75s]\n10:38:16  21 of 21 PASS source_unique_live_external_ext_live_users_id\
        \ .................... [PASS in 0.75s]\n10:38:16  \n10:38:16  Finished running\
        \ 21 data tests in 0 hours 0 minutes and 10.45 seconds (10.45s).\n10:38:17\
        \  \n10:38:17  Completed with 2 warnings:\n10:38:17  \n10:38:17  Warning in\
        \ test source_relationships_live_external_ext_live_events_user_id__id__source_live_external_ext_live_users_\
        \ (models\\sources.yml)\n10:38:17  Got 396000 results, configured to warn\
        \ if != 0\n10:38:17  \n10:38:17    compiled code at target\\compiled\\live_c360\\\
        models\\sources.yml\\source_relationships_live_exte_f8dcab25f08d3d0cecc66acc19c0f320.sql\n\
        10:38:17  \n10:38:17    See test failures:\n  ------------------------------------------------------------------------------------------------------------\n\
        \  select * from MYDB.LIVE_DATA_dbt_test__audit.source_relationships_live_exte_f8dcab25f08d3d0cecc66acc19c0f320\n\
        \  ------------------------------------------------------------------------------------------------------------\n\
        10:38:17  \n10:38:17  Warning in test source_relationships_live_external_ext_live_orders_user_id__id__source_live_external_ext_live_users_\
        \ (models\\sources.yml)\n10:38:17  Got 72000 results, configured to warn if\
        \ != 0\n10:38:17  \n10:38:17    compiled code at target\\compiled\\live_c360\\\
        models\\sources.yml\\source_relationships_live_exte_3905f58a8f4f802f3d4269e076a3f201.sql\n\
        10:38:17  \n10:38:17    See test failures:\n  ------------------------------------------------------------------------------------------------------------\n\
        \  select * from MYDB.LIVE_DATA_dbt_test__audit.source_relationships_live_exte_3905f58a8f4f802f3d4269e076a3f201\n\
        \  ------------------------------------------------------------------------------------------------------------\n\
        10:38:17  \n10:38:17  Done. PASS=19 WARN=2 ERROR=0 SKIP=0 TOTAL=21\n"
  step8_validate_results:
    analytics_mv_fact_orders: 90000
    data_freshness_hours: 'Error: unsupported operand type(s) for -: ''datetime.datetime''
      and ''str'''
    external_ext_live_events: 450000
    external_ext_live_orders: 90000
    external_ext_live_users: 30000
    marts_dim_users: 30000
    marts_dim_users_scd2: 45000
    marts_fact_orders: 90000
    monitoring_data_quality_health: 5
    monitoring_dbt_test_health: 5
    monitoring_etl_health_dashboard: 2
    monitoring_pipeline_runtime_health: 24
    monitoring_query_history_health: 27
    staging_stg_events: 450000
    staging_stg_orders: 90000
    staging_stg_users: 30000
success: true
target_date: '2025-06-04'
