[2025-06-04T04:32:45.281+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=non-requeueable deps ti=<TaskInstance: etl_monitoring_hourly.update_monitoring_models scheduled__2025-06-03T19:00:00+00:00 [queued]>
[2025-06-04T04:32:45.317+0000] {taskinstance.py:1956} INFO - Dependencies all met for dep_context=requeueable deps ti=<TaskInstance: etl_monitoring_hourly.update_monitoring_models scheduled__2025-06-03T19:00:00+00:00 [queued]>
[2025-06-04T04:32:45.322+0000] {taskinstance.py:2170} INFO - Starting attempt 2 of 2
[2025-06-04T04:32:45.371+0000] {taskinstance.py:2191} INFO - Executing <Task(PythonOperator): update_monitoring_models> on 2025-06-03 19:00:00+00:00
[2025-06-04T04:32:45.385+0000] {standard_task_runner.py:87} INFO - Running: ['***', 'tasks', 'run', 'etl_monitoring_hourly', 'update_monitoring_models', 'scheduled__2025-06-03T19:00:00+00:00', '--job-id', '72', '--raw', '--subdir', 'DAGS_FOLDER/monitoring_dag.py', '--cfg-path', '/tmp/tmpm3w_e2i3']
[2025-06-04T04:32:45.381+0000] {standard_task_runner.py:60} INFO - Started process 67 to run task
[2025-06-04T04:32:45.390+0000] {standard_task_runner.py:88} INFO - Job 72: Subtask update_monitoring_models
[2025-06-04T04:32:45.471+0000] {task_command.py:423} INFO - Running <TaskInstance: etl_monitoring_hourly.update_monitoring_models scheduled__2025-06-03T19:00:00+00:00 [running]> on host 8fb7c8dc5097
[2025-06-04T04:32:45.550+0000] {taskinstance.py:2480} INFO - Exporting env vars: AIRFLOW_CTX_DAG_OWNER='data-team' AIRFLOW_CTX_DAG_ID='etl_monitoring_hourly' AIRFLOW_CTX_TASK_ID='update_monitoring_models' AIRFLOW_CTX_EXECUTION_DATE='2025-06-03T19:00:00+00:00' AIRFLOW_CTX_TRY_NUMBER='2' AIRFLOW_CTX_DAG_RUN_ID='scheduled__2025-06-03T19:00:00+00:00'
[2025-06-04T04:32:45.553+0000] {monitoring_dag.py:43} INFO - Updating monitoring models...
[2025-06-04T04:32:52.687+0000] {monitoring_dag.py:53} ERROR - Monitoring models update failed: 
[2025-06-04T04:32:52.689+0000] {taskinstance.py:2698} ERROR - Task failed with exception
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/models/taskinstance.py", line 433, in _execute_task
    result = execute_callable(context=context, **execute_callable_kwargs)
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/operators/python.py", line 199, in execute
    return_value = self.execute_callable()
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/operators/python.py", line 216, in execute_callable
    return self.python_callable(*self.op_args, **self.op_kwargs)
  File "/opt/airflow/dags/monitoring_dag.py", line 54, in update_monitoring_models
    raise Exception(f"Monitoring models update failed: {result.stderr}")
Exception: Monitoring models update failed: 
[2025-06-04T04:32:52.722+0000] {taskinstance.py:1138} INFO - Marking task as FAILED. dag_id=etl_monitoring_hourly, task_id=update_monitoring_models, execution_date=20250603T190000, start_date=20250604T043245, end_date=20250604T043252
[2025-06-04T04:32:52.748+0000] {standard_task_runner.py:107} ERROR - Failed to execute job 72 for task update_monitoring_models (Monitoring models update failed: ; 67)
[2025-06-04T04:32:52.772+0000] {local_task_job_runner.py:234} INFO - Task exited with return code 1
[2025-06-04T04:32:52.795+0000] {taskinstance.py:3280} INFO - 0 downstream tasks scheduled from follow-on schedule check
